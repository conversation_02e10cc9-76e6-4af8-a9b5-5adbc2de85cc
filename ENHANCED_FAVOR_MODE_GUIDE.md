# Enhanced Favor Mode - Complete User Guide

## Overview

The Enhanced Favor Mode is a powerful developer feature that allows you to influence the game outcome by favoring a specific cartella (bingo card). This enhanced version includes real-time switching, advanced hotkeys, animated indicators, and comprehensive functionality.

## 🎮 Enhanced Hotkey Controls

### 1. **Ctrl+Shift+[Number]** - Toggle Favor Mode
- **Function**: Activate or deactivate favor mode for a specific cartella
- **Multi-digit Support**: Type multiple digits within 1.5 seconds for cartella numbers > 9
- **Examples**:
  - `Ctrl+Shift+5` → Toggle favor for cartella 5
  - `Ctrl+Shift+1,2` → Toggle favor for cartella 12 (type 1 then 2 quickly)
  - `Ctrl+Shift+2,5` → Toggle favor for cartella 25
- **Behavior**:
  - If inactive: Activates favor mode for the specified cartella
  - If active for same cartella: Deactivates favor mode
  - If active for different cartella: Switches to the new cartella

### 2. **Ctrl+Alt+[Number]** - Quick Switch (Real-time)
- **Function**: Instantly switch favor to a different cartella during gameplay
- **Single-digit Only**: Works with numbers 1-9
- **Examples**:
  - `Ctrl+Alt+3` → Switch favor to cartella 3
  - `Ctrl+Alt+7` → Switch favor to cartella 7
- **Behavior**:
  - If inactive: Activates favor mode for the specified cartella
  - If active: Switches favor to the new cartella with animation

### 3. **Ctrl+Shift+D** - Force Deactivate
- **Function**: Immediately turn off favor mode regardless of current state
- **Usage**: Emergency deactivation hotkey
- **Behavior**: Always deactivates favor mode with deactivation animation

### 4. **Alt+F** - Emergency Toggle
- **Function**: Quick toggle favor mode for cartella 1
- **Usage**: Emergency/testing hotkey for quick access
- **Behavior**: Toggles favor mode for cartella 1

## 🎨 Enhanced Visual Indicators

### Activation Animation (1.5 seconds)
- **Appearance**: Expanding green circle with gradient effect
- **Location**: Bottom-left corner of screen
- **Purpose**: Confirms favor mode activation

### Switch Animation (1.0 second)
- **Appearance**: Color transition from red to green
- **Location**: Bottom-left corner of screen
- **Purpose**: Confirms real-time switching between cartellas

### Deactivation Animation (1.0 second)
- **Appearance**: Shrinking red circle
- **Location**: Bottom-left corner of screen
- **Purpose**: Confirms favor mode deactivation

**Note**: No persistent indicator is shown while favor mode is active - only temporary animations appear during state changes for a clean, unobtrusive experience.

## ⚡ Real-time Features

### During Gameplay
- **Switch Targets**: Change favored cartella without stopping the game
- **Immediate Effect**: New favor logic applies to the very next number call
- **Visual Feedback**: Switch animation shows the transition
- **No Interruption**: Game continues seamlessly

### Before Gameplay
- **Pre-activation**: Set favor mode before starting the game
- **Pending State**: Favor mode waits for game to start
- **Automatic Application**: Activates when game begins

### Multi-digit Input
- **Buffer System**: Type multiple digits within 1.5 seconds
- **Smart Recognition**: Automatically forms cartella numbers
- **Examples**:
  - Type `1` then `5` quickly → Cartella 15
  - Type `2` then `3` quickly → Cartella 23

## 🔄 Game Reset Behavior

### Comprehensive Cleanup
- **Automatic Deactivation**: Favor mode is always deactivated on game reset
- **Animation Reset**: All animation states are cleared
- **Timer Reset**: All feedback timers are reset
- **State Reset**: All internal states return to default
- **Clean Slate**: Each new game starts with favor mode inactive

### Reset Triggers
- Manual game reset via UI
- Automatic reset after game completion
- Board selection navigation

## 🛠️ Technical Features

### Enhanced Logic
- **Smart Number Selection**: Improved algorithm for natural-looking wins
- **Collision Avoidance**: Prevents other players from winning first
- **Naturalizing Jumps**: Occasionally calls numbers not on favored card for realism
- **Win Guarantee**: Ensures favored cartella wins when conditions are met

### Error Handling
- **Graceful Fallbacks**: Continues working even if some features fail
- **Validation**: Checks cartella existence before activation
- **Safe Operations**: All operations are non-destructive to game state

### Performance
- **Efficient Rendering**: Optimized indicator drawing
- **Minimal Impact**: Low CPU usage for animations
- **Smart Updates**: Only redraws when necessary

## 📊 Status Information

### Available Data
- **Active State**: Whether favor mode is currently active
- **Target Cartella**: Currently favored cartella number
- **Previous Target**: Last favored cartella (for switch operations)
- **Pending State**: Whether activation is waiting for game start
- **Last Action**: Most recent hotkey action performed
- **Timers**: Switch and hotkey feedback timers

### Access Method
```python
status = game.favor_mode.get_status_info()
print(f"Active: {status['active']}")
print(f"Target: {status['target_cartella']}")
```

## 🎯 Usage Examples

### Basic Usage
1. **Start Game**: Launch the bingo game
2. **Activate Favor**: Press `Ctrl+Shift+5` to favor cartella 5
3. **Watch Indicator**: Green pulsing circle appears in bottom-left
4. **Play Game**: Favored cartella will have advantage in winning

### Advanced Usage
1. **Pre-activate**: Set favor before starting game with `Ctrl+Shift+12`
2. **Real-time Switch**: During gameplay, press `Ctrl+Alt+7` to switch to cartella 7
3. **Emergency Stop**: Press `Ctrl+Shift+D` to immediately deactivate
4. **Quick Access**: Use `Alt+F` for instant cartella 1 toggle

### Multi-digit Examples
- **Cartella 15**: Press `Ctrl+Shift+1` then `Ctrl+Shift+5` within 1.5 seconds
- **Cartella 23**: Press `Ctrl+Shift+2` then `Ctrl+Shift+3` within 1.5 seconds
- **Cartella 99**: Press `Ctrl+Shift+9` then `Ctrl+Shift+9` within 1.5 seconds

## ⚠️ Important Notes

### Developer Feature
- This is a developer/testing feature
- Use responsibly in testing environments
- Not intended for production gameplay

### Game Balance
- Favor mode influences but doesn't guarantee immediate wins
- Natural-looking gameplay is maintained
- Other players can still win if conditions align

### Visual Feedback
- All operations provide clear visual feedback
- Animations confirm successful actions
- Persistent indicator shows current state

### Hotkey Conflicts
- Ensure no other applications are capturing the same key combinations
- Hotkeys only work when the game window has focus
- Input fields may block hotkey processing (by design)

## 🔧 Troubleshooting

### Hotkeys Not Working
- Ensure game window has focus
- Check if input fields are active (hotkeys are blocked during text input)
- Verify key combinations are pressed correctly

### Animations Not Showing
- Check if favor mode is properly activated
- Ensure screen is being redrawn (move mouse slightly)
- Verify pygame is functioning correctly

### Favor Not Taking Effect
- Confirm cartella exists in the current game
- Check if game has started (some features wait for game start)
- Verify bingo caller is initialized

The Enhanced Favor Mode provides a comprehensive, professional-grade developer tool for testing and demonstration purposes while maintaining the integrity and natural feel of the bingo game experience.
