def fix_syntax_error_manually():
    # Read the original file line by line
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # Make a backup
    with open('Board_selection_fixed.py.bak3', 'w', encoding='utf-8') as backup:
        backup.writelines(lines)
    
    # Find the line with the problem
    for i in range(len(lines)):
        if 'self.show_message("Invalid prize pool value", "error")' in lines[i] and 'def show_board_selection' in lines[i]:
            # Split the line at the appropriate point
            parts = lines[i].split('def show_board_selection')
            
            # Fix the line by adding proper newlines
            lines[i] = parts[0] + '\n\n' + 'def show_board_selection' + parts[1]
            print(f"Fixed syntax error at line {i+1}")
            break
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.writelines(lines)
    
    print("Fixed syntax error in Board_selection_fixed.py manually")

if __name__ == "__main__":
    fix_syntax_error_manually() 