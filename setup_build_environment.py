#!/usr/bin/env python3
"""
Build Environment Setup Script for WOW Bingo Game
=================================================

This script sets up the build environment by installing all necessary
dependencies and verifying the system is ready for building.

Usage:
    python setup_build_environment.py [--upgrade] [--verbose]
"""

import sys
import subprocess
import importlib
import platform
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with level."""
    print(f"[{level}] {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def check_python_version():
    """Check if Python version is compatible."""
    log("Checking Python version...")

    version = sys.version_info
    if version < (3, 7):
        error(f"Python 3.7 or higher is required. Found: {version.major}.{version.minor}.{version.micro}")

    log(f"Python version: {version.major}.{version.minor}.{version.micro} ✓")

def install_package(package, upgrade=False):
    """Install a Python package using pip."""
    try:
        # Check if package is already installed
        importlib.import_module(package.split('>=')[0].split('==')[0])
        if not upgrade:
            log(f"Package '{package}' already installed ✓")
            return True
    except ImportError:
        pass

    log(f"Installing package: {package}")

    cmd = [sys.executable, '-m', 'pip', 'install']
    if upgrade:
        cmd.append('--upgrade')
    cmd.append(package)

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        log(f"Successfully installed {package} ✓")
        return True
    except subprocess.CalledProcessError as e:
        log(f"Failed to install {package}: {e.stderr}", "ERROR")
        return False

def setup_build_environment(upgrade=False, verbose=False):
    """Set up the complete build environment."""
    log("Setting up WOW Bingo Game build environment...")
    log("=" * 50)

    # Check Python version
    check_python_version()

    # Read requirements
    requirements_file = Path(__file__).parent / "build_requirements.txt"
    if not requirements_file.exists():
        error("build_requirements.txt not found!")

    log("Reading build requirements...")
    with open(requirements_file, 'r') as f:
        lines = f.readlines()

    # Parse requirements (skip comments and empty lines)
    packages = []
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            packages.append(line)

    log(f"Found {len(packages)} required packages")

    # Install packages
    failed_packages = []
    for package in packages:
        if not install_package(package, upgrade):
            failed_packages.append(package)

    if failed_packages:
        log("Failed to install the following packages:", "ERROR")
        for pkg in failed_packages:
            log(f"  - {pkg}", "ERROR")
        error("Build environment setup failed!")

    # Verify Nuitka installation
    log("Verifying Nuitka installation...")
    try:
        result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                              capture_output=True, text=True, check=True)
        nuitka_version = result.stdout.strip()
        log(f"Nuitka version: {nuitka_version} ✓")
    except subprocess.CalledProcessError:
        error("Nuitka verification failed!")

    # Check platform-specific requirements
    log("Checking platform-specific requirements...")
    system = platform.system()

    if system == "Windows":
        log("Windows detected - checking for Visual Studio Build Tools...")
        # Try to detect MSVC
        try:
            result = subprocess.run(['where', 'cl'], capture_output=True, text=True)
            if result.returncode == 0:
                log("Visual Studio Build Tools detected ✓")
            else:
                log("Visual Studio Build Tools not found - builds may be slower", "WARNING")
                log("Consider installing Visual Studio Build Tools for better performance", "WARNING")
        except:
            log("Could not check for Visual Studio Build Tools", "WARNING")

    elif system == "Linux":
        log("Linux detected - checking for GCC...")
        try:
            result = subprocess.run(['gcc', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                log("GCC compiler detected ✓")
            else:
                log("GCC not found - install build-essential package", "WARNING")
        except:
            log("GCC not found - install build-essential package", "WARNING")

    elif system == "Darwin":
        log("macOS detected - checking for Xcode tools...")
        try:
            result = subprocess.run(['xcode-select', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                log("Xcode command line tools detected ✓")
            else:
                log("Xcode tools not found - run 'xcode-select --install'", "WARNING")
        except:
            log("Xcode tools not found - run 'xcode-select --install'", "WARNING")

    # Final verification
    log("Performing final verification...")

    # Check if main script exists
    main_script = Path(__file__).parent / "main.py"
    if main_script.exists():
        log("Main game script found ✓")
    else:
        log("Main game script (main.py) not found", "WARNING")

    # Check if assets directory exists
    assets_dir = Path(__file__).parent / "assets"
    if assets_dir.exists():
        log("Assets directory found ✓")
    else:
        log("Assets directory not found", "WARNING")

    # Check if enhanced build script exists
    enhanced_build_script = Path(__file__).parent / "enhanced_nuitka_build.py"
    if enhanced_build_script.exists():
        log("Enhanced build script found ✓")
    else:
        log("Enhanced build script not found", "WARNING")
        log("The enhanced build script provides better error handling and optimization")

    # Check if batch script exists (Windows)
    if platform.system() == "Windows":
        batch_script = Path(__file__).parent / "enhanced_build.bat"
        if batch_script.exists():
            log("Enhanced build batch script found ✓")
        else:
            log("Enhanced build batch script not found", "WARNING")

    log("=" * 50)
    log("Build environment setup completed successfully! ✓")
    log("=" * 50)
    log("")
    log("Next steps:")
    log("1. Run the enhanced build script:")
    if platform.system() == "Windows":
        log("   Windows (Easy): enhanced_build.bat")
        log("   Windows (Direct): python enhanced_nuitka_build.py")
    else:
        log("   Direct: python enhanced_nuitka_build.py")
    log("")
    log("2. Available build options:")
    log("   - Basic build: python enhanced_nuitka_build.py")
    log("   - Clean build: python enhanced_nuitka_build.py --clean")
    log("   - Optimized build: python enhanced_nuitka_build.py --optimize")
    log("   - Debug build: python enhanced_nuitka_build.py --debug")
    log("   - Verify only: python enhanced_nuitka_build.py --verify")
    log("")
    log("3. The enhanced build system features:")
    log("   - Automatic dependency detection")
    log("   - Comprehensive asset bundling")
    log("   - Error-free Nuitka configuration")
    log("   - Build verification and testing")
    log("   - Detailed build reports")
    log("")

def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Set up build environment for WOW Bingo Game"
    )
    parser.add_argument(
        '--upgrade',
        action='store_true',
        help='Upgrade existing packages to latest versions'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    try:
        setup_build_environment(args.upgrade, args.verbose)
    except KeyboardInterrupt:
        log("Setup interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error during setup: {e}")

if __name__ == "__main__":
    main()
