#!/usr/bin/env python3
"""
Professional Installer Creator for WOW Bingo Game
================================================

This script creates professional Windows installers for the WOW Bingo Game
using multiple installer technologies (Inno Setup, NSIS, and MSI).

Features:
- Inno Setup installer (recommended)
- NSIS installer (alternative)
- MSI installer (enterprise)
- Automatic installer detection and setup
- Professional installer configuration
- Digital signature support (optional)
- Uninstaller creation
- Registry entries and shortcuts

Usage:
    python create_installer.py [options]

Options:
    --type inno|nsis|msi    Choose installer type (default: inno)
    --sign                  Enable digital signature (requires certificate)
    --version VERSION       Set version number (default: auto-detect)
    --output-dir DIR        Output directory for installer
"""

import os
import sys
import subprocess
import shutil
import json
import time
from pathlib import Path
from typing import Optional, Dict, List

class InstallerCreator:
    """Create professional installers for WOW Bingo Game."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.installer_dir = self.project_root / "installer"
        self.executable_path = self.dist_dir / "WOWBingoGame.exe"
        
        # Project information
        self.app_name = "WOW Bingo Game"
        self.app_version = "1.0.0"
        self.company_name = "WOW Games"
        self.app_description = "Professional Bingo Gaming Application"
        self.app_url = "https://wowgames.com"
        self.support_url = "https://wowgames.com/support"
        
        # Installer configuration
        self.installer_type = "inno"
        self.enable_signing = False
        self.output_dir = self.installer_dir
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def error(self, message: str) -> None:
        """Log an error and exit."""
        self.log(message, "ERROR")
        sys.exit(1)
        
    def check_executable(self) -> bool:
        """Check if the compiled executable exists."""
        if not self.executable_path.exists():
            self.error(f"Executable not found: {self.executable_path}")
            return False
            
        file_size = self.executable_path.stat().st_size / (1024 * 1024)
        self.log(f"Found executable: {self.executable_path} ({file_size:.1f} MB)")
        return True
        
    def detect_version(self) -> str:
        """Detect version from build report or use default."""
        build_report = self.dist_dir / "build_report.json"
        if build_report.exists():
            try:
                with open(build_report, 'r') as f:
                    data = json.load(f)
                    version = data.get('build_info', {}).get('project_version', self.app_version)
                    self.log(f"Detected version from build report: {version}")
                    return version
            except Exception as e:
                self.log(f"Could not read build report: {e}", "WARNING")
                
        self.log(f"Using default version: {self.app_version}")
        return self.app_version
        
    def prepare_installer_directory(self) -> None:
        """Prepare the installer directory structure."""
        self.log("Preparing installer directory...")
        
        # Create installer directory
        self.installer_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        for subdir in ['scripts', 'assets', 'output']:
            (self.installer_dir / subdir).mkdir(exist_ok=True)
            
        # Copy executable to installer directory
        installer_exe = self.installer_dir / "WOWBingoGame.exe"
        shutil.copy2(self.executable_path, installer_exe)
        self.log(f"Copied executable to installer directory")
        
        # Copy icon if available
        icon_source = self.project_root / "assets" / "app_logo.ico"
        if icon_source.exists():
            icon_dest = self.installer_dir / "assets" / "app_logo.ico"
            shutil.copy2(icon_source, icon_dest)
            self.log("Copied application icon")
            
    def create_inno_setup_script(self) -> Path:
        """Create Inno Setup script (.iss file)."""
        self.log("Creating Inno Setup script...")
        
        script_content = f'''[Setup]
AppId={{{{B8E5F8A1-2C3D-4E5F-6789-ABCDEF123456}}}}
AppName={self.app_name}
AppVersion={self.app_version}
AppVerName={self.app_name} {self.app_version}
AppPublisher={self.company_name}
AppPublisherURL={self.app_url}
AppSupportURL={self.support_url}
AppUpdatesURL={self.app_url}
DefaultDirName={{autopf}}\\{self.app_name}
DefaultGroupName={self.app_name}
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir={self.installer_dir / "output"}
OutputBaseFilename=WOWBingoGame_Setup_v{self.app_version}
SetupIconFile={self.installer_dir / "assets" / "app_logo.ico"}
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{{cm:CreateQuickLaunchIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "{self.installer_dir / "WOWBingoGame.exe"}"; DestDir: "{{app}}"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{{group}}\\{self.app_name}"; Filename: "{{app}}\\WOWBingoGame.exe"
Name: "{{group}}\\{{cm:ProgramOnTheWeb,{self.app_name}}}"; Filename: "{self.app_url}"
Name: "{{group}}\\{{cm:UninstallProgram,{self.app_name}}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\{self.app_name}"; Filename: "{{app}}\\WOWBingoGame.exe"; Tasks: desktopicon
Name: "{{userappdata}}\\Microsoft\\Internet Explorer\\Quick Launch\\{self.app_name}"; Filename: "{{app}}\\WOWBingoGame.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{{app}}\\WOWBingoGame.exe"; Description: "{{cm:LaunchProgram,{self.app_name}}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{{app}}"

[Code]
procedure InitializeWizard;
begin
  WizardForm.LicenseAcceptedRadio.Checked := True;
end;
'''
        
        script_path = self.installer_dir / "scripts" / "setup.iss"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
            
        self.log(f"Inno Setup script created: {script_path}")
        return script_path
        
    def check_inno_setup(self) -> Optional[Path]:
        """Check if Inno Setup is installed and return compiler path."""
        possible_paths = [
            Path("C:/Program Files (x86)/Inno Setup 6/ISCC.exe"),
            Path("C:/Program Files/Inno Setup 6/ISCC.exe"),
            Path("C:/Program Files (x86)/Inno Setup 5/ISCC.exe"),
            Path("C:/Program Files/Inno Setup 5/ISCC.exe"),
        ]
        
        for path in possible_paths:
            if path.exists():
                self.log(f"Found Inno Setup compiler: {path}")
                return path
                
        return None
        
    def install_inno_setup(self) -> bool:
        """Download and install Inno Setup if not present."""
        self.log("Inno Setup not found. Attempting to download...")
        
        try:
            # Download Inno Setup installer
            import urllib.request
            
            download_url = "https://jrsoftware.org/download.php/is.exe"
            installer_path = self.installer_dir / "innosetup_installer.exe"
            
            self.log("Downloading Inno Setup installer...")
            urllib.request.urlretrieve(download_url, installer_path)
            
            self.log("Please run the downloaded installer manually:")
            self.log(f"Installer location: {installer_path}")
            self.log("After installation, run this script again.")
            
            return False
            
        except Exception as e:
            self.log(f"Failed to download Inno Setup: {e}", "ERROR")
            return False
            
    def build_inno_installer(self, script_path: Path) -> bool:
        """Build installer using Inno Setup."""
        self.log("Building installer with Inno Setup...")
        
        # Check for Inno Setup
        iscc_path = self.check_inno_setup()
        if not iscc_path:
            self.log("Inno Setup not found. Please install it first.", "ERROR")
            self.log("Download from: https://jrsoftware.org/isinfo.php")
            return False
            
        try:
            # Run Inno Setup compiler
            cmd = [str(iscc_path), str(script_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            self.log("Inno Setup compilation completed successfully")
            
            # Find the generated installer
            output_dir = self.installer_dir / "output"
            installer_files = list(output_dir.glob("*.exe"))
            
            if installer_files:
                installer_path = installer_files[0]
                file_size = installer_path.stat().st_size / (1024 * 1024)
                self.log(f"Installer created: {installer_path} ({file_size:.1f} MB)")
                return True
            else:
                self.log("Installer file not found in output directory", "ERROR")
                return False
                
        except subprocess.CalledProcessError as e:
            self.log(f"Inno Setup compilation failed: {e}", "ERROR")
            if e.stdout:
                self.log(f"Output: {e.stdout}")
            if e.stderr:
                self.log(f"Error: {e.stderr}")
            return False
            
    def create_installer(self, installer_type: str = "inno") -> bool:
        """Create installer of specified type."""
        self.log(f"Creating {installer_type.upper()} installer...")
        
        # Check executable exists
        if not self.check_executable():
            return False
            
        # Detect version
        self.app_version = self.detect_version()
        
        # Prepare installer directory
        self.prepare_installer_directory()
        
        if installer_type == "inno":
            script_path = self.create_inno_setup_script()
            return self.build_inno_installer(script_path)
        else:
            self.log(f"Installer type '{installer_type}' not yet implemented", "ERROR")
            return False

def main():
    """Main entry point for installer creation."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Create professional installer for WOW Bingo Game",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--type', choices=['inno', 'nsis', 'msi'], default='inno',
                       help='Installer type (default: inno)')
    parser.add_argument('--sign', action='store_true', help='Enable digital signature')
    parser.add_argument('--version', help='Set version number')
    parser.add_argument('--output-dir', help='Output directory for installer')
    
    args = parser.parse_args()
    
    try:
        creator = InstallerCreator()
        
        if args.version:
            creator.app_version = args.version
        if args.output_dir:
            creator.output_dir = Path(args.output_dir)
        creator.enable_signing = args.sign
        
        print("=" * 80)
        print("WOW Bingo Game - Professional Installer Creator")
        print("=" * 80)
        
        success = creator.create_installer(args.type)
        
        if success:
            print("=" * 80)
            print("INSTALLER CREATED SUCCESSFULLY!")
            print("=" * 80)
            print(f"Installer type: {args.type.upper()}")
            print(f"Output directory: {creator.installer_dir / 'output'}")
            print("=" * 80)
        else:
            print("=" * 80)
            print("INSTALLER CREATION FAILED!")
            print("=" * 80)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nInstaller creation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
