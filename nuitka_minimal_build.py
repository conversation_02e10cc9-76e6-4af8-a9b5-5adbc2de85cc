#!/usr/bin/env python3
"""
Minimal Nuitka Build Script for WOW Bingo Game
==============================================

This is an ultra-minimal build script that avoids the Nuitka bug
by using the absolute minimum number of packages and options.

Usage:
    python nuitka_minimal_build.py [--debug]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def build_minimal_executable(debug=False):
    """Build with absolute minimal options to avoid Nuitka bugs."""
    log("Building with minimal options to avoid Nuitka bugs...")
    
    project_root = Path(__file__).parent
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    # Clean and create directories
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    build_dir.mkdir(exist_ok=True)
    dist_dir.mkdir(exist_ok=True)
    
    # Ultra-minimal Nuitka command - only essential options
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',
        '--onefile',
        '--output-filename=WOWBingoGame',
        f'--output-dir={build_dir}',
        '--assume-yes-for-downloads',
        '--windows-console-mode=disable',
    ]
    
    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.append(f'--windows-icon-from-ico={icon_path}')
    
    # Only include absolutely essential data directories
    essential_dirs = ['assets', 'data']
    for dir_name in essential_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            cmd.append(f'--include-data-dir={dir_path}={dir_name}')
            log(f"Including directory: {dir_name}")
    
    # Only include the most essential packages that we know exist
    essential_packages = ['pygame', 'pyperclip']
    for package in essential_packages:
        cmd.append(f'--include-package={package}')
    
    # Debug mode
    if debug:
        cmd.append('--debug')
    
    # Add main script
    cmd.append('main.py')
    
    log(f"Minimal command: {' '.join(cmd)}")
    
    # Execute build
    start_time = time.time()
    try:
        log("Starting minimal Nuitka compilation...")
        result = subprocess.run(cmd, cwd=project_root, check=True)
        build_time = time.time() - start_time
        log(f"Build completed in {build_time:.1f} seconds")
        
        # Find and copy executable
        executable_name = "WOWBingoGame.exe" if os.name == 'nt' else "WOWBingoGame"
        executable_path = build_dir / executable_name
        
        if executable_path.exists():
            # Copy to dist directory
            dist_executable = dist_dir / executable_name
            shutil.copy2(executable_path, dist_executable)
            
            # Make executable on Unix systems
            if os.name != 'nt':
                os.chmod(dist_executable, 0o755)
            
            file_size_mb = dist_executable.stat().st_size / (1024 * 1024)
            
            log("=" * 60)
            log("MINIMAL BUILD COMPLETED SUCCESSFULLY!")
            log("=" * 60)
            log(f"Executable: {dist_executable}")
            log(f"File size: {file_size_mb:.1f} MB")
            log("=" * 60)
            log("")
            log("NOTE: This is a minimal build that may be missing some features.")
            log("If the game doesn't work properly, you may need to add more packages.")
            
            return True
        else:
            error(f"Executable not found: {executable_path}")
            
    except subprocess.CalledProcessError as e:
        log(f"Build failed with return code {e.returncode}", "ERROR")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Minimal Nuitka build for WOW Bingo Game")
    parser.add_argument('--debug', action='store_true', help='Build in debug mode')
    
    args = parser.parse_args()
    
    try:
        log("=" * 60)
        log("WOW Bingo Game - Minimal Build")
        log("=" * 60)
        log("This build uses the absolute minimum options to avoid Nuitka bugs.")
        log("Some features may not work if required packages are missing.")
        log("=" * 60)
        
        # Check basic prerequisites
        try:
            import pygame
            log("pygame available")
        except ImportError:
            error("pygame not found! Install with: pip install pygame")
        
        try:
            import pyperclip
            log("pyperclip available")
        except ImportError:
            error("pyperclip not found! Install with: pip install pyperclip")
        
        # Check Nuitka
        try:
            result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'], 
                                  capture_output=True, text=True, check=True)
            log(f"Nuitka available: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            error("Nuitka not found! Install with: pip install nuitka")
        
        # Build
        success = build_minimal_executable(args.debug)
        
        if success:
            log("")
            log("If the executable doesn't work properly, try these steps:")
            log("1. Test the executable to see what's missing")
            log("2. Add missing packages to the build script")
            log("3. Try the comprehensive build script after fixing the Nuitka issue")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        log("Build interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
