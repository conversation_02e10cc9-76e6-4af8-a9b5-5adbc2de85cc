#!/usr/bin/env python3
"""
WOW Bingo Game - Comprehensive Build Script
==========================================

This script creates a standalone executable for the WOW Bingo Game that can run
on any Windows PC without requiring Python installation.

Features:
- Automatic dependency installation
- Asset verification and bundling
- Multiple build options (PyInstaller, Nuitka, Auto-py-to-exe)
- Comprehensive error handling
- Build verification and testing
- Optimized for Windows distribution

Usage:
    python build_executable.py [options]

Options:
    --tool {pyinstaller,nuitka,auto-py-to-exe}  Build tool to use (default: pyinstaller)
    --clean                                     Clean build directories first
    --test                                      Test the executable after building
    --install-deps                              Install build dependencies first
    --verbose                                   Enable verbose output
    --optimize                                  Enable optimizations (slower build)
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
import json
import platform
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import importlib.util

class WOWBingoBuilder:
    """Comprehensive build system for WOW Bingo Game."""

    def __init__(self, verbose: bool = False):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.verbose = verbose
        self.start_time = time.time()

        # Project configuration
        self.project_name = "WOW Bingo Game"
        self.executable_name = "WOW_Bingo_Game.exe"
        self.main_script = "main.py"
        self.icon_path = self.project_root / "assets" / "app_logo.ico"

        # Build tools
        self.available_tools = {
            'pyinstaller': self._build_with_pyinstaller,
            'nuitka': self._build_with_nuitka,
            'auto-py-to-exe': self._build_with_auto_py_to_exe
        }

        # Dependencies
        self.build_requirements = [
            'pyinstaller>=6.0.0',
            'pygame>=2.5.0',
            'pyperclip>=1.8.2',
            'psutil>=5.9.0',
            'pillow>=10.0.0',
            'cryptography>=41.0.0',
            'pydantic>=2.0.0',
            'requests>=2.31.0',
        ]

        self.optional_requirements = [
            'flet>=0.24.0',
            'pydub>=0.25.1',
            'rethinkdb>=2.4.10',
            'numpy>=1.24.0',
            'loguru>=0.7.0',
        ]

    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        elapsed = time.time() - self.start_time
        prefix = f"[{timestamp}] [{elapsed:6.1f}s] {level}:"
        print(f"{prefix} {message}")

        if self.verbose and level == "DEBUG":
            print(f"    {message}")

    def error(self, message: str) -> None:
        """Log an error and exit."""
        self.log(message, "ERROR")
        sys.exit(1)

    def warning(self, message: str) -> None:
        """Log a warning."""
        self.log(message, "WARN")

    def check_python_version(self) -> None:
        """Check if Python version is compatible."""
        if sys.version_info < (3, 7):
            self.error("Python 3.7 or higher is required")

        self.log(f"Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    def check_platform(self) -> None:
        """Check if we're on a supported platform."""
        if platform.system() != "Windows":
            self.warning("This build script is optimized for Windows. Some features may not work on other platforms.")

        self.log(f"Platform: {platform.system()} {platform.release()}")

    def install_dependencies(self, include_optional: bool = True) -> None:
        """Install build dependencies."""
        self.log("Installing build dependencies...")

        # Upgrade pip first
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
                         check=True, capture_output=True)
            self.log("Pip upgraded successfully")
        except subprocess.CalledProcessError as e:
            self.warning(f"Failed to upgrade pip: {e}")

        # Install required dependencies
        for requirement in self.build_requirements:
            try:
                self.log(f"Installing {requirement}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', requirement],
                             check=True, capture_output=not self.verbose)
                self.log(f"Successfully installed {requirement}")
            except subprocess.CalledProcessError as e:
                self.error(f"Failed to install {requirement}: {e}")

        # Install optional dependencies
        if include_optional:
            for requirement in self.optional_requirements:
                try:
                    self.log(f"Installing optional {requirement}...")
                    subprocess.run([sys.executable, '-m', 'pip', 'install', requirement],
                                 check=True, capture_output=not self.verbose)
                    self.log(f"Successfully installed optional {requirement}")
                except subprocess.CalledProcessError as e:
                    self.warning(f"Failed to install optional {requirement}: {e}")

    def verify_assets(self) -> bool:
        """Verify that all required assets exist."""
        self.log("Verifying project assets...")

        required_paths = [
            self.project_root / "main.py",
            self.project_root / "assets",
            self.project_root / "data",
        ]

        missing_paths = []
        for path in required_paths:
            if not path.exists():
                missing_paths.append(str(path))

        if missing_paths:
            self.error(f"Missing required files/directories: {', '.join(missing_paths)}")

        # Check for splash screen assets
        splash_dir = self.project_root / "assets" / "Splash_screen"
        if splash_dir.exists():
            splash_files = list(splash_dir.glob("*.jpg")) + list(splash_dir.glob("*.png"))
            self.log(f"Found {len(splash_files)} splash screen images")
        else:
            self.warning("Splash screen directory not found")

        # Check for audio assets
        audio_dir = self.project_root / "assets" / "audio-effects"
        if audio_dir.exists():
            audio_files = list(audio_dir.glob("*.mp3")) + list(audio_dir.glob("*.wav"))
            self.log(f"Found {len(audio_files)} audio files")
        else:
            self.warning("Audio effects directory not found")

        self.log("Asset verification completed")
        return True

    def prepare_build_environment(self, clean: bool = False) -> None:
        """Prepare the build environment."""
        self.log("Preparing build environment...")

        if clean:
            self.log("Cleaning build directories...")
            for dir_path in [self.build_dir, self.dist_dir]:
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    self.log(f"Removed {dir_path}")

        # Create directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

        self.log("Build environment prepared")

    def _build_with_pyinstaller(self, optimize: bool = False) -> bool:
        """Build using PyInstaller."""
        self.log("Building with PyInstaller...")

        # Check if PyInstaller is available
        try:
            import PyInstaller
            self.log(f"PyInstaller version: {PyInstaller.__version__}")
        except ImportError:
            self.error("PyInstaller not found. Install with: pip install pyinstaller")

        # Use the spec file if it exists, otherwise create command
        spec_file = self.project_root / "WOW_Bingo_Game.spec"

        if spec_file.exists():
            cmd = [sys.executable, '-m', 'PyInstaller', str(spec_file)]
            self.log("Using existing spec file")
        else:
            # Create command line arguments
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--onefile',
                '--windowed',
                f'--name={self.executable_name[:-4]}',  # Remove .exe extension
                f'--distpath={self.dist_dir}',
                f'--workpath={self.build_dir}',
                '--add-data=assets;assets',
                '--add-data=data;data',
                '--hidden-import=pygame',
                '--hidden-import=pyperclip',
                '--hidden-import=psutil',
                '--hidden-import=PIL',
                '--hidden-import=cryptography',
            ]

            if self.icon_path.exists():
                cmd.append(f'--icon={self.icon_path}')

            if optimize:
                cmd.append('--optimize=2')

            cmd.append(str(self.main_script))

        # Execute PyInstaller
        try:
            self.log("Executing PyInstaller (this may take several minutes)...")
            result = subprocess.run(cmd, cwd=self.project_root,
                                  capture_output=not self.verbose, text=True)

            if result.returncode == 0:
                self.log("PyInstaller build completed successfully")
                return True
            else:
                self.error(f"PyInstaller build failed with return code {result.returncode}")
                if result.stderr and not self.verbose:
                    self.log(f"Error output: {result.stderr}")
                return False

        except Exception as e:
            self.error(f"Error during PyInstaller build: {e}")
            return False

    def _build_with_nuitka(self, optimize: bool = False) -> bool:
        """Build using Nuitka."""
        self.log("Building with Nuitka...")

        # Check if Nuitka is available
        try:
            result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                                  capture_output=True, text=True, check=True)
            self.log(f"Nuitka version: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.error("Nuitka not found. Install with: pip install nuitka")

        # Build command
        cmd = [
            sys.executable, '-m', 'nuitka',
            '--standalone',
            '--onefile',
            f'--output-filename={self.executable_name}',
            f'--output-dir={self.build_dir}',
            '--assume-yes-for-downloads',
            '--windows-console-mode=disable',
            '--include-data-dir=assets=assets',
            '--include-data-dir=data=data',
            '--include-package=pygame',
            '--include-package=pyperclip',
        ]

        if self.icon_path.exists():
            cmd.append(f'--windows-icon-from-ico={self.icon_path}')

        if optimize:
            cmd.extend(['--lto=yes', '--jobs=4'])

        cmd.append(str(self.main_script))

        # Execute Nuitka
        try:
            self.log("Executing Nuitka (this may take several minutes)...")
            result = subprocess.run(cmd, cwd=self.project_root,
                                  capture_output=not self.verbose, text=True)

            if result.returncode == 0:
                self.log("Nuitka build completed successfully")
                # Copy executable to dist directory
                built_exe = self.build_dir / self.executable_name
                if built_exe.exists():
                    shutil.copy2(built_exe, self.dist_dir / self.executable_name)
                return True
            else:
                self.error(f"Nuitka build failed with return code {result.returncode}")
                return False

        except Exception as e:
            self.error(f"Error during Nuitka build: {e}")
            return False

    def _build_with_auto_py_to_exe(self, optimize: bool = False) -> bool:
        """Build using auto-py-to-exe."""
        self.log("Building with auto-py-to-exe...")

        # Check if auto-py-to-exe is available
        try:
            import auto_py_to_exe
            self.log("auto-py-to-exe is available")
        except ImportError:
            self.error("auto-py-to-exe not found. Install with: pip install auto-py-to-exe")

        self.log("auto-py-to-exe requires manual configuration. Please run:")
        self.log("    auto-py-to-exe")
        self.log("Then configure the settings and build manually.")
        return False

    def verify_executable(self) -> Optional[Path]:
        """Verify that the built executable exists and is functional."""
        self.log("Verifying built executable...")

        # Look for the executable
        possible_paths = [
            self.dist_dir / self.executable_name,
            self.dist_dir / f"{self.executable_name[:-4]}.exe",
            self.build_dir / self.executable_name,
        ]

        executable_path = None
        for path in possible_paths:
            if path.exists():
                executable_path = path
                break

        if not executable_path:
            self.error(f"Executable not found in expected locations: {[str(p) for p in possible_paths]}")
            return None

        # Check file size
        file_size = executable_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        self.log(f"Executable found: {executable_path}")
        self.log(f"File size: {size_mb:.1f} MB")

        if size_mb < 10:
            self.warning("Executable seems unusually small. Build may be incomplete.")
        elif size_mb > 500:
            self.warning("Executable is very large. Consider optimizing the build.")

        return executable_path

    def test_executable(self, executable_path: Path) -> bool:
        """Test the executable to ensure it runs."""
        self.log("Testing executable...")

        try:
            # Try to run the executable with a short timeout
            result = subprocess.run(
                [str(executable_path), '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                self.log("Executable test passed")
                return True
            else:
                self.warning("Executable test failed, but this may be normal for GUI applications")
                return True  # GUI apps may not respond to --version

        except subprocess.TimeoutExpired:
            self.warning("Executable test timed out (normal for GUI applications)")
            return True
        except Exception as e:
            self.warning(f"Error testing executable: {e}")
            return False

    def generate_build_report(self, executable_path: Path, build_tool: str) -> None:
        """Generate a comprehensive build report."""
        self.log("Generating build report...")

        build_time = time.time() - self.start_time

        report = {
            "build_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "build_time_seconds": round(build_time, 2),
                "build_tool": build_tool,
                "project_name": self.project_name,
                "executable_path": str(executable_path),
                "executable_size_mb": round(executable_path.stat().st_size / (1024 * 1024), 2),
                "python_version": sys.version,
                "platform": platform.system(),
            },
            "dependencies": {
                "build_requirements": self.build_requirements,
                "optional_requirements": self.optional_requirements,
            },
            "assets": {
                "assets_dir_exists": (self.project_root / "assets").exists(),
                "data_dir_exists": (self.project_root / "data").exists(),
                "icon_exists": self.icon_path.exists(),
            }
        }

        report_file = self.dist_dir / "build_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.log(f"Build report saved to: {report_file}")

    def build(self, tool: str = 'pyinstaller', clean: bool = False,
              test: bool = False, optimize: bool = False) -> bool:
        """Main build method."""
        self.log("=" * 80)
        self.log(f"WOW Bingo Game - Build System")
        self.log("=" * 80)

        try:
            # Prerequisites
            self.check_python_version()
            self.check_platform()

            # Verify assets
            self.verify_assets()

            # Prepare environment
            self.prepare_build_environment(clean=clean)

            # Build
            if tool not in self.available_tools:
                self.error(f"Unknown build tool: {tool}. Available: {list(self.available_tools.keys())}")

            build_func = self.available_tools[tool]
            success = build_func(optimize=optimize)

            if not success:
                self.error("Build failed")
                return False

            # Verify executable
            executable_path = self.verify_executable()
            if not executable_path:
                return False

            # Test executable
            if test:
                self.test_executable(executable_path)

            # Generate report
            self.generate_build_report(executable_path, tool)

            # Success message
            self.log("=" * 80)
            self.log("BUILD COMPLETED SUCCESSFULLY!")
            self.log("=" * 80)
            self.log(f"Executable: {executable_path}")
            self.log(f"Size: {executable_path.stat().st_size / (1024 * 1024):.1f} MB")
            self.log(f"Build time: {time.time() - self.start_time:.1f} seconds")
            self.log("=" * 80)

            return True

        except Exception as e:
            self.error(f"Build failed with error: {e}")
            return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="WOW Bingo Game - Comprehensive Build Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python build_executable.py                           # Basic PyInstaller build
  python build_executable.py --tool nuitka --optimize  # Optimized Nuitka build
  python build_executable.py --clean --test --verbose  # Clean build with testing
  python build_executable.py --install-deps            # Install dependencies first
        """
    )

    parser.add_argument('--tool', choices=['pyinstaller', 'nuitka', 'auto-py-to-exe'],
                       default='pyinstaller', help='Build tool to use')
    parser.add_argument('--clean', action='store_true', help='Clean build directories first')
    parser.add_argument('--test', action='store_true', help='Test the executable after building')
    parser.add_argument('--install-deps', action='store_true', help='Install build dependencies first')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--optimize', action='store_true', help='Enable optimizations (slower build)')

    args = parser.parse_args()

    # Create builder
    builder = WOWBingoBuilder(verbose=args.verbose)

    try:
        # Install dependencies if requested
        if args.install_deps:
            builder.install_dependencies()

        # Build
        success = builder.build(
            tool=args.tool,
            clean=args.clean,
            test=args.test,
            optimize=args.optimize
        )

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        builder.log("Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        builder.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
