@echo off
:: ================================================================
:: WOW Bingo Game - Enhanced Nuitka Build Script (Windows)
:: ================================================================
:: This batch script provides an easy-to-use interface for building
:: the WOW Bingo Game using the enhanced Nuitka build system.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "MAGENTA=%ESC%[35m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Enhanced Build System%RESET%
echo %CYAN%================================================================%RESET%
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found in PATH!%RESET%
    echo %YELLOW%Please install Python 3.7+ and add it to your PATH.%RESET%
    pause
    exit /b 1
)

:: Check if enhanced build script exists
if not exist "enhanced_nuitka_build.py" (
    echo %RED%Error: enhanced_nuitka_build.py not found!%RESET%
    echo %YELLOW%Please ensure the enhanced build script is in the current directory.%RESET%
    pause
    exit /b 1
)

:: Display menu
:menu
echo %BLUE%Please select a build option:%RESET%
echo.
echo %GREEN%1.%RESET% Basic Build (Recommended)
echo %GREEN%2.%RESET% Clean Build (Remove previous builds first)
echo %GREEN%3.%RESET% Optimized Build (Slower but smaller executable)
echo %GREEN%4.%RESET% Debug Build (Verbose output for troubleshooting)
echo %GREEN%5.%RESET% Verify Dependencies Only
echo %GREEN%6.%RESET% Clean + Optimized Build
echo %GREEN%7.%RESET% Full Build with Testing
echo %GREEN%8.%RESET% Exit
echo.
set /p "choice=Enter your choice (1-8): "

:: Process user choice
if "%choice%"=="1" goto basic_build
if "%choice%"=="2" goto clean_build
if "%choice%"=="3" goto optimized_build
if "%choice%"=="4" goto debug_build
if "%choice%"=="5" goto verify_only
if "%choice%"=="6" goto clean_optimized_build
if "%choice%"=="7" goto full_build_test
if "%choice%"=="8" goto exit
echo %RED%Invalid choice. Please try again.%RESET%
echo.
goto menu

:basic_build
echo %CYAN%Starting basic build...%RESET%
python enhanced_nuitka_build.py
goto build_complete

:clean_build
echo %CYAN%Starting clean build...%RESET%
python enhanced_nuitka_build.py --clean
goto build_complete

:optimized_build
echo %CYAN%Starting optimized build (this will take longer)...%RESET%
python enhanced_nuitka_build.py --optimize
goto build_complete

:debug_build
echo %CYAN%Starting debug build with verbose output...%RESET%
python enhanced_nuitka_build.py --debug
goto build_complete

:verify_only
echo %CYAN%Verifying dependencies only...%RESET%
python enhanced_nuitka_build.py --verify
goto build_complete

:clean_optimized_build
echo %CYAN%Starting clean optimized build...%RESET%
python enhanced_nuitka_build.py --clean --optimize
goto build_complete

:full_build_test
echo %CYAN%Starting full build with testing...%RESET%
python enhanced_nuitka_build.py --clean --optimize --test
goto build_complete

:build_complete
echo.
if %errorlevel% equ 0 (
    echo %GREEN%Build process completed successfully!%RESET%
    echo.
    echo %CYAN%Build artifacts can be found in the 'dist' directory.%RESET%
    echo %CYAN%Check 'dist/build_report.json' for detailed build information.%RESET%
) else (
    echo %RED%Build process failed!%RESET%
    echo %YELLOW%Check the output above for error details.%RESET%
    echo %YELLOW%You can try running with debug mode (option 4) for more information.%RESET%
)
echo.
echo %BLUE%Press any key to return to menu or Ctrl+C to exit...%RESET%
pause >nul
goto menu

:exit
echo %CYAN%Thank you for using the WOW Bingo Game build system!%RESET%
pause
exit /b 0
