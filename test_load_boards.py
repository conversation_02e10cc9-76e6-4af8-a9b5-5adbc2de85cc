import json
import os
import random

def create_deterministic_board(cartella_number):
    """Create a deterministic board based on cartella number"""
    # Use the cartella number as seed for consistent results
    random.seed(cartella_number)

    board = []

    # For each column (B, I, N, G, O)
    for col in range(5):
        column_values = []
        # Range for this column: col*15+1 to col*15+15
        min_val = col * 15 + 1
        max_val = min_val + 14

        # Generate 5 unique random numbers for this column
        values = list(range(min_val, max_val + 1))
        random.shuffle(values)
        column_values = values[:5]

        # Set the center square (N column, 3rd row) to 0 (free space)
        if col == 2:
            column_values[2] = 0

        board.append(column_values)

    # Reset the random seed to avoid affecting other random operations
    random.seed()

    return board

def generate_boards():
    """Generate 100 bingo boards and save them to disk"""
    boards = {}

    # Generate a unique board for each cartella number 1-100
    for cartella in range(1, 101):
        # Generate a unique board for this cartella number
        board = create_deterministic_board(cartella)
        boards[str(cartella)] = board
        print(f"Generated board for cartella {cartella}")

    # Ensure the data directory exists
    os.makedirs('data', exist_ok=True)
    boards_file = os.path.join('data', 'bingo_boards.json')

    # Save generated boards to file
    try:
        with open(boards_file, 'w') as file:
            json.dump(boards, file, indent=2)
        print(f"Successfully saved {len(boards)} boards to {boards_file}")
    except Exception as e:
        print(f"Error saving boards to file: {e}")
        
    return boards

def load_boards():
    """Load bingo boards from the JSON file"""
    try:
        # Path to the bingo boards file
        boards_file = os.path.join('data', 'bingo_boards.json')
        
        # Check if the file exists
        if not os.path.exists(boards_file):
            print(f"Bingo boards file not found at {boards_file}")
            return None
            
        # Load the boards from the file
        with open(boards_file, 'r') as file:
            file_content = file.read().strip()
            if not file_content:
                print(f"Bingo boards file is empty")
                return None
                
            boards = json.loads(file_content)
            print(f"Loaded {len(boards)} bingo boards from {boards_file}")
            
            # Check if we have at least 100 boards
            if len(boards) < 100:
                print(f"Warning: Expected at least 100 boards, but found {len(boards)}")
                
            return boards
    except Exception as e:
        print(f"Error loading bingo boards: {e}")
        return None

def test_board_persistence():
    """Test that boards persist after being saved"""
    # First, generate and save boards
    print("Generating boards...")
    generate_boards()
    
    # Then load them back
    print("\nLoading boards...")
    boards = load_boards()
    
    if boards is None or len(boards) == 0:
        print("ERROR: Failed to load boards after saving them")
        return False
        
    print(f"SUCCESS: Loaded {len(boards)} boards after saving them")
    
    # Save a sample board to a separate file for inspection
    sample_board = boards.get("1")
    if sample_board:
        with open("sample_board.json", "w") as f:
            json.dump(sample_board, f, indent=2)
        print("Saved sample board to sample_board.json for inspection")
    
    return True

if __name__ == "__main__":
    test_board_persistence()
