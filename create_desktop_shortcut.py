"""
Desktop Shortcut Creator for WOW Games

This script creates desktop shortcuts for easy access to WOW Games
with RethinkDB integration.
"""

import os
import sys
import platform
from pathlib import Path

def create_windows_shortcut():
    """Create a Windows desktop shortcut."""
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Get desktop path
        desktop = winshell.desktop()
        
        # Get current directory
        current_dir = os.path.abspath(os.path.dirname(__file__))
        
        # Create shortcut
        shortcut_path = os.path.join(desktop, "WOW Games.lnk")
        target = os.path.join(current_dir, "START_WOW_GAMES.bat")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = current_dir
        shortcut.IconLocation = target
        shortcut.Description = "WOW Games with RethinkDB Integration"
        shortcut.save()
        
        print(f"✅ Windows shortcut created: {shortcut_path}")
        return True
        
    except ImportError:
        print("❌ Required modules not available. Install with:")
        print("   pip install pywin32 winshell")
        return False
    except Exception as e:
        print(f"❌ Error creating Windows shortcut: {e}")
        return False

def create_linux_shortcut():
    """Create a Linux desktop shortcut."""
    try:
        # Get desktop path
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "Escritorio"  # Spanish
        if not desktop.exists():
            desktop = Path.home() / "Bureau"  # French
        if not desktop.exists():
            print("❌ Desktop directory not found")
            return False
        
        # Get current directory
        current_dir = os.path.abspath(os.path.dirname(__file__))
        
        # Create .desktop file
        shortcut_path = desktop / "WOW-Games.desktop"
        
        desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=WOW Games
Comment=WOW Games with RethinkDB Integration
Exec=bash "{current_dir}/start_wow_games.sh"
Icon=applications-games
Path={current_dir}
Terminal=true
StartupNotify=false
"""
        
        with open(shortcut_path, 'w') as f:
            f.write(desktop_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Linux shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating Linux shortcut: {e}")
        return False

def create_mac_shortcut():
    """Create a macOS shortcut."""
    try:
        # Get desktop path
        desktop = Path.home() / "Desktop"
        
        # Get current directory
        current_dir = os.path.abspath(os.path.dirname(__file__))
        
        # Create AppleScript application
        app_path = desktop / "WOW Games.app"
        contents_path = app_path / "Contents"
        macos_path = contents_path / "MacOS"
        resources_path = contents_path / "Resources"
        
        # Create directories
        macos_path.mkdir(parents=True, exist_ok=True)
        resources_path.mkdir(parents=True, exist_ok=True)
        
        # Create Info.plist
        info_plist = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>WOW Games</string>
    <key>CFBundleIdentifier</key>
    <string>com.wowgames.launcher</string>
    <key>CFBundleName</key>
    <string>WOW Games</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
</dict>
</plist>"""
        
        with open(contents_path / "Info.plist", 'w') as f:
            f.write(info_plist)
        
        # Create executable script
        executable_script = f"""#!/bin/bash
cd "{current_dir}"
bash start_wow_games.sh
"""
        
        executable_path = macos_path / "WOW Games"
        with open(executable_path, 'w') as f:
            f.write(executable_script)
        
        # Make executable
        os.chmod(executable_path, 0o755)
        
        print(f"✅ macOS shortcut created: {app_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating macOS shortcut: {e}")
        return False

def main():
    """Main function to create appropriate shortcut."""
    print("WOW Games Desktop Shortcut Creator")
    print("=" * 50)
    
    system = platform.system().lower()
    
    if system == "windows":
        print("🖥️ Detected Windows system")
        success = create_windows_shortcut()
    elif system == "linux":
        print("🐧 Detected Linux system")
        success = create_linux_shortcut()
    elif system == "darwin":
        print("🍎 Detected macOS system")
        success = create_mac_shortcut()
    else:
        print(f"❌ Unsupported system: {system}")
        success = False
    
    if success:
        print("\n✅ Desktop shortcut created successfully!")
        print("You can now double-click the shortcut to start WOW Games.")
    else:
        print("\n❌ Failed to create desktop shortcut.")
        print("You can still run the game using:")
        if system == "windows":
            print("   START_WOW_GAMES.bat")
        else:
            print("   ./start_wow_games.sh")

if __name__ == "__main__":
    main()
