import pygame
import math
import time
import sys
import os
from pygame import gfxdraw
from view_players import ViewPlayers, Player
from player_storage import save_players_to_json, load_players_from_json
import datetime
from game_state_handler import GameS<PERSON>
from game_ui_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from screen_mode_manager import get_screen_mode_manager

# Constants for window dimensions and scaling
SCREEN_WIDTH, SCREEN_HEIGHT = 1024, 768
BASE_WIDTH, BASE_HEIGHT = 1024, 768

# Colors - same as main.py
DARK_BLUE = (10, 30, 45)  # Background color
LIGHT_BLUE = (20, 50, 70)  # Slightly lighter background
YELLOW = (255, 200, 50)   # For 'O' in BINGO
RED = (255, 50, 50)       # For 'B' and 'G' in BINGO
GREEN = (50, 200, 100)    # For highlighted numbers
BLUE = (50, 100, 220)     # For 'N' in BINGO
ORANGE = (255, 120, 30)   # For the "Total Callout" text
WHITE = (255, 255, 255)   # For text
GRAY = (60, 60, 70)       # For non-highlighted number circles
BLACK = (20, 20, 25)      # For number row backgrounds
GOLD = (255, 215, 0)      # For special elements
DARK_GREEN = (0, 100, 50) # Darker green for contrast
LIGHT_GREEN = (50, 200, 100) # For highlighted numbers
PURPLE = (128, 0, 128)    # Unused but available
NAV_BAR_BG = (15, 35, 55) # Dark blue for navigation bar
NAV_BAR_ACTIVE = (30, 100, 130) # Teal highlight for active nav item

# Initialize scaling factors
scale_x = 1.0
scale_y = 1.0

# Fonts will be initialized in the main function

class BoardSelectionWindow:
    def __init__(self, screen):
        self.screen = screen

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Store scaling factors
        self.scale_x = screen_width / BASE_WIDTH
        self.scale_y = screen_height / BASE_HEIGHT

        # Initialize screen mode manager
        self.screen_mode_manager = get_screen_mode_manager()
        self.screen_mode_manager.set_screen_reference(screen)

        # Initialize state variables
        self.selected_cartella_numbers = []
        self.cartella_number = 12  # Default
        self.bet_amount = 50       # Default
        self.prize_pool = 0
        self.input_active = False
        self.input_text = str(self.cartella_number)
        self.input_cursor_visible = True
        self.input_cursor_timer = 0
        self.bet_input_active = False
        self.bet_input_text = str(self.bet_amount)
        self.bet_input_cursor_visible = True
        self.bet_input_cursor_timer = 0
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"
        self.called_numbers = []   # For lucky numbers display
        self.current_number = None
        self.bingo_board = []

        # Board change animation tracking
        self.board_just_changed = False
        self.board_change_time = 0

        # Load bingo boards from JSON file
        self.bingo_boards = self.load_bingo_boards()

        # Create a bingo board based on default cartella number
        self.create_board_from_number(self.cartella_number)

        # Button states
        self.button_states = {
            "add_player": {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            },
            "continue": {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            }
        }

        # Store hit areas for buttons
        self.hit_areas = {}

        # Load players
        self.players = load_players_from_json()
        if self.players is None:
            self.players = []

        # Calculate prize pool
        self.calculate_prize_pool()

        # Sound effects
        try:
            self.button_click_sound = pygame.mixer.Sound("assets/audio-effects/button-click.mp3")
        except Exception as e:
            print(f"Error loading sound effects: {e}")
            self.button_click_sound = None

    def load_bingo_boards(self):
        """Load bingo boards from JSON file"""
        try:
            import json
            # Ensure data directory exists
            os.makedirs('data', exist_ok=True)

            # Use os.path.join to create a proper path to bingo_boards.json in the data directory
            boards_path = os.path.join('data', 'bingo_boards.json')

            # If the file doesn't exist, return empty dict
            if not os.path.exists(boards_path):
                print(f"Bingo boards file not found at {boards_path}")
                return {}

            with open(boards_path, "r") as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading bingo boards: {e}")
            return {}

    def run(self):
        """Run the board selection window loop"""
        running = True
        clock = pygame.time.Clock()

        while running:
            events = pygame.event.get()
            for event in events:
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()

                elif event.type == pygame.MOUSEMOTION:
                    # Update button hover states
                    self.update_button_hover_states(event.pos)

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        self.check_button_click(event.pos)

                elif event.type == pygame.KEYDOWN:
                    self.handle_input(event)

            # Update cursor blink state for input fields
            current_time = pygame.time.get_ticks()
            if self.input_active:
                self.input_cursor_timer += 1
                if self.input_cursor_timer > 30:
                    self.input_cursor_visible = not self.input_cursor_visible
                    self.input_cursor_timer = 0

            if self.bet_input_active:
                self.bet_input_cursor_timer += 1
                if self.bet_input_cursor_timer > 30:
                    self.bet_input_cursor_visible = not self.bet_input_cursor_visible
                    self.bet_input_cursor_timer = 0

            # Update message timer
            if self.message_timer > 0:
                self.message_timer -= 1

            # Check if board change animation has timed out
            if self.board_just_changed:
                if pygame.time.get_ticks() - self.board_change_time > 800:  # 800ms timeout
                    self.board_just_changed = False

            # Draw the screen
            self.draw()

            # Check for "Continue" button - exits the selection screen
            if "continue_clicked" in self.hit_areas:
                del self.hit_areas["continue_clicked"]
                running = False

            pygame.display.flip()
            clock.tick(30)

        # Return the list of selected cartella numbers
        return self.selected_cartella_numbers

    def draw(self):
        """Draw the board selection screen"""
        # Clear the screen with dark background color
        self.screen.fill(DARK_BLUE)

        screen_width, screen_height = self.screen.get_size()

        # Draw the WOW BINGO header
        self.draw_wow_bingo_header()

        # Draw CARTELLA SELECTION title with total count
        title_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("CARTELLA SELECTION", True, WHITE)
        title_x = int(100 * self.scale_x)
        title_y = int(115 * self.scale_y)
        self.screen.blit(title_text, (title_x, title_y))

        # Draw total selected count
        count_font = pygame.font.SysFont("Arial", int(32 * min(self.scale_x, self.scale_y)), bold=True)
        count_text = count_font.render(f"#Total selected:{len(self.selected_cartella_numbers)}", True, ORANGE)
        count_x = screen_width - count_text.get_width() - int(40 * self.scale_x)
        count_y = title_y
        self.screen.blit(count_text, (count_x, count_y))

        # Draw number grid (BINGO board)
        self.draw_bingo_grid()

        # Draw bottom sections
        bottom_y = int(screen_height * 0.7)

        # Draw PRIZE POOL section
        prize_pool_width = int(250 * self.scale_x)
        prize_pool_height = int(150 * self.scale_y)
        prize_pool_x = int(30 * self.scale_x)
        self.draw_prize_pool_section(prize_pool_x, bottom_y, prize_pool_width, prize_pool_height)

        # Draw Add Players section
        add_players_width = int(400 * self.scale_x)
        add_players_height = prize_pool_height
        add_players_x = prize_pool_x + prize_pool_width + int(50 * self.scale_x)
        self.draw_add_players_section(add_players_x, bottom_y, add_players_width, add_players_height)

        # Draw Next button
        next_btn_width = int(180 * self.scale_x)
        next_btn_height = int(70 * self.scale_y)
        next_btn_x = screen_width - next_btn_width - int(40 * self.scale_x)
        next_btn_y = bottom_y + int(prize_pool_height/2) - int(next_btn_height/2)
        self.draw_next_button(next_btn_x, next_btn_y, next_btn_width, next_btn_height)

        # Draw toast message if active
        if self.message_timer > 0:
            self.draw_toast_message()

    def draw_wow_bingo_header(self):
        """Draw the WOW BINGO colorful header"""
        header_font = pygame.font.SysFont("Impact", int(65 * min(self.scale_x, self.scale_y)), bold=True)

        # Colors for each letter
        colors = [
            (255, 50, 0),     # W - Red
            (0, 150, 50),     # O - Green
            (0, 100, 255),    # W - Blue
            (255, 50, 0),     # - - Red
            (10, 100, 255),   # B - Blue
            (0, 150, 50),     # I - Green
            (0, 100, 255),    # N - Blue
            (255, 50, 0),     # G - Red
            (255, 215, 0)     # O - Yellow
        ]

        letters = "WOW-BINGO"
        x_pos = int(30 * self.scale_x)
        y_pos = int(30 * self.scale_y)

        # Draw each letter with its color and shadow
        for i, letter in enumerate(letters):
            # Shadow
            shadow_surf = header_font.render(letter, True, (20, 20, 20))
            self.screen.blit(shadow_surf, (x_pos + int(3 * self.scale_x), y_pos + int(3 * self.scale_y)))

            # Letter
            letter_surf = header_font.render(letter, True, colors[i])
            self.screen.blit(letter_surf, (x_pos, y_pos))

            # Move position for next letter
            x_pos += letter_surf.get_width() * 0.85

    def draw_bingo_grid(self):
        """Draw the BINGO grid with numbers 1-75 as in the image"""
        screen_width = self.screen.get_width()

        # Define row colors and letters
        row_colors = {
            'B': (255, 60, 60),    # Red for B
            'I': (50, 200, 100),   # Green for I
            'N': (30, 100, 220),   # Blue for N
            'G': (255, 60, 60),    # Red for G
            'O': (255, 220, 50)    # Yellow for O
        }
        row_letters = "BINGO"

        # Calculate grid dimensions
        grid_start_x = int(30 * self.scale_x)
        grid_start_y = int(150 * self.scale_y)
        row_height = int(75 * self.scale_y)
        row_spacing = int(8 * self.scale_y)

        # Font sizes
        letter_font = pygame.font.SysFont("Impact", int(60 * min(self.scale_x, self.scale_y)), bold=True)
        number_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)

        # Position for the first row
        current_y = grid_start_y

        # Draw each row (B, I, N, G, O)
        for row_idx, letter in enumerate(row_letters):
            row_color = row_colors[letter]

            # Draw row letter
            letter_surf = letter_font.render(letter, True, row_color)
            letter_x = grid_start_x
            letter_y = current_y + (row_height - letter_surf.get_height()) // 2

            # Create a black background behind the letter
            letter_glow_width = letter_surf.get_width() + int(15 * self.scale_x)
            letter_glow_height = letter_surf.get_height() + int(10 * self.scale_y)
            letter_glow_x = letter_x - int(7 * self.scale_x)
            letter_glow_y = letter_y - int(5 * self.scale_y)

            # Create horizontal lines next to letter
            line_width = int(20 * self.scale_x)
            line_height = int(3 * self.scale_y)
            line_spacing = int(5 * self.scale_y)
            for i in range(3):
                line_y = letter_glow_y + int(10 * self.scale_y) + i * (line_height + line_spacing)
                pygame.draw.rect(self.screen, row_color, (letter_glow_x - line_width, line_y, line_width, line_height))

            # Draw the letter
            self.screen.blit(letter_surf, (letter_x, letter_y))

            # Draw the black background for numbers
            row_bg_width = screen_width - grid_start_x - int(80 * self.scale_x)
            row_bg_x = letter_x + letter_surf.get_width() + int(10 * self.scale_x)
            row_bg_rect = pygame.Rect(row_bg_x, current_y, row_bg_width, row_height)
            pygame.draw.rect(self.screen, BLACK, row_bg_rect, border_radius=int(10 * self.scale_y))

            # Calculate number of numbers in this row and spacing
            nums_per_row = 15
            num_spacing = row_bg_width / nums_per_row
            num_radius = min(int(25 * self.scale_x), int(num_spacing * 0.4))

            # Draw numbers for this row
            for col in range(nums_per_row):
                num = row_idx * nums_per_row + col + 1

                # Calculate number position
                num_x = row_bg_x + int(num_spacing * (col + 0.5))
                num_y = current_y + row_height // 2

                # Check if number is called/selected or current
                is_selected = num in self.called_numbers
                is_current = num == self.cartella_number

                # Determine circle color
                if is_selected or is_current:
                    # If selected or current, use row color (green/red/etc)
                    circle_color = row_colors[letter]
                else:
                    # Otherwise use gray
                    circle_color = GRAY

                # Draw circle
                pygame.draw.circle(self.screen, circle_color, (num_x, num_y), num_radius)

                # Calculate correct font size based on number of digits
                if num < 10:
                    font_size = int(28 * min(self.scale_x, self.scale_y))
                else:
                    font_size = int(25 * min(self.scale_x, self.scale_y))
                current_font = pygame.font.SysFont("Arial", font_size, bold=True)

                # Draw number text
                num_text = str(num)
                num_surf = current_font.render(num_text, True, WHITE)
                num_rect = num_surf.get_rect(center=(num_x, num_y))
                self.screen.blit(num_surf, num_rect)

                # Store hit area for each number
                number_rect = pygame.Rect(num_x - num_radius, num_y - num_radius, num_radius * 2, num_radius * 2)
                self.hit_areas[f"lucky_number_{num}"] = number_rect

            # Move to next row
            current_y += row_height + row_spacing

    def draw_prize_pool_section(self, x, y, width, height):
        """Draw the PRIZE POOL section as shown in the image"""
        # Background
        prize_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(prize_rect, (20, 45, 60), (25, 50, 65), 8)

        # PRIZE POOL title
        title_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("PRIZE POOL", True, WHITE)
        title_x = x + int(20 * self.scale_x)
        title_y = y + int(15 * self.scale_y)
        self.screen.blit(title_text, (title_x, title_y))

        # Bet Amount label and value
        label_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)))
        value_font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)

        # Bet Amount label
        bet_label = label_font.render("Bet Amount:", True, WHITE)
        bet_x = title_x
        bet_y = title_y + title_text.get_height() + int(15 * self.scale_y)
        self.screen.blit(bet_label, (bet_x, bet_y))

        # Bet Amount value box
        value_box_width = int(width * 0.5)
        value_box_height = int(40 * self.scale_y)
        value_box_x = bet_x
        value_box_y = bet_y + bet_label.get_height() + int(5 * self.scale_y)
        value_box_rect = pygame.Rect(value_box_x, value_box_y, value_box_width, value_box_height)

        # Draw value box
        self.draw_gradient_rect(value_box_rect, (5, 15, 25), (8, 18, 28), 6)
        pygame.draw.rect(self.screen, (40, 60, 70), value_box_rect, 1, border_radius=6)

        # Store hit area for bet input
        self.hit_areas["bet_input"] = value_box_rect

        # Draw either input text or current bet amount
        if self.bet_input_active:
            display_text = self.bet_input_text
            if self.bet_input_cursor_visible:
                display_text += "|"
            bet_text = value_font.render(display_text, True, WHITE)
        else:
            bet_text = value_font.render(str(self.bet_amount), True, WHITE)

        # Position bet text in box
        bet_text_x = value_box_x + int(15 * self.scale_x)
        bet_text_y = value_box_y + (value_box_height - bet_text.get_height()) // 2
        self.screen.blit(bet_text, (bet_text_x, bet_text_y))

        # Add SET button
        set_btn_width = int(80 * self.scale_x)
        set_btn_height = value_box_height
        set_btn_x = value_box_x + value_box_width + int(10 * self.scale_x)
        set_btn_y = value_box_y
        set_btn_rect = pygame.Rect(set_btn_x, set_btn_y, set_btn_width, set_btn_height)

        # Draw SET button
        self.draw_gradient_rect(set_btn_rect, (30, 80, 150), (20, 60, 120), 6)

        # Draw SET text
        set_text = value_font.render("SET", True, WHITE)
        set_text_x = set_btn_x + (set_btn_width - set_text.get_width()) // 2
        set_text_y = set_btn_y + (set_btn_height - set_text.get_height()) // 2
        self.screen.blit(set_text, (set_text_x, set_text_y))

        # Store hit area for SET button
        self.hit_areas["bet_set"] = set_btn_rect

        # Draw prize value
        prize_font = pygame.font.SysFont("Arial", int(48 * min(self.scale_x, self.scale_y)), bold=True)
        prize_text = prize_font.render(str(self.prize_pool), True, GOLD)
        prize_x = x + (width - prize_text.get_width()) // 2
        prize_y = value_box_y + value_box_height + int(15 * self.scale_y)

        # Draw prize with glow
        glow_color = (GOLD[0], GOLD[1], GOLD[2], 100)
        glow_surf = pygame.Surface((prize_text.get_width() + int(10 * self.scale_x),
                                    prize_text.get_height() + int(10 * self.scale_y)), pygame.SRCALPHA)
        pygame.draw.ellipse(glow_surf, glow_color, glow_surf.get_rect())
        self.screen.blit(glow_surf, (prize_x - int(5 * self.scale_x), prize_y - int(5 * self.scale_y)))
        self.screen.blit(prize_text, (prize_x, prize_y))

        # Draw ETB label
        etb_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True)
        etb_text = etb_font.render("ETB", True, ORANGE)
        etb_x = x + (width - etb_text.get_width()) // 2
        etb_y = prize_y + prize_text.get_height()
        self.screen.blit(etb_text, (etb_x, etb_y))

    def draw_add_players_section(self, x, y, width, height):
        """Draw the Add Players section as shown in the image"""
        # Background
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (20, 45, 60), (25, 50, 65), 8)

        # Add Players title
        title_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("Add Players", True, WHITE)
        title_x = x + int(20 * self.scale_x)
        title_y = y + int(15 * self.scale_y)
        self.screen.blit(title_text, (title_x, title_y))

        # Cartella Number label and input
        label_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)))
        value_font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)

        # Cartella Number label
        label_text = label_font.render("Cartella Number", True, WHITE)
        label_x = title_x
        label_y = title_y + title_text.get_height() + int(15 * self.scale_y)
        self.screen.blit(label_text, (label_x, label_y))

        # Cartella Number input box
        input_width = int(150 * self.scale_x)
        input_height = int(40 * self.scale_y)
        input_x = label_x
        input_y = label_y + label_text.get_height() + int(5 * self.scale_y)
        input_rect = pygame.Rect(input_x, input_y, input_width, input_height)

        # Draw input box
        self.draw_gradient_rect(input_rect, (5, 15, 25), (8, 18, 28), 6)
        pygame.draw.rect(self.screen, (40, 60, 70), input_rect, 1, border_radius=6)

        # Store hit area for cartella input
        self.hit_areas["cartella_input"] = input_rect

        # Draw up/down arrows
        arrow_size = int(12 * self.scale_x)
        arrow_x = input_x + input_width - int(30 * self.scale_x)

        # Up arrow
        up_arrow_y = input_y + int(input_height * 0.25)
        up_arrow_rect = pygame.Rect(
            arrow_x - arrow_size,
            up_arrow_y - arrow_size,
            arrow_size * 2,
            arrow_size * 2
        )
        pygame.draw.polygon(self.screen, WHITE, [
            (arrow_x, up_arrow_y - arrow_size//2),
            (arrow_x + arrow_size, up_arrow_y + arrow_size//2),
            (arrow_x - arrow_size, up_arrow_y + arrow_size//2)
        ])

        # Down arrow
        down_arrow_y = input_y + int(input_height * 0.75)
        down_arrow_rect = pygame.Rect(
            arrow_x - arrow_size,
            down_arrow_y - arrow_size,
            arrow_size * 2,
            arrow_size * 2
        )
        pygame.draw.polygon(self.screen, WHITE, [
            (arrow_x, down_arrow_y + arrow_size//2),
            (arrow_x - arrow_size, down_arrow_y - arrow_size//2),
            (arrow_x + arrow_size, down_arrow_y - arrow_size//2)
        ])

        # Store hit areas for arrows
        self.hit_areas["cartella_up"] = up_arrow_rect
        self.hit_areas["cartella_down"] = down_arrow_rect

        # Draw input value
        if self.input_active:
            display_text = self.input_text
            if self.input_cursor_visible:
                display_text += "|"
            input_value = value_font.render(display_text, True, WHITE)
        else:
            input_value = value_font.render(str(self.cartella_number), True, WHITE)

        # Position input text
        input_text_x = input_x + int(15 * self.scale_x)
        input_text_y = input_y + (input_height - input_value.get_height()) // 2
        self.screen.blit(input_value, (input_text_x, input_text_y))

        # Add ADD button
        add_btn_width = int(100 * self.scale_x)
        add_btn_height = input_height
        add_btn_x = input_x + input_width + int(20 * self.scale_x)
        add_btn_y = input_y
        add_btn_rect = pygame.Rect(add_btn_x, add_btn_y, add_btn_width, add_btn_height)

        # Draw ADD button with gradient
        self.draw_gradient_rect(add_btn_rect, (30, 80, 150), (20, 60, 120), 8)

        # Draw ADD text
        add_text = value_font.render("ADD", True, WHITE)
        add_text_x = add_btn_x + (add_btn_width - add_text.get_width()) // 2
        add_text_y = add_btn_y + (add_btn_height - add_text.get_height()) // 2
        self.screen.blit(add_text, (add_text_x, add_text_y))

        # Store hit area for ADD button
        self.hit_areas["add_player"] = add_btn_rect

    def draw_next_button(self, x, y, width, height):
        """Draw the Next button as shown in the image"""
        btn_rect = pygame.Rect(x, y, width, height)

        # Draw button with teal gradient
        self.draw_gradient_rect(btn_rect, (20, 120, 100), (30, 140, 120), 10)

        # Draw arrow icon and text
        btn_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)
        btn_text = btn_font.render("Next", True, WHITE)

        # Draw play triangle icon
        triangle_size = int(20 * self.scale_x)
        triangle_x = x + int(width * 0.25)
        triangle_y = y + height // 2

        pygame.draw.polygon(self.screen, WHITE, [
            (triangle_x, triangle_y - triangle_size),
            (triangle_x + triangle_size, triangle_y),
            (triangle_x, triangle_y + triangle_size)
        ])

        # Draw text
        text_x = triangle_x + triangle_size + int(10 * self.scale_x)
        text_y = y + (height - btn_text.get_height()) // 2
        self.screen.blit(btn_text, (text_x, text_y))

        # Store hit area
        self.hit_areas["continue"] = btn_rect

    def draw_continue_button(self, x, y, width, height):
        """Draw the Continue button"""
        # Button background with gradient
        btn_rect = pygame.Rect(x, y, width, height)

        # Get button animation state
        btn_state = self.button_states["continue"]

        # Check for hover animation
        hover_alpha = btn_state["hover_alpha"]

        # Base color and hover color
        base_color = (0, 120, 180)
        hover_color = (0, 150, 220)

        # Calculate effective color based on hover state
        if hover_alpha > 0:
            # Blend colors based on hover alpha
            effective_color = (
                int(base_color[0] + (hover_color[0] - base_color[0]) * hover_alpha / 255),
                int(base_color[1] + (hover_color[1] - base_color[1]) * hover_alpha / 255),
                int(base_color[2] + (hover_color[2] - base_color[2]) * hover_alpha / 255)
            )
        else:
            effective_color = base_color

        # Draw button
        self.draw_gradient_rect(btn_rect, effective_color,
                               (effective_color[0]//2, effective_color[1]//2, effective_color[2]//2), 10)

        # Draw text
        btn_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        btn_text = btn_font.render("Continue to Game", True, WHITE)
        text_x = btn_rect.centerx - btn_text.get_width() // 2
        text_y = btn_rect.centery - btn_text.get_height() // 2
        self.screen.blit(btn_text, (text_x, text_y))

        # Store hit area
        self.hit_areas["continue"] = btn_rect

    def draw_selected_cartellas(self, x, y, width):
        """Draw list of selected cartella numbers"""
        if not self.selected_cartella_numbers:
            return

        # Draw header
        header_font = pygame.font.SysFont("Arial", int(20 * min(self.scale_x, self.scale_y)), bold=True)
        header_text = header_font.render("Selected Cartella Numbers:", True, WHITE)
        self.screen.blit(header_text, (x, y))

        # Draw cartella numbers
        number_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)))
        cartella_text = ", ".join([str(num) for num in self.selected_cartella_numbers])
        cartella_surf = number_font.render(cartella_text, True, LIGHT_GREEN)
        self.screen.blit(cartella_surf, (x, y + header_text.get_height() + int(5 * self.scale_y)))

    def update_button_hover_states(self, mouse_pos):
        """Update hover states for all buttons based on mouse position"""
        for key, area in self.hit_areas.items():
            # Skip if this button doesn't have animation state
            if key not in self.button_states:
                continue

            # Check if mouse is over this button
            hover = area.collidepoint(mouse_pos)
            btn_state = self.button_states[key]

            # Update hover state
            if hover != btn_state["hover"]:
                btn_state["hover"] = hover
                if hover:
                    # Mouse entered - start hover-in animation
                    btn_state["hover_alpha"] = 1
                else:
                    # Mouse left - start hover-out animation
                    btn_state["hover_alpha"] = 255

            # Animate hover alpha with improved smoother transitions
            if hover and btn_state["hover_alpha"] < 255:
                # Faster and smoother fade-in with easing
                progress = btn_state["hover_alpha"] / 255
                ease_factor = 0.15 * (1 - progress)  # Gradually slow down as it approaches 255
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 15 + int(ease_factor * 30))
            elif not hover and btn_state["hover_alpha"] > 0:
                # Smoother fade-out with easing
                progress = btn_state["hover_alpha"] / 255
                ease_factor = 0.15 * progress  # Gradually slow down as it approaches 0
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 8 - int(ease_factor * 15))

            # Handle click animation if active
            if btn_state.get("click", False):
                current_time = pygame.time.get_ticks()
                elapsed = current_time - btn_state.get("click_time", 0)

                # Animation duration (300ms)
                if elapsed > 300:
                    btn_state["click"] = False
                    btn_state["click_scale"] = 0
                else:
                    # Elastic animation effect
                    progress = elapsed / 300
                    if progress < 0.5:
                        # Compress
                        btn_state["click_scale"] = int(15 * (progress * 2))
                    else:
                        # Expand back with slight bounce
                        t = (progress - 0.5) * 2
                        bounce = math.sin(t * math.pi) * 0.2
                        btn_state["click_scale"] = max(0, int(15 * (1 - t + bounce)))

    def check_button_click(self, pos):
        """Check which button was clicked and take appropriate action"""
        # Create a copy of the keys to avoid dictionary changed size during iteration error
        for key in list(self.hit_areas.keys()):
            area = self.hit_areas[key]
            if area.collidepoint(pos):
                if key == "continue":
                    # Store flag to exit the loop on next frame
                    self.hit_areas["continue_clicked"] = True

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # Animate button click
                    if key in self.button_states:
                        btn_state = self.button_states[key]
                        btn_state["click"] = True
                        btn_state["click_time"] = pygame.time.get_ticks()

                elif key == "add_player":
                    self.add_player()

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # Animate button click
                    if key in self.button_states:
                        btn_state = self.button_states[key]
                        btn_state["click"] = True
                        btn_state["click_time"] = pygame.time.get_ticks()

                elif key == "bet_set":
                    # Apply bet input value
                    self._apply_bet_input()
                    self.bet_input_active = False

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                elif key == "cartella_input":
                    # Activate cartella number input
                    self.input_active = True
                    self.bet_input_active = False  # Ensure only one input is active

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                elif key == "cartella_up":
                    # Increase cartella number
                    self.cartella_number = min(75, self.cartella_number + 1)
                    self.input_text = str(self.cartella_number)

                    # Update bingo board based on cartella number
                    self.create_board_from_number(self.cartella_number)

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                elif key == "cartella_down":
                    # Decrease cartella number
                    self.cartella_number = max(1, self.cartella_number - 1)
                    self.input_text = str(self.cartella_number)

                    # Update bingo board based on cartella number
                    self.create_board_from_number(self.cartella_number)

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                elif key == "bet_input":
                    # Activate bet amount input
                    self.bet_input_active = True
                    self.input_active = False  # Ensure only one input is active

                    # Play sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                # Check if this is a lucky number click
                elif key.startswith("lucky_number_"):
                    try:
                        number = int(key.split("_")[-1])
                        # Toggle called state
                        if number in self.called_numbers:
                            self.called_numbers.remove(number)
                        else:
                            self.called_numbers.append(number)

                        # Only update cartella if it's a different number
                        if self.cartella_number != number:
                            # Update cartella number to the clicked lucky number
                            self.cartella_number = number
                            self.input_text = str(number)

                            # Update bingo board based on cartella number
                            self.create_board_from_number(number)

                        # Play sound
                        if self.button_click_sound:
                            self.button_click_sound.play()
                    except ValueError:
                        pass

    def add_player(self):
        """Add player with current cartella number"""
        # Validate cartella number
        is_valid, error_msg = self.validate_cartella_number(str(self.cartella_number))
        if not is_valid:
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180
            return

        # Add cartella number to selected list if not already there
        if self.cartella_number not in self.selected_cartella_numbers:
            self.selected_cartella_numbers.append(self.cartella_number)

            # Create a new player
            player = Player(
                cartela_no=self.cartella_number,
                bet_amount=self.bet_amount
            )

            # Add to players list
            self.players.append(player)

            # Save players to JSON
            save_players_to_json(self.players)

            # Update prize pool
            self.calculate_prize_pool()

            # Show success message
            self.message = f"Added player with cartella #{self.cartella_number}"
            self.message_type = "success"
            self.message_timer = 180
        else:
            # Show error message
            self.message = f"Cartella #{self.cartella_number} already selected"
            self.message_type = "error"
            self.message_timer = 180

    def calculate_prize_pool(self):
        """Calculate prize pool based on players and bet amount"""
        total_bets = sum(player.bet_amount for player in self.players)
        self.prize_pool = total_bets

    def validate_cartella_number(self, number):
        """Validate cartella number input"""
        try:
            num = int(number)
            if num < 1 or num > 100:
                return False, "Cartella number must be between 1 and 100"
            return True, ""
        except ValueError:
            return False, "Cartella number must be a valid integer"

    def handle_input(self, event):
        """Handle keyboard input events"""
        if not self.input_active and not self.bet_input_active:
            return False

        if event.type != pygame.KEYDOWN:
            return False

        # Flag to track if we should play a sound
        play_sound = False

        if event.key == pygame.K_ESCAPE:
            # Cancel input
            if self.input_active:
                self.input_active = False
                self.input_text = str(self.cartella_number)
                play_sound = True
            elif self.bet_input_active:
                self.bet_input_active = False
                self.bet_input_text = str(self.bet_amount)
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
            # Confirm input
            if self.input_active:
                self.input_active = False
                is_valid, error_msg = self.validate_cartella_number(self.input_text)
                if is_valid:
                    self.cartella_number = int(self.input_text)
                    # Update bingo board based on new cartella number
                    self.create_board_from_number(self.cartella_number)
                    play_sound = True
                else:
                    # Show error message
                    self.message = error_msg
                    self.message_type = "error"
                    self.message_timer = 180
                    play_sound = True
            elif self.bet_input_active:
                self._apply_bet_input()
                self.bet_input_active = False
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_BACKSPACE:
            # Remove last character
            if self.input_active and self.input_text:
                self.input_text = self.input_text[:-1]
                play_sound = True
            elif self.bet_input_active and self.bet_input_text:
                self.bet_input_text = self.bet_input_text[:-1]
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        # Only allow numeric input
        if event.unicode.isdigit():
            if self.input_active:
                # Limit input length
                if len(self.input_text) < 2:  # Max 2 digits for cartella (1-75)
                    self.input_text += event.unicode
                    play_sound = True
            elif self.bet_input_active:
                # Limit input length
                if len(self.bet_input_text) < 4:  # Max 4 digits for bet (up to 9999)
                    self.bet_input_text += event.unicode
                    play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        return False

    def _apply_bet_input(self):
        """Apply bet input value"""
        try:
            value = int(self.bet_input_text)
            if value < 10:
                value = 10
            elif value > 1000:
                value = 1000

            self.bet_amount = value
            self.bet_input_text = str(value)
            self.calculate_prize_pool()

        except ValueError:
            # Reset to previous valid value
            self.bet_input_text = str(self.bet_amount)

    def draw_toast_message(self):
        """Draw responsive toast message for feedback"""
        if self.message_timer <= 0:
            return

        # Choose color based on message type
        if self.message_type == "error":
            color = (255, 70, 70)  # Red for errors
        elif self.message_type == "success":
            color = (70, 255, 70)  # Green for success
        else:
            color = (70, 70, 255)  # Blue for info

        # Calculate size and position
        screen_width, screen_height = self.screen.get_size()

        # Responsive padding based on screen size
        padding = max(int(12 * min(self.scale_x, self.scale_y)), 6)

        # Responsive font size based on screen size
        font_size = max(int(18 * min(self.scale_x, self.scale_y)), 12)
        font_size = min(font_size, int(screen_height * 0.03))  # Prevent too large

        # Calculate size based on message length
        font = pygame.font.SysFont("Arial", font_size)
        msg_surf = font.render(self.message, True, WHITE)

        # Ensure toast isn't too wide for the screen
        max_width = screen_width * 0.8
        if msg_surf.get_width() > max_width - padding * 2:
            # Need to wrap text
            wrapped_text = self._wrap_text(self.message, font, max_width - padding * 2)
            total_height = 0
            text_surfs = []

            # Create surfaces for each line
            for line in wrapped_text:
                line_surf = font.render(line, True, WHITE)
                text_surfs.append(line_surf)
                total_height += line_surf.get_height()

            # Calculate toast dimensions
            toast_width = max(surf.get_width() for surf in text_surfs) + padding * 2
            toast_height = total_height + padding * 2

            # Create toast background
            toast_x = (screen_width - toast_width) // 2
            toast_y = screen_height - toast_height - padding * 2
            toast_rect = pygame.Rect(toast_x, toast_y, toast_width, toast_height)

            # Draw rounded rectangle with semi-transparency
            toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
            pygame.draw.rect(toast_surface, (*color, 220), toast_surface.get_rect(),
                        border_radius=int(10 * min(self.scale_x, self.scale_y)))

            # Draw border
            pygame.draw.rect(toast_surface, (*color, 255), toast_surface.get_rect(),
                            width=int(2 * min(self.scale_x, self.scale_y)),
                            border_radius=int(10 * min(self.scale_x, self.scale_y)))

            # Draw to screen
            self.screen.blit(toast_surface, toast_rect)

            # Draw each line of text
            y_offset = padding
            for surf in text_surfs:
                x_pos = toast_x + (toast_width - surf.get_width()) // 2
                self.screen.blit(surf, (x_pos, toast_y + y_offset))
                y_offset += surf.get_height()

        else:
            # Simple single-line toast
            toast_width = msg_surf.get_width() + padding * 2
            toast_height = msg_surf.get_height() + padding

            # Center horizontally, position at bottom
            toast_x = (screen_width - toast_width) // 2
            toast_y = screen_height - toast_height - padding * 2

            # Create toast background
            toast_rect = pygame.Rect(toast_x, toast_y, toast_width, toast_height)

            # Draw rounded rectangle with semi-transparency
            toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
            pygame.draw.rect(toast_surface, (*color, 220), toast_surface.get_rect(),
                        border_radius=int(10 * min(self.scale_x, self.scale_y)))

            # Draw border
            pygame.draw.rect(toast_surface, (*color, 255), toast_surface.get_rect(),
                            width=int(2 * min(self.scale_x, self.scale_y)),
                            border_radius=int(10 * min(self.scale_x, self.scale_y)))

            # Draw to screen
            self.screen.blit(toast_surface, toast_rect)

            # Draw message text
            self.screen.blit(msg_surf, (toast_x + padding, toast_y + padding // 2))

    def _wrap_text(self, text, font, max_width):
        """Helper method to wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []

        for word in words:
            # Try adding this word to the current line
            test_line = ' '.join(current_line + [word])
            test_width = font.size(test_line)[0]

            if test_width <= max_width:
                # Word fits, add it to the current line
                current_line.append(word)
            else:
                # Word doesn't fit, start a new line
                if current_line:  # Only append if there are words in the line
                    lines.append(' '.join(current_line))
                current_line = [word]

        # Add the last line
        if current_line:
            lines.append(' '.join(current_line))

        return lines

    # Methods to be implemented from main.py (copied/modified)
    def draw_gradient_rect(self, rect, color_top, color_bottom, border_radius=0, glow=False, glow_color=None, glow_intensity=10):
        """Draw a rectangle with a vertical gradient from color_top to color_bottom

        Args:
            rect: pygame.Rect object or (x, y, width, height) tuple
            color_top: (r, g, b) tuple for top color
            color_bottom: (r, g, b) tuple for bottom color
            border_radius: radius for rounded corners (0 = rectangular)
            glow: if True, adds a subtle outer glow
            glow_color: color of the glow (defaults to lighter version of color_top)
            glow_intensity: intensity of the glow effect (0-20)
        """
        # Ensure we have a proper Rect
        rect = pygame.Rect(rect)

        # If a glow effect is requested
        if glow:
            # Default glow color is a lighter version of the top color
            if glow_color is None:
                glow_color = tuple(min(255, c + 70) for c in color_top)

            # Create larger rect for the glow
            glow_rect = rect.inflate(glow_intensity*2, glow_intensity*2)

            # Create a surface with alpha for the glow
            glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)

            # Draw the glow with transparency
            for i in range(glow_intensity, 0, -1):
                alpha = 100 - (i * 5)  # Alpha decreases as we move outward
                current_rect = rect.inflate(i*2, i*2)
                current_rect.center = glow_rect.width//2, glow_rect.height//2
                pygame.draw.rect(glow_surface, (*glow_color, alpha),
                                current_rect, 0, border_radius + i//2)

            # Blit the glow surface
            self.screen.blit(glow_surface, glow_rect.topleft)

        # Create a surface for our gradient
        gradient_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Draw gradient
        for y in range(rect.height):
            # Calculate color for this line
            progress = y / rect.height
            color = [
                int(color_top[0] + (color_bottom[0] - color_top[0]) * progress),
                int(color_top[1] + (color_bottom[1] - color_top[1]) * progress),
                int(color_top[2] + (color_bottom[2] - color_top[2]) * progress)
            ]

            # Draw a line with this color
            pygame.draw.line(gradient_surface, color, (0, y), (rect.width, y))

        # If we need rounded corners, use a mask
        if border_radius > 0:
            # Create a mask surface with rounded rect
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255, 255),
                             (0, 0, rect.width, rect.height), 0, border_radius)

            # Apply mask to our gradient
            masked_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            masked_surface.blit(gradient_surface, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)

            # Blit the result to the screen
            self.screen.blit(masked_surface, rect.topleft)
        else:
            # No rounded corners, just blit directly
            self.screen.blit(gradient_surface, rect.topleft)

    def draw_lucky_numbers(self, x_position, y_position, section_width, section_height):
        """Draw the Lucky Numbers section at the specified position with the given dimensions"""
        # Position header correctly using passed coordinates and height
        header_rect = pygame.Rect(
            x_position,  # x coordinate
            y_position,  # y coordinate
            section_width,
            section_height
        )
        self.draw_gradient_rect(header_rect, DARK_BLUE, LIGHT_BLUE, 10)

        # Calculate responsive font sizes based on available space
        header_size = max(int(28 * min(self.scale_x, self.scale_y)), int(section_width * 0.03))
        header_size = min(header_size, int(section_height * 0.06))  # Prevent too large fonts

        # Draw "LUCKY NUMBERS" text
        header_font = pygame.font.SysFont("Arial", header_size, bold=True)
        lucky_surf = header_font.render("CARTELLA NUMBERS", True, WHITE)
        lucky_x = header_rect.x + int(20 * self.scale_x)
        lucky_y = header_rect.y + int(10 * self.scale_y)  # Positioned relative to header_rect
        self.screen.blit(lucky_surf, (lucky_x, lucky_y))

        # Calculate available width for total players text
        screen_width = self.screen.get_width()
        continue_btn_width = min(int(200 * self.scale_x), int(screen_width * 0.2))
        right_margin = screen_width - int(20 * self.scale_x) - continue_btn_width

        # Draw total callout with orange color - positioned below title
        total_surf = header_font.render(f"#Total players:{len(self.called_numbers)}", True, ORANGE)

        # Position below the CARTELLA NUMBERS text
        total_x = lucky_x
        total_y = lucky_y + lucky_surf.get_height() + int(5 * self.scale_y)
        self.screen.blit(total_surf, (total_x, total_y))

        # Letters for BINGO rows with improved colors for better visibility
        bingo_letters = "BINGO"
        row_colors = {
            'B': (255, 60, 60),    # Slightly brighter red for B
            'I': (50, 220, 100),   # Brighter green for I
            'N': (80, 120, 255),   # Brighter blue for N
            'G': (255, 60, 60),    # Same red for G (matching B)
            'O': (255, 220, 50)    # Brighter gold/yellow for O
        }

        # Calculate responsive row spacing based on available height - make more compact
        content_start_y = total_y + total_surf.get_height() + int(6 * self.scale_y)  # Start after total players
        available_height = header_rect.bottom - content_start_y - int(8 * self.scale_y)  # Reduced bottom padding
        row_height = available_height / 5  # 5 rows for B, I, N, G, O

        # Create responsive font sizes for letters and numbers
        letter_size = max(int(54 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.04))
        letter_size = min(letter_size, int(row_height * 0.7))  # Prevent too large fonts
        bingo_letter_font = pygame.font.SysFont("Arial Black", letter_size, bold=True)

        # DOUBLED base number size (from 28 to 56)
        number_size = max(int(56 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.04))
        number_size = min(number_size, int(row_height * 0.8))  # Increased max size to prevent capping
        number_font = pygame.font.SysFont("Arial", number_size, bold=True)

        # Calculate pulsating effect for animations
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1
        fast_pulse = (math.sin(time.time() * 6) + 1) / 2  # Faster pulsing for current number

        # Calculate enhanced pulsating effect for current cartella
        current_pulse = (math.sin(time.time() * 4) + 1) / 2  # Medium-speed pulse for current cartella

        # Draw each row of the bingo board
        for row_idx, letter in enumerate(bingo_letters):
            row_color = row_colors[letter]

            # Use more compact layout with letters on left and numbers spread across width
            letter_x = header_rect.x + int(20 * self.scale_x)  # Moved closer to left edge
            letter_y = content_start_y + row_idx * row_height

            # Draw cleaner horizontal lines (reduced count for cleaner look)
            line_count = 2  # Reduced line count
            line_length = int(10 * self.scale_x)  # Shorter lines
            line_thickness = max(1, int(2 * min(self.scale_x, self.scale_y)))
            line_spacing = int(5 * self.scale_y)  # Reduced spacing
            line_start_y = letter_y + int(28 * self.scale_y)

            for i in range(line_count):
                line_y_pos = line_start_y + i * line_spacing
                pygame.draw.line(self.screen, row_color,
                               (letter_x - line_length - int(4 * self.scale_x), line_y_pos),
                               (letter_x - int(4 * self.scale_x), line_y_pos),
                               line_thickness)

            # Draw the BINGO letter with reduced 3D effect for better readability
            letter_surf = bingo_letter_font.render(letter, True, row_color)

            # Add subtle shadow for minimal 3D effect (reduced offset)
            shadow_offset = int(1 * self.scale_x)  # Reduced shadow
            shadow_surf = bingo_letter_font.render(letter, True, (row_color[0]//2, row_color[1]//2, row_color[2]//2))
            shadow_pos = (letter_x + shadow_offset, letter_y + shadow_offset)
            self.screen.blit(shadow_surf, shadow_pos)

            # Draw main letter
            self.screen.blit(letter_surf, (letter_x, letter_y))

            # Calculate responsive row background width - use more of the available width
            letter_width = letter_surf.get_width()
            number_area_width = section_width - letter_width - int(50 * self.scale_x)  # More width for numbers
            row_bg_width = min(number_area_width, header_rect.right - letter_x - int(40 * self.scale_x))

            # Make row background taller to fit larger numbers
            row_bg_rect = pygame.Rect(
                letter_x + int(45 * self.scale_x),  # Reduced gap between letter and numbers
                letter_y + int(6 * self.scale_y),   # Moved closer to letter
                row_bg_width,
                min(int(54 * self.scale_y), int(row_height * 0.85))  # Taller row background for larger numbers
            )
            pygame.draw.rect(self.screen, (20, 20, 25), row_bg_rect, border_radius=int(20 * self.scale_y))

            # Calculate number sizes and spacing based on available width
            available_width = row_bg_width
            # Increased circle size to accommodate larger numbers
            circle_radius = min(int(22 * min(self.scale_x, self.scale_y)), int(available_width / 42))

            # Each row will display two sets of numbers for a total of 20 numbers per row
            # For example: B row will have numbers 1-20, I row will have 21-40, etc.
            numbers_per_row = 20
            # Adjusted spacing to account for larger circles
            num_spacing = available_width / (numbers_per_row + 0.2)  # Reduced margin to fit larger circles

            # Draw the numbers in a single row - with animation for called numbers
            for col in range(numbers_per_row):
                num = row_idx * numbers_per_row + col + 1  # Numbers 1-100

                # Skip numbers beyond 100
                if num > 100:
                    continue

                # Calculate number position - ensure it's within the row background
                num_x = row_bg_rect.x + int(num_spacing/2) + col * num_spacing
                num_x = min(num_x, row_bg_rect.right - circle_radius)  # Ensure it doesn't exceed right boundary
                num_y = row_bg_rect.centery

                # Check if number has been called (for Board Selection, we want all numbers with hover effect)
                is_called = num in self.called_numbers
                is_current = num == self.current_number
                is_current_cartella = num == self.cartella_number
                is_recently_called = is_current

                # Enhanced visual treatment for current cartella number
                if is_current_cartella:
                    # Create pulsating multi-layer glow effect for current cartella number
                    # Use a brighter version of the row color for the glow
                    glow_color = (
                        min(255, row_color[0] + 80),
                        min(255, row_color[1] + 80),
                        min(255, row_color[2] + 80)
                    )

                    # Calculate dynamic glow radius based on pulse
                    glow_radius = circle_radius * (1.3 + 0.3 * current_pulse)

                    # Draw multiple outer glow circles with decreasing opacity for smooth fade
                    for i in range(3):  # Use 3 layers for smoother gradient
                        alpha = int(180 - i * 40)
                        outer_glow_color = (*glow_color, alpha)
                        outer_glow_size = int(glow_radius) + i * 2
                        pygame.draw.circle(self.screen, outer_glow_color, (num_x, num_y), outer_glow_size, 0)

                    # Draw inner highlight circle with high saturation
                    highlight_intensity = int(220 + 35 * current_pulse)
                    inner_highlight = (
                        min(255, row_color[0] + highlight_intensity//2),
                        min(255, row_color[1] + highlight_intensity//2),
                        min(255, row_color[2] + highlight_intensity//2)
                    )

                    # Draw main background circle with pulsating brightness
                    pygame.draw.circle(self.screen, inner_highlight, (num_x, num_y), int(circle_radius * 1.1))

                    # Add white ring for additional emphasis
                    pygame.draw.circle(self.screen, WHITE, (num_x, num_y), int(circle_radius * 0.9), 2)

                    # Draw reflections for 3D effect
                    reflection_pos = (num_x - int(circle_radius * 0.3), num_y - int(circle_radius * 0.3))
                    reflection_radius = int(circle_radius * 0.2)
                    pygame.draw.circle(self.screen, (255, 255, 255, 150), reflection_pos, reflection_radius)

                elif is_recently_called:
                    # Recently called number - more dramatic pulsating glow effect
                    # Create pulsating glow effect with multiple layers
                    glow_radius = circle_radius * (1.0 + 0.5 * fast_pulse)

                    # Draw outer glow circles with decreasing opacity
                    for i in range(2):  # Reduced glow layers
                        alpha = int(230 - i * 70)
                        glow_color = (*row_color, alpha)
                        glow_size = int(glow_radius) + i * 2
                        pygame.draw.circle(self.screen, glow_color, (num_x, num_y), glow_size, 2)

                    # Strong highlight fill with pulsating brightness
                    brightness = int(220 + 35 * fast_pulse)
                    highlight_color = (min(255, row_color[0] + brightness//3),
                                      min(255, row_color[1] + brightness//3),
                                      min(255, row_color[2] + brightness//3))
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)

                    # Add a white center for extra pop
                    pygame.draw.circle(self.screen, WHITE, (num_x, num_y), int(circle_radius * 0.7))

                elif is_called:
                    # Called but not recent - highlight with row color
                    # Subtle pulsating effect for all called numbers
                    highlight_intensity = int(170 + 40 * pulse)
                    highlight_color = (min(255, row_color[0] * highlight_intensity // 255),
                                      min(255, row_color[1] * highlight_intensity // 255),
                                      min(255, row_color[2] * highlight_intensity // 255))

                    # Draw background circle with row color
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)

                    # Add inner circle for better contrast
                    inner_color = (min(255, highlight_color[0] + 30),
                                  min(255, highlight_color[1] + 30),
                                  min(255, highlight_color[2] + 30))
                    pygame.draw.circle(self.screen, inner_color, (num_x, num_y), int(circle_radius * 0.75))
                else:
                    # Not called - dark background
                    pygame.draw.circle(self.screen, (60, 60, 70), (num_x, num_y), circle_radius)

                # Calculate responsive font size for numbers based on circle size - maximized
                # Increased font-to-circle ratio significantly for 2x larger numbers
                num_font_size = min(number_size, int(circle_radius * 1.8))
                if num_font_size < 10:  # Min font size for readability (increased)
                    continue  # Skip rendering numbers if they'd be too small

                current_font = pygame.font.SysFont("Arial", num_font_size, bold=True)

                # Two-digit numbers need special handling
                text_to_render = str(num)
                # Adjusted scaling for multi-digit numbers to better fit larger text
                if num >= 10 and num < 100:
                    current_font = pygame.font.SysFont("Arial", int(num_font_size * 0.95), bold=True)
                elif num >= 100:
                    current_font = pygame.font.SysFont("Arial", int(num_font_size * 0.85), bold=True)

                # Draw the number text with enhanced visuals for current cartella
                if is_current_cartella:
                    # Create bold and brighter text appearance for current cartella
                    # Use a custom font for the current cartella to make it stand out
                    special_font = pygame.font.SysFont("Arial Black", int(num_font_size * 1.1), bold=True)
                    num_surf = special_font.render(text_to_render, True, (255, 255, 255))

                    # Add glow effect to text by drawing slightly larger, semi-transparent text behind
                    glow_font = pygame.font.SysFont("Arial Black", int(num_font_size * 1.15), bold=True)
                    glow_surf = glow_font.render(text_to_render, True, (255, 255, 200))
                    glow_surf.set_alpha(150)
                    glow_rect = glow_surf.get_rect(center=(num_x, num_y))
                    self.screen.blit(glow_surf, glow_rect)

                    # Draw main text on top
                    num_rect = num_surf.get_rect(center=(num_x, num_y))
                    self.screen.blit(num_surf, num_rect)

                elif is_recently_called:
                    # Text color for recently called number - always black for contrast against white center
                    text_color = (0, 0, 0)
                    # Use larger, bolder font for the recently called number
                    num_surf = current_font.render(text_to_render, True, text_color)
                    num_rect = num_surf.get_rect(center=(num_x, num_y))
                    self.screen.blit(num_surf, num_rect)

                elif is_called:
                    # Text color for called numbers - white with high contrast
                    text_color = WHITE
                    num_surf = current_font.render(text_to_render, True, text_color)
                    num_rect = num_surf.get_rect(center=(num_x, num_y))
                    self.screen.blit(num_surf, num_rect)

                else:
                    # Text color for uncalled numbers - slightly dimmer
                    text_color = (200, 200, 200)
                    num_surf = current_font.render(text_to_render, True, text_color)
                    num_rect = num_surf.get_rect(center=(num_x, num_y))
                    self.screen.blit(num_surf, num_rect)

                # Store hit area for each number - each number circle is clickable
                lucky_number_rect = pygame.Rect(
                    num_x - circle_radius,
                    num_y - circle_radius,
                    circle_radius * 2,
                    circle_radius * 2
                )
                self.hit_areas[f"lucky_number_{num}"] = lucky_number_rect

    def draw_add_players_section(self, x, y, width, height):
        """Draw the Add Players section as shown in the image"""
        # Background
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (20, 45, 60), (25, 50, 65), 8)

        # Add Players title
        title_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("Add Players", True, WHITE)
        title_x = x + int(20 * self.scale_x)
        title_y = y + int(15 * self.scale_y)
        self.screen.blit(title_text, (title_x, title_y))

        # Cartella Number label and input
        label_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)))
        value_font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)

        # Cartella Number label
        label_text = label_font.render("Cartella Number", True, WHITE)
        label_x = title_x
        label_y = title_y + title_text.get_height() + int(15 * self.scale_y)
        self.screen.blit(label_text, (label_x, label_y))

        # Cartella Number input box
        input_width = int(150 * self.scale_x)
        input_height = int(40 * self.scale_y)
        input_x = label_x
        input_y = label_y + label_text.get_height() + int(5 * self.scale_y)
        input_rect = pygame.Rect(input_x, input_y, input_width, input_height)

        # Draw input box
        self.draw_gradient_rect(input_rect, (5, 15, 25), (8, 18, 28), 6)
        pygame.draw.rect(self.screen, (40, 60, 70), input_rect, 1, border_radius=6)

        # Store hit area for cartella input
        self.hit_areas["cartella_input"] = input_rect

        # Draw up/down arrows
        arrow_size = int(12 * self.scale_x)
        arrow_x = input_x + input_width - int(30 * self.scale_x)

        # Up arrow
        up_arrow_y = input_y + int(input_height * 0.25)
        up_arrow_rect = pygame.Rect(
            arrow_x - arrow_size,
            up_arrow_y - arrow_size,
            arrow_size * 2,
            arrow_size * 2
        )
        pygame.draw.polygon(self.screen, WHITE, [
            (arrow_x, up_arrow_y - arrow_size//2),
            (arrow_x + arrow_size, up_arrow_y + arrow_size//2),
            (arrow_x - arrow_size, up_arrow_y + arrow_size//2)
        ])

        # Down arrow
        down_arrow_y = input_y + int(input_height * 0.75)
        down_arrow_rect = pygame.Rect(
            arrow_x - arrow_size,
            down_arrow_y - arrow_size,
            arrow_size * 2,
            arrow_size * 2
        )
        pygame.draw.polygon(self.screen, WHITE, [
            (arrow_x, down_arrow_y + arrow_size//2),
            (arrow_x - arrow_size, down_arrow_y - arrow_size//2),
            (arrow_x + arrow_size, down_arrow_y - arrow_size//2)
        ])

        # Store hit areas for arrows
        self.hit_areas["cartella_up"] = up_arrow_rect
        self.hit_areas["cartella_down"] = down_arrow_rect

        # Draw input value
        if self.input_active:
            display_text = self.input_text
            if self.input_cursor_visible:
                display_text += "|"
            input_value = value_font.render(display_text, True, WHITE)
        else:
            input_value = value_font.render(str(self.cartella_number), True, WHITE)

        # Position input text
        input_text_x = input_x + int(15 * self.scale_x)
        input_text_y = input_y + (input_height - input_value.get_height()) // 2
        self.screen.blit(input_value, (input_text_x, input_text_y))

        # Add ADD button
        add_btn_width = int(100 * self.scale_x)
        add_btn_height = input_height
        add_btn_x = input_x + input_width + int(20 * self.scale_x)
        add_btn_y = input_y
        add_btn_rect = pygame.Rect(add_btn_x, add_btn_y, add_btn_width, add_btn_height)

        # Draw ADD button with gradient
        self.draw_gradient_rect(add_btn_rect, (30, 80, 150), (20, 60, 120), 8)

        # Draw ADD text
        add_text = value_font.render("ADD", True, WHITE)
        add_text_x = add_btn_x + (add_btn_width - add_text.get_width()) // 2
        add_text_y = add_btn_y + (add_btn_height - add_text.get_height()) // 2
        self.screen.blit(add_text, (add_text_x, add_text_y))

        # Store hit area for ADD button
        self.hit_areas["add_player"] = add_btn_rect

    def draw_next_button(self, x, y, width, height):
        """Draw the Next button as shown in the image"""
        btn_rect = pygame.Rect(x, y, width, height)

        # Draw button with teal gradient
        self.draw_gradient_rect(btn_rect, (20, 120, 100), (30, 140, 120), 10)

        # Draw arrow icon and text
        btn_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)
        btn_text = btn_font.render("Next", True, WHITE)

        # Draw play triangle icon
        triangle_size = int(20 * self.scale_x)
        triangle_x = x + int(width * 0.25)
        triangle_y = y + height // 2

        pygame.draw.polygon(self.screen, WHITE, [
            (triangle_x, triangle_y - triangle_size),
            (triangle_x + triangle_size, triangle_y),
            (triangle_x, triangle_y + triangle_size)
        ])

        # Draw text
        text_x = triangle_x + triangle_size + int(10 * self.scale_x)
        text_y = y + (height - btn_text.get_height()) // 2
        self.screen.blit(btn_text, (text_x, text_y))

        # Store hit area
        self.hit_areas["continue"] = btn_rect

    def draw_continue_button(self, x, y, width, height):
        """Draw the Continue button"""
        # Button background with gradient
        btn_rect = pygame.Rect(x, y, width, height)

        # Get button animation state
        btn_state = self.button_states["continue"]

        # Check for hover animation
        hover_alpha = btn_state["hover_alpha"]

        # Base color and hover color
        base_color = (0, 120, 180)
        hover_color = (0, 150, 220)

        # Calculate effective color based on hover state
        if hover_alpha > 0:
            # Blend colors based on hover alpha
            effective_color = (
                int(base_color[0] + (hover_color[0] - base_color[0]) * hover_alpha / 255),
                int(base_color[1] + (hover_color[1] - base_color[1]) * hover_alpha / 255),
                int(base_color[2] + (hover_color[2] - base_color[2]) * hover_alpha / 255)
            )
        else:
            effective_color = base_color

        # Draw button
        self.draw_gradient_rect(btn_rect, effective_color,
                               (effective_color[0]//2, effective_color[1]//2, effective_color[2]//2), 10)

        # Draw text
        btn_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        btn_text = btn_font.render("Continue to Game", True, WHITE)
        text_x = btn_rect.centerx - btn_text.get_width() // 2
        text_y = btn_rect.centery - btn_text.get_height() // 2
        self.screen.blit(btn_text, (text_x, text_y))

        # Store hit area
        self.hit_areas["continue"] = btn_rect

    def draw_selected_cartellas(self, x, y, width):
        """Draw list of selected cartella numbers"""
        if not self.selected_cartella_numbers:
            return

        # Draw header
        header_font = pygame.font.SysFont("Arial", int(20 * min(self.scale_x, self.scale_y)), bold=True)
        header_text = header_font.render("Selected Cartella Numbers:", True, WHITE)
        self.screen.blit(header_text, (x, y))

        # Draw cartella numbers
        number_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)))
        cartella_text = ", ".join([str(num) for num in self.selected_cartella_numbers])
        cartella_surf = number_font.render(cartella_text, True, LIGHT_GREEN)
        self.screen.blit(cartella_surf, (x, y + header_text.get_height() + int(5 * self.scale_y)))

    def update_button_hover_states(self, mouse_pos):
        """Update hover states for all buttons based on mouse position"""
        for key, area in self.hit_areas.items():
            # Skip if this button doesn't have animation state
            if key not in self.button_states:
                continue

            # Check if mouse is over this button
            hover = area.collidepoint(mouse_pos)
            btn_state = self.button_states[key]

            # Update hover state
            if hover != btn_state["hover"]:
                btn_state["hover"] = hover
                if hover:
                    # Mouse entered - start hover-in animation
                    btn_state["hover_alpha"] = 1
                else:
                    # Mouse left - start hover-out animation
                    btn_state["hover_alpha"] = 255

            # Animate hover alpha with improved smoother transitions
            if hover and btn_state["hover_alpha"] < 255:
                # Faster and smoother fade-in with easing
                progress = btn_state["hover_alpha"] / 255
                ease_factor = 0.15 * (1 - progress)  # Gradually slow down as it approaches 255
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 15 + int(ease_factor * 30))
            elif not hover and btn_state["hover_alpha"] > 0:
                # Smoother fade-out with easing
                progress = btn_state["hover_alpha"] / 255
                ease_factor = 0.15 * progress  # Gradually slow down as it approaches 0
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 8 - int(ease_factor * 15))

            # Handle click animation if active
            if btn_state.get("click", False):
                current_time = pygame.time.get_ticks()
                elapsed = current_time - btn_state.get("click_time", 0)

                # Animation duration (300ms)
                if elapsed > 300:
                    btn_state["click"] = False
                    btn_state["click_scale"] = 0
                else:
                    # Elastic animation effect
                    progress = elapsed / 300
                    if progress < 0.5:
                        # Compress
                        btn_state["click_scale"] = int(15 * (progress * 2))
                    else:
                        # Expand back with slight bounce
                        t = (progress - 0.5) * 2
                        bounce = math.sin(t * math.pi) * 0.2
                        btn_state["click_scale"] = max(0, int(15 * (1 - t + bounce)))

    def create_board_from_number(self, number):
        """Get a bingo board for a specific cartella number from the loaded JSON file"""
        # Convert number to string for JSON dictionary key
        number_key = str(number)

        # Set flag to indicate board has changed (used for animations)
        self.board_just_changed = True
        self.board_change_time = pygame.time.get_ticks()

        # Check if the board exists in the loaded JSON data
        if number_key in self.bingo_boards:
            self.bingo_board = self.bingo_boards[number_key]
        else:
            print(f"Board for cartella {number} not found in loaded data, generating deterministically...")
            # If not found, generate a deterministic board
            import random

            # Use the cartella number as seed for consistent results
            random.seed(number)

            self.bingo_board = []

            # For each column (B, I, N, G, O)
            for col in range(5):
                column_values = []
                # Range for this column: col*15+1 to col*15+15
                min_val = col * 15 + 1
                max_val = min_val + 14

                # Generate 5 unique random numbers for this column
                values = list(range(min_val, max_val + 1))
                random.shuffle(values)
                column_values = values[:5]

                # Set the center square (N column, 3rd row) to 0 (free space)
                if col == 2:
                    column_values[2] = 0

                self.bingo_board.append(column_values)

            # Reset the random seed to avoid affecting other random operations
            random.seed()

        return self.bingo_board

def show_board_selection(screen):
    """
    Show the board selection window

    Args:
        screen: The pygame screen surface

    Returns:
        List of selected cartella numbers
    """
    # Create and run the board selection window
    board_selection = BoardSelectionWindow(screen)
    selected_cartellas = board_selection.run()

    return selected_cartellas