#!/usr/bin/env python3
"""
PyInstaller Build Script for WOW Bingo Game
===========================================

Alternative build script using PyInstaller instead of Nuitka.
This can be used if Nuitka continues to have issues.

Usage:
    python pyinstaller_build.py [--debug] [--clean]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def check_pyinstaller():
    """Check if PyInstaller is available."""
    try:
        result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        log(f"PyInstaller version: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        log("PyInstaller not found. Installing...", "WARNING")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            log("PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            error("Failed to install PyInstaller")

def build_with_pyinstaller(debug=False, clean=False):
    """Build the executable using PyInstaller."""
    log("Building with PyInstaller...")
    
    project_root = Path(__file__).parent
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    # Clean directories if requested
    if clean:
        log("Cleaning build directories...")
        for dir_path in [build_dir, dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                log(f"Cleaned {dir_path}")
    
    # PyInstaller command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--windowed',  # No console window
        '--name=WOWBingoGame',
    ]
    
    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.extend(['--icon', str(icon_path)])
    
    # Add data directories
    data_dirs = ['assets', 'data']
    for dir_name in data_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            cmd.extend(['--add-data', f'{dir_path};{dir_name}'])
            log(f"Including directory: {dir_name}")
    
    # Add payment module if it exists
    payment_dir = project_root / 'payment'
    if payment_dir.exists() and any(payment_dir.glob('*.py')):
        cmd.extend(['--add-data', f'{payment_dir};payment'])
        log("Including payment module")
    
    # Hidden imports for packages that might not be detected
    hidden_imports = [
        'pygame',
        'pyperclip',
        'sqlite3',
        'json',
        'datetime',
        'threading',
        'collections',
        'itertools',
        'functools',
        'hashlib',
        'uuid',
        'pathlib',
        'glob',
        'shutil',
        'tempfile',
        'logging',
        'traceback',
        'ctypes',
        'struct',
        'base64',
        'zlib',
        'gzip',
        're',
        'colorsys'
    ]
    
    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])
    
    # Debug options
    if debug:
        cmd.append('--debug=all')
        cmd.append('--console')  # Show console in debug mode
    
    # Add main script
    cmd.append('main.py')
    
    log(f"PyInstaller command: {' '.join(cmd)}")
    
    # Execute build
    start_time = time.time()
    try:
        log("Starting PyInstaller compilation...")
        result = subprocess.run(cmd, cwd=project_root, check=True)
        build_time = time.time() - start_time
        log(f"Build completed in {build_time:.1f} seconds")
        
        # Find executable
        executable_name = "WOWBingoGame.exe" if os.name == 'nt' else "WOWBingoGame"
        executable_path = dist_dir / executable_name
        
        if executable_path.exists():
            file_size_mb = executable_path.stat().st_size / (1024 * 1024)
            
            log("=" * 60)
            log("PYINSTALLER BUILD COMPLETED SUCCESSFULLY!")
            log("=" * 60)
            log(f"Executable: {executable_path}")
            log(f"File size: {file_size_mb:.1f} MB")
            log("=" * 60)
            
            return True
        else:
            error(f"Executable not found: {executable_path}")
            
    except subprocess.CalledProcessError as e:
        log(f"Build failed with return code {e.returncode}", "ERROR")
        return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="PyInstaller build for WOW Bingo Game")
    parser.add_argument('--debug', action='store_true', help='Build in debug mode')
    parser.add_argument('--clean', action='store_true', help='Clean build directories first')
    
    args = parser.parse_args()
    
    try:
        log("=" * 60)
        log("WOW Bingo Game - PyInstaller Build")
        log("=" * 60)
        log("Using PyInstaller as an alternative to Nuitka")
        log("=" * 60)
        
        # Check prerequisites
        log("Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 7):
            error("Python 3.7 or higher is required!")
        
        log(f"Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        # Check PyInstaller
        check_pyinstaller()
        
        # Check required packages
        required = ['pygame', 'pyperclip']
        for pkg in required:
            try:
                __import__(pkg)
                log(f"Found package: {pkg}")
            except ImportError:
                error(f"Missing package: {pkg}. Install with: pip install {pkg}")
        
        # Check main script
        if not Path('main.py').exists():
            error("main.py not found!")
        
        # Build
        success = build_with_pyinstaller(args.debug, args.clean)
        
        if success:
            log("")
            log("PyInstaller build completed successfully!")
            log("The executable should work without Python installation.")
            log("")
            log("If you encounter issues:")
            log("1. Try running with --debug to see detailed output")
            log("2. Check that all required files are included")
            log("3. Test the executable on a clean system")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        log("Build interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
