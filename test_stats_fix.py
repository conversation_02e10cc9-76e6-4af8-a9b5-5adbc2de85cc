#!/usr/bin/env python3
"""
Test script to verify the stats page fix is working
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stats_fix():
    """Test that the stats page fix is working correctly"""
    
    print("=" * 80)
    print("TESTING STATS PAGE FIX")
    print("=" * 80)
    
    # Test 1: Verify database has correct data
    print("\n1. Verifying database content...")
    try:
        import sqlite3
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f"✅ Database contains {total_count} total records")
        
        cursor.execute('''
        SELECT username, COUNT(*) as count 
        FROM game_history 
        GROUP BY username 
        ORDER BY count DESC
        ''')
        
        username_counts = cursor.fetchall()
        print(f"✅ Record breakdown by username:")
        for username, count in username_counts:
            print(f"   - {username}: {count} records")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    # Test 2: Test stats integration
    print("\n2. Testing stats integration...")
    try:
        from stats_integration import get_game_history
        
        history, total_pages = get_game_history(page=0, page_size=20)
        print(f"✅ Stats integration returns {len(history)} records, {total_pages} pages")
        
        if history:
            print(f"✅ Sample records:")
            for i, record in enumerate(history[:5]):
                username = record.get('username', 'Unknown')
                date_time = record.get('date_time', 'No date')
                prize = record.get('total_prize', 0)
                print(f"   {i+1}. {username} - {date_time} - {prize} ETB")
        
    except Exception as e:
        print(f"❌ Error with stats integration: {e}")
    
    # Test 3: Test that filtering is removed
    print("\n3. Testing filtering behavior...")
    try:
        # Count how many "Game Reset" records should be visible
        from stats_integration import get_game_history
        
        history, _ = get_game_history(page=0, page_size=20)
        
        game_reset_count = sum(1 for record in history if record.get('username') == 'Game Reset')
        cartella_count = sum(1 for record in history if 'Cartella #' in record.get('username', ''))
        no_winner_count = sum(1 for record in history if record.get('username') == 'No Winner')
        
        print(f"✅ Record types in data:")
        print(f"   - Game Reset records: {game_reset_count}")
        print(f"   - Cartella winner records: {cartella_count}")
        print(f"   - No Winner records: {no_winner_count}")
        print(f"   - Total records: {len(history)}")
        
        if game_reset_count > 1:
            print(f"✅ FIXED: Multiple Game Reset records are now available for display")
        else:
            print(f"⚠️  Only {game_reset_count} Game Reset record found")
        
    except Exception as e:
        print(f"❌ Error testing filtering: {e}")
    
    # Test 4: Test summary stats
    print("\n4. Testing summary statistics...")
    try:
        from stats_integration import get_stats_summary
        
        summary = get_stats_summary()
        print(f"✅ Summary stats:")
        print(f"   - Total Earnings: {summary.get('total_earnings', 0)} ETB")
        print(f"   - Daily Earnings: {summary.get('daily_earnings', 0)} ETB")
        print(f"   - Daily Games: {summary.get('daily_games', 0)}")
        print(f"   - Wallet Balance: {summary.get('wallet_balance', 0)} ETB")
        
    except Exception as e:
        print(f"❌ Error with summary stats: {e}")
    
    print(f"\n{'=' * 80}")
    print("STATS PAGE FIX TEST COMPLETE")
    print("The stats page should now display all game records correctly!")
    print("Navigate to the stats page in the game to verify the fix.")
    print(f"{'=' * 80}")

if __name__ == "__main__":
    test_stats_fix()
