"""
External Display Test Script for WOW Games

This script helps test and diagnose external display issues,
particularly with HDMI connections.
"""

# Import SDL fixes before pygame
import sdl_display_fix

import pygame
import sys
import time
from external_display_manager import get_external_display_manager
from hdmi_display_fixer import get_hdmi_display_fixer


def test_display_detection():
    """Test display detection capabilities"""
    print("=" * 60)
    print("EXTERNAL DISPLAY DETECTION TEST")
    print("=" * 60)

    try:
        # Initialize pygame
        pygame.init()

        # Get display manager
        display_manager = get_external_display_manager()

        # Detect displays
        display_info = display_manager.detect_displays()

        print(f"Display Driver: {display_info.get('driver', 'Unknown')}")
        print(f"External Display Detected: {display_info.get('external_detected', False)}")

        primary = display_info.get('primary_display', {})
        print(f"Primary Display: {primary.get('width', 'Unknown')}x{primary.get('height', 'Unknown')}")
        print(f"Color Depth: {primary.get('depth', 'Unknown')} bits")

        available_modes = display_info.get('available_modes', [])
        print(f"Available Display Modes: {len(available_modes)}")

        if available_modes:
            print("Top 10 Available Modes:")
            for i, mode in enumerate(available_modes[:10]):
                print(f"  {i+1}. {mode[0]}x{mode[1]}")

        return True

    except Exception as e:
        print(f"Error during display detection: {e}")
        return False


def test_display_creation():
    """Test display creation with different modes"""
    print("\n" + "=" * 60)
    print("DISPLAY CREATION TEST")
    print("=" * 60)

    try:
        display_manager = get_external_display_manager()

        # Test windowed mode
        print("Testing windowed mode...")
        screen, width, height = display_manager.create_compatible_display(
            preferred_width=1280,
            preferred_height=720,
            fullscreen=False
        )
        print(f"Windowed display created: {width}x{height}")

        # Draw test pattern
        screen.fill((50, 50, 100))  # Dark blue background
        pygame.draw.rect(screen, (255, 255, 255), (50, 50, width-100, height-100), 5)
        pygame.draw.circle(screen, (255, 0, 0), (width//2, height//2), 50)

        # Add text
        font = pygame.font.Font(None, 36)
        text = font.render("External Display Test - Windowed Mode", True, (255, 255, 255))
        text_rect = text.get_rect(center=(width//2, height//2 + 100))
        screen.blit(text, text_rect)

        pygame.display.flip()
        print("Test pattern displayed. Press any key to continue...")

        # Wait for key press
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.KEYDOWN or event.type == pygame.QUIT:
                    waiting = False

        # Test fullscreen mode
        print("Testing fullscreen mode...")
        screen, width, height = display_manager.create_compatible_display(
            fullscreen=True
        )
        print(f"Fullscreen display created: {width}x{height}")

        # Draw test pattern for fullscreen
        screen.fill((100, 50, 50))  # Dark red background
        pygame.draw.rect(screen, (255, 255, 255), (50, 50, width-100, height-100), 5)
        pygame.draw.circle(screen, (0, 255, 0), (width//2, height//2), 100)

        # Add text
        text = font.render("External Display Test - Fullscreen Mode", True, (255, 255, 255))
        text_rect = text.get_rect(center=(width//2, height//2 + 150))
        screen.blit(text, text_rect)

        pygame.display.flip()
        print("Fullscreen test pattern displayed. Press ESC to exit fullscreen...")

        # Wait for ESC key
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        waiting = False
                elif event.type == pygame.QUIT:
                    waiting = False

        # Return to windowed mode
        screen, width, height = display_manager.create_compatible_display(
            preferred_width=1280,
            preferred_height=720,
            fullscreen=False
        )

        return True

    except Exception as e:
        print(f"Error during display creation test: {e}")
        return False


def test_hdmi_recovery():
    """Test HDMI connection issue recovery"""
    print("\n" + "=" * 60)
    print("HDMI RECOVERY TEST")
    print("=" * 60)

    try:
        display_manager = get_external_display_manager()
        hdmi_fixer = get_hdmi_display_fixer()

        print("Testing HDMI connection issue handling...")

        # Test basic HDMI recovery
        recovery_success = display_manager.handle_hdmi_connection_issues()
        print(f"Basic HDMI recovery result: {'Success' if recovery_success else 'Failed'}")

        # Test HDMI stability fixes
        if display_manager.current_display:
            stability_success = hdmi_fixer.apply_hdmi_stability_fixes(display_manager.current_display)
            print(f"HDMI stability fixes result: {'Success' if stability_success else 'Failed'}")

        # Test display change detection
        print("Testing display change detection...")
        change_detected = display_manager.detect_display_change_and_recover()
        print(f"Display change detection result: {'Changes detected' if change_detected else 'No changes'}")

        # Test display mode switching with HDMI fixer
        print("Testing HDMI display mode switching...")
        if display_manager.current_display:
            current_size = display_manager.current_display.get_size()
            test_surface = hdmi_fixer.handle_display_mode_switch(
                display_manager.current_display,
                current_size[0], current_size[1],
                fullscreen=False
            )
            mode_switch_success = test_surface is not None
            print(f"HDMI mode switch result: {'Success' if mode_switch_success else 'Failed'}")

        return True

    except Exception as e:
        print(f"Error during HDMI recovery test: {e}")
        return False


def main():
    """Main test function"""
    print("WOW Games - External Display Test")
    print("This script will test external display functionality")
    print("Make sure your external display (HDMI) is connected before running")
    print("\nPress Enter to start tests...")
    input()

    # Run tests
    tests_passed = 0
    total_tests = 3

    if test_display_detection():
        tests_passed += 1

    if test_display_creation():
        tests_passed += 1

    if test_hdmi_recovery():
        tests_passed += 1

    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("✅ All tests passed! External display support should work correctly.")
    elif tests_passed > 0:
        print("⚠️  Some tests passed. External display may work with limitations.")
    else:
        print("❌ All tests failed. External display support may not work properly.")

    print("\nRecommendations:")
    if tests_passed < total_tests:
        print("- Check HDMI cable connection")
        print("- Verify external display is powered on")
        print("- Try different display resolutions")
        print("- Update graphics drivers")
    else:
        print("- External display support is working correctly")
        print("- You can now run the main game with confidence")

    print("\nPress Enter to exit...")
    input()

    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
