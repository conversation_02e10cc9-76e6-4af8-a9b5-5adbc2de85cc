"""
Stats page integration for the payment system.

This module integrates the payment system with the stats page,
adding a recharge button and credit display.
"""

import pygame
from .recharge_ui import RechargeUI
from . import get_voucher_manager

def integrate_with_stats_page(stats_page):
    """
    Integrate the payment system with the stats page.

    Args:
        stats_page: StatsPage instance

    Returns:
        RechargeUI: The recharge UI component
    """
    print("Starting integration with stats page...")

    # Get the voucher manager
    voucher_manager = get_voucher_manager()
    print(f"Got voucher manager: {voucher_manager}")

    # Create the recharge UI component
    recharge_ui = RechargeUI(
        stats_page.screen,
        voucher_manager,
        stats_page.scale_x,
        stats_page.scale_y
    )
    print("Created recharge UI component")

    # Store original draw method
    original_draw = stats_page.draw
    print(f"Stored original draw method: {original_draw}")

    # Enhanced draw method that adds credit display and recharge button
    def enhanced_draw():
        """Enhanced draw method that adds credit display and recharge button."""
        # Call original draw method
        original_draw()

        # Draw credit display and recharge button
        draw_credit_display(stats_page, voucher_manager)

        # Draw recharge UI if visible
        if hasattr(recharge_ui, 'visible') and recharge_ui.visible:
            recharge_ui.draw()

    # Store original update method
    original_update = stats_page.update

    # Enhanced update method that updates recharge UI
    def enhanced_update():
        """Enhanced update method that updates recharge UI."""
        # Call original update method
        original_update()

        # Update recharge UI
        recharge_ui.update()

    # Store original handle_event method
    original_handle_event = stats_page.handle_event if hasattr(stats_page, 'handle_event') else None

    # Enhanced handle_event method that handles recharge UI events
    def enhanced_handle_event(event):
        """
        Enhanced handle_event method that handles recharge UI events.

        Args:
            event: Pygame event

        Returns:
            bool: True if event was handled, False otherwise
        """
        # PRIORITY 1: Check if recharge UI is visible and handle ALL events for it first
        if recharge_ui.visible:
            # Handle all keyboard events (including TEXTINPUT) when recharge UI is visible
            if event.type in (pygame.KEYDOWN, pygame.TEXTINPUT):
                if recharge_ui.handle_event(event):
                    return True
            # Handle mouse events for recharge UI
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if recharge_ui.handle_event(event):
                    return True

        # PRIORITY 2: Check if recharge button was clicked (only when recharge UI is not visible)
        if not recharge_ui.visible and event.type == pygame.MOUSEBUTTONDOWN:
            if "recharge_button" in stats_page.hit_areas and \
               stats_page.hit_areas["recharge_button"].collidepoint(event.pos):
                print("Recharge button clicked!")
                # Play click sound
                if hasattr(stats_page, 'button_click_sound') and stats_page.button_click_sound:
                    stats_page.button_click_sound.play()

                # Show recharge UI
                recharge_ui.show()
                return True

        # PRIORITY 3: Call original handle_event method only if recharge UI didn't handle the event
        if not recharge_ui.visible and original_handle_event:
            return original_handle_event(event)
        return False

    # Replace methods
    print("Replacing methods...")
    stats_page.draw = enhanced_draw
    stats_page.update = enhanced_update

    # Only replace handle_event if it exists
    if hasattr(stats_page, 'handle_event'):
        stats_page.handle_event = enhanced_handle_event
    else:
        print("Warning: stats_page does not have a handle_event method")

    # Add recharge UI to stats page
    stats_page.recharge_ui = recharge_ui

    print("Integration complete!")
    return recharge_ui

def draw_credit_display(stats_page, voucher_manager):
    """
    Draw credit display and recharge button on the stats page.

    Args:
        stats_page: StatsPage instance
        voucher_manager: VoucherManager instance
    """
    # Get screen dimensions
    screen_width, screen_height = stats_page.screen.get_size()

    # Calculate position for credit display
    credit_x = screen_width - int(200 * stats_page.scale_x)
    credit_y = int(120 * stats_page.scale_y)  # Below header

    # Draw credit display background
    credit_rect = pygame.Rect(
        credit_x,
        credit_y,
        int(180 * stats_page.scale_x),
        int(80 * stats_page.scale_y)
    )

    # Draw with gradient
    draw_gradient_rect(
        stats_page.screen,
        credit_rect,
        (40, 40, 60),  # Dark blue-gray
        (60, 60, 80),  # Lighter blue-gray
        10,  # Border radius
        stats_page.scale_x,
        stats_page.scale_y
    )

    # Draw border
    pygame.draw.rect(
        stats_page.screen,
        (80, 80, 100),  # Light blue-gray
        credit_rect,
        2,  # Border width
        10  # Border radius
    )

    # Draw credit label
    label_font = pygame.font.SysFont(
        "Arial",
        int(16 * min(stats_page.scale_x, stats_page.scale_y)),
        bold=True
    )
    label_text = label_font.render("Credits Available:", True, (200, 200, 200))
    label_rect = label_text.get_rect(
        x=credit_rect.x + int(10 * stats_page.scale_x),
        y=credit_rect.y + int(10 * stats_page.scale_y)
    )
    stats_page.screen.blit(label_text, label_rect)

    # Draw credit amount
    amount_font = pygame.font.SysFont(
        "Arial",
        int(24 * min(stats_page.scale_x, stats_page.scale_y)),
        bold=True
    )
    amount_text = amount_font.render(
        str(voucher_manager.credits),
        True,
        (255, 215, 0)  # Gold
    )
    amount_rect = amount_text.get_rect(
        x=credit_rect.x + int(20 * stats_page.scale_x),
        y=label_rect.bottom + int(5 * stats_page.scale_y)
    )
    stats_page.screen.blit(amount_text, amount_rect)

    # Draw recharge button
    button_rect = pygame.Rect(
        credit_rect.right - int(100 * stats_page.scale_x) - int(10 * stats_page.scale_x),
        amount_rect.y,
        int(100 * stats_page.scale_x),
        int(30 * stats_page.scale_y)
    )

    # Draw button with gradient
    draw_gradient_rect(
        stats_page.screen,
        button_rect,
        (0, 100, 50),  # Dark green
        (0, 150, 80),  # Light green
        5,  # Border radius
        stats_page.scale_x,
        stats_page.scale_y
    )

    # Draw button text
    button_font = pygame.font.SysFont(
        "Arial",
        int(14 * min(stats_page.scale_x, stats_page.scale_y)),
        bold=True
    )
    button_text = button_font.render("Recharge", True, (255, 255, 255))
    button_text_rect = button_text.get_rect(center=button_rect.center)
    stats_page.screen.blit(button_text, button_text_rect)

    # Store button hit area if it doesn't exist or has changed
    if "recharge_button" not in stats_page.hit_areas or stats_page.hit_areas["recharge_button"] != button_rect:
        stats_page.hit_areas["recharge_button"] = button_rect

def draw_gradient_rect(screen, rect, color1, color2, border_radius=0, scale_x=1.0, scale_y=1.0):
    """
    Draw a rectangle with a vertical gradient.

    Args:
        screen: Pygame screen surface
        rect: Rectangle to draw
        color1: Top color
        color2: Bottom color
        border_radius: Border radius
        scale_x: Horizontal scaling factor
        scale_y: Vertical scaling factor
    """
    # Create a surface for the gradient
    surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

    # Draw the gradient
    for y in range(rect.height):
        # Calculate color for this line
        ratio = y / rect.height
        r = color1[0] + (color2[0] - color1[0]) * ratio
        g = color1[1] + (color2[1] - color1[1]) * ratio
        b = color1[2] + (color2[2] - color1[2]) * ratio
        color = (int(r), int(g), int(b))

        # Draw a line with this color
        pygame.draw.line(surface, color, (0, y), (rect.width, y))

    # Create a mask for rounded corners if needed
    if border_radius > 0:
        mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)
        surface.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

    # Blit the gradient surface to the screen
    screen.blit(surface, rect)
