#!/usr/bin/env python3
"""
Test Script for Enhanced Nuitka Build System
============================================

This script tests the enhanced build system to ensure all components
are working correctly before attempting a full build.

Usage:
    python test_enhanced_build.py [--verbose]
"""

import os
import sys
import subprocess
import json
import platform
from pathlib import Path
from typing import Dict, List, Tuple

class BuildSystemTester:
    """Test the enhanced build system components."""
    
    def __init__(self, verbose: bool = False):
        self.project_root = Path(__file__).parent.absolute()
        self.verbose = verbose
        self.test_results = {}
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with level indicator."""
        if level == "DEBUG" and not self.verbose:
            return
        print(f"[{level}] {message}")
        
    def test_python_environment(self) -> bool:
        """Test Python environment and version."""
        self.log("Testing Python environment...")
        
        try:
            # Check Python version
            version = sys.version_info
            if version < (3, 7):
                self.log(f"Python version too old: {version.major}.{version.minor}.{version.micro}", "ERROR")
                return False
            
            self.log(f"Python version: {version.major}.{version.minor}.{version.micro} ✓")
            
            # Check if pip is available
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.log("pip is available ✓")
            else:
                self.log("pip is not available", "ERROR")
                return False
                
            return True
            
        except Exception as e:
            self.log(f"Python environment test failed: {e}", "ERROR")
            return False
            
    def test_required_packages(self) -> bool:
        """Test if required packages are available."""
        self.log("Testing required packages...")
        
        required_packages = [
            'pygame',
            'pyperclip',
            'nuitka'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'nuitka':
                    # Test Nuitka specifically
                    result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        self.log(f"Nuitka is available: {result.stdout.strip()} ✓")
                    else:
                        missing_packages.append(package)
                        self.log(f"Nuitka is not available", "ERROR")
                else:
                    __import__(package)
                    self.log(f"Package {package} is available ✓")
                    
            except ImportError:
                missing_packages.append(package)
                self.log(f"Package {package} is missing", "ERROR")
            except subprocess.CalledProcessError:
                missing_packages.append(package)
                self.log(f"Package {package} is not working correctly", "ERROR")
                
        if missing_packages:
            self.log("Missing packages found. Run: python setup_build_environment.py", "ERROR")
            return False
            
        return True
        
    def test_project_structure(self) -> bool:
        """Test if project structure is correct."""
        self.log("Testing project structure...")
        
        required_files = [
            'main.py',
            'enhanced_nuitka_build.py',
            'setup_build_environment.py'
        ]
        
        required_dirs = [
            'assets',
            'data'
        ]
        
        missing_items = []
        
        # Check required files
        for file_name in required_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self.log(f"Required file found: {file_name} ✓")
            else:
                missing_items.append(file_name)
                self.log(f"Required file missing: {file_name}", "ERROR")
                
        # Check required directories
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                file_count = len(list(dir_path.rglob('*')))
                self.log(f"Required directory found: {dir_name} ({file_count} files) ✓")
            else:
                missing_items.append(dir_name)
                self.log(f"Required directory missing: {dir_name}", "ERROR")
                
        if missing_items:
            self.log("Project structure is incomplete", "ERROR")
            return False
            
        return True
        
    def test_build_scripts(self) -> bool:
        """Test if build scripts are functional."""
        self.log("Testing build scripts...")
        
        # Test enhanced build script
        enhanced_script = self.project_root / "enhanced_nuitka_build.py"
        if enhanced_script.exists():
            try:
                # Test with --verify option
                result = subprocess.run([sys.executable, str(enhanced_script), '--verify'], 
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    self.log("Enhanced build script verification passed ✓")
                else:
                    self.log(f"Enhanced build script verification failed: {result.stderr}", "ERROR")
                    return False
            except subprocess.TimeoutExpired:
                self.log("Enhanced build script verification timed out", "ERROR")
                return False
            except Exception as e:
                self.log(f"Error testing enhanced build script: {e}", "ERROR")
                return False
        else:
            self.log("Enhanced build script not found", "ERROR")
            return False
            
        # Test batch script on Windows
        if platform.system() == "Windows":
            batch_script = self.project_root / "enhanced_build.bat"
            if batch_script.exists():
                self.log("Enhanced build batch script found ✓")
            else:
                self.log("Enhanced build batch script not found", "WARNING")
                
        return True
        
    def test_asset_accessibility(self) -> bool:
        """Test if assets are accessible and properly structured."""
        self.log("Testing asset accessibility...")
        
        assets_dir = self.project_root / "assets"
        if not assets_dir.exists():
            self.log("Assets directory not found", "ERROR")
            return False
            
        # Check for essential asset files
        essential_assets = [
            'assets/app_logo.ico',
            'assets/audio-effects',
            'assets/Audios'
        ]
        
        missing_assets = []
        
        for asset_path in essential_assets:
            full_path = self.project_root / asset_path
            if full_path.exists():
                if full_path.is_file():
                    size = full_path.stat().st_size
                    self.log(f"Essential asset found: {asset_path} ({size} bytes) ✓")
                else:
                    file_count = len(list(full_path.rglob('*')))
                    self.log(f"Essential asset directory found: {asset_path} ({file_count} files) ✓")
            else:
                missing_assets.append(asset_path)
                self.log(f"Essential asset missing: {asset_path}", "WARNING")
                
        # Assets missing is a warning, not an error (game might still work)
        if missing_assets:
            self.log("Some assets are missing - game functionality may be limited", "WARNING")
            
        return True
        
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results."""
        self.log("=" * 60)
        self.log("Enhanced Build System Test Suite")
        self.log("=" * 60)
        
        tests = [
            ("Python Environment", self.test_python_environment),
            ("Required Packages", self.test_required_packages),
            ("Project Structure", self.test_project_structure),
            ("Build Scripts", self.test_build_scripts),
            ("Asset Accessibility", self.test_asset_accessibility)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.log(f"\n--- {test_name} ---")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    self.log(f"{test_name}: PASSED ✓", "INFO")
                else:
                    self.log(f"{test_name}: FAILED ✗", "ERROR")
            except Exception as e:
                results[test_name] = False
                self.log(f"{test_name}: ERROR - {e}", "ERROR")
                
        return results
        
    def generate_test_report(self, results: Dict[str, bool]) -> None:
        """Generate a test report."""
        self.log("\n" + "=" * 60)
        self.log("TEST RESULTS SUMMARY")
        self.log("=" * 60)
        
        passed_tests = sum(1 for result in results.values() if result)
        total_tests = len(results)
        
        for test_name, result in results.items():
            status = "PASS ✓" if result else "FAIL ✗"
            self.log(f"{test_name:<25} {status}")
            
        self.log("-" * 60)
        self.log(f"Tests passed: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            self.log("\n🎉 ALL TESTS PASSED! The build system is ready to use.", "INFO")
            self.log("\nNext steps:")
            self.log("1. Run: enhanced_build.bat (Windows) or python enhanced_nuitka_build.py")
            self.log("2. Select 'Basic Build' for your first compilation")
            self.log("3. Wait for compilation to complete (~5-10 minutes)")
            self.log("4. Test the executable in the 'dist' directory")
        else:
            self.log(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please fix the issues before building.", "ERROR")
            self.log("\nRecommended actions:")
            if not results.get("Required Packages", True):
                self.log("- Run: python setup_build_environment.py")
            if not results.get("Project Structure", True):
                self.log("- Ensure all required files are present")
            if not results.get("Build Scripts", True):
                self.log("- Check that enhanced_nuitka_build.py is working correctly")
                
        # Save test report
        report_file = self.project_root / "test_report.json"
        report_data = {
            "timestamp": __import__('time').strftime("%Y-%m-%d %H:%M:%S"),
            "platform": platform.system(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "test_results": results,
            "tests_passed": passed_tests,
            "total_tests": total_tests,
            "all_passed": passed_tests == total_tests
        }
        
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
            
        self.log(f"\nDetailed test report saved to: {report_file}")

def main():
    """Main entry point for the test script."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Test the enhanced Nuitka build system"
    )
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    try:
        tester = BuildSystemTester(verbose=args.verbose)
        results = tester.run_all_tests()
        tester.generate_test_report(results)
        
        # Exit with appropriate code
        all_passed = all(results.values())
        sys.exit(0 if all_passed else 1)
        
    except KeyboardInterrupt:
        print("\n[WARNING] Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Unexpected error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
