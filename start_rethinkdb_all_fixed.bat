@echo off
echo ========================================================
echo     WOW Games with RethinkDB - Automatic Startup
echo ========================================================
echo.

REM Create required directories
mkdir data\sync_cache 2>nul

REM Install required packages
echo Step 1: Installing required packages...
python -m pip install rethinkdb==2.4.9
if %ERRORLEVEL% NEQ 0 (
    echo Error installing packages. Please try again.
    pause
    exit /b 1
)
echo Packages installed successfully.
echo.

REM Fix any import issues
echo Step 2: Ensuring correct module structure...
echo import sys > check_rethink.py
echo try: >> check_rethink.py
echo     import rethinkdb >> check_rethink.py
echo     print(dir(rethinkdb)) >> check_rethink.py
echo     if not hasattr(rethinkdb, 'connect'): >> check_rethink.py
echo         # Fix r namespace >> check_rethink.py
echo         print("Fixing RethinkDB import") >> check_rethink.py
echo         with open("rethink_db.py", "r") as f: >> check_rethink.py
echo             content = f.read() >> check_rethink.py
echo         content = content.replace("import rethinkdb as r", "import rethinkdb\r\nr = rethinkdb") >> check_rethink.py
echo         with open("rethink_db.py", "w") as f: >> check_rethink.py
echo             f.write(content) >> check_rethink.py
echo except Exception as e: >> check_rethink.py
echo     print(f"Error checking RethinkDB: {e}") >> check_rethink.py

python check_rethink.py
echo Module structure check completed.
echo.

REM Integrate RethinkDB
echo Step 3: Integrating RethinkDB with the application...
python integrate_rethinkdb.py --all
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Error during integration. Continuing anyway...
)
echo Integration completed.
echo.

REM Start game in offline mode (more reliable)
echo Note: Starting the game in offline mode for better reliability.
echo You can switch to online mode later by running:
echo   python rethink_status.py --online
echo.

REM Create a simple offline mode script
echo import sys > start_offline.py
echo try: >> start_offline.py
echo     from hybrid_db_integration import get_hybrid_db_integration >> start_offline.py
echo     db = get_hybrid_db_integration() >> start_offline.py
echo     db.force_offline_mode() >> start_offline.py
echo     print("Forced offline mode") >> start_offline.py
echo except Exception as e: >> start_offline.py
echo     print(f"Error forcing offline mode: {e}") >> start_offline.py
echo import main >> start_offline.py

echo Step 4: Starting the game...
echo.
echo ========================================================
echo     WOW Games is starting with RethinkDB integration
echo     The game will start in offline mode
echo     All data will be stored locally and can be synced later
echo ========================================================
echo.

timeout /t 5

python start_offline.py