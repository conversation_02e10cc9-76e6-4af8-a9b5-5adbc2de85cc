import re
import os
import sys

def fix_prize_pool_function():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Define the correct version of the function
    correct_function = """    def set_prize_pool_manually(self, value):
        \"\"\"Set the prize pool manually and activate override mode\"\"\"
        try:
            value = int(value)
            if value < 0:
                value = 0
            self.prize_pool = value
            self.prize_pool_manual_override = True
            
            # Save UI state if option is enabled
            if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
                try:
                    from player_storage import save_ui_state
                    save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, True)
                    print(f"Updated UI state after setting prize pool manually: {self.prize_pool}")
                except Exception as e:
                    print(f"Error saving UI state after setting prize pool manually: {e}")
            
            self.show_message(f"Prize pool set manually to {value} ETB", "success")
        except ValueError:
            self.show_message("Invalid prize pool value", "error")"""
    
    # Find the beginning and end of the current function
    pattern = r'    def set_prize_pool_manually\(self, value\):.*?def show_board_selection'
    
    # Search for the pattern
    match = re.search(pattern, content, re.DOTALL)
    if match:
        # Get the function text
        old_function = match.group(0)
        # Replace only the function part without "def show_board_selection"
        show_board_def = "def show_board_selection"
        end_of_old_function = old_function.rfind(show_board_def)
        old_function_only = old_function[:end_of_old_function]
        
        # Replace the function with the correct version
        new_content = content.replace(old_function_only, correct_function)
        
        # Write the updated content back to the file
        with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
            file.write(new_content)
        
        print("Function fixed successfully!")
    else:
        print("Function pattern not found.")

def fix_prize_pool_calculation():
    # Read the original file
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Make a backup
    with open('Board_selection_fixed.py.bak', 'w', encoding='utf-8') as backup:
        backup.write(content)
    
    # First ensure we import Player at the top if not already present
    if 'from view_players import Player' not in content:
        # Add the import after other imports
        content = re.sub(
            r'(import .*?\n\n)',
            r'\1from view_players import Player\n\n',
            content,
            count=1,
            flags=re.DOTALL
        )
    
    # Find the section after loading players but before calculating prize pool
    pattern = r'(# Load players\s+self\.players = load_players_from_json\(\)\s+if self\.players is None:\s+self\.players = \[\]\s+)(# Only calculate prize pool)'
    
    # Replacement code that adds logic to create players for remembered cartella numbers
    replacement = r'\1# Ensure players list matches the remembered cartella numbers\n        if self.remember_cartella_checkbox and hasattr(self, "selected_cartella_numbers") and self.selected_cartella_numbers:\n            # Create a set of existing cartella numbers\n            existing_cartellas = {player.cartela_no for player in self.players}\n            \n            # Add players for any remembered cartella numbers not already in the players list\n            for cartella_number in self.selected_cartella_numbers:\n                if cartella_number not in existing_cartellas:\n                    # Create a new player with the remembered bet amount\n                    player = Player(cartela_no=cartella_number, bet_amount=self.bet_amount)\n                    self.players.append(player)\n                    print(f"Created player for remembered cartella {cartella_number}")\n            \n            # Save updated players list\n            save_players_to_json(self.players)\n\n        \2'
    
    # Apply the replacement
    updated_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # Check if we made any changes
    if updated_content == content:
        print("Pattern not found - trying alternate approach")
        
        # Try a more flexible pattern
        pattern2 = r'(# Load players.*?self\.players = \[.*?\].*?)(# Only calculate prize pool)'
        updated_content = re.sub(pattern2, replacement, content, flags=re.DOTALL)
        
        if updated_content == content:
            print("Second pattern also not found. Manual inspection needed.")
            return False
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print("Updated Board_selection_fixed.py to fix prize pool calculation for remembered cartella numbers.")
    return True

if __name__ == "__main__":
    fix_prize_pool_function()
    fix_prize_pool_calculation() 