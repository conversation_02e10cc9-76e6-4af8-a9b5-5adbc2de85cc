{% extends "layout.html" %}
{% block content %}
    <h2>Dashboard</h2>

    <div style="margin-bottom: 20px;">
        <button onclick="location.reload()" style="background-color: #2196F3;">Refresh Data</button>
        {% if summary.data_source %}
            <span style="margin-left: 10px; color: #666;">Data Source: {{ summary.data_source }}</span>
        {% endif %}
    </div>

    <div class="dashboard-summary">
        <div class="summary-card">
            <h3>Total Games</h3>
            <div class="value">{{ summary.total_games }}</div>
        </div>
        <div class="summary-card">
            <h3>Total Earnings</h3>
            <div class="value">{{ summary.total_earnings }} ETB</div>
        </div>
        <div class="summary-card">
            <h3>Today's Games</h3>
            <div class="value">{{ summary.todays_games }}</div>
        </div>
        <div class="summary-card">
            <h3>Today's Earnings</h3>
            <div class="value">{{ summary.todays_earnings }} ETB</div>
        </div>
    </div>

    <h3>Sync Status</h3>
    <table>
        <thead>
            <tr>
                <th>Table</th>
                <th>Last Sync</th>
                <th>Records</th>
                <th>Pending Operations</th>
            </tr>
        </thead>
        <tbody>
            {% for table in tables %}
            <tr>
                <td>{{ table.name }}</td>
                <td>{{ table.last_sync }}</td>
                <td>{{ table.records }}</td>
                <td>{{ table.pending }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}
