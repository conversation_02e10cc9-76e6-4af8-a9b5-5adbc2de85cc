@echo off
:: ================================================================
:: WOW Bingo Game - Install Build Dependencies
:: ================================================================
:: This script installs PyInstaller and all other dependencies
:: required to build the WOW Bingo Game executable.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Dependency Installer%RESET%
echo %CYAN%================================================================%RESET%
echo.
echo %BLUE%This script will install all required dependencies including:%RESET%
echo %BLUE%• PyInstaller (for building executables)%RESET%
echo %BLUE%• Pygame (game engine)%RESET%
echo %BLUE%• All other required packages%RESET%
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found in PATH!%RESET%
    echo.
    echo %YELLOW%Please install Python 3.7+ from:%RESET%
    echo %BLUE%https://www.python.org/downloads/%RESET%
    echo.
    echo %YELLOW%Important: During installation, make sure to:%RESET%
    echo %BLUE%✓ Check "Add Python to PATH"%RESET%
    echo %BLUE%✓ Check "Install pip"%RESET%
    echo.
    pause
    exit /b 1
)

echo %GREEN%✓ Python found:%RESET%
python --version
echo.

:: Check if pip is available
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: pip not found!%RESET%
    echo %YELLOW%Please reinstall Python with pip included.%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ pip found:%RESET%
python -m pip --version
echo.

:: Ask user for confirmation
echo %YELLOW%This will install the following packages:%RESET%
echo %BLUE%• pyinstaller (build tool)%RESET%
echo %BLUE%• pygame (game engine)%RESET%
echo %BLUE%• pyperclip (clipboard support)%RESET%
echo %BLUE%• psutil (system utilities)%RESET%
echo %BLUE%• pillow (image processing)%RESET%
echo %BLUE%• cryptography (security)%RESET%
echo %BLUE%• and other dependencies...%RESET%
echo.
set /p "confirm=Do you want to continue? (y/n): "

if /i not "%confirm%"=="y" (
    echo %YELLOW%Installation cancelled by user.%RESET%
    pause
    exit /b 0
)

echo.
echo %CYAN%Starting dependency installation...%RESET%
echo.

:: Run the Python installer script
python install_build_dependencies.py

:: Check if installation was successful
if %errorlevel% equ 0 (
    echo.
    echo %GREEN%================================================================%RESET%
    echo %GREEN%    DEPENDENCIES INSTALLED SUCCESSFULLY!%RESET%
    echo %GREEN%================================================================%RESET%
    echo.
    echo %CYAN%You can now build your game using any of these methods:%RESET%
    echo.
    echo %BLUE%Method 1 - Easy Build:%RESET%
    echo %GREEN%  Double-click: build_game.bat%RESET%
    echo.
    echo %BLUE%Method 2 - Complete Workflow:%RESET%
    echo %GREEN%  Double-click: build_and_package.bat%RESET%
    echo.
    echo %BLUE%Method 3 - Command Line:%RESET%
    echo %GREEN%  python build_executable.py%RESET%
    echo.
    echo %YELLOW%Next step: Choose one of the build methods above!%RESET%
) else (
    echo.
    echo %RED%================================================================%RESET%
    echo %RED%    INSTALLATION HAD SOME ISSUES%RESET%
    echo %RED%================================================================%RESET%
    echo.
    echo %YELLOW%Some packages may have failed to install.%RESET%
    echo %YELLOW%You can try the following:%RESET%
    echo.
    echo %BLUE%1. Run this script again%RESET%
    echo %BLUE%2. Try manual installation:%RESET%
    echo %GREEN%   pip install pyinstaller pygame pyperclip psutil pillow%RESET%
    echo %BLUE%3. Check the error messages above%RESET%
    echo.
)

echo.
echo %BLUE%Press any key to exit...%RESET%
pause >nul
exit /b %errorlevel%
