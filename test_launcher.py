"""
Simple test launcher to verify the automated startup concept.
"""

import os
import sys
import subprocess
import time

def test_command(command, description):
    """Test running a command."""
    print(f"Testing: {description}")
    print(f"Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            if result.stdout:
                print(f"Output: {result.stdout[:200]}...")
        else:
            print(f"❌ {description} - FAILED")
            if result.stderr:
                print(f"Error: {result.stderr[:200]}...")
        
        print("-" * 50)
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        print("-" * 50)
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        print("-" * 50)
        return False

def main():
    """Test the launcher components."""
    print("🧪 Testing WOW Games Launcher Components")
    print("=" * 60)
    
    # Test individual commands
    tests = [
        ([sys.executable, "setup_rethinkdb.py", "--check"], "RethinkDB Status Check"),
        ([sys.executable, "-c", "import rethink_db; print('RethinkDB module OK')"], "RethinkDB Module Import"),
        ([sys.executable, "-c", "import rethink_dashboard_fixed; print('Dashboard module OK')"], "Dashboard Module Import"),
        ([sys.executable, "-c", "import main; print('Main module OK')"], "Main Module Import"),
        ([sys.executable, "comprehensive_rethinkdb_test.py"], "Quick Validation (with input)"),
    ]
    
    results = []
    for command, description in tests:
        if "comprehensive_rethinkdb_test.py" in command:
            # Special handling for the test script that needs input
            try:
                process = subprocess.Popen(
                    command,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                stdout, stderr = process.communicate(input="2\n", timeout=15)
                
                success = "🎉 All components are available and ready!" in stdout
                print(f"Testing: {description}")
                print(f"✅ {description} - {'SUCCESS' if success else 'PARTIAL'}")
                if success:
                    print("All components validated!")
                results.append(success)
                
            except Exception as e:
                print(f"❌ {description} - ERROR: {e}")
                results.append(False)
        else:
            results.append(test_command(command, description))
        
        print()
    
    # Summary
    print("=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! The launcher should work correctly.")
    elif passed >= total * 0.8:
        print("\n✅ Most tests passed! The launcher should work with minor issues.")
    else:
        print("\n⚠️ Some tests failed. Check the issues above.")
    
    print("\n🚀 You can now use the automated launchers:")
    print("   • Windows: START_WOW_GAMES.bat")
    print("   • Linux/Mac: ./start_wow_games.sh")
    print("   • Python: python start_wow_games_complete.py")

if __name__ == "__main__":
    main()
