"""
UI Theme Module for the WOW Games application.

This module provides theming capabilities for UI elements, supporting
different color schemes and style configurations.
"""

import pygame
import os
import json
from functools import lru_cache

# Default theme colors
DEFAULT_THEME = {
    # Base colors
    'background': (18, 25, 38),  # Dark blue background
    'card_bg': (30, 37, 50),     # Slightly lighter card background
    'card_header_bg': (40, 47, 60),  # Card header background
    
    # Text colors
    'text_primary': (255, 255, 255),  # White text
    'text_secondary': (200, 210, 220),  # Light gray text
    'text_muted': (150, 160, 170),  # Muted text
    
    # Accent colors
    'accent_primary': (86, 143, 255),  # Blue accent
    'accent_secondary': (255, 184, 76),  # Orange accent
    'accent_success': (76, 217, 100),  # Green accent
    'accent_danger': (255, 85, 85),  # Red accent
    'accent_warning': (255, 184, 76),  # Yellow accent
    
    # Table colors
    'table_header_bg': (40, 50, 75),  # Table header background
    'table_row_bg_1': (35, 45, 65),  # Table row background (odd)
    'table_row_bg_2': (45, 55, 75),  # Table row background (even)
    
    # Day colors for weekly stats
    'sunday_color': (255, 100, 100),  # Red
    'monday_color': (100, 180, 255),  # Blue
    'tuesday_color': (180, 120, 255),  # Purple
    'wednesday_color': (100, 220, 150),  # Green
    'thursday_color': (255, 180, 100),  # Orange
    'friday_color': (100, 150, 255),  # Light blue
    'saturday_color': (255, 120, 150),  # Pink
    
    # Navigation colors
    'nav_bar_bg': (25, 35, 55),  # Navigation bar background
    'nav_item_active': (86, 143, 255),  # Active navigation item
    'nav_item_hover': (60, 80, 120),  # Hover navigation item
    
    # Button colors
    'button_primary': (86, 143, 255),  # Primary button
    'button_secondary': (60, 70, 90),  # Secondary button
    'button_hover': (106, 163, 255),  # Button hover state
    'button_active': (66, 123, 235),  # Button active state
    'button_disabled': (80, 90, 110),  # Disabled button
    
    # Border colors
    'border_light': (70, 80, 100),  # Light border
    'border_dark': (40, 50, 70),  # Dark border
    
    # Shadow colors
    'shadow': (10, 15, 25, 128),  # Shadow with alpha
    
    # Chart colors
    'chart_grid': (60, 70, 90),  # Chart grid lines
    'chart_line': (86, 143, 255),  # Chart line
    'chart_fill': (86, 143, 255, 40),  # Chart fill with alpha
    
    # Gradient stops
    'gradient_top': (25, 35, 55),  # Gradient top color
    'gradient_bottom': (18, 25, 38),  # Gradient bottom color
}

class UITheme:
    """
    Manages UI themes and provides access to colors and styles.
    """
    
    def __init__(self):
        self.current_theme = DEFAULT_THEME.copy()
        self.load_theme()
    
    def load_theme(self, theme_name="default"):
        """
        Load a theme from file or use default if not found.
        
        Args:
            theme_name: Name of the theme to load
        """
        theme_path = os.path.join('data', 'themes', f"{theme_name}.json")
        
        try:
            if os.path.exists(theme_path):
                with open(theme_path, 'r') as f:
                    loaded_theme = json.load(f)
                    # Update current theme with loaded values
                    for key, value in loaded_theme.items():
                        self.current_theme[key] = tuple(value)
                    print(f"Loaded theme: {theme_name}")
            else:
                print(f"Theme file not found: {theme_path}, using default theme")
        except Exception as e:
            print(f"Error loading theme: {e}, using default theme")
    
    def get_color(self, color_name):
        """
        Get a color by name.
        
        Args:
            color_name: Name of the color to get
            
        Returns:
            tuple: RGB or RGBA color tuple
        """
        return self.current_theme.get(color_name, (255, 255, 255))
    
    def get_colors(self):
        """
        Get all colors as a dictionary.
        
        Returns:
            dict: Dictionary of all colors
        """
        return self.current_theme
    
    @lru_cache(maxsize=32)
    def get_gradient_surface(self, width, height, top_color=None, bottom_color=None, horizontal=False):
        """
        Create a gradient surface.
        
        Args:
            width: Surface width
            height: Surface height
            top_color: Top color (or left if horizontal)
            bottom_color: Bottom color (or right if horizontal)
            horizontal: Whether the gradient is horizontal
            
        Returns:
            pygame.Surface: Gradient surface
        """
        if top_color is None:
            top_color = self.get_color('gradient_top')
        if bottom_color is None:
            bottom_color = self.get_color('gradient_bottom')
        
        surface = pygame.Surface((width, height), pygame.SRCALPHA)
        
        if horizontal:
            for x in range(width):
                # Calculate gradient color
                ratio = x / width
                r = int(top_color[0] * (1 - ratio) + bottom_color[0] * ratio)
                g = int(top_color[1] * (1 - ratio) + bottom_color[1] * ratio)
                b = int(top_color[2] * (1 - ratio) + bottom_color[2] * ratio)
                a = 255
                if len(top_color) > 3 and len(bottom_color) > 3:
                    a = int(top_color[3] * (1 - ratio) + bottom_color[3] * ratio)
                
                pygame.draw.line(surface, (r, g, b, a), (x, 0), (x, height))
        else:
            for y in range(height):
                # Calculate gradient color
                ratio = y / height
                r = int(top_color[0] * (1 - ratio) + bottom_color[0] * ratio)
                g = int(top_color[1] * (1 - ratio) + bottom_color[1] * ratio)
                b = int(top_color[2] * (1 - ratio) + bottom_color[2] * ratio)
                a = 255
                if len(top_color) > 3 and len(bottom_color) > 3:
                    a = int(top_color[3] * (1 - ratio) + bottom_color[3] * ratio)
                
                pygame.draw.line(surface, (r, g, b, a), (0, y), (width, y))
        
        return surface
    
    @lru_cache(maxsize=16)
    def get_rounded_rect_surface(self, width, height, color, radius=10, alpha=255):
        """
        Create a surface with a rounded rectangle.
        
        Args:
            width: Surface width
            height: Surface height
            color: Rectangle color
            radius: Corner radius
            alpha: Alpha value (0-255)
            
        Returns:
            pygame.Surface: Surface with rounded rectangle
        """
        surface = pygame.Surface((width, height), pygame.SRCALPHA)
        rect = pygame.Rect(0, 0, width, height)
        
        # Convert color to RGBA if needed
        if len(color) == 3:
            color = (color[0], color[1], color[2], alpha)
        
        # Draw rounded rectangle
        pygame.draw.rect(surface, color, rect, border_radius=radius)
        
        return surface
    
    @lru_cache(maxsize=16)
    def get_card_surface(self, width, height, with_header=False, header_height=40):
        """
        Create a card surface with optional header.
        
        Args:
            width: Card width
            height: Card height
            with_header: Whether to include a header
            header_height: Height of the header
            
        Returns:
            pygame.Surface: Card surface
        """
        surface = pygame.Surface((width, height), pygame.SRCALPHA)
        
        # Draw card background
        card_bg = self.get_color('card_bg')
        pygame.draw.rect(surface, card_bg, pygame.Rect(0, 0, width, height), border_radius=10)
        
        # Draw header if requested
        if with_header:
            header_bg = self.get_color('card_header_bg')
            header_rect = pygame.Rect(0, 0, width, header_height)
            pygame.draw.rect(surface, header_bg, header_rect, border_radius=10)
            # Fix the bottom corners of the header
            pygame.draw.rect(surface, header_bg, pygame.Rect(0, header_height-10, width, 10))
        
        return surface
    
    def draw_text(self, surface, text, font, color, position, align="left", shadow=False):
        """
        Draw text with optional shadow.
        
        Args:
            surface: Surface to draw on
            text: Text to draw
            font: Font to use
            color: Text color
            position: Position tuple (x, y)
            align: Text alignment ("left", "center", "right")
            shadow: Whether to draw a shadow
        """
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect()
        
        # Set position based on alignment
        if align == "left":
            text_rect.topleft = position
        elif align == "center":
            text_rect.midtop = position
        elif align == "right":
            text_rect.topright = position
        
        # Draw shadow if requested
        if shadow:
            shadow_surface = font.render(text, True, self.get_color('shadow'))
            shadow_rect = shadow_surface.get_rect()
            shadow_rect.x = text_rect.x + 2
            shadow_rect.y = text_rect.y + 2
            surface.blit(shadow_surface, shadow_rect)
        
        # Draw text
        surface.blit(text_surface, text_rect)
        
        return text_rect


# Create a singleton instance
_ui_theme = None

def get_ui_theme():
    """Get the singleton UI theme instance"""
    global _ui_theme
    if _ui_theme is None:
        _ui_theme = UITheme()
    return _ui_theme