#!/usr/bin/env python3
"""
Enhanced Nuitka Build Script for WOW Bingo Game
===============================================

This script provides a comprehensive, error-free build system for the WOW Bingo Game
using Nuitka with automatic dependency detection, asset bundling, and robust error handling.

Features:
- Automatic dependency detection and validation
- Comprehensive asset bundling with verification
- Error-free build configuration that avoids known Nuitka issues
- Non-portable standalone executable generation
- Windows-specific optimizations and metadata
- Automatic cleanup and verification
- Progress monitoring and detailed logging

Usage:
    python enhanced_nuitka_build.py [options]

Options:
    --clean         Clean build directories before building
    --debug         Enable debug mode with verbose output
    --test          Test the executable after building
    --optimize      Enable maximum optimizations (slower build)
    --verify        Verify all dependencies before building
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
import json
import platform
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import importlib.util

class EnhancedNuitkaBuild:
    """Enhanced Nuitka build system with comprehensive error handling and optimization."""

    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build_enhanced"
        self.dist_dir = self.project_root / "dist"
        self.executable_name = "WOWBingoGame.exe"
        self.main_script = "main.py"
        self.icon_path = self.project_root / "assets" / "app_logo.ico"

        # Build configuration
        self.project_name = "WOW Bingo Game"
        self.project_version = "1.0.0"
        self.company_name = "WOW Games"
        self.description = "WOW Bingo Game - Professional Bingo Gaming Application"

        # Logging
        self.verbose = False
        self.start_time = time.time()

        # Dependencies tracking
        self.required_packages = []
        self.optional_packages = []
        self.missing_packages = []
        self.verified_assets = []

    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with timestamp and level."""
        timestamp = time.strftime("%H:%M:%S")
        elapsed = time.time() - self.start_time
        print(f"[{timestamp}] [{elapsed:6.1f}s] {level}: {message}")

    def error(self, message: str) -> None:
        """Log an error and exit."""
        self.log(message, "ERROR")
        sys.exit(1)

    def warning(self, message: str) -> None:
        """Log a warning."""
        self.log(message, "WARN")

    def debug(self, message: str) -> None:
        """Log a debug message if verbose mode is enabled."""
        if self.verbose:
            self.log(message, "DEBUG")

    def check_prerequisites(self) -> None:
        """Check all prerequisites for building."""
        self.log("Checking prerequisites...")

        # Check Python version
        if sys.version_info < (3, 7):
            self.error("Python 3.7 or higher is required")

        # Check if we're on Windows (for Windows-specific features)
        if platform.system() != "Windows":
            self.warning("This build script is optimized for Windows. Some features may not work on other platforms.")

        # Check main script exists
        if not (self.project_root / self.main_script).exists():
            self.error(f"Main script '{self.main_script}' not found")

        # Check Nuitka
        try:
            result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                                  capture_output=True, text=True, check=True)
            nuitka_version = result.stdout.strip()
            self.log(f"Nuitka version: {nuitka_version}")

            # Check if Nuitka version is recent enough
            if "2.7" not in nuitka_version and "2.8" not in nuitka_version and "2.9" not in nuitka_version:
                self.warning(f"Nuitka version {nuitka_version} may have compatibility issues. Recommended: 2.7+")

        except subprocess.CalledProcessError:
            self.error("Nuitka not found! Install with: pip install nuitka")
        except FileNotFoundError:
            self.error("Python not found in PATH")

        self.log("Prerequisites check completed successfully")

    def detect_dependencies(self) -> None:
        """Detect and categorize all project dependencies."""
        self.log("Detecting project dependencies...")

        # Core required packages that must be present
        core_packages = [
            'pygame',
            'pyperclip',
            'json',
            'datetime',
            'sqlite3',
            'os',
            'sys',
            'time',
            'math',
            'random',
            'colorsys'
        ]

        # Optional packages that enhance functionality
        optional_packages = [
            'psutil',
            'rethinkdb',
            'kivy',
            'PIL',
            'numpy',
            'tkinter'
        ]

        # Check core packages
        for package in core_packages:
            if self._check_package_availability(package):
                self.required_packages.append(package)
                self.debug(f"Core package available: {package}")
            else:
                if package not in ['json', 'datetime', 'sqlite3', 'os', 'sys', 'time', 'math', 'random', 'colorsys']:
                    self.missing_packages.append(package)
                    self.error(f"Required package missing: {package}")

        # Check optional packages
        for package in optional_packages:
            if self._check_package_availability(package):
                self.optional_packages.append(package)
                self.debug(f"Optional package available: {package}")
            else:
                self.debug(f"Optional package not available: {package}")

        self.log(f"Found {len(self.required_packages)} required packages and {len(self.optional_packages)} optional packages")

    def _check_package_availability(self, package_name: str) -> bool:
        """Check if a package is available for import."""
        try:
            if package_name in ['json', 'datetime', 'sqlite3', 'os', 'sys', 'time', 'math', 'random', 'colorsys']:
                # These are built-in modules
                return True
            spec = importlib.util.find_spec(package_name)
            return spec is not None
        except (ImportError, ValueError, ModuleNotFoundError):
            return False

    def verify_assets(self) -> None:
        """Verify and catalog all required assets."""
        self.log("Verifying project assets...")

        # Essential asset directories
        essential_dirs = [
            'assets',
            'data'
        ]

        # Essential files
        essential_files = [
            'assets/app_logo.ico',
            'data/settings.json'
        ]

        # Check essential directories
        for dir_name in essential_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                file_count = len(list(dir_path.rglob('*')))
                self.verified_assets.append(dir_name)
                self.debug(f"Asset directory verified: {dir_name} ({file_count} files)")
            else:
                self.warning(f"Asset directory missing: {dir_name}")

        # Check essential files
        for file_path in essential_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.debug(f"Essential file verified: {file_path}")
            else:
                self.warning(f"Essential file missing: {file_path}")

        # Create missing data directory and default settings if needed
        data_dir = self.project_root / "data"
        if not data_dir.exists():
            self.log("Creating missing data directory...")
            data_dir.mkdir(exist_ok=True)

        settings_file = data_dir / "settings.json"
        if not settings_file.exists():
            self.log("Creating default settings.json...")
            default_settings = {
                "game_settings": {
                    "volume": 0.7,
                    "fullscreen": False,
                    "language": "english"
                }
            }
            with open(settings_file, 'w') as f:
                json.dump(default_settings, f, indent=2)

        self.log(f"Asset verification completed. {len(self.verified_assets)} directories verified")

    def prepare_build_environment(self, clean: bool = False) -> None:
        """Prepare the build environment."""
        self.log("Preparing build environment...")

        # Clean build directories if requested
        if clean:
            self.log("Cleaning build directories...")
            for dir_path in [self.build_dir, self.dist_dir]:
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    self.debug(f"Removed directory: {dir_path}")

        # Create build directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

        # Remove any existing crash reports
        crash_report = self.project_root / "nuitka-crash-report.xml"
        if crash_report.exists():
            crash_report.unlink()
            self.debug("Removed previous crash report")

        self.log("Build environment prepared successfully")

    def build_executable(self, optimize: bool = False) -> bool:
        """Build the executable using Nuitka."""
        self.log("Starting Nuitka compilation...")

        # Prepare Nuitka command
        cmd = self._prepare_nuitka_command(optimize)

        # Log the command for debugging
        self.debug(f"Nuitka command: {' '.join(cmd)}")

        # Execute Nuitka compilation
        try:
            self.log("Executing Nuitka compilation (this may take several minutes)...")

            # Use subprocess with real-time output
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor output in real-time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output and self.verbose:
                    print(output.strip())

            # Wait for completion
            return_code = process.poll()

            if return_code == 0:
                self.log("Nuitka compilation completed successfully")
                return True
            else:
                self.error(f"Nuitka compilation failed with return code {return_code}")
                return False

        except Exception as e:
            self.error(f"Error during Nuitka compilation: {e}")
            return False

    def _prepare_nuitka_command(self, optimize: bool = False) -> List[str]:
        """Prepare the Nuitka command with all necessary arguments."""
        cmd = [sys.executable, '-m', 'nuitka']

        # Basic compilation options
        cmd.extend([
            '--standalone',
            '--onefile',
            f'--output-filename={self.executable_name}',
            f'--output-dir={self.build_dir}',
            '--assume-yes-for-downloads',
            '--windows-console-mode=disable',
            '--show-progress',
        ])

        # Windows-specific metadata
        if platform.system() == "Windows":
            if self.icon_path.exists():
                cmd.append(f'--windows-icon-from-ico={self.icon_path}')
            cmd.extend([
                f'--windows-company-name={self.company_name}',
                f'--windows-product-name={self.project_name}',
                f'--windows-file-version={self.project_version}',
                f'--windows-product-version={self.project_version}',
                f'--windows-file-description={self.description}',
            ])

        # Asset inclusion
        for asset_dir in self.verified_assets:
            asset_path = self.project_root / asset_dir
            if asset_path.exists():
                cmd.append(f'--include-data-dir={asset_path}={asset_dir}')

        # Package inclusion (only essential packages to avoid Nuitka bugs)
        essential_packages = ['pygame', 'pyperclip']  # Only include packages we absolutely need

        for package in essential_packages:
            if package in self.required_packages:
                cmd.append(f'--include-package={package}')
                self.debug(f"Including essential package: {package}")

        # Skip optional packages that can cause Nuitka crashes
        # Built-in modules (json, datetime, sqlite3, etc.) are automatically included

        # Optimization options (conservative to avoid Nuitka bugs)
        if optimize:
            cmd.extend([
                '--lto=yes',
                f'--jobs={min(os.cpu_count() or 4, 4)}',  # Limit cores to avoid issues
            ])
        else:
            # Use minimal options for maximum compatibility
            cmd.extend([
                f'--jobs={min(os.cpu_count() or 4, 2)}',  # Even more conservative for basic builds
            ])

        # Skip problematic plugins that can cause crashes
        # multiprocessing plugin is enabled by default in newer Nuitka versions

        # Add main script
        cmd.append(str(self.project_root / self.main_script))

        return cmd

    def verify_executable(self) -> Optional[Path]:
        """Verify the built executable exists and get its path."""
        self.log("Verifying built executable...")

        # Look for the executable in the build directory
        executable_path = self.build_dir / self.executable_name

        if executable_path.exists():
            file_size = executable_path.stat().st_size
            size_mb = file_size / (1024 * 1024)
            self.log(f"Executable found: {executable_path}")
            self.log(f"File size: {size_mb:.1f} MB")
            return executable_path
        else:
            self.error(f"Executable not found at expected location: {executable_path}")
            return None

    def copy_to_dist(self, executable_path: Path) -> Path:
        """Copy the executable to the distribution directory."""
        self.log("Copying executable to distribution directory...")

        dist_executable = self.dist_dir / self.executable_name
        shutil.copy2(executable_path, dist_executable)

        self.log(f"Executable copied to: {dist_executable}")
        return dist_executable

    def test_executable(self, executable_path: Path) -> bool:
        """Test the executable to ensure it runs without errors."""
        self.log("Testing executable...")

        try:
            # Try to run the executable with a timeout
            result = subprocess.run(
                [str(executable_path), '--help'],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                self.log("Executable test passed")
                return True
            else:
                self.warning(f"Executable test failed with return code {result.returncode}")
                if result.stderr:
                    self.warning(f"Error output: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.warning("Executable test timed out (this may be normal for GUI applications)")
            return True  # GUI apps may not respond to --help
        except Exception as e:
            self.warning(f"Error testing executable: {e}")
            return False

    def generate_build_report(self, executable_path: Path) -> None:
        """Generate a comprehensive build report."""
        self.log("Generating build report...")

        build_time = time.time() - self.start_time

        report = {
            "build_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "build_time_seconds": round(build_time, 2),
                "project_name": self.project_name,
                "project_version": self.project_version,
                "executable_path": str(executable_path),
                "executable_size_mb": round(executable_path.stat().st_size / (1024 * 1024), 2)
            },
            "dependencies": {
                "required_packages": self.required_packages,
                "optional_packages": self.optional_packages,
                "missing_packages": self.missing_packages
            },
            "assets": {
                "verified_directories": self.verified_assets
            },
            "system_info": {
                "platform": platform.system(),
                "python_version": sys.version,
                "cpu_count": os.cpu_count()
            }
        }

        report_file = self.dist_dir / "build_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.log(f"Build report saved to: {report_file}")

def main():
    """Main entry point for the enhanced build script."""
    parser = argparse.ArgumentParser(
        description="Enhanced Nuitka build script for WOW Bingo Game",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_nuitka_build.py                    # Basic build
  python enhanced_nuitka_build.py --clean --debug   # Clean build with debug output
  python enhanced_nuitka_build.py --optimize --test # Optimized build with testing
  python enhanced_nuitka_build.py --verify          # Verify dependencies only
        """
    )

    parser.add_argument('--clean', action='store_true', help='Clean build directories before building')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode with verbose output')
    parser.add_argument('--test', action='store_true', help='Test the executable after building')
    parser.add_argument('--optimize', action='store_true', help='Enable maximum optimizations (slower build)')
    parser.add_argument('--verify', action='store_true', help='Verify all dependencies before building')

    args = parser.parse_args()

    # Create build instance
    builder = EnhancedNuitkaBuild()
    builder.verbose = args.debug

    try:
        builder.log("=" * 80)
        builder.log("WOW Bingo Game - Enhanced Nuitka Build System")
        builder.log("=" * 80)

        # Check prerequisites
        builder.check_prerequisites()

        # Detect dependencies
        builder.detect_dependencies()

        # Verify assets
        builder.verify_assets()

        # If only verifying, stop here
        if args.verify:
            builder.log("Verification completed successfully")
            return

        # Prepare build environment
        builder.prepare_build_environment(clean=args.clean)

        # Build executable
        if builder.build_executable(optimize=args.optimize):
            # Verify executable
            executable_path = builder.verify_executable()
            if executable_path:
                # Copy to distribution directory
                dist_executable = builder.copy_to_dist(executable_path)

                # Test executable if requested
                if args.test:
                    builder.test_executable(dist_executable)

                # Generate build report
                builder.generate_build_report(dist_executable)

                # Success message
                builder.log("=" * 80)
                builder.log("BUILD COMPLETED SUCCESSFULLY!")
                builder.log("=" * 80)
                builder.log(f"Executable: {dist_executable}")
                builder.log(f"Build time: {time.time() - builder.start_time:.1f} seconds")
                builder.log("=" * 80)

                # Ask if user wants to run the executable
                if not args.test:
                    try:
                        response = input("\nDo you want to run the executable now? (y/n): ")
                        if response.lower() in ['y', 'yes']:
                            builder.log("Starting executable...")
                            subprocess.Popen([str(dist_executable)], shell=True)
                    except KeyboardInterrupt:
                        pass
            else:
                builder.error("Build verification failed")
        else:
            builder.error("Build failed")

    except KeyboardInterrupt:
        builder.log("Build interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        builder.error(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
