#!/usr/bin/env python3
"""
WOW Bingo Game - Hardware Acceleration Test
===========================================

Quick test script to verify hardware acceleration detection and optimization.
Run this to see what hardware acceleration is available on your system.
"""

import asyncio
import sys
from pathlib import Path

# Add the modern application path
modern_app_path = Path(__file__).parent / "src"
sys.path.insert(0, str(modern_app_path))

try:
    from wow_bingo_game.utils.hardware_acceleration import HardwareAccelerationManager
    from wow_bingo_game.utils.performance import PerformanceMonitor
    from wow_bingo_game.utils.logger import setup_logging, get_logger

    # Setup logging
    setup_logging(level="INFO", console_output=True)
    logger = get_logger(__name__)

    MODERN_APP_AVAILABLE = True
except ImportError as e:
    print(f"Modern app not available: {e}")
    print("Please run 'python quick_start_modern.py --install-deps' first")
    MODERN_APP_AVAILABLE = False


async def test_hardware_acceleration():
    """Test hardware acceleration detection."""
    if not MODERN_APP_AVAILABLE:
        return False

    print("🔍 Testing Hardware Acceleration Detection...")
    print("=" * 60)

    try:
        # Initialize hardware acceleration manager
        hw_manager = HardwareAccelerationManager()
        success = await hw_manager.initialize()

        if success:
            print("✅ Hardware acceleration detection successful!")

            # Get hardware info
            hw_info = hw_manager.get_hardware_info()
            print(f"\n🖥️  Hardware Information:")
            print(f"   CPU: {hw_info['cpu_brand']}")
            print(f"   Cores/Threads: {hw_info['cpu_cores']}/{hw_info['cpu_threads']}")
            print(f"   Memory: {hw_info['memory_total_gb']:.1f}GB")
            print(f"   GPU Devices: {len(hw_info['gpu_devices'])}")

            for i, gpu in enumerate(hw_info['gpu_devices']):
                print(f"     {i+1}. {gpu['name']} ({gpu['vendor']})")

            # Show acceleration support
            accel_support = hw_info['acceleration_support']
            print(f"\n⚡ Acceleration Support:")
            print(f"   Basic GPU: {'✅ Available' if accel_support['basic_gpu'] else '❌ Not Available'}")
            if accel_support['basic_gpu']:
                print(f"     GPU Memory: {hw_info.get('gpu_memory_mb', 0)}MB")
                print(f"     Driver Version: {hw_info.get('gpu_driver_version', 'Unknown')}")
            print(f"   Intel QSV: {'✅ Available' if accel_support['qsv'] else '❌ Not Available'}")
            print(f"   NVIDIA NVENC: {'✅ Available' if accel_support['nvenc'] else '❌ Not Available'}")
            print(f"   AMD VCE: {'✅ Available' if accel_support['vce'] else '❌ Not Available'}")

            # Get optimization profile
            profile = hw_manager.get_optimization_profile()
            print(f"\n🎯 Optimization Profile:")
            print(f"   Profile Name: {profile['profile_name']}")
            print(f"   Acceleration Type: {profile['acceleration_type']}")
            print(f"   Render Quality: {profile['render_quality']}")
            print(f"   Animation Quality: {profile['animation_quality']}")
            print(f"   Max FPS: {profile['max_fps']}")
            print(f"   GPU Acceleration: {'✅ Enabled' if profile['gpu_acceleration'] else '❌ Disabled'}")
            print(f"   Hardware Decoding: {'✅ Enabled' if profile['hardware_decoding'] else '❌ Disabled'}")
            print(f"   Cache Size: {profile['cache_size_mb']}MB")

            # Test performance monitoring
            print(f"\n📊 Performance Monitoring Test:")
            perf_monitor = PerformanceMonitor(
                target_fps=profile['max_fps'],
                memory_limit_mb=profile['cache_size_mb']
            )
            perf_monitor.apply_hardware_profile(profile)
            perf_monitor.start()

            # Simulate some frames
            print("   Simulating performance monitoring...")
            for i in range(10):
                perf_monitor.record_frame()
                await asyncio.sleep(0.016)  # ~60 FPS

            # Get performance settings
            perf_settings = perf_monitor.get_hardware_optimized_settings()
            print(f"   Current FPS: {perf_settings['current_fps']:.1f}")
            print(f"   Target FPS: {perf_settings['target_fps']}")
            print(f"   Memory Usage: {perf_settings['memory_usage_mb']:.1f}MB")
            print(f"   Optimization Level: {perf_settings['optimization_level']}")

            perf_monitor.stop()

            # Show recommendations
            print(f"\n💡 Recommendations:")
            if profile['gpu_acceleration']:
                if accel_support['basic_gpu']:
                    print("   ✅ Your system supports basic GPU acceleration!")
                    if accel_support['qsv'] or accel_support['nvenc'] or accel_support['vce']:
                        print("   🚀 Advanced GPU acceleration also available!")
                    print("   ✅ GPU-optimized settings enabled")
                    print("   ✅ Better performance than CPU-only mode")
                else:
                    print("   ✅ Advanced GPU acceleration available!")
                print("   ✅ High quality settings recommended")
                print("   ✅ Smooth animations and effects enabled")
            else:
                print("   ⚠️  No GPU acceleration detected")
                print("   ⚠️  CPU optimization mode will be used")
                print("   ⚠️  Consider updating GPU drivers or checking GPU compatibility")

                # Provide specific fallback reasons
                if not hw_info['gpu_devices']:
                    print("   📋 Reason: No GPU devices detected")
                elif hw_info['gpu_devices']:
                    print("   📋 Reason: GPU detected but failed functionality tests")
                    for gpu in hw_info['gpu_devices']:
                        print(f"      - {gpu['name']}: Driver or compatibility issue")

            return True

        else:
            print("❌ Hardware acceleration detection failed")
            return False

    except Exception as e:
        print(f"❌ Error during hardware acceleration test: {e}")
        return False


def show_integration_example():
    """Show integration example for existing code."""
    print("\n" + "="*60)
    print("INTEGRATION EXAMPLE FOR EXISTING CODE")
    print("="*60)

    print("""
To integrate this hardware acceleration with your existing main.py:

1. Add to the top of main.py:
   ```python
   import asyncio
   from pathlib import Path

   # Add modern app path
   modern_app_path = Path(__file__).parent / "src"
   sys.path.insert(0, str(modern_app_path))

   from wow_bingo_game.utils.hardware_acceleration import HardwareAccelerationManager
   ```

2. In BingoGame.__init__():
   ```python
   # Add hardware acceleration
   self.hw_accel_manager = None
   self.optimization_profile = None
   asyncio.run(self._init_hardware_acceleration())
   ```

3. Add this method to BingoGame class:
   ```python
   async def _init_hardware_acceleration(self):
       try:
           self.hw_accel_manager = HardwareAccelerationManager()
           await self.hw_accel_manager.initialize()
           self.optimization_profile = self.hw_accel_manager.get_optimization_profile()
           print(f"Hardware profile: {self.optimization_profile['profile_name']}")
       except:
           self.optimization_profile = {'render_quality': 'medium', 'max_fps': 60}
   ```

4. Use optimization profile in your rendering:
   ```python
   def draw_optimized(self):
       quality = self.optimization_profile.get('render_quality', 'medium')

       if quality == 'ultra':
           # High quality rendering with all effects
           self.draw_with_effects()
       elif quality == 'high':
           # Good quality rendering
           self.draw_standard()
       else:
           # Optimized rendering for performance
           self.draw_fast()
   ```

This will automatically:
✅ Detect Intel QSV, NVIDIA NVENC, or AMD VCE acceleration
✅ Optimize rendering quality based on hardware
✅ Provide intelligent fallback to CPU optimization
✅ Monitor performance and adjust settings
""")


async def main():
    """Main test function."""
    print("🎮 WOW Bingo Game - Hardware Acceleration Test")
    print("=" * 60)

    if not MODERN_APP_AVAILABLE:
        print("❌ Modern application components not available")
        print("Please run: python quick_start_modern.py --install-deps")
        return

    # Test hardware acceleration
    success = await test_hardware_acceleration()

    if success:
        print("\n✅ Hardware acceleration test completed successfully!")
        print("Your system is ready for optimized WOW Bingo Game performance!")
    else:
        print("\n⚠️  Hardware acceleration test completed with issues")
        print("The game will still work with CPU optimization")

    # Show integration example
    show_integration_example()

    print("\n🏁 Test completed!")


if __name__ == "__main__":
    asyncio.run(main())
