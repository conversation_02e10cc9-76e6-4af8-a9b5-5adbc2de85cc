"""
Comprehensive Test Suite for RethinkDB Integration.

This script tests all aspects of the RethinkDB integration including:
- Database connectivity
- Data synchronization
- Performance monitoring
- Backup and recovery
- Web dashboard functionality
"""

import os
import sys
import time
import json
import unittest
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class RethinkDBIntegrationTest(unittest.TestCase):
    """Comprehensive test suite for RethinkDB integration."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        print("=" * 80)
        print("🚀 STARTING COMPREHENSIVE RETHINKDB INTEGRATION TESTS")
        print("=" * 80)
        
        cls.test_results = {
            'connectivity': False,
            'missing_functions': False,
            'sync_manager': False,
            'performance_monitor': False,
            'backup_manager': False,
            'web_dashboard': False,
            'data_operations': False
        }
    
    def test_01_missing_functions_availability(self):
        """Test that all previously missing functions are now available."""
        print("\n📋 Testing Missing Functions Availability...")
        
        try:
            # Test stats_event_hooks functions
            from stats_event_hooks import register_stats_event_handler, unregister_stats_event_handler
            print("✅ stats_event_hooks functions available")
            
            # Test rethink_db functions
            from rethink_db import get_rethink_db_manager, test_rethink_connection
            print("✅ rethink_db functions available")
            
            # Test game_state_handler functions
            from game_state_handler import get_current_game_state
            print("✅ game_state_handler functions available")
            
            self.test_results['missing_functions'] = True
            print("✅ All missing functions are now available!")
            
        except ImportError as e:
            print(f"❌ Missing function import error: {e}")
            self.fail(f"Missing functions not available: {e}")
    
    def test_02_rethinkdb_connectivity(self):
        """Test RethinkDB server connectivity."""
        print("\n🔌 Testing RethinkDB Connectivity...")
        
        try:
            from rethink_db import get_rethink_db_manager
            
            # Get RethinkDB manager
            rethink_manager = get_rethink_db_manager()
            self.assertIsNotNone(rethink_manager, "RethinkDB manager should not be None")
            
            # Test connection
            is_connected = rethink_manager.is_connected()
            print(f"Connection status: {'Connected' if is_connected else 'Disconnected'}")
            
            if is_connected:
                print("✅ RethinkDB connection successful!")
                self.test_results['connectivity'] = True
            else:
                print("⚠️ RethinkDB not connected (this is OK if server is not running)")
                
        except Exception as e:
            print(f"❌ RethinkDB connectivity test failed: {e}")
    
    def test_03_sync_manager_functionality(self):
        """Test sync manager functionality."""
        print("\n🔄 Testing Sync Manager Functionality...")
        
        try:
            from sync_manager import SyncManager
            
            # Create sync manager instance
            sync_manager = SyncManager()
            self.assertIsNotNone(sync_manager, "Sync manager should not be None")
            
            # Test sync status
            status = sync_manager.get_sync_status()
            self.assertIsInstance(status, dict, "Sync status should be a dictionary")
            
            print(f"✅ Sync manager initialized")
            print(f"   - Queue size: {status.get('queue_size', 'N/A')}")
            print(f"   - Real-time sync: {status.get('real_time_enabled', 'N/A')}")
            print(f"   - Connected: {status.get('connected', 'N/A')}")
            
            self.test_results['sync_manager'] = True
            
        except Exception as e:
            print(f"❌ Sync manager test failed: {e}")
    
    def test_04_performance_monitoring(self):
        """Test performance monitoring functionality."""
        print("\n📊 Testing Performance Monitoring...")
        
        try:
            from rethink_performance_monitor import get_performance_monitor
            
            # Get performance monitor
            perf_monitor = get_performance_monitor()
            self.assertIsNotNone(perf_monitor, "Performance monitor should not be None")
            
            # Test recording a query
            perf_monitor.record_query('SELECT', 'test_table', 50.0, True)
            
            # Get performance summary
            summary = perf_monitor.get_performance_summary()
            self.assertIsInstance(summary, dict, "Performance summary should be a dictionary")
            
            print(f"✅ Performance monitoring working")
            print(f"   - Total queries: {summary.get('total_queries', 'N/A')}")
            print(f"   - Monitoring enabled: {summary.get('monitoring_enabled', 'N/A')}")
            
            self.test_results['performance_monitor'] = True
            
        except Exception as e:
            print(f"❌ Performance monitoring test failed: {e}")
    
    def test_05_backup_manager(self):
        """Test backup manager functionality."""
        print("\n💾 Testing Backup Manager...")
        
        try:
            from rethink_backup_manager import get_backup_manager
            
            # Get backup manager
            backup_manager = get_backup_manager()
            self.assertIsNotNone(backup_manager, "Backup manager should not be None")
            
            # List existing backups
            backups = backup_manager.list_backups()
            self.assertIsInstance(backups, list, "Backups list should be a list")
            
            print(f"✅ Backup manager working")
            print(f"   - Existing backups: {len(backups)}")
            print(f"   - Backup directory: {backup_manager.backup_dir}")
            
            self.test_results['backup_manager'] = True
            
        except Exception as e:
            print(f"❌ Backup manager test failed: {e}")
    
    def test_06_data_operations(self):
        """Test basic data operations."""
        print("\n📝 Testing Data Operations...")
        
        try:
            from stats_db import get_stats_db_manager
            
            # Get SQLite manager
            sqlite_manager = get_stats_db_manager()
            self.assertIsNotNone(sqlite_manager, "SQLite manager should not be None")
            
            # Test database initialization
            sqlite_manager.ensure_database_exists()
            print("✅ SQLite database operations working")
            
            # Test RethinkDB operations if available
            try:
                from rethink_db import get_rethink_db_manager
                rethink_manager = get_rethink_db_manager()
                
                if rethink_manager and rethink_manager.is_connected():
                    # Test basic query
                    tables = rethink_manager.get_all_records('daily_stats', limit=1)
                    print("✅ RethinkDB data operations working")
                else:
                    print("⚠️ RethinkDB not connected for data operations test")
                    
            except Exception as e:
                print(f"⚠️ RethinkDB data operations test skipped: {e}")
            
            self.test_results['data_operations'] = True
            
        except Exception as e:
            print(f"❌ Data operations test failed: {e}")
    
    def test_07_web_dashboard_components(self):
        """Test web dashboard components."""
        print("\n🌐 Testing Web Dashboard Components...")
        
        try:
            # Test dashboard imports
            import rethink_dashboard_fixed
            print("✅ Dashboard module imports successfully")
            
            # Test Flask app creation
            app = rethink_dashboard_fixed.app
            self.assertIsNotNone(app, "Flask app should not be None")
            
            print("✅ Flask app created successfully")
            print(f"   - App name: {app.name}")
            
            self.test_results['web_dashboard'] = True
            
        except Exception as e:
            print(f"❌ Web dashboard test failed: {e}")
    
    def test_08_configuration_files(self):
        """Test configuration files and settings."""
        print("\n⚙️ Testing Configuration Files...")
        
        try:
            # Test RethinkDB configuration
            from rethink_config import RETHINKDB_HOST, RETHINKDB_PORT, RETHINKDB_DB
            print(f"✅ RethinkDB config loaded")
            print(f"   - Host: {RETHINKDB_HOST}")
            print(f"   - Port: {RETHINKDB_PORT}")
            print(f"   - Database: {RETHINKDB_DB}")
            
            # Check if config file exists
            config_file = os.path.join('data', 'rethink_config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                print(f"✅ Configuration file exists and is valid JSON")
            else:
                print("⚠️ Configuration file not found (using defaults)")
                
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
    
    def test_09_integration_status(self):
        """Test overall integration status."""
        print("\n🔍 Testing Overall Integration Status...")
        
        try:
            # Test the status utility
            import rethink_status
            print("✅ Status utility available")
            
            # Test game state integration
            from game_state_handler import get_current_game_state
            game_state = get_current_game_state()
            self.assertIsInstance(game_state, dict, "Game state should be a dictionary")
            print(f"✅ Game state integration working")
            
        except Exception as e:
            print(f"❌ Integration status test failed: {e}")
    
    @classmethod
    def tearDownClass(cls):
        """Print final test results."""
        print("\n" + "=" * 80)
        print("📊 FINAL TEST RESULTS")
        print("=" * 80)
        
        total_tests = len(cls.test_results)
        passed_tests = sum(cls.test_results.values())
        
        for test_name, result in cls.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title():<25} {status}")
        
        print("-" * 80)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! RethinkDB integration is fully functional!")
        elif passed_tests >= total_tests * 0.8:
            print("\n✅ Most tests passed! RethinkDB integration is mostly functional.")
        else:
            print("\n⚠️ Some tests failed. Please check the issues above.")
        
        print("=" * 80)

def run_quick_validation():
    """Run a quick validation of key components."""
    print("🔍 Running Quick Validation...")
    
    validations = {
        'RethinkDB Module': False,
        'Stats Event Hooks': False,
        'Game State Handler': False,
        'Sync Manager': False,
        'Performance Monitor': False,
        'Backup Manager': False,
        'Web Dashboard': False
    }
    
    # Test RethinkDB module
    try:
        from rethink_db import get_rethink_db_manager
        validations['RethinkDB Module'] = True
    except:
        pass
    
    # Test stats event hooks
    try:
        from stats_event_hooks import register_stats_event_handler
        validations['Stats Event Hooks'] = True
    except:
        pass
    
    # Test game state handler
    try:
        from game_state_handler import get_current_game_state
        validations['Game State Handler'] = True
    except:
        pass
    
    # Test sync manager
    try:
        from sync_manager import SyncManager
        validations['Sync Manager'] = True
    except:
        pass
    
    # Test performance monitor
    try:
        from rethink_performance_monitor import get_performance_monitor
        validations['Performance Monitor'] = True
    except:
        pass
    
    # Test backup manager
    try:
        from rethink_backup_manager import get_backup_manager
        validations['Backup Manager'] = True
    except:
        pass
    
    # Test web dashboard
    try:
        import rethink_dashboard_fixed
        validations['Web Dashboard'] = True
    except:
        pass
    
    print("\nValidation Results:")
    for component, status in validations.items():
        print(f"  {component:<20} {'✅' if status else '❌'}")
    
    passed = sum(validations.values())
    total = len(validations)
    print(f"\nOverall: {passed}/{total} components available ({(passed/total)*100:.1f}%)")
    
    return passed == total

if __name__ == '__main__':
    print("RethinkDB Integration Test Suite")
    print("Choose an option:")
    print("1. Run full test suite")
    print("2. Run quick validation")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        # Run full test suite
        unittest.main(verbosity=2, exit=False)
    elif choice == '2':
        # Run quick validation
        success = run_quick_validation()
        if success:
            print("\n🎉 All components are available and ready!")
        else:
            print("\n⚠️ Some components are missing. Run full tests for details.")
    else:
        print("Exiting...")
        sys.exit(0)
