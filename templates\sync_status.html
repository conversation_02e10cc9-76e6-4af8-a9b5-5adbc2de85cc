{% extends "layout.html" %}
{% block content %}
    <h2>Synchronization Status</h2>

    <div class="dashboard-summary">
        <div class="summary-card">
            <h3>Connection Status</h3>
            <div class="value">{{ 'Connected' if sync_status.connected else 'Disconnected' }}</div>
        </div>
        <div class="summary-card">
            <h3>Queue Size</h3>
            <div class="value">{{ sync_status.queue_size }}</div>
        </div>
        <div class="summary-card">
            <h3>Total Synced</h3>
            <div class="value">{{ sync_status.stats.total_synced }}</div>
        </div>
        <div class="summary-card">
            <h3>Sync Errors</h3>
            <div class="value">{{ sync_status.stats.sync_errors }}</div>
        </div>
    </div>

    <h3>Sync Configuration</h3>
    <table>
        <tr>
            <td>Real-time Sync</td>
            <td>{{ 'Enabled' if sync_status.real_time_enabled else 'Disabled' }}</td>
        </tr>
        <tr>
            <td>Batch Sync</td>
            <td>{{ 'Enabled' if sync_status.batch_sync_enabled else 'Disabled' }}</td>
        </tr>
        <tr>
            <td>Conflict Resolution</td>
            <td>{{ sync_status.conflict_resolution }}</td>
        </tr>
    </table>

    <h3>Last Sync Times</h3>
    <table>
        <thead>
            <tr>
                <th>Table</th>
                <th>Last Sync</th>
            </tr>
        </thead>
        <tbody>
            {% for table, timestamp in sync_status.last_sync_times.items() %}
            <tr>
                <td>{{ table }}</td>
                <td>{{ timestamp if timestamp else 'Never' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <h3>Actions</h3>
    <form method="post" action="/sync-status/force-sync" style="display: inline;">
        <button type="submit">Force Full Sync</button>
    </form>
{% endblock %}
