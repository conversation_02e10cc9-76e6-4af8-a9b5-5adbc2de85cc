"""
RethinkDB Setup Utility for the WOW Games application.

This script helps with setting up RethinkDB, initializing the database,
and migrating data from SQLite to RethinkDB.
"""

import os
import sys
import json
import time
import subprocess
import argparse
import logging
import threading
import platform
from datetime import datetime

# Try importing rethinkdb
try:
    import rethinkdb
    r = rethinkdb.r
    from rethinkdb.errors import ReqlDriverError
    RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False

# Import configuration
try:
    from rethink_config import (
        RETHINKDB_HOST,
        RETHINKDB_PORT,
        RETHINKDB_DB,
        RETHINKDB_USER,
        RETHINKDB_PASSWORD,
        RETHINKDB_SSL,
        SYNC_TABLES,
        save_config_to_file,
        load_config_from_file
    )
except ImportError:
    print("Error: rethink_config.py not found. Please create it first.")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'rethinkdb_setup.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_rethinkdb_installed():
    """
    Check if RethinkDB is installed on the system.

    Returns:
        bool: True if installed, False otherwise
    """
    try:
        # Try to run 'rethinkdb --version' to check if it's installed
        result = subprocess.run(
            ['rethinkdb', '--version'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode == 0 and 'rethinkdb' in result.stdout.lower():
            version = result.stdout.strip()
            print(f"RethinkDB is installed: {version}")
            return True
        else:
            print("RethinkDB is not installed or not in PATH.")
            return False
    except FileNotFoundError:
        print("RethinkDB is not installed or not in PATH.")
        return False

def download_rethinkdb():
    """
    Open the RethinkDB download page in the default web browser.

    Returns:
        None
    """
    import webbrowser

    # Open the RethinkDB download page
    url = "https://rethinkdb.com/docs/install/"
    print(f"Opening RethinkDB download page in browser: {url}")
    webbrowser.open(url)

    print("\nPlease follow the installation instructions for your operating system.")
    print("After installation, run this script again with the --check flag.")

def check_rethinkdb_running():
    """
    Check if RethinkDB is running on the configured host and port.

    Returns:
        bool: True if running, False otherwise
    """
    if not RETHINKDB_AVAILABLE:
        print("Error: rethinkdb module not installed. Please install it with pip.")
        return False

    try:
        # Try to connect to RethinkDB
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            user=RETHINKDB_USER,
            password=RETHINKDB_PASSWORD,
            ssl={"ca_certs": None} if RETHINKDB_SSL else None
        )

        # If we can connect, it's running
        conn.close()
        print(f"RethinkDB is running on {RETHINKDB_HOST}:{RETHINKDB_PORT}")
        return True
    except ReqlDriverError as e:
        print(f"RethinkDB is not running or not accessible: {e}")
        return False

def start_rethinkdb():
    """
    Start RethinkDB server locally.

    Returns:
        subprocess.Popen: Process object for the RethinkDB server
    """
    # First check if RethinkDB is installed
    if not check_rethinkdb_installed():
        print("Error: RethinkDB is not installed or not in PATH.")
        print("Please install RethinkDB first:")
        print("  Windows: Download from https://rethinkdb.com/docs/install/windows/")
        print("  Linux: sudo apt-get install rethinkdb")
        print("  macOS: brew install rethinkdb")
        return None

    # Check if already running
    if check_rethinkdb_running():
        print("RethinkDB is already running.")
        return None

    # Create data directory if it doesn't exist
    data_dir = os.path.join('data', 'rethinkdb_data')
    os.makedirs(data_dir, exist_ok=True)

    print(f"Starting RethinkDB server with data directory: {data_dir}")

    # Determine log file path
    log_file = os.path.join('data', 'rethinkdb.log')

    # Start RethinkDB server with improved command
    # Find an available port for the web admin interface
    import socket

    def find_free_port(start_port=8080):
        """Find a free port starting from start_port"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return None

    web_port = find_free_port(8080)
    if web_port is None:
        print("Warning: Could not find a free port for web admin interface")
        web_port = 8081  # Fallback

    cmd = [
        'rethinkdb',
        '--directory', data_dir,
        '--bind', '127.0.0.1',  # Bind to localhost only for security
        '--http-port', str(web_port),  # Web admin interface
        '--driver-port', str(RETHINKDB_PORT)  # Driver port
    ]

    # Don't use --daemon on Windows as it's not supported
    if platform.system() != 'Windows':
        cmd.append('--daemon')

    try:
        print(f"Executing command: {' '.join(cmd)}")

        # Start RethinkDB as a subprocess
        with open(log_file, 'a') as log_f:
            process = subprocess.Popen(
                cmd,
                stdout=log_f,
                stderr=subprocess.STDOUT,
                text=True
            )

        # Wait longer for it to start
        print("Waiting for RethinkDB to start...")
        time.sleep(5)

        # Check if it's running by testing connection
        connection_attempts = 0
        max_attempts = 10

        while connection_attempts < max_attempts:
            if check_rethinkdb_running():
                print(f"RethinkDB server started successfully!")
                print(f"Admin interface available at: http://localhost:{web_port}")
                print(f"Driver port: {RETHINKDB_PORT}")
                print(f"Log file: {log_file}")

                # Create a pid file
                pid_file = os.path.join('data', 'rethinkdb.pid')
                with open(pid_file, 'w') as f:
                    f.write(str(process.pid))

                return process

            connection_attempts += 1
            print(f"Waiting for connection... (attempt {connection_attempts}/{max_attempts})")
            time.sleep(2)

        # If we get here, the server didn't start properly
        print("Error: RethinkDB server failed to start or is not accepting connections")

        # Try to read the log file for error details
        try:
            with open(log_file, 'r') as f:
                log_content = f.read()
                if log_content:
                    print("Recent log entries:")
                    print(log_content[-1000:])  # Show last 1000 characters
        except:
            pass

        return None

    except FileNotFoundError:
        print("Error: RethinkDB executable not found in PATH.")
        print("Please ensure RethinkDB is properly installed and accessible.")
        return None
    except Exception as e:
        print(f"Error starting RethinkDB server: {e}")

        # Try to read the log file for error details
        try:
            with open(log_file, 'r') as f:
                log_content = f.read()
                if log_content:
                    print("Recent log entries:")
                    print(log_content[-500:])  # Show last 500 characters
        except:
            pass

        return None

def stop_rethinkdb():
    """
    Stop the local RethinkDB server.

    Returns:
        bool: True if stopped successfully, False otherwise
    """
    # Check if pid file exists
    pid_file = os.path.join('data', 'rethinkdb.pid')
    if not os.path.exists(pid_file):
        print("No RethinkDB server running (PID file not found)")
        return False

    try:
        # Read the PID
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # Try to terminate the process
        if platform.system() == 'Windows':
            subprocess.run(['taskkill', '/F', '/PID', str(pid)])
        else:
            subprocess.run(['kill', str(pid)])

        # Remove the PID file
        os.remove(pid_file)

        print(f"RethinkDB server (PID: {pid}) stopped successfully")
        return True
    except Exception as e:
        print(f"Error stopping RethinkDB server: {e}")
        return False

def initialize_database():
    """
    Initialize the RethinkDB database and tables.

    Returns:
        bool: True if successful, False otherwise
    """
    if not RETHINKDB_AVAILABLE:
        print("Error: rethinkdb module not installed. Please install it with pip.")
        return False

    if not check_rethinkdb_running():
        print("Error: RethinkDB is not running. Please start it first.")
        return False

    try:
        # Connect to RethinkDB
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            user=RETHINKDB_USER,
            password=RETHINKDB_PASSWORD,
            ssl={"ca_certs": None} if RETHINKDB_SSL else None
        )

        # Create the database if it doesn't exist
        if RETHINKDB_DB not in rethinkdb.r.db_list().run(conn):
            r.db_create(RETHINKDB_DB).run(conn)
            print(f"Created database '{RETHINKDB_DB}'")
        else:
            print(f"Database '{RETHINKDB_DB}' already exists")

        # Use the database
        conn.use(RETHINKDB_DB)

        # Create tables if they don't exist
        existing_tables = rethinkdb.r.table_list().run(conn)

        # Define table configurations with primary keys
        table_configs = {
            'daily_stats': {'primary_key': 'date'},
            'game_history': {'primary_key': 'id'},
            'wallet_transactions': {'primary_key': 'id'},
            'admin_users': {'primary_key': 'id'},
            'audit_log': {'primary_key': 'id'},
            'sync_metadata': {'primary_key': 'id'}  # Table to track sync status
        }

        for table, config in table_configs.items():
            if table not in existing_tables:
                rethinkdb.r.table_create(table, primary_key=config['primary_key']).run(conn)
                print(f"Created table '{table}' with primary key '{config['primary_key']}'")

                # Create indexes for the tables
                if table == 'daily_stats':
                    rethinkdb.r.table('daily_stats').index_create('date').run(conn)
                    print(f"Created index 'date' on table 'daily_stats'")
                elif table == 'game_history':
                    rethinkdb.r.table('game_history').index_create('date_time').run(conn)
                    rethinkdb.r.table('game_history').index_create('username').run(conn)
                    print(f"Created indexes 'date_time', 'username' on table 'game_history'")
                elif table == 'wallet_transactions':
                    rethinkdb.r.table('wallet_transactions').index_create('date_time').run(conn)
                    rethinkdb.r.table('wallet_transactions').index_create('transaction_type').run(conn)
                    print(f"Created indexes 'date_time', 'transaction_type' on table 'wallet_transactions'")
                elif table == 'admin_users':
                    rethinkdb.r.table('admin_users').index_create('username').run(conn)
                    print(f"Created index 'username' on table 'admin_users'")
                elif table == 'sync_metadata':
                    rethinkdb.r.table('sync_metadata').index_create('table_name').run(conn)
                    rethinkdb.r.table('sync_metadata').index_create('last_sync_time').run(conn)
                    print(f"Created indexes 'table_name', 'last_sync_time' on table 'sync_metadata'")
            else:
                print(f"Table '{table}' already exists")

        conn.close()
        print("Database initialization completed successfully")
        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        logging.error(f"Error initializing database: {e}")
        return False

def migrate_data_from_sqlite():
    """
    Migrate data from SQLite to RethinkDB.

    Returns:
        bool: True if successful, False otherwise
    """
    if not RETHINKDB_AVAILABLE:
        print("Error: rethinkdb module not installed. Please install it with pip.")
        return False

    if not check_rethinkdb_running():
        print("Error: RethinkDB is not running. Please start it first.")
        return False

    try:
        # Import SQLite manager
        from stats_db import get_stats_db_manager
        local_db = get_stats_db_manager()

        # Connect to RethinkDB
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            user=RETHINKDB_USER,
            password=RETHINKDB_PASSWORD,
            ssl={"ca_certs": None} if RETHINKDB_SSL else None,
            db=RETHINKDB_DB
        )

        # Migrate data for each table
        tables_migrated = 0

        # Migrate daily_stats
        try:
            print("Migrating daily_stats table...")
            with local_db.get_connection_context() as sqlite_conn:
                cursor = sqlite_conn.cursor()
                cursor.execute('SELECT * FROM daily_stats')

                # Convert to list of dictionaries
                columns = [col[0] for col in cursor.description]
                daily_stats = [dict(zip(columns, row)) for row in cursor.fetchall()]

                if daily_stats:
                    # Insert into RethinkDB
                    result = rethinkdb.r.table('daily_stats').insert(daily_stats).run(conn)
                    print(f"Migrated {len(daily_stats)} records to daily_stats table")
                    tables_migrated += 1
                else:
                    print("No records found in daily_stats table")
        except Exception as e:
            print(f"Error migrating daily_stats table: {e}")
            logging.error(f"Error migrating daily_stats table: {e}")

        # Migrate game_history
        try:
            print("Migrating game_history table...")
            with local_db.get_connection_context() as sqlite_conn:
                cursor = sqlite_conn.cursor()
                cursor.execute('SELECT * FROM game_history')

                # Convert to list of dictionaries
                columns = [col[0] for col in cursor.description]
                game_history = [dict(zip(columns, row)) for row in cursor.fetchall()]

                if game_history:
                    # Insert into RethinkDB
                    result = rethinkdb.r.table('game_history').insert(game_history).run(conn)
                    print(f"Migrated {len(game_history)} records to game_history table")
                    tables_migrated += 1
                else:
                    print("No records found in game_history table")
        except Exception as e:
            print(f"Error migrating game_history table: {e}")
            logging.error(f"Error migrating game_history table: {e}")

        # Migrate wallet_transactions
        try:
            print("Migrating wallet_transactions table...")
            with local_db.get_connection_context() as sqlite_conn:
                cursor = sqlite_conn.cursor()
                cursor.execute('SELECT * FROM wallet_transactions')

                # Convert to list of dictionaries
                columns = [col[0] for col in cursor.description]
                wallet_transactions = [dict(zip(columns, row)) for row in cursor.fetchall()]

                if wallet_transactions:
                    # Insert into RethinkDB
                    result = rethinkdb.r.table('wallet_transactions').insert(wallet_transactions).run(conn)
                    print(f"Migrated {len(wallet_transactions)} records to wallet_transactions table")
                    tables_migrated += 1
                else:
                    print("No records found in wallet_transactions table")
        except Exception as e:
            print(f"Error migrating wallet_transactions table: {e}")
            logging.error(f"Error migrating wallet_transactions table: {e}")

        # Migrate admin_users
        try:
            print("Migrating admin_users table...")
            with local_db.get_connection_context() as sqlite_conn:
                cursor = sqlite_conn.cursor()
                cursor.execute('SELECT * FROM admin_users')

                # Convert to list of dictionaries
                columns = [col[0] for col in cursor.description]
                admin_users = [dict(zip(columns, row)) for row in cursor.fetchall()]

                if admin_users:
                    # Insert into RethinkDB
                    result = rethinkdb.r.table('admin_users').insert(admin_users).run(conn)
                    print(f"Migrated {len(admin_users)} records to admin_users table")
                    tables_migrated += 1
                else:
                    print("No records found in admin_users table")
        except Exception as e:
            print(f"Error migrating admin_users table: {e}")
            logging.error(f"Error migrating admin_users table: {e}")

        conn.close()

        if tables_migrated > 0:
            print(f"Data migration completed successfully ({tables_migrated} tables migrated)")
            return True
        else:
            print("No tables were migrated")
            return False
    except Exception as e:
        print(f"Error migrating data: {e}")
        logging.error(f"Error migrating data: {e}")
        return False

def update_config():
    """
    Update RethinkDB configuration settings.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Import from current values
        global RETHINKDB_HOST, RETHINKDB_PORT, RETHINKDB_DB, RETHINKDB_USER, RETHINKDB_PASSWORD, RETHINKDB_SSL

        print("Current RethinkDB configuration:")
        print(f"Host: {RETHINKDB_HOST}")
        print(f"Port: {RETHINKDB_PORT}")
        print(f"Database: {RETHINKDB_DB}")
        print(f"User: {RETHINKDB_USER}")
        print(f"Password: {'*' * len(RETHINKDB_PASSWORD) if RETHINKDB_PASSWORD else ''}")
        print(f"SSL: {RETHINKDB_SSL}")

        print("\nEnter new values (or press Enter to keep current values):")

        # Get new values
        new_host = input(f"Host [{RETHINKDB_HOST}]: ").strip() or RETHINKDB_HOST
        new_port_str = input(f"Port [{RETHINKDB_PORT}]: ").strip() or str(RETHINKDB_PORT)
        new_db = input(f"Database [{RETHINKDB_DB}]: ").strip() or RETHINKDB_DB
        new_user = input(f"User [{RETHINKDB_USER}]: ").strip() or RETHINKDB_USER
        new_password = input(f"Password: ").strip() or RETHINKDB_PASSWORD
        new_ssl_str = input(f"SSL (true/false) [{RETHINKDB_SSL}]: ").strip().lower() or str(RETHINKDB_SSL).lower()

        # Validate values
        try:
            new_port = int(new_port_str)
            if new_port <= 0 or new_port > 65535:
                print("Error: Port must be a valid port number (1-65535)")
                return False
        except ValueError:
            print("Error: Port must be a valid port number")
            return False

        new_ssl = new_ssl_str.lower() == 'true'

        # Update global variables
        RETHINKDB_HOST = new_host
        RETHINKDB_PORT = new_port
        RETHINKDB_DB = new_db
        RETHINKDB_USER = new_user
        RETHINKDB_PASSWORD = new_password
        RETHINKDB_SSL = new_ssl

        # Save to config file
        config_path = save_config_to_file()

        print(f"Configuration updated and saved to {config_path}")
        return True
    except Exception as e:
        print(f"Error updating configuration: {e}")
        logging.error(f"Error updating configuration: {e}")
        return False

def test_connection():
    """
    Test the connection to the RethinkDB server.

    Returns:
        bool: True if successful, False otherwise
    """
    if not RETHINKDB_AVAILABLE:
        print("Error: rethinkdb module not installed. Please install it with pip.")
        return False

    try:
        print(f"Testing connection to RethinkDB at {RETHINKDB_HOST}:{RETHINKDB_PORT}...")

        # Try to connect to RethinkDB
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            user=RETHINKDB_USER,
            password=RETHINKDB_PASSWORD,
            ssl={"ca_certs": None} if RETHINKDB_SSL else None
        )

        # Test the connection by getting the server info
        server_info = rethinkdb.r.db('rethinkdb').table('server_config').run(conn)
        server_list = list(server_info)

        print(f"Connection successful! RethinkDB server version: {conn.server_info.get('version', 'unknown')}")
        print(f"Server name: {server_list[0]['name'] if server_list else 'unknown'}")

        # Check if the database exists
        databases = rethinkdb.r.db_list().run(conn)
        if RETHINKDB_DB in databases:
            print(f"Database '{RETHINKDB_DB}' exists")

            # Connect to the database
            conn.use(RETHINKDB_DB)

            # Check tables
            tables = rethinkdb.r.table_list().run(conn)
            print(f"Tables in database: {', '.join(tables) if tables else 'none'}")

            # Count records in each table
            for table in tables:
                count = rethinkdb.r.table(table).count().run(conn)
                print(f"Table '{table}' has {count} records")
        else:
            print(f"Database '{RETHINKDB_DB}' does not exist")

        conn.close()
        return True
    except Exception as e:
        print(f"Connection test failed: {e}")
        logging.error(f"Connection test failed: {e}")
        return False

def main():
    """Main function for the RethinkDB setup utility."""
    parser = argparse.ArgumentParser(description='RethinkDB Setup Utility for WOW Games')

    # Define command-line arguments
    parser.add_argument('--check', action='store_true', help='Check if RethinkDB is installed and running')
    parser.add_argument('--download', action='store_true', help='Open RethinkDB download page in browser')
    parser.add_argument('--start', action='store_true', help='Start RethinkDB server')
    parser.add_argument('--stop', action='store_true', help='Stop RethinkDB server')
    parser.add_argument('--init', action='store_true', help='Initialize RethinkDB database')
    parser.add_argument('--migrate', action='store_true', help='Migrate data from SQLite to RethinkDB')
    parser.add_argument('--config', action='store_true', help='Update RethinkDB configuration')
    parser.add_argument('--test', action='store_true', help='Test RethinkDB connection')

    args = parser.parse_args()

    # If no arguments provided, show help
    if len(sys.argv) == 1:
        parser.print_help()
        return

    # Check if RethinkDB module is installed
    if not RETHINKDB_AVAILABLE:
        print("Warning: rethinkdb Python module is not installed.")
        install = input("Would you like to install it now? (y/n): ").strip().lower()
        if install == 'y':
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "rethinkdb"])
                print("rethinkdb module installed successfully. Please restart this script.")
                return
            except Exception as e:
                print(f"Error installing rethinkdb module: {e}")
                return

    # Process command-line arguments
    if args.check:
        installed = check_rethinkdb_installed()
        if installed:
            running = check_rethinkdb_running()

    if args.download:
        download_rethinkdb()

    if args.start:
        installed = check_rethinkdb_installed()
        if installed:
            running = check_rethinkdb_running()
            if not running:
                start_rethinkdb()
            else:
                print("RethinkDB is already running.")

    if args.stop:
        stop_rethinkdb()

    if args.init:
        initialize_database()

    if args.migrate:
        migrate_data_from_sqlite()

    if args.config:
        update_config()

    if args.test:
        test_connection()

if __name__ == '__main__':
    main()