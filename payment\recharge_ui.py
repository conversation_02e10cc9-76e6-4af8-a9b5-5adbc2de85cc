"""
Recharge UI component for the payment system.

This module provides UI components for recharging credits through vouchers.
It integrates with the voucher_manager module for validation and processing.
"""

import pygame
import os
import time
from datetime import datetime
import pyperclip  # For clipboard operations

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (50, 50, 50)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
LIGHT_BLUE = (100, 150, 255)
GOLD = (255, 215, 0)
ORANGE = (255, 165, 0)
NAV_BAR_BG = (30, 40, 50)

class RechargeUI:
    """UI component for recharging credits."""

    def __init__(self, screen, voucher_manager, scale_x=1.0, scale_y=1.0):
        """
        Initialize the recharge UI.

        Args:
            screen: Pygame screen surface
            voucher_manager: VoucherManager instance
            scale_x: Horizontal scaling factor
            scale_y: Vertical scaling factor
        """
        self.screen = screen
        self.voucher_manager = voucher_manager
        self.scale_x = scale_x
        self.scale_y = scale_y

        # UI state
        self.visible = False
        self.voucher_input = ""
        self.input_active = False
        self.cursor_visible = True
        self.cursor_timer = 0
        self.message = ""
        self.message_type = "info"  # info, success, error
        self.message_timer = 0
        self.debug = False  # Debug flag to control logging

        # History state
        self.show_history = False
        self.voucher_history = []

        # Button states
        self.hit_areas = {}
        self.button_states = {}

        # Load sound effects if available
        self.load_sound_effects()

    def load_sound_effects(self):
        """Load sound effects for UI interactions."""
        try:
            pygame.mixer.init()
            self.button_click_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'button_click.wav'))
            self.success_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'success.wav'))
            self.error_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'error.wav'))
        except:
            self.button_click_sound = None
            self.success_sound = None
            self.error_sound = None

    def scaled_font_size(self, size):
        """
        Calculate a scaled font size based on screen dimensions.

        Args:
            size: Base font size

        Returns:
            int: Scaled font size
        """
        return int(size * min(self.scale_x, self.scale_y))

    def show(self):
        """Show the recharge UI."""
        self.visible = True
        self.voucher_input = ""
        self.input_active = True
        self.cursor_visible = True
        self.cursor_timer = 0
        self.message = ""
        self.message_timer = 0

        # Refresh voucher history
        self.refresh_history()

    def hide(self):
        """Hide the recharge UI."""
        self.visible = False
        self.input_active = False

    def refresh_history(self):
        """Refresh the voucher history."""
        if self.voucher_manager:
            self.voucher_history = self.voucher_manager.get_voucher_history(10)

    def toggle_history(self):
        """Toggle the history view."""
        self.show_history = not self.show_history
        if self.show_history:
            self.refresh_history()

    def handle_event(self, event):
        """
        Handle pygame events.

        Args:
            event: Pygame event

        Returns:
            bool: True if event was handled, False otherwise
        """
        if not self.visible:
            return False

        try:
            # Handle TEXTINPUT events for typing (modern pygame method)
            if event.type == pygame.TEXTINPUT:
                if self.input_active:
                    # Filter and add typed characters
                    char = event.text.upper()
                    if char.isalnum() or char == '-':
                        self.voucher_input += char
                        print(f"Added character: {char}, current input: {self.voucher_input}")
                        return True

            elif event.type == pygame.MOUSEBUTTONDOWN:
                return self.handle_mouse_click(event.pos)

            elif event.type == pygame.KEYDOWN:
                return self.handle_key_press(event)

            elif event.type == pygame.MOUSEMOTION:
                return self.handle_mouse_motion(event.pos)
        except Exception as e:
            if self.debug:
                print(f"Error handling event in recharge UI: {e}")
            return True  # Prevent the error from propagating

        return False

    def handle_mouse_click(self, pos):
        """
        Handle mouse click events.

        Args:
            pos: Mouse position

        Returns:
            bool: True if event was handled, False otherwise
        """
        try:
            # Check if click is on a button
            for btn_id, rect in self.hit_areas.items():
                if rect.collidepoint(pos):
                    # Play click sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # Handle button click
                    if btn_id == "close_button":
                        self.hide()
                        return True

                    elif btn_id == "submit_button":
                        self.validate_voucher()
                        return True

                    elif btn_id == "history_button":
                        self.toggle_history()
                        return True

                    elif btn_id == "input_field":
                        self.input_active = True
                        self.cursor_visible = True
                        self.cursor_timer = 0
                        return True

            # If click is outside the dialog, close it
            dialog_rect = self.get_dialog_rect()
            if not dialog_rect.collidepoint(pos):
                self.hide()
                return True
        except Exception as e:
            if self.debug:
                print(f"Error in handle_mouse_click: {e}")
            return True  # Prevent the error from propagating

        return False

    def handle_key_press(self, event):
        """
        Handle keyboard events.

        Args:
            event: Pygame key event

        Returns:
            bool: True if event was handled, False otherwise
        """
        try:
            if not self.input_active:
                # Handle escape key to close dialog
                if event.key == pygame.K_ESCAPE:
                    self.hide()
                    return True
                return False

            # Handle text input for voucher code
            if event.key == pygame.K_RETURN:
                self.validate_voucher()
                return True

            elif event.key == pygame.K_BACKSPACE:
                self.voucher_input = self.voucher_input[:-1]
                return True

            elif event.key == pygame.K_ESCAPE:
                self.hide()
                return True

            # Handle clipboard operations
            # Check for Ctrl modifier from both event and current keyboard state
            ctrl_pressed = (pygame.key.get_mods() & pygame.KMOD_CTRL) or (hasattr(event, 'mod') and event.mod & pygame.KMOD_CTRL)

            # Ctrl+V to paste
            if event.key == pygame.K_v and ctrl_pressed:
                try:
                    clipboard_text = pyperclip.paste().strip().upper()
                    # Filter out invalid characters
                    filtered_text = ''.join(c for c in clipboard_text if c.isalnum() or c == '-')
                    self.voucher_input = filtered_text
                    print(f"Pasted from clipboard: '{clipboard_text}' -> '{filtered_text}'")
                    return True
                except Exception as e:
                    if self.debug:
                        print(f"Error pasting from clipboard: {e}")
                    return True

            # Ctrl+C to copy
            elif event.key == pygame.K_c and ctrl_pressed:
                try:
                    pyperclip.copy(self.voucher_input)
                    self.show_message("Copied to clipboard!", "info")
                    print(f"Copied to clipboard: {self.voucher_input}")
                    return True
                except Exception as e:
                    if self.debug:
                        print(f"Error copying to clipboard: {e}")
                    return True

            # Ctrl+A to select all (clear and prepare for new input)
            elif event.key == pygame.K_a and ctrl_pressed:
                # For voucher input, "select all" means clear the field for new input
                self.voucher_input = ""
                print("Selected all (cleared input field)")
                return True

            # Only allow alphanumeric characters and dash
            elif event.unicode:
                char = event.unicode.upper()
                if char.isalnum() or char == '-':
                    self.voucher_input += char
                    return True
        except Exception as e:
            if self.debug:
                print(f"Error in handle_key_press: {e}")
            return True  # Prevent the error from propagating

        return False

    def handle_mouse_motion(self, pos):
        """
        Handle mouse motion events.

        Args:
            pos: Mouse position

        Returns:
            bool: True if event was handled, False otherwise
        """
        try:
            # Update button hover states
            for btn_id, rect in self.hit_areas.items():
                hover = rect.collidepoint(pos)
                if btn_id in self.button_states:
                    self.button_states[btn_id]["hover"] = hover
        except Exception as e:
            if self.debug:
                print(f"Error in handle_mouse_motion: {e}")
            return True  # Prevent the error from propagating

        return False

    def validate_voucher(self):
        """Validate the entered voucher code."""
        # Trim whitespace from input
        cleaned_input = self.voucher_input.strip()

        if not cleaned_input:
            self.show_message("Please enter a voucher code.", "error")
            if self.error_sound:
                self.error_sound.play()
            return

        # Check for minimum length before sending to validation
        # Allow both compact vouchers (10-15 chars) and legacy vouchers (20+ chars)
        cleaned_length = len(cleaned_input.replace('-', ''))
        if cleaned_length < 10 or (15 < cleaned_length < 20):
            self.show_message("Invalid voucher format. Vouchers must be either 10-15 characters (compact) or 20+ characters (legacy).", "error")
            if self.error_sound:
                self.error_sound.play()
            return

        # Check for valid characters
        from .crypto_utils import CROCKFORD_ALPHABET
        for char in cleaned_input.upper().replace('-', ''):
            if char not in CROCKFORD_ALPHABET:
                self.show_message(f"Invalid character '{char}' in voucher code. Only letters and numbers are allowed.", "error")
                if self.error_sound:
                    self.error_sound.play()
                return

        # Validate voucher
        result = self.voucher_manager.validate_voucher(cleaned_input)

        if result["success"]:
            # Play success sound
            if self.success_sound:
                self.success_sound.play()

            # Create a more detailed success message
            detailed_message = f"Added {result['amount']} credits! Share: {result['share']}% | Balance: {result['new_balance']} credits"

            # Show detailed success message
            self.show_message(detailed_message, "success")

            # Clear input
            self.voucher_input = ""

            # Refresh history
            self.refresh_history()
        else:
            # Play error sound
            if self.error_sound:
                self.error_sound.play()

            # Show error message
            self.show_message(result["message"], "error")

    def show_message(self, message, message_type="info"):
        """
        Show a message in the UI.

        Args:
            message: Message text
            message_type: Message type (info, success, error)
        """
        self.message = message
        self.message_type = message_type
        self.message_timer = 180  # Show for 3 seconds at 60 FPS

    def get_dialog_rect(self):
        """
        Get the rectangle for the dialog.

        Returns:
            pygame.Rect: Dialog rectangle
        """
        screen_width, screen_height = self.screen.get_size()

        # Calculate dialog dimensions
        dialog_width = int(500 * self.scale_x)
        dialog_height = int(400 * self.scale_y)

        # Center dialog on screen
        dialog_x = (screen_width - dialog_width) // 2
        dialog_y = (screen_height - dialog_height) // 2

        return pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)

    def update(self):
        """Update UI state."""
        if not self.visible:
            return

        # Update cursor blink
        self.cursor_timer += 1
        if self.cursor_timer >= 30:  # Blink every half second at 60 FPS
            self.cursor_visible = not self.cursor_visible
            self.cursor_timer = 0

        # Update message timer
        if self.message_timer > 0:
            self.message_timer -= 1

    def draw(self):
        """Draw the recharge UI."""
        if not self.visible:
            return

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))  # Semi-transparent black
        self.screen.blit(overlay, (0, 0))

        # Draw dialog
        dialog_rect = self.get_dialog_rect()

        # Draw dialog background with gradient
        self.draw_gradient_rect(
            dialog_rect,
            (40, 40, 60),  # Dark blue-gray
            (60, 60, 80),  # Lighter blue-gray
            10  # Border radius
        )

        # Draw dialog border
        pygame.draw.rect(
            self.screen,
            (80, 80, 100),  # Light blue-gray
            dialog_rect,
            2,  # Border width
            10  # Border radius
        )

        # Draw title
        title_font = pygame.font.SysFont("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Recharge Credits", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=dialog_rect.centerx,
            y=dialog_rect.y + int(20 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Draw current balance
        balance_font = pygame.font.SysFont("Arial", self.scaled_font_size(18))
        balance_text = balance_font.render(
            f"Current Balance: {self.voucher_manager.credits} credits",
            True, GOLD
        )
        balance_rect = balance_text.get_rect(
            centerx=dialog_rect.centerx,
            y=title_rect.bottom + int(20 * self.scale_y)
        )
        self.screen.blit(balance_text, balance_rect)

        # Draw input field label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        label_text = label_font.render("Enter Voucher Code:", True, WHITE)
        label_rect = label_text.get_rect(
            x=dialog_rect.x + int(30 * self.scale_x),
            y=balance_rect.bottom + int(30 * self.scale_y)
        )
        self.screen.blit(label_text, label_rect)

        # Draw input field
        input_rect = pygame.Rect(
            dialog_rect.x + int(30 * self.scale_x),
            label_rect.bottom + int(10 * self.scale_y),
            dialog_rect.width - int(60 * self.scale_x),
            int(40 * self.scale_y)
        )
        pygame.draw.rect(self.screen, DARK_GRAY, input_rect, border_radius=5)
        pygame.draw.rect(self.screen, LIGHT_GRAY, input_rect, 2, border_radius=5)

        # Store input field hit area
        self.hit_areas["input_field"] = input_rect

        # Draw input text with cursor
        input_font = pygame.font.SysFont("Arial", self.scaled_font_size(18))
        input_text = self.voucher_input

        # Add cursor if active
        if self.input_active and self.cursor_visible:
            input_text += "|"

        # Render input text
        if input_text:
            text_surface = input_font.render(input_text, True, WHITE)
            text_rect = text_surface.get_rect(
                x=input_rect.x + int(10 * self.scale_x),
                centery=input_rect.centery
            )
            self.screen.blit(text_surface, text_rect)

        # Draw submit button
        button_width = int(150 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_rect = pygame.Rect(
            dialog_rect.centerx - button_width // 2,
            input_rect.bottom + int(20 * self.scale_y),
            button_width,
            button_height
        )

        # Draw button with gradient
        self.draw_gradient_rect(
            button_rect,
            (0, 100, 50),  # Dark green
            (0, 150, 80),  # Light green
            5  # Border radius
        )

        # Draw button text
        button_font = pygame.font.SysFont("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Redeem Voucher", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Store button hit area
        self.hit_areas["submit_button"] = button_rect

        # Draw history button
        history_button_rect = pygame.Rect(
            dialog_rect.right - int(120 * self.scale_x) - int(20 * self.scale_x),
            dialog_rect.bottom - int(40 * self.scale_y) - int(20 * self.scale_y),
            int(120 * self.scale_x),
            int(40 * self.scale_y)
        )

        # Draw button with gradient
        self.draw_gradient_rect(
            history_button_rect,
            (60, 60, 100),  # Dark blue
            (80, 80, 130),  # Light blue
            5  # Border radius
        )

        # Draw button text
        history_button_text = button_font.render("History", True, WHITE)
        history_button_text_rect = history_button_text.get_rect(center=history_button_rect.center)
        self.screen.blit(history_button_text, history_button_text_rect)

        # Store button hit area
        self.hit_areas["history_button"] = history_button_rect

        # Draw close button
        close_button_rect = pygame.Rect(
            dialog_rect.right - int(100 * self.scale_x) - int(20 * self.scale_x),
            dialog_rect.y + int(20 * self.scale_y),
            int(100 * self.scale_x),
            int(30 * self.scale_y)
        )

        # Draw button with gradient
        self.draw_gradient_rect(
            close_button_rect,
            (150, 50, 50),  # Dark red
            (180, 60, 60),  # Light red
            5  # Border radius
        )

        # Draw button text
        close_button_text = button_font.render("Close", True, WHITE)
        close_button_text_rect = close_button_text.get_rect(center=close_button_rect.center)
        self.screen.blit(close_button_text, close_button_text_rect)

        # Store button hit area
        self.hit_areas["close_button"] = close_button_rect

        # Draw message if active
        if self.message and self.message_timer > 0:
            self.draw_message()

        # Draw history if active
        if self.show_history:
            self.draw_history(dialog_rect)

    def draw_message(self):
        """Draw the message box."""
        dialog_rect = self.get_dialog_rect()

        # Calculate message box dimensions
        message_width = dialog_rect.width - int(60 * self.scale_x)
        message_height = int(70 * self.scale_y)  # Increased height for detailed messages
        message_x = dialog_rect.x + int(30 * self.scale_x)
        message_y = dialog_rect.bottom - message_height - int(30 * self.scale_y)

        message_rect = pygame.Rect(message_x, message_y, message_width, message_height)

        # Determine color based on message type
        if self.message_type == "error":
            bg_color = (100, 30, 30)
            border_color = (150, 50, 50)
        elif self.message_type == "success":
            bg_color = (30, 100, 30)
            border_color = (50, 150, 50)
        else:  # info
            bg_color = (30, 30, 100)
            border_color = (50, 50, 150)

        # Draw message box
        pygame.draw.rect(self.screen, bg_color, message_rect, border_radius=5)
        pygame.draw.rect(self.screen, border_color, message_rect, 2, border_radius=5)

        # Check if message contains multiple parts (for success messages with detailed info)
        if "Added" in self.message and "|" in self.message:
            # Split the message into parts
            parts = self.message.split("|")

            # Draw first part (Added X credits! Share: Y%)
            message_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
            first_part = parts[0].strip()
            first_text = message_font.render(first_part, True, WHITE)
            first_rect = first_text.get_rect(
                centerx=message_rect.centerx,
                y=message_rect.y + int(15 * self.scale_y)
            )
            self.screen.blit(first_text, first_rect)

            # Draw second part (Balance: Z credits)
            second_part = parts[1].strip()
            second_text = message_font.render(second_part, True, GOLD)
            second_rect = second_text.get_rect(
                centerx=message_rect.centerx,
                y=first_rect.bottom + int(10 * self.scale_y)
            )
            self.screen.blit(second_text, second_rect)
        else:
            # Draw regular message text
            message_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
            message_text = message_font.render(self.message, True, WHITE)
            message_text_rect = message_text.get_rect(center=message_rect.center)
            self.screen.blit(message_text, message_text_rect)

    def draw_history(self, dialog_rect):
        """
        Draw the voucher history.

        Args:
            dialog_rect: Dialog rectangle
        """
        # Calculate history box dimensions
        history_width = dialog_rect.width - int(60 * self.scale_x)
        history_height = int(200 * self.scale_y)
        history_x = dialog_rect.x + int(30 * self.scale_x)
        history_y = dialog_rect.y + int(150 * self.scale_y)

        history_rect = pygame.Rect(history_x, history_y, history_width, history_height)

        # Draw history box
        pygame.draw.rect(self.screen, DARK_GRAY, history_rect, border_radius=5)
        pygame.draw.rect(self.screen, LIGHT_GRAY, history_rect, 2, border_radius=5)

        # Draw history title
        title_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Recent Vouchers", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=history_rect.centerx,
            y=history_rect.y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Draw history items
        item_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
        item_y = title_rect.bottom + int(10 * self.scale_y)

        if not self.voucher_history:
            # No history
            no_history_text = item_font.render("No vouchers redeemed yet.", True, LIGHT_GRAY)
            no_history_rect = no_history_text.get_rect(
                centerx=history_rect.centerx,
                centery=history_rect.centery
            )
            self.screen.blit(no_history_text, no_history_rect)
        else:
            # Draw column headers
            header_font = pygame.font.SysFont("Arial", self.scaled_font_size(14), bold=True)

            # Calculate column widths
            date_width = int(120 * self.scale_x)
            amount_width = int(80 * self.scale_x)
            share_width = int(80 * self.scale_x)
            expiry_width = int(100 * self.scale_x)

            # Draw headers
            date_text = header_font.render("Date", True, LIGHT_BLUE)
            amount_text = header_font.render("Amount", True, LIGHT_BLUE)
            share_text = header_font.render("Share %", True, LIGHT_BLUE)
            expiry_text = header_font.render("Expiry", True, LIGHT_BLUE)

            self.screen.blit(date_text, (history_rect.x + int(10 * self.scale_x), item_y))
            self.screen.blit(amount_text, (history_rect.x + date_width + int(10 * self.scale_x), item_y))
            self.screen.blit(share_text, (history_rect.x + date_width + amount_width + int(10 * self.scale_x), item_y))
            self.screen.blit(expiry_text, (history_rect.x + date_width + amount_width + share_width + int(10 * self.scale_x), item_y))

            item_y += int(25 * self.scale_y)

            # Draw separator line
            pygame.draw.line(
                self.screen,
                LIGHT_GRAY,
                (history_rect.x + int(10 * self.scale_x), item_y - int(5 * self.scale_y)),
                (history_rect.right - int(10 * self.scale_x), item_y - int(5 * self.scale_y)),
                1
            )

            # Draw history items
            for i, item in enumerate(self.voucher_history):
                if i >= 5:  # Show only 5 items
                    break

                date_text = item_font.render(item["redeemed_at"], True, WHITE)
                amount_text = item_font.render(str(item["amount"]), True, WHITE)
                share_text = item_font.render(str(item["share"]) + "%", True, WHITE)
                expiry_text = item_font.render(item["expiry"], True, WHITE)

                self.screen.blit(date_text, (history_rect.x + int(10 * self.scale_x), item_y))
                self.screen.blit(amount_text, (history_rect.x + date_width + int(10 * self.scale_x), item_y))
                self.screen.blit(share_text, (history_rect.x + date_width + amount_width + int(10 * self.scale_x), item_y))
                self.screen.blit(expiry_text, (history_rect.x + date_width + amount_width + share_width + int(10 * self.scale_x), item_y))

                item_y += int(25 * self.scale_y)

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0):
        """
        Draw a rectangle with a vertical gradient.

        Args:
            rect: Rectangle to draw
            color1: Top color
            color2: Bottom color
            border_radius: Border radius
        """
        # Create a surface for the gradient
        surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Draw the gradient
        for y in range(rect.height):
            # Calculate color for this line
            ratio = y / rect.height
            r = color1[0] + (color2[0] - color1[0]) * ratio
            g = color1[1] + (color2[1] - color1[1]) * ratio
            b = color1[2] + (color2[2] - color1[2]) * ratio
            color = (int(r), int(g), int(b))

            # Draw a line with this color
            pygame.draw.line(surface, color, (0, y), (rect.width, y))

        # Create a mask for rounded corners if needed
        if border_radius > 0:
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)
            surface.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Blit the gradient surface to the screen
        self.screen.blit(surface, rect)
