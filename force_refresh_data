#!/usr/bin/env python
"""
Utility script to force refresh all game statistics data.
This helps diagnose and fix issues with game winner identification.
"""

import os
import sys
import time
import datetime
import sqlite3

def print_separator():
    print("=" * 80)

def main():
    print_separator()
    print("GAME DATA REFRESH UTILITY")
    print("This utility forces a refresh of all game statistics data")
    print("and checks for winner identification issues.")
    print_separator()
    
    # Try to import required modules
    try:
        from stats_integration import force_refresh_data
        print("✓ Successfully imported force_refresh_data function")
    except ImportError as e:
        print(f"✗ Error: Could not import force_refresh_data function: {e}")
        print("  Make sure you're running this script from the correct directory")
        return 1
    
    # Check database file exists
    db_path = os.path.join('data', 'stats.db')
    if not os.path.exists(db_path):
        print(f"✗ Error: Database file not found at {db_path}")
        return 1
    else:
        print(f"✓ Found database file at {db_path}")
    
    # Connect to database and check for winner inconsistencies
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get total game count
        cursor.execute("SELECT COUNT(*) FROM game_history")
        game_count = cursor.fetchone()[0]
        print(f"✓ Found {game_count} games in database")
        
        # Check for status inconsistencies
        cursor.execute("SELECT status, COUNT(*) FROM game_history GROUP BY status")
        status_counts = cursor.fetchall()
        print("Game status distribution:")
        for status, count in status_counts:
            print(f"  - {status}: {count} games")
        
        # Check if any inconsistencies found
        if len(status_counts) > 1:
            print("⚠ WARNING: Found multiple different status values for games.")
            print("  This can cause issues with winner identification.")
            print("  The force refresh will attempt to consolidate these.")
        
        # Close database connection before refresh
        conn.close()
    except Exception as e:
        print(f"✗ Error accessing database: {e}")
        return 1
    
    # Run force refresh
    print_separator()
    print("Starting force refresh of all game data...")
    try:
        result = force_refresh_data()
        if result:
            print("✓ Force refresh completed successfully")
        else:
            print("⚠ Force refresh completed but reported errors")
    except Exception as e:
        print(f"✗ Error during force refresh: {e}")
        return 1
    
    # Verify changes
    try:
        time.sleep(1)  # Give database time to settle
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check status counts after refresh
        cursor.execute("SELECT status, COUNT(*) FROM game_history GROUP BY status")
        new_status_counts = cursor.fetchall()
        print("Game status distribution after refresh:")
        for status, count in new_status_counts:
            print(f"  - {status}: {count} games")
        
        # Close database connection
        conn.close()
    except Exception as e:
        print(f"✗ Error verifying changes: {e}")
        return 1
    
    print_separator()
    print("Refresh process complete. If you still experience issues with")
    print("game winner identification, please review the status values")
    print("and ensure they are consistent across all database entries.")
    print_separator()
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 