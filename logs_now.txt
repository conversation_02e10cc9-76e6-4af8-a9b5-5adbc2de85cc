 Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:05
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:05
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:05
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:05
  Column status: completed
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:05 -> 2025-05-08 14:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:05 -> 2025-05-08 13:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:05 -> 2025-05-08 12:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:05 -> 2025-05-08 11:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:05 -> 2025-05-08 10:32:05
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:05
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:05
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:05
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:05
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:05
  Column status: completed
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:05 -> 2025-05-08 14:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:05 -> 2025-05-08 13:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:05 -> 2025-05-08 12:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:05 -> 2025-05-08 11:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:05 -> 2025-05-08 10:32:05
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:05
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:05
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:05
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:05
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:05
  Column status: completed
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:05 -> 2025-05-08 14:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:05 -> 2025-05-08 13:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:05 -> 2025-05-08 12:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:05 -> 2025-05-08 11:32:05
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:05 -> 2025-05-08 10:32:05
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:05
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:05
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:05
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:05
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:05', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:05
  Column status: completed
Added delay to ensure database writes are complete
Added delay to ensure database writes are complete
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:05 -> 2025-05-08 14:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:05 -> 2025-05-08 13:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:05 -> 2025-05-08 12:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:05 -> 2025-05-08 11:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:05 -> 2025-05-08 10:32:06
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:06
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:06
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:06
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:06
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:06
  Column status: completed
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:06 -> 2025-05-08 14:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:06 -> 2025-05-08 13:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:06 -> 2025-05-08 12:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:06 -> 2025-05-08 11:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:06 -> 2025-05-08 10:32:06
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:06
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:06
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:06
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:06
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:06
  Column status: completed
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:06 -> 2025-05-08 14:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:06 -> 2025-05-08 13:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:06 -> 2025-05-08 12:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:06 -> 2025-05-08 11:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:06 -> 2025-05-08 10:32:06
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:06
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:06
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:06
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:06
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:06
  Column status: completed
Exiting stats page to: play
[DB DEBUG 2025-05-08 14:32:06.350] Database connection closed
Closed database connection on exit
Drawing weekly earnings with data: [{'date': '2025-05-08', 'games_played': 5, 'earnings': 1000.0, 'winners': 5, 'total_players': 50}, {'date': '2025-05-07', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 143}, {'date': '2025-05-06', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-05', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-04', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-03', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}, {'date': '2025-05-02', 'games_played': 0, 'earnings': 0.0, 'winners': 0, 'total_players': 0}]
CRITICAL: Fixing future date in weekly_stats: 2025-05-08 -> 2025-05-08
CRITICAL: Fixing future date in weekly_stats: 2025-05-07 -> 2025-05-07
CRITICAL: Fixing future date in weekly_stats: 2025-05-06 -> 2025-05-06
CRITICAL: Fixing future date in weekly_stats: 2025-05-05 -> 2025-05-05
CRITICAL: Fixing future date in weekly_stats: 2025-05-04 -> 2025-05-04
CRITICAL: Fixing future date in weekly_stats: 2025-05-03 -> 2025-05-03
CRITICAL: Fixing future date in weekly_stats: 2025-05-02 -> 2025-05-02
CRITICAL: Fixed weekly_stats dates
CRITICAL: Forced has_real_data to True
Day 0 (Sunday): earnings=1000.0
Drawing day card: day=Sunday, earnings=1000.0, is_total=False
Day 1 (Monday): earnings=0.0
Drawing day card: day=Monday, earnings=0.0, is_total=False
Day 2 (Tuesday): earnings=0.0
Drawing day card: day=Tuesday, earnings=0.0, is_total=False
Day 3 (Wednesday): earnings=0.0
Drawing day card: day=Wednesday, earnings=0.0, is_total=False
Day 4 (Thursday): earnings=0.0
Drawing day card: day=Thursday, earnings=0.0, is_total=False
Day 5 (Friday): earnings=0.0
Drawing day card: day=Friday, earnings=0.0, is_total=False
Day 6 (Saturday): earnings=0.0
Drawing day card: day=Saturday, earnings=0.0, is_total=False
Total earnings calculated: 1000.0
Drawing day card: day=Total, earnings=1000.0, is_total=True
Drawing summary cards with data: total_earnings=1000.0, daily_games=5, daily_earnings=1000.0, wallet_balance=0
CRITICAL: Set total_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_earnings to 1000.0 from weekly stats
CRITICAL: Set daily_games to 5 from weekly stats
CRITICAL: Forced has_real_data to True based on summary values
CRITICAL: Fixing future date in game_history: 2025-05-08 14:32:06 -> 2025-05-08 14:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 13:32:06 -> 2025-05-08 13:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 12:32:06 -> 2025-05-08 12:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 11:32:06 -> 2025-05-08 11:32:06
CRITICAL: Fixing future date in game_history: 2025-05-08 10:32:06 -> 2025-05-08 10:32:06
CRITICAL: Fixed game_history dates
CRITICAL: Forced has_real_data to True
Drawing game history with data: 5 entries, 1 pages
First game entry: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
Rendering 5 game history rows
Drawing game history row 0: {'id': 5, 'date_time': '2025-05-08 14:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 5
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 14:32:06
  Column status: completed
Drawing game history row 1: {'id': 4, 'date_time': '2025-05-08 13:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 4
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 13:32:06
  Column status: completed
Drawing game history row 2: {'id': 3, 'date_time': '2025-05-08 12:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 3
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 12:32:06
  Column status: completed
Drawing game history row 3: {'id': 2, 'date_time': '2025-05-08 11:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 2
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 11:32:06
  Column status: completed
Drawing game history row 4: {'id': 1, 'date_time': '2025-05-08 10:32:06', 'username': 'Test Player', 'house': 'Main House', 'stake': 0.0, 'players': 10, 'total_calls': 5, 'commission_percent': 20.0, 'fee': 200.0, 'total_prize': 1000.0, 'details': '{"winner_name": "Test Player", "winner_cartella": 123, "claim_type": "Full House", "game_duration": 300, "player_count": 10, "prize_amount": 1000, "commission_percentage": 20, "called_numbers": [1, 2, 3, 4, 5], "is_demo_mode": false}', 'status': 'completed'}
  Column id: 1
  Column username: Test Player
  Column stake: 0.0
  Formatted stake: 0.0 ETB
  Formatted fee: 200.0 ETB
  Column total_prize: 1000.0
  Formatted total_prize: 1,000.0 ETB
  Column date_time: 2025-05-08 10:32:06
  Column status: completed
Pygame initialized: True
Posted refresh_stats event with fresh data
Error syncing stats: module 'datetime' has no attribute 'now'
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\stats_db.py", line 1286, in sync_stats
    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
AttributeError: module 'datetime' has no attribute 'now'
Stats synchronization result: False
Started preloader data refresh
Enhanced force refresh result: True
Refreshing optimized loader data
[DB DEBUG 2025-05-08 14:32:06.600] Creating new database connection to data\stats.db
Forced refresh of optimized loader data
Closing and reopening database connection to ensure fresh data
[DB DEBUG 2025-05-08 14:32:06.609] New database connection created successfully
Getting weekly stats from 2025-05-02 to 2025-05-08
[DB DEBUG 2025-05-08 14:32:06.618] Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 12096.
CRITICAL: Found 13 records with future dates - fixing them
[DB DEBUG 2025-05-08 14:32:06.626] Using existing database connection
Forced refresh of thread_safe_db data
Error getting weekly stats: UNIQUE constraint failed: daily_stats.date
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\thread_safe_db.py", line 660, in get_weekly_stats
    cursor.execute('UPDATE daily_stats SET date = ? WHERE date = ?', (new_date, old_date))
sqlite3.IntegrityError: UNIQUE constraint failed: daily_stats.date
Error posting stats_loaded event: module 'thread_safe_db' has no attribute 'get_total_earnings'
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\stats_integration.py", line 210, in post_stats_loaded_event
    total_earnings = thread_safe_db.get_total_earnings()
AttributeError: module 'thread_safe_db' has no attribute 'get_total_earnings'
Cleared stats preloader cache
[STATS PROVIDER 2025-05-08 14:32:06.653] get_stats_provider called, returning provider with initialized=True
Refreshing stats_data_provider
[STATS PROVIDER 2025-05-08 14:32:06.656] Forcing refresh of all stats data
[STATS PROVIDER 2025-05-08 14:32:06.657] Attempting to force refresh via GameStatsIntegration
================================================================================
FORCE REFRESH DATA CALLED IN GAME_STATS_INTEGRATION
================================================================================
Using enhanced stats_integration.force_refresh_data
================================================================================
FORCING REFRESH OF ALL STATS DATA
================================================================================
Cleared stats preloader cache: True
Synchronizing all stats data to ensure consistency...
Syncing stats data...
Pygame initialized: True
Posted refresh_stats event with fresh data
Started preloader data refresh
[DB DEBUG 2025-05-08 14:32:06.932] Using existing database connection
Enhanced force refresh result: True
Refreshing optimized loader data
Forced refresh of optimized loader data
Closing and reopening database connection to ensure fresh data
Error getting weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 14804.
[DB DEBUG 2025-05-08 14:32:06.948] Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 15756.
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\thread_safe_db.py", line 630, in get_weekly_stats
    cursor = conn.cursor()
sqlite3.ProgrammingError: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 14804.
Error posting stats_loaded event: module 'thread_safe_db' has no attribute 'get_total_earnings'
[DB DEBUG 2025-05-08 14:32:06.957] Using existing database connection
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\stats_integration.py", line 210, in post_stats_loaded_event
    total_earnings = thread_safe_db.get_total_earnings()
Forced refresh of thread_safe_db data
AttributeError: module 'thread_safe_db' has no attribute 'get_total_earnings'
Cleared stats preloader cache
[STATS PROVIDER 2025-05-08 14:32:06.982] get_stats_provider called, returning provider with initialized=True
Refreshing stats_data_provider
[STATS PROVIDER 2025-05-08 14:32:06.985] Forcing refresh of all stats data
[STATS PROVIDER 2025-05-08 14:32:06.988] Attempting to force refresh via GameStatsIntegration
Added delay to ensure database writes are complete
================================================================================
FORCE REFRESH DATA CALLED IN GAME_STATS_INTEGRATION
================================================================================
Using enhanced stats_integration.force_refresh_data
================================================================================
FORCING REFRESH OF ALL STATS DATA
================================================================================
Cleared stats preloader cache: True
Synchronizing all stats data to ensure consistency...
Syncing stats data...
Pygame initialized: True
Posted refresh_stats event with fresh data
Started preloader data refresh
[DB DEBUG 2025-05-08 14:32:08.262] Using existing database connection
Enhanced force refresh result: True
Refreshing optimized loader data
Error getting weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 5264.
Traceback (most recent call last):
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\thread_safe_db.py", line 630, in get_weekly_stats
    cursor = conn.cursor()
sqlite3.ProgrammingError: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 5264.
Error posting stats_loaded event: module 'thread_safe_db' has no attribute 'get_total_earnings'
Forced refresh of optimized loader data
Traceback (most recent call last):
Closing and reopening database connection to ensure fresh data
  File "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\stats_integration.py", line 210, in post_stats_loaded_event
    total_earnings = thread_safe_db.get_total_earnings()
[DB DEBUG 2025-05-08 14:32:08.288] Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 748 and this is thread id 15404.
AttributeError: module 'thread_safe_db' has no attribute 'get_total_earnings'
[DB DEBUG 2025-05-08 14:32:08.298] Using existing database connection
Forced refresh of thread_safe_db data
Cleared stats preloader cache
[STATS PROVIDER 2025-05-08 14:32:08.319] get_stats_provider called, returning provider with initialized=True
Refreshing stats_data_provider
[STATS PROVIDER 2025-05-08 14:32:08.320] Forcing refresh of all stats data
[STATS PROVIDER 2025-05-08 14:32:08.321] Attempting to force refresh via GameStatsIntegration
================================================================================
FORCE REFRESH DATA CALLED IN GAME_STATS_INTEGRATION
================================================================================
Using enhanced stats_integration.force_refresh_data
================================================================================
FORCING REFRESH OF ALL STATS DATA
================================================================================
Cleared stats preloader cache: True
Synchronizing all stats data to ensure consistency...
Syncing stats data...
Pygame initialized: False
Pygame not initialized, skipping event posting