"""
Integration Test for Main Game Page Keyboard Shortcuts

This test simulates realistic main game scenarios to verify that F key and Escape key
work correctly for screen mode toggling, including edge cases and UI interactions.
"""

import pygame
import sys
import os
import time

def simulate_main_game_event_handling(event, game_state):
    """
    Simulate the main game event handling logic for keyboard shortcuts
    This replicates the priority handling from main.py
    """
    if event.type != pygame.KEYDOWN:
        return False, game_state
    
    # Initialize screen mode manager
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    
    # PRIORITY 1: Handle F key and Escape key for screen mode toggling FIRST
    
    # F key: Toggle fullscreen mode (highest priority)
    if event.key == pygame.K_f:
        # Only skip F key handling if actively typing in input fields
        if not (game_state.get('input_active', False) or 
               game_state.get('bet_input_active', False) or 
               game_state.get('reason_input_active', False)):
            
            # Toggle fullscreen with F key using screen mode manager
            screen = screen_mode_manager.handle_f_key_toggle(pygame.display.get_surface())
            
            return True, {**game_state, 'screen_mode_changed': True, 'last_action': 'f_key_toggle'}
    
    # Escape key: Exit fullscreen or close popups (high priority)
    if event.key == pygame.K_ESCAPE:
        # First check if settings window is visible and close it
        if game_state.get('settings_window_visible', False):
            return True, {**game_state, 'settings_window_visible': False, 'last_action': 'close_settings'}
        
        # Then check if view players popup is active, close it
        elif game_state.get('view_players_visible', False):
            return True, {**game_state, 'view_players_visible': False, 'last_action': 'close_view_players'}
        
        # Handle input field cancellation
        elif game_state.get('input_active', False) or game_state.get('bet_input_active', False):
            return True, {**game_state, 'input_active': False, 'bet_input_active': False, 'last_action': 'cancel_input'}
        
        # Handle modal input cancellation
        elif game_state.get('reason_input_active', False):
            return True, {**game_state, 'reason_input_active': False, 'last_action': 'cancel_modal_input'}
        
        # Otherwise exit fullscreen mode if in fullscreen using screen mode manager
        else:
            screen, mode_changed = screen_mode_manager.handle_escape_key(pygame.display.get_surface())
            if mode_changed:
                return True, {**game_state, 'screen_mode_changed': True, 'last_action': 'escape_fullscreen'}
    
    return False, game_state

def test_main_game_integration():
    """Test keyboard shortcuts in realistic main game scenarios"""
    print("=" * 70)
    print("INTEGRATION TEST: Main Game Page Keyboard Shortcuts")
    print("=" * 70)
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Main Game Integration Test")
    
    # Initialize screen mode manager
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    screen_mode_manager.settings_manager.set_screen_mode(False)  # Start windowed
    
    # Test 1: F key works in normal game state
    print("\n1. Testing F key in normal game state...")
    game_state = {
        'input_active': False,
        'bet_input_active': False,
        'reason_input_active': False,
        'settings_window_visible': False,
        'view_players_visible': False
    }
    
    f_key_event = type('Event', (), {'type': pygame.KEYDOWN, 'key': pygame.K_f})()
    handled, new_state = simulate_main_game_event_handling(f_key_event, game_state)
    
    print(f"   F key handled: {handled}")
    print(f"   Action taken: {new_state.get('last_action', 'none')}")
    assert handled, "F key should be handled in normal game state"
    assert new_state['last_action'] == 'f_key_toggle', "Should toggle screen mode"
    print("   ✓ F key works in normal game state")
    
    # Test 2: F key blocked when input field is active
    print("\n2. Testing F key blocked when input field is active...")
    game_state['input_active'] = True
    
    handled, new_state = simulate_main_game_event_handling(f_key_event, game_state)
    
    print(f"   F key handled: {handled}")
    assert not handled, "F key should be blocked when input field is active"
    print("   ✓ F key correctly blocked during input")
    
    # Test 3: Escape key priority handling
    print("\n3. Testing Escape key priority handling...")
    escape_event = type('Event', (), {'type': pygame.KEYDOWN, 'key': pygame.K_ESCAPE})()
    
    # Test with settings window visible (highest priority)
    game_state = {
        'input_active': False,
        'bet_input_active': False,
        'reason_input_active': False,
        'settings_window_visible': True,
        'view_players_visible': True  # Both visible, settings should take priority
    }
    
    handled, new_state = simulate_main_game_event_handling(escape_event, game_state)
    
    print(f"   Escape handled: {handled}")
    print(f"   Action taken: {new_state.get('last_action', 'none')}")
    assert handled, "Escape should be handled"
    assert new_state['last_action'] == 'close_settings', "Should close settings first"
    assert not new_state['settings_window_visible'], "Settings should be closed"
    print("   ✓ Escape correctly prioritizes settings window")
    
    # Test 4: Escape key for fullscreen exit
    print("\n4. Testing Escape key for fullscreen exit...")
    
    # First switch to fullscreen
    screen_mode_manager.settings_manager.set_screen_mode(True)
    
    game_state = {
        'input_active': False,
        'bet_input_active': False,
        'reason_input_active': False,
        'settings_window_visible': False,
        'view_players_visible': False
    }
    
    handled, new_state = simulate_main_game_event_handling(escape_event, game_state)
    
    print(f"   Escape handled: {handled}")
    print(f"   Action taken: {new_state.get('last_action', 'none')}")
    assert handled, "Escape should be handled"
    assert new_state['last_action'] == 'escape_fullscreen', "Should exit fullscreen"
    print("   ✓ Escape correctly exits fullscreen")
    
    # Test 5: Multiple F key presses (toggle behavior)
    print("\n5. Testing multiple F key presses...")
    
    game_state = {
        'input_active': False,
        'bet_input_active': False,
        'reason_input_active': False,
        'settings_window_visible': False,
        'view_players_visible': False
    }
    
    # Start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    initial_mode = screen_mode_manager.is_fullscreen()
    print(f"   Initial mode: {'fullscreen' if initial_mode else 'windowed'}")
    
    # First F key press
    handled1, state1 = simulate_main_game_event_handling(f_key_event, game_state)
    mode_after_first = screen_mode_manager.is_fullscreen()
    print(f"   After 1st F key: {'fullscreen' if mode_after_first else 'windowed'}")
    
    # Second F key press
    handled2, state2 = simulate_main_game_event_handling(f_key_event, game_state)
    mode_after_second = screen_mode_manager.is_fullscreen()
    print(f"   After 2nd F key: {'fullscreen' if mode_after_second else 'windowed'}")
    
    assert handled1 and handled2, "Both F key presses should be handled"
    assert initial_mode != mode_after_first, "First F key should change mode"
    assert mode_after_first != mode_after_second, "Second F key should toggle back"
    assert initial_mode == mode_after_second, "Should return to initial mode"
    print("   ✓ F key toggle behavior works correctly")
    
    pygame.quit()
    return True

def test_edge_cases():
    """Test edge cases and error conditions"""
    print("\n" + "=" * 70)
    print("TESTING EDGE CASES")
    print("=" * 70)
    
    # Test with invalid events
    print("\n1. Testing with invalid events...")
    
    invalid_event = type('Event', (), {'type': pygame.MOUSEBUTTONDOWN, 'key': pygame.K_f})()
    game_state = {}
    
    handled, new_state = simulate_main_game_event_handling(invalid_event, game_state)
    
    print(f"   Invalid event handled: {handled}")
    assert not handled, "Invalid events should not be handled"
    print("   ✓ Invalid events correctly ignored")
    
    # Test with multiple input fields active
    print("\n2. Testing with multiple input fields active...")
    
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    f_key_event = type('Event', (), {'type': pygame.KEYDOWN, 'key': pygame.K_f})()
    game_state = {
        'input_active': True,
        'bet_input_active': True,
        'reason_input_active': True
    }
    
    handled, new_state = simulate_main_game_event_handling(f_key_event, game_state)
    
    print(f"   F key handled with multiple inputs: {handled}")
    assert not handled, "F key should be blocked when any input is active"
    print("   ✓ Multiple input fields correctly block F key")
    
    pygame.quit()
    return True

def main():
    """Run the integration test suite"""
    print("MAIN GAME PAGE KEYBOARD SHORTCUTS - INTEGRATION TEST")
    print("=" * 70)
    
    success = True
    
    try:
        # Test 1: Main game integration
        if not test_main_game_integration():
            success = False
        
        # Test 2: Edge cases
        if not test_edge_cases():
            success = False
        
        # Final results
        print("\n" + "=" * 70)
        print("INTEGRATION TEST RESULTS")
        print("=" * 70)
        
        if success:
            print("🎉 ALL INTEGRATION TESTS PASSED! ✓")
            print("\nThe keyboard shortcuts are working correctly in realistic scenarios:")
            print("✓ F key toggles screen mode in normal game state")
            print("✓ F key is properly blocked during input operations")
            print("✓ Escape key has correct priority handling")
            print("✓ Escape key exits fullscreen when appropriate")
            print("✓ Multiple key presses work correctly")
            print("✓ Edge cases are handled properly")
            print("\nThe main game page now has fully functional keyboard shortcuts!")
        else:
            print("❌ INTEGRATION TESTS FAILED! ✗")
            print("\nThe implementation needs additional work.")
        
        return success
        
    except Exception as e:
        print(f"\n❌ INTEGRATION TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
