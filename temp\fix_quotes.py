import re

def fix_quotes():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Look for the problematic section
    pattern = r"if hasattr\(self, \\'remember_cartella_checkbox\\'\) and self\.remember_cartella_checkbox:"
    replacement = r"if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:"
    
    # Make the replacement
    new_content = re.sub(pattern, replacement, content)
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Quotes fixed successfully!")

if __name__ == "__main__":
    fix_quotes() 