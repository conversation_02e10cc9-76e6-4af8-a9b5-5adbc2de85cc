#!/bin/bash

echo "========================================================"
echo "    WOW Games with RethinkDB - Automatic Startup"
echo "========================================================"
echo

# Install required packages
echo "Step 1: Installing required packages..."
pip install rethinkdb
if [ $? -ne 0 ]; then
    echo "Error installing packages. Please try again."
    read -p "Press Enter to exit..."
    exit 1
fi
echo "Packages installed successfully."
echo

# Integrate RethinkDB
echo "Step 2: Integrating RethinkDB with the application..."
python integrate_rethinkdb.py --all
if [ $? -ne 0 ]; then
    echo "Error integrating RethinkDB. Please check the logs."
    read -p "Press Enter to exit..."
    exit 1
fi
echo "Integration completed successfully."
echo

# Start RethinkDB server
echo "Step 3: Starting RethinkDB server..."
python setup_rethinkdb.py --start
if [ $? -ne 0 ]; then
    echo "Warning: Could not start RethinkDB server. The application will run in offline mode."
else
    echo "RethinkDB server started successfully."
fi
echo

# Initialize database
echo "Step 4: Initializing RethinkDB database..."
python setup_rethinkdb.py --init
if [ $? -ne 0 ]; then
    echo "Warning: Could not initialize database. The application will run in offline mode."
else
    echo "Database initialized successfully."
fi
echo

# Migrate data
echo "Step 5: Migrating data from SQLite to RethinkDB..."
python setup_rethinkdb.py --migrate
if [ $? -ne 0 ]; then
    echo "Warning: Could not migrate data. The application will continue with existing data."
else
    echo "Data migration completed successfully."
fi
echo

# Check status
echo "Step 6: Checking RethinkDB status..."
python rethink_status.py --status
echo

# Start the game
echo "Step 7: Starting the game..."
echo
echo "========================================================"
echo "    WOW Games is starting with RethinkDB integration"
echo "    You can check the connection status in the top-right"
echo "    corner of the application (green = online, red = offline)"
echo "========================================================"
echo

sleep 5

python main.py