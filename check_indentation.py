import tokenize
import io

def check_indentation(filename):
    with open(filename, 'r') as f:
        content = f.read()
    
    # Check for mixed tabs and spaces
    has_tabs = '\t' in content
    has_spaces = '    ' in content
    
    if has_tabs and has_spaces:
        print(f"WARNING: File contains both tabs and spaces for indentation")
    
    # Check for consistent indentation
    lines = content.split('\n')
    indentation_levels = []
    
    for i, line in enumerate(lines):
        if line.strip() and not line.strip().startswith('#'):
            # Count leading spaces
            leading_spaces = len(line) - len(line.lstrip())
            if leading_spaces > 0:
                indentation_levels.append(leading_spaces)
    
    # Check if indentation is consistent (multiples of same base unit)
    if indentation_levels:
        min_indent = min(level for level in indentation_levels if level > 0)
        for level in indentation_levels:
            if level % min_indent != 0:
                print(f"WARNING: Inconsistent indentation detected. Base unit: {min_indent}, Found: {level}")
                return False
    
    print("Indentation check passed!")
    return True

if __name__ == "__main__":
    check_indentation("stats_page.py")