@echo off
:: ================================================================
:: WOW Bingo Game - Quick Dependency Installation
:: ================================================================
:: This script quickly installs the essential dependencies needed
:: to build the WOW Bingo Game executable using pip commands.
:: ================================================================

echo Installing PyInstaller and essential dependencies...
echo.

:: Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip

:: Install essential build dependencies
echo.
echo Installing PyInstaller (build tool)...
python -m pip install pyinstaller>=6.0.0

echo Installing Pygame (game engine)...
python -m pip install pygame>=2.5.0

echo Installing Pyperclip (clipboard support)...
python -m pip install pyperclip>=1.8.2

echo Installing Psutil (system utilities)...
python -m pip install psutil>=5.9.0

echo Installing Pillow (image processing)...
python -m pip install pillow>=10.0.0

echo Installing Cryptography (security)...
python -m pip install cryptography>=41.0.0

echo.
echo ================================================================
echo Installation complete!
echo ================================================================
echo.
echo You can now build your game using:
echo   python build_executable.py
echo.
echo Or use the easy build script:
echo   build_game.bat
echo.
pause
