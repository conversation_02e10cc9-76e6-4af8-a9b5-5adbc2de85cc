"""
Test script for the Screen Mode Manager

This script tests the global screen mode manager functionality
to ensure consistent screen mode behavior across all pages.
"""

import pygame
import sys
import os
from screen_mode_manager import get_screen_mode_manager

def test_screen_mode_manager():
    """Test the screen mode manager functionality"""
    print("Testing Screen Mode Manager...")
    
    # Initialize pygame
    pygame.init()
    
    # Create initial screen
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Screen Mode Manager Test")
    
    # Get the screen mode manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    print(f"Initial screen mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    print(f"Initial screen size: {screen.get_size()}")
    
    # Test toggling to fullscreen
    print("\nToggling to fullscreen...")
    screen, new_mode = screen_mode_manager.toggle_screen_mode(screen)
    print(f"New screen mode: {'fullscreen' if new_mode else 'windowed'}")
    print(f"New screen size: {screen.get_size()}")
    
    # Wait a moment
    pygame.time.delay(1000)
    
    # Test toggling back to windowed
    print("\nToggling back to windowed...")
    screen, new_mode = screen_mode_manager.toggle_screen_mode(screen)
    print(f"New screen mode: {'fullscreen' if new_mode else 'windowed'}")
    print(f"New screen size: {screen.get_size()}")
    
    # Test ensuring consistent mode
    print("\nTesting ensure_consistent_mode...")
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    print(f"Consistent mode applied: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    
    # Test settings persistence
    print("\nTesting settings persistence...")
    current_setting = screen_mode_manager.get_current_screen_mode()
    print(f"Current setting from settings: {'fullscreen' if current_setting else 'windowed'}")
    
    print("\nScreen Mode Manager test completed successfully!")
    
    # Clean up
    pygame.quit()

def test_multiple_instances():
    """Test that multiple instances return the same object (singleton pattern)"""
    print("\nTesting singleton pattern...")
    
    manager1 = get_screen_mode_manager()
    manager2 = get_screen_mode_manager()
    
    if manager1 is manager2:
        print("✓ Singleton pattern working correctly - same instance returned")
    else:
        print("✗ Singleton pattern failed - different instances returned")
    
    return manager1 is manager2

def test_callback_system():
    """Test the callback system for screen mode changes"""
    print("\nTesting callback system...")
    
    callback_called = False
    callback_mode = None
    
    def test_callback(is_fullscreen):
        nonlocal callback_called, callback_mode
        callback_called = True
        callback_mode = is_fullscreen
        print(f"Callback called with mode: {'fullscreen' if is_fullscreen else 'windowed'}")
    
    # Initialize pygame for this test
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    manager = get_screen_mode_manager()
    manager.set_screen_reference(screen)
    
    # Register callback
    manager.register_screen_mode_callback(test_callback)
    
    # Toggle screen mode to trigger callback
    screen, new_mode = manager.toggle_screen_mode(screen)
    
    if callback_called and callback_mode == new_mode:
        print("✓ Callback system working correctly")
        success = True
    else:
        print("✗ Callback system failed")
        success = False
    
    # Unregister callback
    manager.unregister_screen_mode_callback(test_callback)
    
    # Clean up
    pygame.quit()
    
    return success

def main():
    """Run all tests"""
    print("=" * 50)
    print("SCREEN MODE MANAGER TESTS")
    print("=" * 50)
    
    try:
        # Test basic functionality
        test_screen_mode_manager()
        
        # Test singleton pattern
        singleton_success = test_multiple_instances()
        
        # Test callback system
        callback_success = test_callback_system()
        
        print("\n" + "=" * 50)
        print("TEST RESULTS:")
        print(f"Singleton Pattern: {'PASS' if singleton_success else 'FAIL'}")
        print(f"Callback System: {'PASS' if callback_success else 'FAIL'}")
        print("Basic Functionality: PASS")
        print("=" * 50)
        
        if singleton_success and callback_success:
            print("All tests PASSED! ✓")
            return True
        else:
            print("Some tests FAILED! ✗")
            return False
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
