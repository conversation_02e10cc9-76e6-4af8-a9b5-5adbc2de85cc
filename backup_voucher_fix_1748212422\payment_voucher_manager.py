"""
Voucher Manager for the payment system.

This module handles voucher validation, redemption, and tracking.
It integrates with the crypto_utils module for cryptographic operations.
It also provides support for external PCs and their vouchers.
"""

import os
import json
import time
import hashlib
import sqlite3
import uuid
from datetime import datetime, timedelta
from .crypto_utils import CryptoUtils

# Constants - Use absolute paths to ensure consistency
def get_data_dir():
    """Get the data directory path, creating it if necessary."""
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
    os.makedirs(data_dir, exist_ok=True)
    return data_dir

VOUCHERS_DB_PATH = os.path.join(get_data_dir(), 'vouchers.db')
USAGE_LOG_PATH = os.path.join(get_data_dir(), 'usage_log.json')
EXTERNAL_PCS_DB_PATH = os.path.join(get_data_dir(), 'external_pcs.db')

class VoucherManager:
    """Manager for voucher operations."""

    def __init__(self):
        """Initialize the voucher manager."""
        self.initialization_errors = []
        self.credits = 0

        try:
            print(f"Initializing VoucherManager...")
            print(f"Data directory: {get_data_dir()}")
            print(f"Vouchers DB path: {VOUCHERS_DB_PATH}")

            # Get machine UUID with error handling
            try:
                self.machine_uuid = CryptoUtils.get_machine_uuid()
                print(f"Machine UUID: {self.machine_uuid}")
            except Exception as e:
                error_msg = f"Error getting machine UUID: {e}"
                print(error_msg)
                self.initialization_errors.append(error_msg)
                self.machine_uuid = None

            # Initialize databases
            try:
                self.ensure_database_exists()
                print("Database initialization successful")
            except Exception as e:
                error_msg = f"Error initializing database: {e}"
                print(error_msg)
                self.initialization_errors.append(error_msg)

            # Initialize usage log
            try:
                self.ensure_usage_log_exists()
                print("Usage log initialization successful")
            except Exception as e:
                error_msg = f"Error initializing usage log: {e}"
                print(error_msg)
                self.initialization_errors.append(error_msg)

            # Load credits
            try:
                self.credits = self.get_current_credits()
                print(f"Current credits loaded: {self.credits}")
            except Exception as e:
                error_msg = f"Error loading credits: {e}"
                print(error_msg)
                self.initialization_errors.append(error_msg)
                self.credits = 0

            if self.initialization_errors:
                print(f"VoucherManager initialized with {len(self.initialization_errors)} errors:")
                for error in self.initialization_errors:
                    print(f"  - {error}")
            else:
                print(f"VoucherManager initialized successfully. Current credits: {self.credits}")

        except Exception as e:
            error_msg = f"Critical error initializing VoucherManager: {e}"
            print(error_msg)
            self.initialization_errors.append(error_msg)
            # Continue with default values to prevent complete failure
            self.machine_uuid = None
            self.credits = 0

    def ensure_database_exists(self):
        """Ensure the vouchers database exists and has the correct schema."""
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(VOUCHERS_DB_PATH), exist_ok=True)

        # Create and initialize the database
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()

        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vouchers (
            voucher_hash TEXT PRIMARY KEY,
            amount INTEGER,
            share INTEGER,
            expiry INTEGER,
            redeemed_at INTEGER
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS credits (
            id INTEGER PRIMARY KEY,
            balance INTEGER,
            last_updated INTEGER
        )
        ''')

        # Initialize credits if not present
        cursor.execute('SELECT COUNT(*) FROM credits')
        if cursor.fetchone()[0] == 0:
            cursor.execute(
                'INSERT INTO credits (id, balance, last_updated) VALUES (1, 0, ?)',
                (int(time.time()),)
            )

        conn.commit()
        conn.close()

        # Ensure external PCs database exists
        self.ensure_external_pcs_database_exists()

    def ensure_external_pcs_database_exists(self):
        """Ensure the external PCs database exists and has the required tables."""
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(EXTERNAL_PCS_DB_PATH), exist_ok=True)

        # Connect to the database
        conn = sqlite3.connect(EXTERNAL_PCS_DB_PATH)
        cursor = conn.cursor()

        # Create external_pcs table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS external_pcs (
            uuid TEXT PRIMARY KEY,
            name TEXT,
            registration_date TEXT,
            last_voucher_date TEXT,
            notes TEXT
        )
        ''')

        # Create external_vouchers table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS external_vouchers (
            voucher_code TEXT PRIMARY KEY,
            uuid TEXT,
            amount INTEGER,
            share INTEGER,
            expiry_date TEXT,
            generation_date TEXT,
            redeemed INTEGER DEFAULT 0,
            redemption_date TEXT,
            FOREIGN KEY (uuid) REFERENCES external_pcs (uuid)
        )
        ''')

        conn.commit()
        conn.close()

    def ensure_usage_log_exists(self):
        """Ensure the usage log file exists."""
        if not os.path.exists(USAGE_LOG_PATH):
            with open(USAGE_LOG_PATH, 'w') as f:
                json.dump({
                    "usage": [],
                    "total_usage": 0,
                    "last_updated": int(time.time())
                }, f)

    def get_current_credits(self):
        """
        Get the current credit balance.

        Returns:
            int: Current credit balance
        """
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT balance FROM credits WHERE id = 1')
        result = cursor.fetchone()
        conn.close()

        return result[0] if result else 0

    def update_credits(self, amount):
        """
        Update the credit balance.

        Args:
            amount: Amount to add to the balance

        Returns:
            int: New balance
        """
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()

        # Get current balance
        cursor.execute('SELECT balance FROM credits WHERE id = 1')
        current_balance = cursor.fetchone()[0]

        # Update balance
        new_balance = current_balance + amount
        cursor.execute(
            'UPDATE credits SET balance = ?, last_updated = ? WHERE id = 1',
            (new_balance, int(time.time()))
        )

        conn.commit()
        conn.close()

        self.credits = new_balance
        return new_balance

    def is_voucher_used(self, voucher_hash):
        """
        Check if a voucher has already been used.

        Args:
            voucher_hash: Hash of the voucher

        Returns:
            bool: True if voucher has been used, False otherwise
        """
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM vouchers WHERE voucher_hash = ?', (voucher_hash,))
        result = cursor.fetchone()[0] > 0
        conn.close()

        return result

    def record_voucher_usage(self, voucher_hash, amount, share, expiry):
        """
        Record voucher usage in the database.

        Args:
            voucher_hash: Hash of the voucher
            amount: Credit amount
            share: Commission share percentage
            expiry: Expiry timestamp

        Returns:
            bool: True if recorded successfully, False otherwise
        """
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()

        try:
            cursor.execute(
                'INSERT INTO vouchers (voucher_hash, amount, share, expiry, redeemed_at) VALUES (?, ?, ?, ?, ?)',
                (voucher_hash, amount, share, expiry, int(time.time()))
            )
            conn.commit()
            conn.close()
            return True
        except sqlite3.Error:
            conn.close()
            return False

    def get_voucher_history(self, limit=10):
        """
        Get the voucher redemption history.

        Args:
            limit: Maximum number of records to return

        Returns:
            list: List of voucher history records
        """
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()

        try:
            # Get the most recent voucher redemptions
            cursor.execute(
                'SELECT amount, share, expiry, redeemed_at FROM vouchers ORDER BY redeemed_at DESC LIMIT ?',
                (limit,)
            )

            results = cursor.fetchall()
            history = []

            for row in results:
                amount, share, expiry, redeemed_at = row

                # Format dates
                redeemed_date = datetime.fromtimestamp(redeemed_at).strftime('%Y-%m-%d %H:%M')
                expiry_date = datetime.fromtimestamp(expiry).strftime('%Y-%m-%d')

                history.append({
                    'amount': amount,
                    'share': share,
                    'expiry': expiry_date,
                    'redeemed_at': redeemed_date
                })

            conn.close()
            return history

        except sqlite3.Error as e:
            print(f"Error getting voucher history: {e}")
            conn.close()
            return []

    def log_usage(self, credits_used, share_percentage, game_id):
        """
        Log credit usage to the usage log file.

        Args:
            credits_used: Number of credits used
            share_percentage: Commission share percentage
            game_id: Game identifier

        Returns:
            bool: True if logged successfully, False otherwise
        """
        try:
            # Ensure the usage log file exists
            self.ensure_usage_log_exists()

            # Load current usage log
            try:
                with open(USAGE_LOG_PATH, 'r') as f:
                    usage_log = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                # If file doesn't exist or is invalid, create a new log
                usage_log = {
                    "usage": [],
                    "total_usage": 0,
                    "last_updated": int(time.time())
                }

            print(f"Logging usage: credits_used={credits_used}, share_percentage={share_percentage}, game_id={game_id}")

            # Add new usage entry
            usage_entry = {
                "game_id": game_id,
                "credits_used": credits_used,
                "share_percentage": share_percentage,
                "timestamp": int(time.time())
            }

            # Update log
            usage_log["usage"].append(usage_entry)
            usage_log["total_usage"] += credits_used
            usage_log["last_updated"] = int(time.time())

            # Save updated log
            with open(USAGE_LOG_PATH, 'w') as f:
                json.dump(usage_log, f, indent=4)

            print(f"Usage logged successfully. New total usage: {usage_log['total_usage']}")
            return True
        except Exception as e:
            print(f"Error logging usage: {e}")
            import traceback
            traceback.print_exc()
            return False

    def validate_voucher(self, voucher_code):
        """
        Validate a voucher code.

        Args:
            voucher_code: Voucher code to validate

        Returns:
            dict: Result of validation with keys:
                - success: True if validation succeeded, False otherwise
                - message: Message describing the result
                - amount: Credit amount (if success)
                - share: Commission share percentage (if success)
        """
        try:
            # Check if voucher code is empty
            if not voucher_code or voucher_code.isspace():
                return {
                    "success": False,
                    "message": "Please enter a voucher code."
                }

            # Clean up voucher code
            voucher_code = voucher_code.strip().upper()

            # Remove any dashes
            voucher_code = voucher_code.replace('-', '')

            # Check for valid characters
            from .crypto_utils import CROCKFORD_ALPHABET
            for char in voucher_code:
                if char not in CROCKFORD_ALPHABET:
                    return {
                        "success": False,
                        "message": f"Invalid character '{char}' in voucher code. Only letters and numbers are allowed."
                    }

            # Basic format validation before sending to crypto utils
            # Check minimum length - allow compact vouchers (10-15 chars) or legacy vouchers (20+ chars)
            if len(voucher_code) < 10 or (15 < len(voucher_code) < 20):
                return {
                    "success": False,
                    "message": "Invalid voucher format. Vouchers must be either 10-15 characters (compact) or 20+ characters (legacy)."
                }

            # Validate with crypto utils
            result = CryptoUtils.validate_voucher(voucher_code, self.machine_uuid)

            if not result["valid"]:
                return {
                    "success": False,
                    "message": "Invalid voucher code. Please check and try again."
                }

            # Check if voucher has already been used
            voucher_hash = CryptoUtils.hash_voucher(voucher_code)
            if self.is_voucher_used(voucher_hash):
                return {
                    "success": False,
                    "message": "This voucher has already been redeemed."
                }

            # Check if voucher has expired
            # An expiry of 0 means the voucher never expires
            if result["expiry"] != 0 and result["expiry"] < int(time.time()):
                return {
                    "success": False,
                    "message": "This voucher has expired."
                }

            # Record voucher usage
            self.record_voucher_usage(
                voucher_hash,
                result["amount"],
                result["share"],
                result["expiry"]
            )

            # Update credits
            new_balance = self.update_credits(result["amount"])

            return {
                "success": True,
                "message": f"Successfully redeemed voucher for {result['amount']} credits!",
                "amount": result["amount"],
                "share": result["share"],
                "new_balance": new_balance
            }

        except Exception as e:
            print(f"Error validating voucher: {e}")
            return {
                "success": False,
                "message": "An error occurred while validating the voucher."
            }

    def clear_game_session(self):
        """
        Clear the current game session and deduct credits based on the active game.
        This is called when the app is exited while a game is playing.

        Returns:
            dict: Result of clearing the session with keys:
                - success: True if clearing was successful, False otherwise
                - message: Message describing the result
                - credits_used: Number of credits deducted
                - new_balance: New credit balance
        """
        try:
            print("Clearing game session and deducting credits...")
            print(f"Current credits: {self.credits}")

            # Get the usage tracker
            from .usage_tracker import get_usage_tracker
            usage_tracker = get_usage_tracker()
            print(f"Got usage tracker: {usage_tracker}")
            print(f"Usage tracker state: active={usage_tracker.active}, game_id={usage_tracker.current_game_id}, start_time={usage_tracker.game_start_time}")

            # Check if there's an active game
            if not usage_tracker.active:
                print("No active game tracking. No credits will be deducted.")
                print(f"Usage tracker state details: active={usage_tracker.active}, game_id={usage_tracker.current_game_id}, start_time={usage_tracker.game_start_time}")
                return {
                    "success": True,
                    "message": "No active game session to clear.",
                    "credits_used": 0,
                    "new_balance": self.credits
                }

            # Get the most recent voucher's share percentage
            voucher_history = self.get_voucher_history(1)
            share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

            # Get total bets and commission percentage from the game
            # Since we don't have direct access to the game object here, we'll use the usage tracker's estimates
            total_bets = 0
            commission_percentage = 20  # Default commission percentage

            # CRITICAL FIX: Set a minimum total bets amount to ensure credits are deducted
            # This is needed because the usage tracker calculates credits based on total bets
            print("CRITICAL FIX: Setting minimum total bets amount to ensure credits are deducted")
            min_total_bets = 100

            # Try to get total bets from the game if available
            try:
                from main import get_game_instance
                game = get_game_instance()
                if game and hasattr(game, 'bet_amount') and hasattr(game, 'players'):
                    total_bets = game.bet_amount * len(game.players)
                    print(f"Got total bets from game: {total_bets}")
            except Exception as e:
                print(f"Error getting total bets from game: {e}")

            # Use the maximum of current total bets and minimum total bets
            forced_total_bets = max(total_bets, min_total_bets)
            print(f"CRITICAL FIX: Using total bets amount: {forced_total_bets} (current: {total_bets}, minimum: {min_total_bets})")

            # End the game session through the usage tracker
            result = usage_tracker.end_game(
                share_percentage=share_percentage,
                total_bets=forced_total_bets,
                commission_percentage=commission_percentage
            )

            print(f"Game session cleared. Result: {result}")

            return {
                "success": True,
                "message": "Game session cleared and credits deducted.",
                "credits_used": result.get("credits_used", 0),
                "new_balance": result.get("new_balance", self.credits)
            }

        except Exception as e:
            print(f"Error clearing game session: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"An error occurred while clearing the game session: {e}",
                "credits_used": 0,
                "new_balance": self.credits
            }

    # External PC management methods

    def register_external_pc(self, uuid_str, name=None, notes=None):
        """
        Register an external PC.

        Args:
            uuid_str: UUID of the external PC
            name: Name of the external PC (optional)
            notes: Additional notes (optional)

        Returns:
            dict: Result of registration with keys:
                - success: True if registration was successful, False otherwise
                - message: Message describing the result
        """
        try:
            # Validate UUID format
            try:
                uuid_obj = uuid.UUID(uuid_str)
                uuid_str = str(uuid_obj).upper()
            except ValueError:
                return {
                    "success": False,
                    "message": f"Invalid UUID format: {uuid_str}"
                }

            # Connect to the database
            conn = sqlite3.connect(EXTERNAL_PCS_DB_PATH)
            cursor = conn.cursor()

            # Check if PC is already registered
            cursor.execute('SELECT uuid FROM external_pcs WHERE uuid = ?', (uuid_str,))
            if cursor.fetchone():
                # Update name and notes if provided
                if name or notes:
                    update_fields = []
                    update_values = []

                    if name:
                        update_fields.append('name = ?')
                        update_values.append(name)

                    if notes:
                        update_fields.append('notes = ?')
                        update_values.append(notes)

                    update_query = f'UPDATE external_pcs SET {", ".join(update_fields)} WHERE uuid = ?'
                    update_values.append(uuid_str)

                    cursor.execute(update_query, update_values)
                    conn.commit()
                    conn.close()

                    return {
                        "success": True,
                        "message": f"Updated information for PC with UUID {uuid_str}."
                    }
                else:
                    conn.close()
                    return {
                        "success": True,
                        "message": f"PC with UUID {uuid_str} is already registered."
                    }

            # Register new PC
            registration_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute(
                'INSERT INTO external_pcs (uuid, name, registration_date, notes) VALUES (?, ?, ?, ?)',
                (uuid_str, name or f"External PC {uuid_str[:8]}", registration_date, notes)
            )

            conn.commit()
            conn.close()

            return {
                "success": True,
                "message": f"Successfully registered PC with UUID {uuid_str}."
            }

        except Exception as e:
            print(f"Error registering external PC: {e}")
            return {
                "success": False,
                "message": f"An error occurred while registering the external PC: {e}"
            }

    def list_external_pcs(self):
        """
        List all registered external PCs.

        Returns:
            list: List of dictionaries containing PC information
        """
        try:
            # Connect to the database
            conn = sqlite3.connect(EXTERNAL_PCS_DB_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get all registered PCs
            cursor.execute('''
            SELECT uuid, name, registration_date, last_voucher_date, notes,
                   (SELECT COUNT(*) FROM external_vouchers WHERE uuid = external_pcs.uuid) as voucher_count
            FROM external_pcs
            ORDER BY registration_date DESC
            ''')

            pcs = [dict(row) for row in cursor.fetchall()]
            conn.close()

            return pcs

        except Exception as e:
            print(f"Error listing external PCs: {e}")
            return []

    def generate_external_vouchers(self, uuid_str, amount, count=1, days_valid=30, share=80, output_file=None):
        """
        Generate vouchers for an external PC.

        Args:
            uuid_str: UUID of the external PC
            amount: Credit amount
            count: Number of vouchers to generate
            days_valid: Number of days the vouchers are valid (0 = no expiry)
            share: Commission share percentage (0-100)
            output_file: Path to save the vouchers to (optional)

        Returns:
            dict: Result of voucher generation with keys:
                - success: True if generation was successful, False otherwise
                - message: Message describing the result
                - vouchers: List of generated vouchers (if success)
        """
        try:
            # Validate UUID format
            try:
                uuid_obj = uuid.UUID(uuid_str)
                uuid_str = str(uuid_obj).upper()
            except ValueError:
                return {
                    "success": False,
                    "message": f"Invalid UUID format: {uuid_str}"
                }

            # Connect to the database
            conn = sqlite3.connect(EXTERNAL_PCS_DB_PATH)
            cursor = conn.cursor()

            # Check if PC is registered
            cursor.execute('SELECT uuid FROM external_pcs WHERE uuid = ?', (uuid_str,))
            if not cursor.fetchone():
                conn.close()
                return {
                    "success": False,
                    "message": f"PC with UUID {uuid_str} is not registered. Please register the PC first."
                }

            # Import VoucherGenerator here to avoid circular imports
            from .voucher_generator import VoucherGenerator
            voucher_generator = VoucherGenerator()

            # Generate vouchers
            vouchers = []
            for i in range(count):
                # Generate a voucher
                voucher = voucher_generator.make_voucher(uuid_str, amount, share, days_valid)
                expiry_date = "Never" if days_valid == 0 else (
                    datetime.now() + timedelta(days=days_valid)).strftime("%Y-%m-%d")
                generation_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Add to list
                voucher_data = {
                    "voucher": voucher,
                    "uuid": uuid_str,
                    "amount": amount,
                    "share": share,
                    "expiry": expiry_date,
                    "generated": generation_date
                }
                vouchers.append(voucher_data)

                # Save to database
                cursor.execute(
                    'INSERT INTO external_vouchers (voucher_code, uuid, amount, share, expiry_date, generation_date) VALUES (?, ?, ?, ?, ?, ?)',
                    (voucher, uuid_str, amount, share, expiry_date, generation_date)
                )

            # Update last_voucher_date for the PC
            cursor.execute(
                'UPDATE external_pcs SET last_voucher_date = ? WHERE uuid = ?',
                (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), uuid_str)
            )

            conn.commit()
            conn.close()

            # Save to file if specified
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump(vouchers, f, indent=2)

            return {
                "success": True,
                "message": f"Successfully generated {count} voucher(s) for PC with UUID {uuid_str}.",
                "vouchers": vouchers
            }

        except Exception as e:
            print(f"Error generating external vouchers: {e}")
            return {
                "success": False,
                "message": f"An error occurred while generating vouchers: {e}"
            }

    def list_external_vouchers(self, uuid_str=None):
        """
        List vouchers for an external PC.

        Args:
            uuid_str: UUID of the external PC (optional, if None, list all vouchers)

        Returns:
            list: List of dictionaries containing voucher information
        """
        try:
            # Connect to the database
            conn = sqlite3.connect(EXTERNAL_PCS_DB_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get vouchers
            if uuid_str:
                # Validate UUID format
                try:
                    uuid_obj = uuid.UUID(uuid_str)
                    uuid_str = str(uuid_obj).upper()
                except ValueError:
                    print(f"Error: Invalid UUID format: {uuid_str}")
                    return []

                cursor.execute('''
                SELECT v.voucher_code, v.uuid, v.amount, v.share, v.expiry_date, v.generation_date,
                       v.redeemed, v.redemption_date, p.name
                FROM external_vouchers v
                JOIN external_pcs p ON v.uuid = p.uuid
                WHERE v.uuid = ?
                ORDER BY v.generation_date DESC
                ''', (uuid_str,))
            else:
                cursor.execute('''
                SELECT v.voucher_code, v.uuid, v.amount, v.share, v.expiry_date, v.generation_date,
                       v.redeemed, v.redemption_date, p.name
                FROM external_vouchers v
                JOIN external_pcs p ON v.uuid = p.uuid
                ORDER BY v.generation_date DESC
                ''')

            vouchers = [dict(row) for row in cursor.fetchall()]
            conn.close()

            return vouchers

        except Exception as e:
            print(f"Error listing external vouchers: {e}")
            return []
