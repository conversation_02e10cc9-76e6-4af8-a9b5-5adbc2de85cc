import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox, Querybox # For simple dialogs, or Toplevel for custom
from ttkbootstrap.toast import ToastNotification # Added specific import for ToastNotification
import sqlalchemy
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Date, ForeignKey, Enum as SQLAlchemyEnum, func, or_, text
from sqlalchemy.orm import sessionmaker, relationship, declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
import datetime
import os
import enum
import math # For pagination calculation
from payment import get_voucher_manager  # Import the voucher manager
from payment.usage_tracker import get_usage_tracker  # Import the usage tracker for credit usage
import time # Added for voucher history
from ttkbootstrap.style import Bootstyle
from PIL import Image, ImageTk, ImageDraw  # For gradients and card shine

# Matplotlib for charts
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import random # For dummy chart data initially

# ReportLab for PDF export
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image as RLImage
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("ReportLab not available. PDF export will be disabled.")

# Constants
DATA_DIR = os.path.join('data')
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)
STATS_DB_URL = f"sqlite:///{os.path.join(DATA_DIR, 'stats.db')}"
HISTORY_PAGE_SIZE = 15 # Number of items per page in game history

# SQLAlchemy Setup
Base = declarative_base()
engine = create_engine(STATS_DB_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# CRITICAL FIX: Add a function to force database creation
def ensure_database_exists():
    """
    Ensure the stats database exists and has the required tables.
    This can be called from other modules to force database creation.
    """
    try:
        # Ensure data directory exists
        if not os.path.exists(DATA_DIR):
            os.makedirs(DATA_DIR, exist_ok=True)
            print(f"Created data directory at: {DATA_DIR}")
        
        # Check if database file exists
        db_path = os.path.join(DATA_DIR, 'stats.db')
        db_file_existed = os.path.exists(db_path)
        
        if db_file_existed:
            print(f"Database file exists at: {db_path}")
            # Check if file is writable
            try:
                with open(db_path, 'a'):
                    pass
                print(f"Database file is writable")
            except Exception as e:
                print(f"Database file is not writable: {e}")
                # Try to fix permissions
                try:
                    os.chmod(db_path, 0o666)  # rw-rw-rw-
                    print(f"Fixed permissions on database file")
                except Exception as e:
                    print(f"Failed to fix permissions: {e}")
        else:
            print(f"Database file does not exist, will create: {db_path}")
        
        # Create database tables if they don't exist
        print("Creating database tables if they don't exist...")
        
        # Always create tables regardless of whether the file existed
        Base.metadata.create_all(bind=engine)
        
        # Verify tables were created
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"Tables in database: {tables}")
        
        # Verify we can connect to the database
        try:
            session = SessionLocal()
            # Execute a simple query
            query_result = session.execute("SELECT 1").fetchone()
            if query_result and query_result[0] == 1:
                print("Successfully connected to database and executed query")
            session.close()
            return True
        except Exception as e:
            print(f"Error connecting to database: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"Error ensuring database exists: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- SQLAlchemy Models ---

class GameStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    WON = "won"
    LOST = "lost"
    CANCELLED = "cancelled"
    
    @classmethod
    def from_string(cls, status_str):
        """
        Convert a string to the appropriate enum value, handling case variations.
        """
        if not status_str:
            return cls.PENDING
            
        # Normalize the string: lowercase and strip whitespace
        normalized = status_str.lower().strip()
        
        # Check each enum value
        for status in cls:
            if status.value.lower() == normalized:
                return status
                
        # If no match, use a direct attribute lookup with uppercase (handle 'won' -> WON)
        try:
            return cls[normalized.upper()]
        except (KeyError, AttributeError):
            # Default to PENDING if nothing matches
            print(f"Warning: Could not convert '{status_str}' to a valid GameStatus enum. Using PENDING.")
            return cls.PENDING

class GameHistory(Base):
    __tablename__ = "game_history"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, index=True)
    house = Column(String)
    stake = Column(Float)
    players = Column(Integer)
    total_calls = Column(Integer)
    commission_percent = Column(Float)
    fee = Column(Float)
    total_prize = Column(Float)
    date_time = Column(DateTime, default=datetime.datetime.utcnow, index=True)
    status = Column(SQLAlchemyEnum(GameStatus, create_constraint=True, case_sensitive=False), default=GameStatus.PENDING, index=True)

    @hybrid_property
    def stake_etb(self):
        return f"{self.stake:,.1f} ETB"
    
    @hybrid_property
    def total_prize_etb(self):
        return f"{self.total_prize:,.1f} ETB"
    
    @hybrid_property
    def fee_etb(self):
        return f"{self.fee:,.1f} ETB"
    
    @hybrid_property
    def commission_display(self):
        return f"{self.commission_percent:.1f}%"

    def __repr__(self):
        return f"<GameHistory(id={self.id}, user='{self.username}', prize={self.total_prize}, status='{self.status.value if self.status else 'N/A'}')>"

class DailyStat(Base):
    __tablename__ = "daily_stats"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, unique=True, index=True)
    earnings = Column(Float, default=0.0)
    games_played = Column(Integer, default=0)

    def __repr__(self):
        return f"<DailyStat(date='{self.date.strftime('%Y-%m-%d')}', earnings={self.earnings}, games={self.games_played})>"

class OverallSummary(Base):
    __tablename__ = "overall_summary"
    id = Column(Integer, primary_key=True)
    total_earnings = Column(Float, default=0.0)
    current_daily_games = Column(Integer, default=0)
    current_daily_earnings = Column(Float, default=0.0)
    wallet_balance = Column(Float, default=0.0)
    last_updated = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __repr__(self):
        return f"<OverallSummary(total_earnings={self.total_earnings}, wallet={self.wallet_balance})>"

Base.metadata.create_all(bind=engine)

# --- TTKBootstrap Application ---

class GameDetailDialog(ttk.Toplevel):
    def __init__(self, master, game_entry: GameHistory):
        super().__init__(master)
        self.title("Game Details")
        self.geometry("450x400") # Adjusted size
        self.transient(master) # Make it behave like a modal
        self.grab_set() # Capture input
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        ttk.Label(main_frame, text=f"Game ID: {game_entry.id}", font=("Helvetica", 14, "bold")).grid(row=0, column=0, columnspan=2, sticky=W, pady=5)
        
        # Format date_time safely
        if hasattr(game_entry, 'date_time') and game_entry.date_time:
            if isinstance(game_entry.date_time, str):
                try:
                    date_time_obj = datetime.datetime.strptime(game_entry.date_time, '%Y-%m-%d %H:%M:%S')
                    date_time_str = date_time_obj.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    date_time_str = str(game_entry.date_time)
            else:
                try:
                    date_time_str = game_entry.date_time.strftime("%Y-%m-%d %H:%M:%S")
                except AttributeError:
                    date_time_str = str(game_entry.date_time)
        else:
            date_time_str = "N/A"
        
        details = [
            ("Username:", game_entry.username),
            ("House:", game_entry.house),
            ("Stake:", game_entry.stake_etb),
            ("Players:", game_entry.players),
            ("Total Calls:", game_entry.total_calls),
            ("Commission:", game_entry.commission_display),
            ("Fee:", game_entry.fee_etb),
            ("Total Prize:", game_entry.total_prize_etb),
            ("Date & Time:", date_time_str),
            ("Status:", game_entry.status.value.title() if game_entry.status else "N/A")
        ]
        for i, (label_text, value_text) in enumerate(details):
            ttk.Label(main_frame, text=label_text, font=("Helvetica", 10, "bold")).grid(row=i+1, column=0, sticky=W, padx=5, pady=2)
            ttk.Label(main_frame, text=str(value_text)).grid(row=i+1, column=1, sticky=W, padx=5, pady=2)
        close_button = ttk.Button(main_frame, text="Close", command=self.destroy, bootstyle=SECONDARY)
        close_button.grid(row=len(details)+1, column=0, columnspan=2, pady=15)

class StatsPage(ttk.Frame):
    def __init__(self, master, **kwargs):
        super().__init__(master, padding=(0,0), **kwargs)
        self.master = master
        self.db_session = SessionLocal()
        self.current_history_page = 1
        self.total_history_pages = 1
        self.history_page_size = HISTORY_PAGE_SIZE
        self.search_username_var = ttk.StringVar()
        self.search_status_var = ttk.StringVar()
        self.status_text = ttk.StringVar(value="Ready")
        self.time_period_var = ttk.StringVar(value="Weekly")  # Added time period variable
        self.master.title("WOW Bingo - Statistics")
        self.header_canvas = None
        # Initialize the voucher manager and usage tracker
        self.voucher_manager = get_voucher_manager()
        self.usage_tracker = get_usage_tracker()
        self.create_widgets()
        self.load_data_and_populate_ui()
        # Bind resize event for responsiveness
        self.master.bind('<Configure>', self._on_resize)
        # Set minimum window size to prevent components from becoming too cramped
        self.master.minsize(900, 600)
        # Schedule periodic wallet balance updates
        self.schedule_wallet_sync()
        # Check if daily stats need to be reset
        self.check_daily_reset()
        # Schedule daily reset
        self.schedule_daily_reset()
        # Schedule auto-refresh for stats data
        self.schedule_auto_refresh()

    def schedule_wallet_sync(self):
        """Schedule periodic wallet balance synchronization."""
        self.sync_wallet_balance()  # Sync immediately
        # Schedule next sync after 10 seconds
        self.master.after(10000, self.schedule_wallet_sync)

    def sync_wallet_balance(self):
        """Sync the wallet balance with the voucher manager's current credits."""
        try:
            credits = self.voucher_manager.get_current_credits()
            summary = self.db_session.query(OverallSummary).first()
            if summary:
                if summary.wallet_balance != credits:
                    summary.wallet_balance = credits
                    summary.last_updated = datetime.datetime.utcnow()
                    self.db_session.commit()
                    self.load_summary_data()  # Refresh UI only if balance changed
        except Exception as e:
            print(f"Error syncing wallet balance: {e}")

    def _on_resize(self, event):
        # Only handle resize for window events, not widgets
        if event.widget == self.master:
            # Get window dimensions
            width = event.width
            height = event.height
            
            # Redraw header
            if self.header_canvas:
                self.header_canvas.config(width=width)
                self.header_canvas.delete("all")
                self._draw_wow_bingo_header()
            
            # Resize the contents proportionally
            # Scale game history tree height based on available vertical space
            min_height = 200  # Minimum height for the game history section
            available_height = height - 350  # Adjusting for header, nav, voucher, summary cards, and action buttons
            
            # Scale the voucher history height if window is tall enough
            if height > 700:
                self.voucher_history_tree.configure(height=3)
            else:
                self.voucher_history_tree.configure(height=2)
            
            # Dynamically adjust the font sizes
            if width < 1000:
                small_font_size = 8
                medium_font_size = 10
                large_font_size = 12
            else:
                small_font_size = 9
                medium_font_size = 11
                large_font_size = 14
                
            # Update font size on some elements
            try:
                self.total_weekly_earnings_label.configure(font=("Helvetica", medium_font_size, "bold"))
                self.page_info_label.configure(font=("Helvetica", small_font_size))
                for key, label in self.summary_labels.items():
                    label.configure(font=("Helvetica", large_font_size, "bold"))
            except Exception as e:
                print(f"Error adjusting font sizes: {e}")
            
            # Resize charts
            if hasattr(self, 'fig') and hasattr(self, 'canvas'):
                self.fig.tight_layout()
                self.canvas.draw()

    def create_widgets(self):
        # Configure main frame to use grid layout for better control
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=0)  # Nav bar
        self.rowconfigure(1, weight=0)  # Header 
        self.rowconfigure(2, weight=1)  # Content
        self.rowconfigure(3, weight=0)  # Status bar
        self.rowconfigure(4, weight=0)  # Action buttons
        
        # Navigation Bar
        nav_bar_frame = ttk.Frame(self, bootstyle="dark", padding=(5,3))
        nav_bar_frame.grid(row=0, column=0, sticky="ew")
        
        nav_buttons = [
            ("🎮 Game", self.navigate_to_game, SECONDARY),
            ("📊 Stats", self.navigate_to_stats, PRIMARY),
            ("⚙️ Settings", self.navigate_to_settings, SECONDARY),
            ("❓ Help", self.show_help_dialog, INFO)
        ]
        for i, (text, command, style) in enumerate(nav_buttons):
            btn = ttk.Button(nav_bar_frame, text=text, command=command, bootstyle=style, width=10)
            btn.pack(side=LEFT, padx=3)
            if "Stats" in text:
                btn.config(state=DISABLED)
        
        # Add Recharge Wallet button to the right side of the nav bar
        self.recharge_button = ttk.Button(nav_bar_frame, text="💳 Recharge", command=self.show_recharge_dialog, bootstyle="info")
        self.recharge_button.pack(side=RIGHT, padx=5)

        # Responsive WOW-BINGO Header - reduced height
        self.header_canvas = ttk.Canvas(self, height=80, highlightthickness=0)
        self.header_canvas.grid(row=1, column=0, sticky="ew")
        self._draw_wow_bingo_header()

        # Main content area below header - using frame with grid layout
        self.content_area = ttk.Frame(self, padding=(10,5))
        self.content_area.grid(row=2, column=0, sticky="nsew")
        
        # Configure content area grid
        self.content_area.columnconfigure(0, weight=1)
        # Row weights: summary cards (0), voucher history (0), game history (1)
        self.content_area.rowconfigure(0, weight=0)  # Summary row - fixed height
        self.content_area.rowconfigure(1, weight=0)  # Voucher row - fixed but compact
        self.content_area.rowconfigure(2, weight=1)  # Game history - takes remaining space

        # Top row: Summary Cards and Weekly Earnings Chart
        self.top_row_frame = ttk.Frame(self.content_area)
        self.top_row_frame.grid(row=0, column=0, sticky="ew", pady=(0,5))
        self.top_row_frame.columnconfigure(0, weight=0)  # Summary cards - fixed width
        self.top_row_frame.columnconfigure(1, weight=1)  # Chart - expands

        # Summary cards - more compact
        self.summary_cards_container = ttk.Frame(self.top_row_frame)
        self.summary_cards_container.grid(row=0, column=0, sticky="ns", padx=(0,5))

        self.summary_card_data = {
            "total_earning": {"title": "TOTAL EARNINGS", "value": "0.0 ETB", "icon": "💰", "bootstyle": INFO},
            "daily_games": {"title": "TODAY'S GAMES", "value": "0", "icon": "🎮", "bootstyle": SUCCESS},
            "daily_earning": {"title": "TODAY'S EARNINGS", "value": "0.0 ETB", "icon": "📈", "bootstyle": WARNING},
            "wallet_balance": {"title": "WALLET BALANCE", "value": "0.0 ETB", "icon": "💼", "bootstyle": PRIMARY}
        }
        self.summary_labels = {}
        for i, (key, data) in enumerate(self.summary_card_data.items()):
            card = ttk.Labelframe(self.summary_cards_container, text=data["title"], padding=8, bootstyle=data["bootstyle"])
            card.pack(fill=X, pady=3, ipady=2)
            icon_label = ttk.Label(card, text=data["icon"], font=("Helvetica", 16), bootstyle=f"inverse-{data['bootstyle'].lower()}")
            icon_label.pack(side=LEFT, padx=(0,5))
            value_label = ttk.Label(card, text=data["value"], font=("Helvetica", 14, "bold"), bootstyle=f"inverse-{data['bootstyle'].lower()}")
            value_label.pack(side=LEFT)
            self.summary_labels[key] = value_label

        # Weekly Earnings Chart
        self.weekly_earnings_frame = ttk.Labelframe(self.top_row_frame, text="Earnings Chart", padding=5, bootstyle=INFO)
        self.weekly_earnings_frame.grid(row=0, column=1, sticky="nsew")
        
        # Add time period selection
        time_period_frame = ttk.Frame(self.weekly_earnings_frame)
        time_period_frame.pack(side=TOP, pady=(0, 5), fill=X)
        ttk.Label(time_period_frame, text="Time Period:", bootstyle="info").pack(side=LEFT, padx=(5, 5))
        for period in ["Weekly", "Monthly", "Yearly", "All-time"]:
            ttk.Radiobutton(time_period_frame, text=period, variable=self.time_period_var, 
                           value=period, command=self.update_earnings_chart, 
                           bootstyle="info-toolbutton").pack(side=LEFT, padx=(2, 2))

        self.fig = Figure(figsize=(6, 2.5), dpi=100)  # Smaller figure
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.weekly_earnings_frame)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(fill=BOTH, expand=True, pady=(0,3))
        self.fig.patch.set_alpha(0)
        self.ax.patch.set_alpha(0)
        style_color = ttk.Style().colors.info # Get a theme color
        self.ax.tick_params(axis='x', colors=style_color)
        self.ax.tick_params(axis='y', colors=style_color)
        for spine in self.ax.spines.values(): spine.set_color(style_color)
        self.ax.yaxis.label.set_color(style_color)
        self.ax.xaxis.label.set_color(style_color)
        self.ax.title.set_color(style_color)

        self.total_weekly_earnings_label = ttk.Label(self.weekly_earnings_frame, text="Total Weekly: 0.0 ETB", font=("Helvetica", 11, "bold"), bootstyle=SECONDARY)
        self.total_weekly_earnings_label.pack(pady=(2,0))

        # Middle Row: Voucher History Section (more compact)
        self.voucher_history_frame = ttk.Labelframe(self.content_area, text="Voucher Information", padding=5, bootstyle="dark")
        self.voucher_history_frame.grid(row=1, column=0, sticky="ew", pady=(0,5))

        # Add an explanatory header - smaller font
        header_text = "Recent voucher recharges showing share percentages and days remaining until expiration"
        header_label = ttk.Label(self.voucher_history_frame, text=header_text, font=("Helvetica", 9), bootstyle="warning-inverse")
        header_label.pack(fill=X, pady=(0, 3))

        # Voucher History Treeview - more compact (height 2 instead of 3)
        voucher_tree_frame = ttk.Frame(self.voucher_history_frame)
        voucher_tree_frame.pack(fill=BOTH, expand=True)
        
        voucher_cols = ("recharge_date", "amount", "share", "days_remaining", "expiry_date")
        voucher_headings = ("Recharge Date", "Amount", "Share %", "Days Left", "Expiry Date")
        voucher_widths = (130, 80, 80, 80, 130)
        
        self.voucher_history_tree = ttk.Treeview(voucher_tree_frame, bootstyle="warning", columns=voucher_cols, show="headings", height=2)
        for i, col in enumerate(voucher_cols):
            self.voucher_history_tree.heading(col, text=voucher_headings[i])
            self.voucher_history_tree.column(col, width=voucher_widths[i], anchor=CENTER)
        
        self.voucher_history_tree.pack(fill=BOTH, expand=True, side=LEFT)
        voucher_scrollbar = ttk.Scrollbar(voucher_tree_frame, orient=VERTICAL, command=self.voucher_history_tree.yview, bootstyle="warning-round")
        self.voucher_history_tree.configure(yscrollcommand=voucher_scrollbar.set)
        voucher_scrollbar.pack(fill=Y, side=RIGHT)

        # Game History Section - takes remaining space
        self.game_history_outer_frame = ttk.Labelframe(self.content_area, text="Game History", padding=5, bootstyle="dark")
        self.game_history_outer_frame.grid(row=2, column=0, sticky="nsew")
        self.game_history_outer_frame.rowconfigure(1, weight=1)  # Make treeview expand
        self.game_history_outer_frame.columnconfigure(0, weight=1)

        # Search/Filter Controls - more compact
        search_frame = ttk.Frame(self.game_history_outer_frame, padding=(0, 3))
        search_frame.grid(row=0, column=0, sticky="ew")
        ttk.Label(search_frame, text="Username:", font=("Helvetica", 9)).pack(side=LEFT, padx=(0,2))
        username_search_entry = ttk.Entry(search_frame, textvariable=self.search_username_var, width=15)
        username_search_entry.pack(side=LEFT, padx=(0,5))
        username_search_entry.bind("<Return>", self.apply_search_filters)
        ttk.Label(search_frame, text="Status:", font=("Helvetica", 9)).pack(side=LEFT, padx=(0,2))
        status_values = ["All Statuses"] + [s.value.title() for s in GameStatus]
        self.status_search_combo = ttk.Combobox(search_frame, textvariable=self.search_status_var, values=status_values, state="readonly", width=12)
        self.status_search_combo.set("All Statuses")
        self.status_search_combo.pack(side=LEFT, padx=(0,5))
        self.status_search_combo.bind("<<ComboboxSelected>>", self.apply_search_filters)
        
        # More compact buttons
        search_button = ttk.Button(search_frame, text="🔍 Search", command=self.apply_search_filters, bootstyle="outline-info", width=8)
        search_button.pack(side=LEFT, padx=3)
        clear_button = ttk.Button(search_frame, text="Clear", command=self.clear_search_filters, bootstyle="outline-secondary", width=6)
        clear_button.pack(side=LEFT, padx=3)

        # Game history tree with scrolling - takes remaining space
        game_history_tree_frame = ttk.Frame(self.game_history_outer_frame)
        game_history_tree_frame.grid(row=1, column=0, sticky="nsew")
        
        cols = ("id", "username", "stake", "prize", "status", "date")
        col_widths = (40, 120, 100, 100, 100, 150)
        self.game_history_tree = ttk.Treeview(game_history_tree_frame, bootstyle="dark", columns=cols, show="headings")
        for i, col in enumerate(cols):
            self.game_history_tree.heading(col, text=col.replace("_", " ").title())
            self.game_history_tree.column(col, width=col_widths[i], anchor=CENTER if col != 'username' else W)
        
        self.game_history_tree.pack(fill=BOTH, expand=True, side=LEFT)
        self.game_history_tree.bind("<Double-1>", self.show_game_details)
        scrollbar = ttk.Scrollbar(game_history_tree_frame, orient=VERTICAL, command=self.game_history_tree.yview, bootstyle="primary-round")
        self.game_history_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(fill=Y, side=RIGHT)

        # Pagination Controls for Game History - at the bottom of game history section
        pagination_frame = ttk.Frame(self.game_history_outer_frame)
        pagination_frame.grid(row=2, column=0, sticky="ew", pady=(3,0))
        
        self.prev_page_button = ttk.Button(pagination_frame, text="<< Prev", command=self.prev_history_page, bootstyle="outline-secondary", width=8)
        self.prev_page_button.pack(side=LEFT, padx=3)
        self.page_info_label = ttk.Label(pagination_frame, text="Page 1 of 1", font=("Helvetica", 9), bootstyle=SECONDARY)
        self.page_info_label.pack(side=LEFT, padx=3)
        self.next_page_button = ttk.Button(pagination_frame, text="Next >>", command=self.next_history_page, bootstyle="outline-secondary", width=8)
        self.next_page_button.pack(side=LEFT, padx=3)

        # Bottom Action Buttons Frame
        action_buttons_frame = ttk.Frame(self, bootstyle="dark", padding=(5,5))
        action_buttons_frame.grid(row=4, column=0, sticky="ew")
        
        # Add export to PDF and print buttons
        if REPORTLAB_AVAILABLE:
            export_pdf_button = ttk.Button(action_buttons_frame, text="📄 Export PDF", 
                                         bootstyle="success", command=self.export_to_pdf, width=12)
            export_pdf_button.pack(side=LEFT, padx=5)
            self.create_tooltip(export_pdf_button, "Export statistics to PDF report")
        
        print_button = ttk.Button(action_buttons_frame, text="🖨️ Print", 
                                bootstyle="light", command=self.print_report, width=8)
        print_button.pack(side=LEFT, padx=5)
        self.create_tooltip(print_button, "Print statistics report")
        
        refresh_button = ttk.Button(action_buttons_frame, text="🔄 Refresh", bootstyle="info", command=self.refresh_data, width=8)
        refresh_button.pack(side=RIGHT, padx=5)
        self.create_tooltip(refresh_button, "Refresh all statistics data")
        
        close_button = ttk.Button(action_buttons_frame, text="✖ Close", bootstyle="danger", command=self.on_close, width=8)
        close_button.pack(side=RIGHT, padx=5)
        self.create_tooltip(close_button, "Close the statistics window")
        
        self.fullscreen = True
        self.fullscreen_button = ttk.Button(action_buttons_frame, text="🗗", bootstyle="secondary", command=self.toggle_fullscreen, width=3)
        self.fullscreen_button.pack(side=RIGHT, padx=5)
        self.create_tooltip(self.fullscreen_button, "Toggle fullscreen mode")
        
        # Status bar at the bottom
        status_frame = ttk.Frame(self, bootstyle="dark")
        status_frame.grid(row=3, column=0, sticky="ew")
        status_frame.columnconfigure(0, weight=1)  # Make the label expand to fill space
        
        # Current time indicator with auto-update
        self.time_label = ttk.Label(status_frame, text="", font=("Helvetica", 9), bootstyle="inverse-dark")
        self.time_label.pack(side=RIGHT, padx=10)
        self.update_time()  # Start the time update
        
        # Status message on the left
        self.status_label = ttk.Label(status_frame, textvariable=self.status_text, font=("Helvetica", 9), bootstyle="inverse-dark")
        self.status_label.pack(side=LEFT, padx=10)
        
        # Add a separation line
        ttk.Separator(status_frame, orient="horizontal").pack(side=TOP, fill=X)

    def _draw_wow_bingo_header(self):
        import tkinter.font as tkFont
        header_height = 80  # Reduced from 120px to 80px
        if self.header_canvas:
            width = self.header_canvas.winfo_width()
            self.header_canvas.delete("all")
        else:
            width = 1280
        header_canvas = self.header_canvas or ttk.Canvas(self, height=header_height, highlightthickness=0)
        # Draw dark blue gradient background
        for i in range(header_height):
            r = int(20 + (40-20) * (i/header_height))
            g = int(30 + (60-30) * (i/header_height))
            b = int(50 + (100-50) * (i/header_height))
            color = f'#{r:02x}{g:02x}{b:02x}'
            header_canvas.create_line(0, i, width, i, fill=color)
        
        # Responsive font sizes
        wow_font = tkFont.Font(family="Impact", size=max(20, int(width/45)), weight="bold")
        bingo_font = tkFont.Font(family="Impact", size=max(20, int(width/45)), weight="bold")
        
        wow_colors = ["#FF3C00", "#FFD700", "#0064FF"]
        bingo_colors = ["#1E64FF", "#00B43C", "#1E64FF", "#FF3C00", "#FFD700"]
        x = 30  # Less left margin
        y = 15  # Less top margin
        for i, letter in enumerate("WOW"):
            header_canvas.create_text(x+2, y+2, text=letter, font=wow_font, fill="#222222", anchor="nw")
            header_canvas.create_text(x, y, text=letter, font=wow_font, fill=wow_colors[i], anchor="nw")
            x += wow_font.measure(letter) * 0.82
        header_canvas.create_text(x, y, text="-", font=wow_font, fill="#FFFFFF", anchor="nw")
        x += wow_font.measure("-") * 0.8
        x += 5
        for i, letter in enumerate("BINGO"):
            color = bingo_colors[i]
            header_canvas.create_text(x+2, y+4, text=letter, font=bingo_font, fill="#222222", anchor="nw")
            header_canvas.create_text(x, y+2, text=letter, font=bingo_font, fill=color, anchor="nw")
            for j in range(2):  # Reduced stripes from 3 to 2
                stripe_x = x + 2 + j * (bingo_font.measure(letter) * 0.2)
                stripe_y = y + 22 + j*2  # Reduced spacing
                stripe_w = bingo_font.measure(letter) * 0.8
                stripe_h = 4  # Smaller stripes
                stripe_color = color if j == 0 else self._lighter_color(color, 0.2 * j)
                header_canvas.create_rectangle(stripe_x, stripe_y, stripe_x+stripe_w, stripe_y+stripe_h, fill=stripe_color, outline="")
            x += bingo_font.measure(letter) * 0.82
        
        # Smaller stats text
        header_canvas.create_text(width//2, 60, text="STATISTICS", font=("Arial", max(12, int(width/60)), "bold"), fill="#F8F8F8", anchor="n")
        header_canvas.create_line(0, header_height-2, width, header_height-2, fill="#3A5A7A", width=2)

    def _lighter_color(self, hex_color, factor):
        # Lighten a hex color by a factor (0-1)
        hex_color = hex_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        r = int(r + (255 - r) * factor)
        g = int(g + (255 - g) * factor)
        b = int(b + (255 - b) * factor)
        return f'#{r:02x}{g:02x}{b:02x}'

    def load_data_and_populate_ui(self):
        self.set_status("Loading data...")
        try:
            print(f"Loading data from database: {STATS_DB_URL}")
            
            # Check database path exists
            db_path = os.path.join(DATA_DIR, 'stats.db')
            if os.path.exists(db_path):
                print(f"Database file exists at {db_path}, size: {os.path.getsize(db_path)} bytes")
            else:
                print(f"WARNING: Database file does not exist at {db_path}")
                
            # Count records in each table for diagnostics
            try:
                game_count = self.db_session.query(GameHistory).count()
                daily_stats_count = self.db_session.query(DailyStat).count()
                summary_count = self.db_session.query(OverallSummary).count()
                print(f"Database record counts - Games: {game_count}, Daily Stats: {daily_stats_count}, Summary: {summary_count}")
            except Exception as count_e:
                print(f"Error counting records: {count_e}")
            
            # Load data for each section with individual error handling
            try:
                self.load_summary_data()
            except Exception as e:
                print(f"Error loading summary data: {e}")
                import traceback
                traceback.print_exc()
                
            try:
                self.load_earnings_chart()
            except Exception as e:
                print(f"Error loading earnings chart: {e}")
                import traceback
                traceback.print_exc()
                
            try:
                self.load_voucher_history()
            except Exception as e:
                print(f"Error loading voucher history: {e}")
                import traceback
                traceback.print_exc()
                
            try:
                self.load_game_history()
            except Exception as e:
                print(f"Error loading game history: {e}")
                import traceback
                traceback.print_exc()
                
            self.set_status("Data loaded successfully")
        except Exception as e:
            self.set_status(f"Error loading data: {str(e)}", is_error=True)
            print(f"Error in load_data_and_populate_ui: {e}")
            import traceback
            traceback.print_exc()

    def load_summary_data(self):
        try:
            summary = self.db_session.query(OverallSummary).order_by(OverallSummary.last_updated.desc()).first()
            if summary:
                self.summary_labels["total_earning"].config(text=f"{summary.total_earnings:,.1f} ETB")
                self.summary_labels["daily_games"].config(text=f"{summary.current_daily_games}")
                self.summary_labels["daily_earning"].config(text=f"{summary.current_daily_earnings:,.1f} ETB")
                self.summary_labels["wallet_balance"].config(text=f"{summary.wallet_balance:,.1f} ETB")
            else:
                print("WARNING: No summary data found in database. Attempting to recover from thread_safe_db...")
                self.attempt_data_recovery()
        except Exception as e:
            print(f"Error loading summary data: {e}")
            self.attempt_data_recovery()
            
    def attempt_data_recovery(self):
        """Attempt to recover data from thread_safe_db if SQLAlchemy models don't provide data."""
        try:
            print("Attempting to recover data from thread_safe_db...")
            # Try to import thread_safe_db
            try:
                import thread_safe_db
                print("Successfully imported thread_safe_db for recovery")
                
                # Get summary stats from thread_safe_db
                summary_stats = thread_safe_db.get_summary_stats()
                print(f"Retrieved summary stats from thread_safe_db: {summary_stats}")
                
                if summary_stats:
                    # Update UI with recovered data
                    self.summary_labels["total_earning"].config(text=f"{summary_stats['total_earnings']:,.1f} ETB")
                    self.summary_labels["daily_games"].config(text=f"{summary_stats['daily_games']}")
                    self.summary_labels["daily_earning"].config(text=f"{summary_stats['daily_earnings']:,.1f} ETB")
                    self.summary_labels["wallet_balance"].config(text=f"{summary_stats['wallet_balance']:,.1f} ETB")
                    print("Updated UI with recovered data from thread_safe_db")
                    
                    # Try to create/update OverallSummary record
                    try:
                        # Check if there's an existing record
                        summary = self.db_session.query(OverallSummary).first()
                        if summary:
                            # Update existing record
                            summary.total_earnings = summary_stats['total_earnings']
                            summary.current_daily_games = summary_stats['daily_games']
                            summary.current_daily_earnings = summary_stats['daily_earnings']
                            summary.wallet_balance = summary_stats['wallet_balance']
                            summary.last_updated = datetime.datetime.utcnow()
                        else:
                            # Create new record
                            new_summary = OverallSummary(
                                total_earnings=summary_stats['total_earnings'],
                                current_daily_games=summary_stats['daily_games'],
                                current_daily_earnings=summary_stats['daily_earnings'],
                                wallet_balance=summary_stats['wallet_balance']
                            )
                            self.db_session.add(new_summary)
                        
                        # Commit changes
                        self.db_session.commit()
                        print("Updated OverallSummary record with recovered data")
                    except Exception as db_e:
                        print(f"Error updating database with recovered data: {db_e}")
                else:
                    print("No data recovered from thread_safe_db")
            except ImportError:
                print("thread_safe_db module not available for recovery")
            
        except Exception as e:
            print(f"Error in data recovery attempt: {e}")
            import traceback
            traceback.print_exc()
            
            # Set UI elements to indicate error
            for key in self.summary_labels:
                self.summary_labels[key].config(text="Error")

    def load_earnings_chart(self):
        try:
            time_period = self.time_period_var.get()
            today = datetime.date.today()
            
            # Define the date range based on selected time period
            if time_period == "Weekly":
                start_date = today - datetime.timedelta(days=6)
                group_by = "day"
                date_format = "%a"  # Day abbreviation
                chart_title = "Weekly Earnings"
            elif time_period == "Monthly":
                start_date = today.replace(day=1)  # First day of current month
                days_in_month = (today.replace(month=today.month % 12 + 1, day=1) - 
                              datetime.timedelta(days=1)).day
                # Get date from 30 days ago or first of month, whichever is later
                start_date = max(start_date, today - datetime.timedelta(days=29))
                group_by = "day"
                date_format = "%d"  # Day of month
                chart_title = "Monthly Earnings"
            elif time_period == "Yearly":
                start_date = today.replace(month=1, day=1)  # First day of current year
                group_by = "month"
                date_format = "%b"  # Month abbreviation
                chart_title = "Yearly Earnings"
            else:  # All-time
                # Get the earliest date in the database
                earliest_record = self.db_session.query(func.min(DailyStat.date)).scalar()
                start_date = earliest_record if earliest_record else today - datetime.timedelta(days=365)
                group_by = "month"
                date_format = "%b %Y"  # Month and year
                chart_title = "All-time Earnings"
            
            # Get daily stats within the date range
            query = (
                self.db_session.query(DailyStat.date, DailyStat.earnings)
                .filter(DailyStat.date >= start_date, DailyStat.date <= today)
                .order_by(DailyStat.date)
            )
            
            # Execute the query
            stats_data = query.all()
            
            # Process the data for the chart
            if group_by == "day":
                # For weekly and monthly, show daily data
                date_earnings_map = {}
                for stat_date, earnings_val in stats_data:
                    date_obj = stat_date
                    if isinstance(date_obj, datetime.datetime):
                        date_obj = date_obj.date()
                    date_earnings_map[date_obj] = earnings_val or 0.0
                
                # Create lists of dates and earnings
                dates = []
                earnings = []
                
                # Fill in dates from start_date to today
                current_date = start_date
                while current_date <= today:
                    dates.append(current_date)
                    earnings.append(date_earnings_map.get(current_date, 0.0))
                    current_date += datetime.timedelta(days=1)
                
                # Create labels for x-axis
                labels = [date_obj.strftime(date_format) for date_obj in dates]
                
            elif group_by == "month":
                # For yearly and all-time, aggregate by month
                month_earnings_map = {}
                for stat_date, earnings_val in stats_data:
                    date_obj = stat_date
                    if isinstance(date_obj, datetime.datetime):
                        date_obj = date_obj.date()
                    
                    # Create month key (year-month)
                    month_key = date_obj.replace(day=1)
                    
                    # Aggregate earnings by month
                    month_earnings_map[month_key] = month_earnings_map.get(month_key, 0.0) + (earnings_val or 0.0)
                
                # Sort months
                sorted_months = sorted(month_earnings_map.keys())
                
                # Create lists of dates and earnings
                dates = sorted_months
                earnings = [month_earnings_map[month] for month in sorted_months]
                
                # Create labels for x-axis
                labels = [date_obj.strftime(date_format) for date_obj in dates]
            
            # Update chart title
            self.weekly_earnings_frame.configure(text=chart_title)
            
            # Plot the data
            self.ax.clear()
            theme_color = ttk.Style().colors.info
            bars = self.ax.bar(labels, earnings, color=theme_color)
            self.ax.set_ylabel("Earnings (ETB)", color='gray')
            self.fig.tight_layout()
            
            self.ax.spines['top'].set_visible(False)
            self.ax.spines['right'].set_visible(False)
            self.ax.yaxis.grid(True, linestyle='--', alpha=0.7, color='gray')
            
            # Add labels to bars
            max_earn = max(earnings) if earnings else 0
            for bar in bars:
                yval = bar.get_height()
                self.ax.text(bar.get_x() + bar.get_width()/2.0, 
                            yval + 0.02 * max_earn if max_earn > 0 else 0.1, 
                            f'{yval:.1f}', ha='center', va='bottom', color='gray', fontsize=8)
            
            self.ax.set_ylim(top=max_earn * 1.1 if max_earn > 0 else 1)
            
            # Adjust x-axis labels if there are too many
            if len(labels) > 12:
                for label in self.ax.get_xticklabels():
                    label.set_rotation(45)
                    label.set_ha('right')
            
            self.canvas.draw()
            
            # Update the total earnings label
            total_earnings = sum(earnings)
            period_text = time_period.lower()
            self.total_weekly_earnings_label.config(text=f"Total {period_text}: {total_earnings:,.1f} ETB")
            
        except Exception as e:
            print(f"Error loading earnings chart: {e}")
            self.ax.clear()
            self.ax.text(0.5, 0.5, "Error loading chart data", ha='center', va='center', 
                        transform=self.ax.transAxes, color='red')
            self.canvas.draw()

    def load_voucher_history(self):
        """Load voucher history data into the treeview."""
        try:
            # Clear existing entries
            for item in self.voucher_history_tree.get_children():
                self.voucher_history_tree.delete(item)
                
            # Get voucher history from voucher manager
            voucher_history = self.voucher_manager.get_voucher_history(limit=5)
            
            # Current time for days remaining calculation
            current_time = time.time()
            
            # Add entries to the treeview
            for voucher in voucher_history:
                # Calculate days remaining - handle non-expiring vouchers (expiry=0)
                days_remaining = "Never Expires"
                expiry_date = "No Expiration"
                
                if voucher['expiry'] != 0:
                    # Handle expiry timestamp to calculate days remaining
                    expiry_timestamp = voucher['expiry']
                    if isinstance(expiry_timestamp, str):
                        # If already a string, use it directly
                        expiry_date = expiry_timestamp
                        try:
                            # Try to parse the date string to calculate days remaining
                            from datetime import datetime
                            expiry_dt = datetime.strptime(expiry_timestamp, '%Y-%m-%d')
                            seconds_remaining = (expiry_dt - datetime.now()).total_seconds()
                            if seconds_remaining > 0:
                                days_remaining = f"{seconds_remaining // 86400:.0f}"
                            else:
                                days_remaining = "Expired"
                        except:
                            days_remaining = "Unknown"
                    else:
                        # If it's a timestamp, calculate days and format the date
                        seconds_remaining = expiry_timestamp - current_time
                        if seconds_remaining > 0:
                            days_remaining = f"{seconds_remaining // 86400:.0f}"
                        else:
                            days_remaining = "Expired"
                        
                        # Format the expiry date from timestamp
                        from datetime import datetime
                        expiry_date = datetime.fromtimestamp(expiry_timestamp).strftime('%Y-%m-%d')
                
                # Add the entry to the treeview
                self.voucher_history_tree.insert("", 0, values=(
                    voucher['redeemed_at'],  # Recharge date
                    f"{voucher['amount']} credits",  # Amount
                    f"{voucher['share']}%",  # Share percentage
                    days_remaining,  # Days remaining
                    expiry_date  # Expiry date
                ))
                
        except Exception as e:
            print(f"Error loading voucher history: {e}")
            import traceback
            traceback.print_exc()

    def load_game_history(self, page_to_load=None, search_username=None, search_status=None):
        # Show loading indicator
        try:
            # Create loading indicator in the game history tree if not already exists
            if hasattr(self, 'game_history_tree'):
                for item in self.game_history_tree.get_children():
                    self.game_history_tree.delete(item)
                self.game_history_tree.insert("", END, values=("Loading...", "", "", "", "", ""))
                self.game_history_tree.update()  # Force update to show loading message
                
            # Fix enum issues in database first
            try:
                self.repair_database_status_values()
            except Exception as repair_e:
                print(f"Warning: Could not repair database values: {repair_e}")
                
            # Continue with normal loading
            if page_to_load is not None:
                self.current_history_page = page_to_load
            else:
                self.current_history_page = 1
                
            username_filter = search_username if search_username is not None else self.search_username_var.get()
            status_filter_str = search_status if search_status is not None else self.search_status_var.get()
            
            # Use direct SQL for querying to avoid enum issues
            if status_filter_str and status_filter_str != "All Statuses":
                # Get normalized status for comparison
                try:
                    status_enum = GameStatus.from_string(status_filter_str)
                    normalized_status = status_enum.value.lower()
                except Exception:
                    normalized_status = None
            else:
                normalized_status = None
                
            # Build base SQL query
            if username_filter and normalized_status:
                base_sql = f"SELECT id, username, stake, total_prize, status, date_time FROM game_history WHERE username LIKE '%{username_filter}%' AND LOWER(status) = '{normalized_status}'"
                count_sql = f"SELECT COUNT(*) FROM game_history WHERE username LIKE '%{username_filter}%' AND LOWER(status) = '{normalized_status}'"
            elif username_filter:
                base_sql = f"SELECT id, username, stake, total_prize, status, date_time FROM game_history WHERE username LIKE '%{username_filter}%'"
                count_sql = f"SELECT COUNT(*) FROM game_history WHERE username LIKE '%{username_filter}%'"
            elif normalized_status:
                base_sql = f"SELECT id, username, stake, total_prize, status, date_time FROM game_history WHERE LOWER(status) = '{normalized_status}'"
                count_sql = f"SELECT COUNT(*) FROM game_history WHERE LOWER(status) = '{normalized_status}'"
            else:
                base_sql = "SELECT id, username, stake, total_prize, status, date_time FROM game_history"
                count_sql = "SELECT COUNT(*) FROM game_history"
                
            # Execute count query to get pagination info
            total_items = self.db_session.execute(text(count_sql)).scalar()
            self.total_history_pages = math.ceil(total_items / self.history_page_size)
            if self.total_history_pages == 0:
                self.total_history_pages = 1
            if self.current_history_page > self.total_history_pages:
                self.current_history_page = self.total_history_pages
                
            # Calculate offset for pagination
            offset = (self.current_history_page - 1) * self.history_page_size
            
            # Execute main query with pagination
            query_sql = f"{base_sql} ORDER BY date_time DESC LIMIT {self.history_page_size} OFFSET {offset}"
            history_rows = self.db_session.execute(text(query_sql)).fetchall()
            
            # Remove loading indicator
            for item in self.game_history_tree.get_children():
                self.game_history_tree.delete(item)
                
            # Add actual data
            for row in history_rows:
                id, username, stake, total_prize, status, date_time = row
                
                # Handle status display safely
                try:
                    if status:
                        enum_status = GameStatus.from_string(status)
                        status_display = enum_status.value.title()
                    else:
                        status_display = "N/A"
                except Exception:
                    status_display = str(status).title() if status else "N/A"
                
                # Format numeric values
                stake_display = f"{stake:,.1f} ETB" if stake else "0.0 ETB"
                prize_display = f"{total_prize:,.1f} ETB" if total_prize else "0.0 ETB"
                
                # Format date - handle both datetime objects and strings
                if date_time:
                    if isinstance(date_time, str):
                        # If it's a string, try to parse it
                        try:
                            # Try ISO format first (YYYY-MM-DD HH:MM:SS)
                            parsed_date = datetime.datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S')
                            date_display = parsed_date.strftime("%Y-%m-%d %H:%M")
                        except ValueError:
                            # If that fails, try other common formats
                            try:
                                parsed_date = datetime.datetime.strptime(date_time, '%Y-%m-%d')
                                date_display = parsed_date.strftime("%Y-%m-%d %H:%M")
                            except ValueError:
                                # If all parsing fails, just use the string
                                date_display = date_time
                    else:
                        # It's a datetime object
                        date_display = date_time.strftime("%Y-%m-%d %H:%M")
                else:
                    date_display = "Unknown"
                
                self.game_history_tree.insert("", END, iid=str(id), values=(
                    id,
                    username,
                    stake_display,
                    prize_display,
                    status_display,
                    date_display
                ))
                
            # If no entries found, show a message
            if not history_rows:
                self.game_history_tree.insert("", END, values=("No records found", "", "", "", "", ""))
                
            self.update_pagination_controls()
            
        except Exception as e:
            print(f"Error loading game history: {e}")
            import traceback
            traceback.print_exc()
            self.page_info_label.config(text="Error loading history")
            # Show error in tree
            if hasattr(self, 'game_history_tree'):
                for item in self.game_history_tree.get_children():
                    self.game_history_tree.delete(item)
                self.game_history_tree.insert("", END, values=(f"Error: {str(e)}", "", "", "", "", ""))

    def update_pagination_controls(self):
        self.page_info_label.config(text=f"Page {self.current_history_page} of {self.total_history_pages}")
        if self.current_history_page == 1:
            self.prev_page_button.config(state=DISABLED)
        else:
            self.prev_page_button.config(state=NORMAL)
        if self.current_history_page >= self.total_history_pages:
            self.next_page_button.config(state=DISABLED)
        else:
            self.next_page_button.config(state=NORMAL)

    def prev_history_page(self):
        if self.current_history_page > 1:
            self.load_game_history(self.current_history_page - 1)

    def next_history_page(self):
        if self.current_history_page < self.total_history_pages:
            self.load_game_history(self.current_history_page + 1)
            
    def show_game_details(self, event):
        selected_item_iid = self.game_history_tree.focus() # Get IID of focused item
        if not selected_item_iid:
            return

        try:
            game_id = int(selected_item_iid) # IID was set to game_entry.id
            
            # Use direct SQL to avoid enum issues
            sql_query = f"SELECT id, username, house, stake, players, total_calls, commission_percent, fee, total_prize, date_time, status FROM game_history WHERE id = {game_id}"
            result = self.db_session.execute(text(sql_query)).fetchone()
            
            if result:
                # Convert row to a dictionary
                game_data = {
                    "id": result[0],
                    "username": result[1],
                    "house": result[2],
                    "stake": result[3],
                    "players": result[4],
                    "total_calls": result[5],
                    "commission_percent": result[6],
                    "fee": result[7],
                    "total_prize": result[8],
                    "date_time": result[9],
                    "status": result[10]
                }
                
                # Process date_time if it's a string
                if isinstance(game_data["date_time"], str):
                    try:
                        game_data["date_time"] = datetime.datetime.strptime(game_data["date_time"], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            game_data["date_time"] = datetime.datetime.strptime(game_data["date_time"], '%Y-%m-%d')
                        except ValueError:
                            # Keep it as string if parsing fails
                            pass
                
                # Create a temporary GameHistory object for display
                class TempGameHistory:
                    def __init__(self, data):
                        self.id = data["id"]
                        self.username = data["username"]
                        self.house = data["house"]
                        self.stake = data["stake"]
                        self.players = data["players"]
                        self.total_calls = data["total_calls"]
                        self.commission_percent = data["commission_percent"]
                        self.fee = data["fee"]
                        self.total_prize = data["total_prize"]
                        self.date_time = data["date_time"]
                        
                        # Convert status safely
                        raw_status = data["status"]
                        try:
                            self.status = GameStatus.from_string(raw_status) if raw_status else None
                        except Exception:
                            # Create an ad-hoc status object with title() method
                            class TempStatus:
                                def __init__(self, value):
                                    self.value = value
                            self.status = TempStatus(str(raw_status))
                    
                    @property
                    def stake_etb(self):
                        return f"{self.stake:,.1f} ETB" if self.stake else "0.0 ETB"
                    
                    @property
                    def total_prize_etb(self):
                        return f"{self.total_prize:,.1f} ETB" if self.total_prize else "0.0 ETB"
                    
                    @property
                    def fee_etb(self):
                        return f"{self.fee:,.1f} ETB" if self.fee else "0.0 ETB"
                    
                    @property
                    def commission_display(self):
                        return f"{self.commission_percent:.1f}%" if self.commission_percent else "0.0%"

                    def __str__(self):
                        # Better string representation for debugging
                        return f"TempGameHistory(id={self.id}, user={self.username})"
                
                # Create temporary object
                temp_game = TempGameHistory(game_data)
                
                # Show dialog
                GameDetailDialog(self.master, temp_game)
            else:
                Messagebox.show_error(f"Could not find game with ID: {game_id}", "Error")
        except ValueError:
            Messagebox.show_error("Invalid game ID selected.", "Error")
        except Exception as e:
            print(f"Error showing game details: {e}")
            import traceback
            traceback.print_exc()
            Messagebox.show_error(f"Error retrieving game details: {e}", "Error")

    def refresh_data(self):
        self.set_status("Refreshing data...")
        print("Refresh button clicked. Reloading data...")
        self.sync_wallet_balance()  # Sync wallet balance before loading data
        self.clear_search_filters() # Also clear search filters on full refresh
        
        # Refresh the session to ensure we get the latest data
        self.refresh_session()
        
        self.load_data_and_populate_ui() # This reloads all sections
        
        toast = ToastNotification(
            title="Data Refreshed",
            message="All statistics have been updated.",
            duration=3000,
            bootstyle=SUCCESS,
            position=(50, 50, 'se') 
        )
        toast.show_toast()
        self.set_status("Data refreshed successfully")

    def show_recharge_dialog(self):
        voucher_code = Querybox.get_string(prompt="Enter voucher code:", title="Recharge Wallet", initialvalue="")
        if not voucher_code:
            return  # User cancelled
        
        # Clean up the voucher code
        voucher_code = voucher_code.strip().upper()
        
        self.set_status(f"Validating voucher: {voucher_code}...")
        
        try:
            # Validate and redeem the voucher
            result = self.voucher_manager.validate_voucher(voucher_code)
            
            if result["success"]:
                # Sync wallet balance from voucher manager
                self.sync_wallet_balance()
                
                # Get the current balance
                current_credits = self.voucher_manager.get_current_credits()
                
                # Determine expiry information
                expiry_info = ""
                if "expiry" in result and result["expiry"] != 0:
                    from datetime import datetime
                    expiry_date = datetime.fromtimestamp(result["expiry"]).strftime('%Y-%m-%d')
                    days_left = max(0, int((result["expiry"] - time.time()) / 86400))
                    expiry_info = f"\nExpires: {expiry_date} ({days_left} days left)"
                
                # Create a more detailed success message
                message = (
                    f"Added {result['amount']} credits!\n"
                    f"Share: {result['share']}%{expiry_info}\n"
                    f"Balance: {current_credits} credits"
                )
                
                # Update the voucher history display
                self.load_voucher_history()
                
                # Show toast notification for success
                toast = ToastNotification(
                    title="Voucher Redeemed Successfully",
                    message=message,
                    duration=3000,
                    bootstyle=SUCCESS,
                    position=(50, 50, 'se') 
                )
                toast.show_toast()
                self.set_status(f"Voucher redeemed successfully. Added {result['amount']} credits.")
                
            else:
                # Show error message
                Messagebox.show_error(result["message"], "Voucher Invalid")
                # Show error toast
                toast = ToastNotification(
                    title="Voucher Invalid",
                    message=result["message"],
                    duration=3000,
                    bootstyle=DANGER,
                    position=(50, 50, 'se') 
                )
                toast.show_toast()
                self.set_status(f"Voucher invalid: {result['message']}", is_error=True)
                
        except Exception as e:
            Messagebox.show_error(f"Error processing voucher: {e}", "Recharge Error")
            self.db_session.rollback()
            self.set_status(f"Error processing voucher: {str(e)}", is_error=True)

    def navigate_to_game(self):
        try:
            from game_page import show_game_page
            self.on_close()  # Close stats window
            show_game_page()  # No argument!
        except ImportError:
            Messagebox.show_info("Game page is not implemented yet.", "Navigation")
        except Exception as e:
            Messagebox.show_error(f"Error navigating to Game page: {e}", "Navigation Error")

    def navigate_to_stats(self):
        # Already on stats page, could refresh or do nothing
        print("Already on Stats screen. Refreshing...")
        self.refresh_data()

    def navigate_to_settings(self):
        try:
            from settings_page import show_settings_page
            self.on_close()  # Close stats window
            show_settings_page()  # No argument!
        except ImportError:
            Messagebox.show_info("Settings page is not implemented yet.", "Navigation")
        except Exception as e:
            Messagebox.show_error(f"Error navigating to Settings page: {e}", "Navigation Error")

    def show_help_dialog(self):
        help_text = ("WOW Bingo Statistics Page\n\n"+
                     "- View your overall earnings, daily activity, and wallet balance.\n"+
                     "- Analyze weekly earnings trends with an interactive chart.\n"+
                     "- See your voucher history with recharge dates, share percentages, and days remaining.\n"+
                     "- Filter game history by username and/or status.\n"+
                     "- Browse your complete game history with pagination.\n"+
                     "- Double-click a game history entry for detailed information.\n"+
                     "- Use 'Recharge Wallet' to add funds with voucher codes.\n"+
                     "- Credits are automatically deducted after each game based on usage.\n"+
                     "- Click 'Refresh All Data' to update all displayed statistics and clear filters.")
        Messagebox.ok(help_text, "Help - Stats Page")

    def on_close(self):
        if self.db_session:
            self.db_session.close()
        self.master.destroy()

    def apply_search_filters(self, event=None): # event=None for button click
        self.load_game_history(page_to_load=1) # Reset to page 1 on new search

    def clear_search_filters(self):
        self.search_username_var.set("")
        self.status_search_combo.set("All Statuses")
        self.load_game_history(page_to_load=1) # Reload with cleared filters

    def toggle_fullscreen(self):
        self.fullscreen = not self.fullscreen
        self.master.attributes('-fullscreen', self.fullscreen)
        if self.fullscreen:
            self.fullscreen_button.config(text="🗗")
        else:
            self.fullscreen_button.config(text="⛶")

    def update_time(self):
        """Update the time display in the status bar."""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.master.after(1000, self.update_time)  # Update every second

    def set_status(self, message, is_error=False):
        """Update the status bar with a message."""
        self.status_text.set(message)
        if is_error:
            self.status_label.configure(bootstyle="danger")
        else:
            self.status_label.configure(bootstyle="default")
            
    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget."""
        # Simple alternative to tooltips without requiring additional libraries
        def enter(event):
            self.tooltip = ttk.Label(self.master, text=text, bootstyle="secondary-inverse", padding=5)
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25
            self.tooltip.place(x=x, y=y)
            
        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()
                
        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def update_earnings_chart(self):
        """Update the earnings chart when the time period selection changes."""
        self.load_earnings_chart()

    def schedule_daily_reset(self):
        """Schedule the daily reset of statistics."""
        # Get current time
        now = datetime.datetime.now()
        
        # Calculate time until midnight
        midnight = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        time_until_midnight = (midnight - now).total_seconds() * 1000  # Convert to milliseconds
        
        # Schedule reset at midnight
        self.master.after(int(time_until_midnight), self.reset_daily_stats)
        
    def reset_daily_stats(self):
        """Reset daily statistics at midnight."""
        try:
            # Reset daily stats in the database
            summary = self.db_session.query(OverallSummary).first()
            if summary:
                summary.current_daily_games = 0
                summary.current_daily_earnings = 0
                summary.last_updated = datetime.datetime.utcnow()
                self.db_session.commit()
            
            # Reload UI
            self.load_summary_data()
            
            print("Daily statistics reset at midnight")
        except Exception as e:
            print(f"Error resetting daily statistics: {e}")
        
        # Schedule next reset
        self.schedule_daily_reset()

    def check_daily_reset(self):
        """Check if we need to reset daily stats (if it's a new day)."""
        # Get the current date
        now = datetime.datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        
        # Get the last reset date
        try:
            # Query the latest statistics
            summary = self.db_session.query(OverallSummary).first()
            if summary and summary.last_updated:
                last_updated = summary.last_updated
                last_updated_date = last_updated.strftime("%Y-%m-%d")
                
                # If it's a new day, reset daily stats
                if last_updated_date != current_date:
                    print(f"New day detected. Last update: {last_updated_date}, Current: {current_date}")
                    self.reset_daily_stats()
            else:
                # No summary record exists yet, create one
                print("No summary record found, creating initial record")
                self.reset_daily_stats()
                
            # Also run verification and repair
            self.verify_and_repair_game_data()
            
        except Exception as e:
            print(f"Error checking daily reset: {e}")
            
    def verify_and_repair_game_data(self):
        """Verify game history data and repair if needed."""
        try:
            print("Running game data verification and repair...")
            
            # First repair any inconsistent status values
            self.repair_database_status_values()
            
            # Check if any game history exists
            game_count = self.db_session.query(GameHistory).count()
            print(f"Found {game_count} games in SQLAlchemy database")
            
            # Fix inconsistent data values in existing records
            try:
                # Check for records with zero values that should be non-zero
                zero_value_games = self.db_session.query(GameHistory).filter(
                    or_(
                        GameHistory.stake == 0,
                        GameHistory.players == 0,
                        GameHistory.total_prize == 0
                    )
                ).all()
                
                if zero_value_games:
                    print(f"Found {len(zero_value_games)} games with zero values that need repair")
                    fixed_count = 0
                    
                    for game in zero_value_games:
                        needs_update = False
                        
                        # Fix zero stake values
                        if game.stake == 0 and game.total_prize > 0 and game.players > 0:
                            # Calculate stake from prize and player count
                            game.stake = (game.total_prize / 0.8) / game.players
                            needs_update = True
                            print(f"Fixed zero stake value for game {game.id}: new stake={game.stake}")
                        
                        # Fix zero player values
                        if game.players == 0 and game.total_prize > 0 and game.stake > 0:
                            # Calculate players from prize and stake
                            game.players = int((game.total_prize / 0.8) / game.stake)
                            if game.players == 0:  # Ensure minimum of 1 player
                                game.players = 1
                            needs_update = True
                            print(f"Fixed zero player count for game {game.id}: new players={game.players}")
                        
                        # Fix zero prize values
                        if game.total_prize == 0 and game.stake > 0 and game.players > 0:
                            # Calculate prize from stake and player count
                            game.total_prize = game.stake * game.players * 0.8
                            needs_update = True
                            print(f"Fixed zero prize amount for game {game.id}: new prize={game.total_prize}")
                            
                            # Also update fee if needed
                            if game.fee == 0 and game.commission_percent > 0:
                                game.fee = game.total_prize * (game.commission_percent / 100)
                                print(f"Fixed zero fee amount for game {game.id}: new fee={game.fee}")
                        
                        if needs_update:
                            fixed_count += 1
                    
                    if fixed_count > 0:
                        self.db_session.commit()
                        print(f"Fixed {fixed_count} games with zero values")
            except Exception as repair_e:
                print(f"Error repairing games with zero values: {repair_e}")
                self.db_session.rollback()
                import traceback
                traceback.print_exc()
            
            # If no games found, try to import from thread_safe_db
            if game_count == 0:
                # [Existing code for importing from thread_safe_db]
                try:
                    import thread_safe_db
                    print("Attempting to recover game history from thread_safe_db...")
                    
                    # Get game history from thread_safe_db
                    history, _ = thread_safe_db.get_game_history(0, 100)  # Get up to 100 recent games
                    
                    if history and len(history) > 0:
                        print(f"Found {len(history)} games in thread_safe_db, importing to SQLAlchemy models...")
                        
                        # Import games to SQLAlchemy
                        for game in history:
                            try:
                                # Check if game already exists to avoid duplicates
                                existing_game = self.db_session.query(GameHistory).filter_by(id=game.get('id')).first()
                                if not existing_game:
                                    # Convert status string to enum
                                    status_str = game.get('status', 'pending')
                                    try:
                                        status_enum = GameStatus.from_string(status_str)
                                    except (KeyError, AttributeError):
                                        status_enum = GameStatus.PENDING
                                        
                                    # Convert date_time string to datetime object
                                    date_time_str = game.get('date_time')
                                    try:
                                        date_time = datetime.datetime.strptime(date_time_str, '%Y-%m-%d %H:%M:%S')
                                    except (ValueError, TypeError):
                                        date_time = datetime.datetime.now()
                                    
                                    # Create new game history record
                                    new_game = GameHistory(
                                        username=game.get('username', 'Unknown'),
                                        house=game.get('house', 'Main Hall'),
                                        stake=game.get('stake', 0),
                                        players=game.get('players', 0),
                                        total_calls=game.get('total_calls', 0),
                                        commission_percent=game.get('commission_percent', 20),
                                        fee=game.get('fee', 0),
                                        total_prize=game.get('total_prize', 0),
                                        date_time=date_time,
                                        status=status_enum
                                    )
                                    self.db_session.add(new_game)
                            except Exception as game_e:
                                print(f"Error importing game {game.get('id')}: {game_e}")
                                
                        # Commit all changes
                        self.db_session.commit()
                        print(f"Successfully imported {len(history)} games from thread_safe_db")
                    else:
                        print("No game history found in thread_safe_db")
                        
                except ImportError:
                    print("thread_safe_db module not available for game history recovery")
                except Exception as e:
                    print(f"Error recovering game history: {e}")
                    
            # Also try to update daily stats based on game history
            try:
                # Calculate daily stats from game history
                today = datetime.date.today()
                today_str = today.strftime('%Y-%m-%d')
                
                # Query games played today with direct SQL instead of ORM to avoid enum issues
                sql_query = f"""
                    SELECT COUNT(*) as count, SUM(fee) as fees
                    FROM game_history 
                    WHERE date(date_time) = '{today_str}'
                """
                result = self.db_session.execute(text(sql_query)).fetchone()
                
                if result:
                    games_played = result[0] or 0  # Default to 0 if None
                    total_earnings = result[1] or 0.0  # Default to 0.0 if None
                    
                    print(f"Found {games_played} games for today ({today_str}) with earnings {total_earnings}")
                    
                    # Use SQLite UPSERT syntax to either insert or update
                    upsert_sql = f"""
                        INSERT INTO daily_stats (date, earnings, games_played)
                        VALUES ('{today_str}', {total_earnings}, {games_played})
                        ON CONFLICT(date) 
                        DO UPDATE SET
                            earnings = {total_earnings},
                            games_played = {games_played}
                    """
                    self.db_session.execute(text(upsert_sql))
                    self.db_session.commit()
                    print(f"Updated daily stats: games={games_played}, earnings={total_earnings}")
                    
                    # Update overall summary with direct query to get total earnings
                    summary = self.db_session.query(OverallSummary).first()
                    if summary:
                        # Get total earnings from daily_stats
                        all_earnings_sql = "SELECT SUM(earnings) FROM daily_stats"
                        all_earnings = self.db_session.execute(text(all_earnings_sql)).scalar() or 0
                        
                        if abs(summary.total_earnings - all_earnings) > 0.01 or summary.current_daily_games != games_played or abs(summary.current_daily_earnings - total_earnings) > 0.01:
                            print(f"Updating overall summary: total_earnings={all_earnings}, daily_games={games_played}, daily_earnings={total_earnings}")
                            summary.total_earnings = all_earnings
                            summary.current_daily_games = games_played
                            summary.current_daily_earnings = total_earnings
                            summary.last_updated = datetime.datetime.now()
                            self.db_session.commit()
                
            except Exception as daily_e:
                print(f"Error updating daily stats from game history: {daily_e}")
                import traceback
                traceback.print_exc()
                
        except Exception as e:
            print(f"Error in verify_and_repair_game_data: {e}")
            import traceback
            traceback.print_exc()

    def repair_database_status_values(self):
        """Fix any inconsistent status values in the database."""
        try:
            print("Checking and repairing game status values in the database...")
            
            # Use direct SQL to bypass SQLAlchemy's enum validation
            # This allows us to read raw values regardless of case sensitivity issues
            result = self.db_session.execute(text("SELECT id, status FROM game_history")).fetchall()
            fixed_count = 0
            
            # Create a mapping of lowercase values to correct enum values
            # This ensures we always use the exact case defined in the enum
            enum_value_map = {status.value.lower(): status.value for status in GameStatus}
            
            for game_id, raw_status in result:
                if raw_status:
                    try:
                        raw_lower = raw_status.lower()
                        # Check if the value exists in our enum (case insensitive)
                        if raw_lower in enum_value_map:
                            correct_value = enum_value_map[raw_lower]
                            # If the case doesn't match exactly, fix it
                            if raw_status != correct_value:
                                self.db_session.execute(
                                    text(f"UPDATE game_history SET status = '{correct_value}' WHERE id = {game_id}")
                                )
                                fixed_count += 1
                                print(f"Fixed case sensitivity for status '{raw_status}' to '{correct_value}' for game_id={game_id}")
                        else:
                            # Invalid status value not in enum
                            print(f"Invalid status value found: '{raw_status}' for game_id={game_id}")
                            # Set to a default valid value
                            default_status = GameStatus.PENDING.value
                            self.db_session.execute(
                                text(f"UPDATE game_history SET status = '{default_status}' WHERE id = {game_id}")
                            )
                            fixed_count += 1
                            print(f"Changed invalid status '{raw_status}' to '{default_status}' for game_id={game_id}")
                    except Exception as update_err:
                        print(f"Error updating status for game {game_id}: {update_err}")
            
            if fixed_count > 0:
                self.db_session.commit()
                print(f"Fixed {fixed_count} game status values in the database.")
            else:
                print("No game status values needed repair.")
                
        except Exception as e:
            print(f"Error repairing database status values: {e}")
            import traceback
            traceback.print_exc()
            self.db_session.rollback()

    def export_to_pdf(self):
        """Export statistics to a PDF report."""
        if not REPORTLAB_AVAILABLE:
            Messagebox.show_error("ReportLab library is not installed. Cannot export to PDF.", 
                                "Export Error")
            return

        # Ask user where to save the PDF
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
            title="Save Statistics Report As"
        )
        
        if not filename:
            return  # User cancelled
        
        self.set_status("Generating PDF report...")
        
        try:
            # First repair any inconsistent status values in the database
            self.repair_database_status_values()
            
            # Create the PDF document
            doc = SimpleDocTemplate(
                filename,
                pagesize=A4,
                rightMargin=72, leftMargin=72,
                topMargin=72, bottomMargin=72
            )
            
            # Container for the 'Flowable' objects
            elements = []
            
            # Styles
            styles = getSampleStyleSheet()
            title_style = styles['Heading1']
            heading2_style = styles['Heading2']
            normal_style = styles['Normal']
            
            # Title
            elements.append(Paragraph("WOW Bingo Statistics Report", title_style))
            elements.append(Paragraph(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
                                     normal_style))
            elements.append(Spacer(1, 0.25*inch))
            
            # Summary Section
            elements.append(Paragraph("Summary Statistics", heading2_style))
            
            # Get summary data
            summary = self.db_session.query(OverallSummary).first()
            if summary:
                summary_data = [
                    ["Metric", "Value"],
                    ["Total Earnings", f"{summary.total_earnings:,.2f} ETB"],
                    ["Today's Games", f"{summary.current_daily_games}"],
                    ["Today's Earnings", f"{summary.current_daily_earnings:,.2f} ETB"],
                    ["Wallet Balance", f"{summary.wallet_balance:,.2f} ETB"],
                    ["Last Updated", f"{summary.last_updated.strftime('%Y-%m-%d %H:%M:%S')}"]
                ]
                
                # Create a table with the data
                summary_table = Table(summary_data, colWidths=[doc.width/2.0]*2)
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (1, 0), 12),
                    ('BACKGROUND', (0, 1), (1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(summary_table)
            else:
                elements.append(Paragraph("No summary data available", normal_style))
            
            elements.append(Spacer(1, 0.25*inch))
            
            # Earnings Chart Section
            elements.append(Paragraph(f"{self.time_period_var.get()} Earnings", heading2_style))
            
            # Get chart data (reuse the query from the chart)
            time_period = self.time_period_var.get()
            today = datetime.date.today()
            
            # Define the date range based on selected time period
            if time_period == "Weekly":
                start_date = today - datetime.timedelta(days=6)
                date_format = "%a"
            elif time_period == "Monthly":
                start_date = today.replace(day=1)
                start_date = max(start_date, today - datetime.timedelta(days=29))
                date_format = "%d"
            elif time_period == "Yearly":
                start_date = today.replace(month=1, day=1)
                date_format = "%b"
            else:  # All-time
                earliest_record = self.db_session.query(func.min(DailyStat.date)).scalar()
                start_date = earliest_record if earliest_record else today - datetime.timedelta(days=365)
                date_format = "%b %Y"
            
            # Query for earnings data
            stats_query = (
                self.db_session.query(DailyStat.date, DailyStat.earnings)
                .filter(DailyStat.date >= start_date, DailyStat.date <= today)
                .order_by(DailyStat.date)
                .all()
            )
            
            # Process data for chart and table
            chart_data = []
            table_data = [["Date", "Earnings (ETB)"]]
            
            for stat_date, earnings_val in stats_query:
                date_obj = stat_date
                if isinstance(date_obj, datetime.datetime):
                    date_obj = date_obj.date()
                
                formatted_date = date_obj.strftime("%Y-%m-%d")
                earnings = earnings_val if earnings_val is not None else 0.0
                
                chart_data.append((formatted_date, earnings))
                table_data.append([formatted_date, f"{earnings:,.2f}"])
            
            # If we have data, create a chart and table
            if chart_data:
                # Create table
                earnings_table = Table(table_data, colWidths=[doc.width/2.0]*2)
                earnings_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (1, 0), 12),
                    ('BACKGROUND', (0, 1), (1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (1, 1), (1, -1), 'RIGHT')
                ]))
                elements.append(earnings_table)
                
                # Add total
                total_earnings = sum(earnings for _, earnings in chart_data)
                elements.append(Paragraph(f"Total {time_period} Earnings: {total_earnings:,.2f} ETB", 
                                         styles['Heading3']))
            else:
                elements.append(Paragraph("No earnings data available for the selected period", normal_style))
            
            elements.append(Spacer(1, 0.25*inch))
            
            # Game History Section
            elements.append(Paragraph("Recent Game History", heading2_style))
            
            # Get recent game history (last 15 games) - using direct SQL to avoid enum issues
            sql_query = """
                SELECT id, username, stake, total_prize, status, date_time 
                FROM game_history 
                ORDER BY date_time DESC 
                LIMIT 15
            """
            history_rows = self.db_session.execute(text(sql_query)).fetchall()
            
            if history_rows:
                # Create table data
                history_table_data = [
                    ["ID", "Username", "Stake", "Prize", "Status", "Date/Time"]
                ]
                
                for row in history_rows:
                    id, username, stake, total_prize, status, date_time = row
                    
                    # Format values safely
                    stake_display = f"{stake:,.1f} ETB" if stake else "0.0 ETB"
                    prize_display = f"{total_prize:,.1f} ETB" if total_prize else "0.0 ETB"
                    
                    # Handle status display safely
                    try:
                        if status:
                            enum_status = GameStatus.from_string(status)
                            status_display = enum_status.value.title()
                        else:
                            status_display = "N/A"
                    except Exception:
                        status_display = str(status).title() if status else "N/A"
                    
                    # Format date safely
                    if date_time:
                        if isinstance(date_time, str):
                            try:
                                date_obj = datetime.datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S')
                                date_display = date_obj.strftime("%Y-%m-%d %H:%M")
                            except ValueError:
                                date_display = str(date_time)
                        else:
                            date_display = date_time.strftime("%Y-%m-%d %H:%M")
                    else:
                        date_display = "N/A"
                    
                    # Add row to table
                    history_table_data.append([
                        str(id),
                        username,
                        stake_display,
                        prize_display,
                        status_display,
                        date_display
                    ])
                
                # Create table
                history_table = Table(history_table_data, colWidths=[doc.width/6.0]*6)
                history_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ]))
                elements.append(history_table)
            else:
                elements.append(Paragraph("No game history available", normal_style))
            
            # Build the PDF
            doc.build(elements)
            
            # Show success message
            self.set_status("PDF report generated successfully")
            toast = ToastNotification(
                title="PDF Export Successful",
                message=f"Report saved to {filename}",
                duration=3000,
                bootstyle=SUCCESS,
                position=(50, 50, 'se') 
            )
            toast.show_toast()
            
        except Exception as e:
            print(f"Error generating PDF: {e}")
            import traceback
            traceback.print_exc()
            self.set_status(f"Error generating PDF: {str(e)}", is_error=True)
            Messagebox.show_error(f"Error generating PDF: {str(e)}", "Export Error")

    def print_report(self):
        """Print a statistics report."""
        # Create a new window for print preview
        preview_window = ttk.Toplevel(self.master)
        preview_window.title("Print Preview - Statistics Report")
        preview_window.geometry("800x600")
        preview_window.transient(self.master)  # Make it a modal window
        preview_window.grab_set()
        
        # Create a scrollable canvas for the print preview
        canvas_frame = ttk.Frame(preview_window)
        canvas_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(canvas_frame, orient=VERTICAL)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # Create canvas
        canvas = ttk.Canvas(canvas_frame, yscrollcommand=scrollbar.set, background="white")
        canvas.pack(side=LEFT, fill=BOTH, expand=True)
        
        scrollbar.config(command=canvas.yview)
        
        # Create a frame inside the canvas to hold the report content
        report_frame = ttk.Frame(canvas, padding=20, bootstyle="white")
        
        # Add the report frame to the canvas
        canvas.create_window((0, 0), window=report_frame, anchor="nw")
        
        # Add report content
        self.create_print_report_content(report_frame)
        
        # Update the scrollregion after the report content is created
        report_frame.update_idletasks()
        canvas.config(scrollregion=canvas.bbox("all"))
        
        # Add print and close buttons at the bottom
        button_frame = ttk.Frame(preview_window)
        button_frame.pack(fill=X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="Print", command=lambda: self.send_to_printer(preview_window, report_frame),
                  bootstyle="success").pack(side=RIGHT, padx=5)
        ttk.Button(button_frame, text="Close", command=preview_window.destroy,
                  bootstyle="secondary").pack(side=RIGHT, padx=5)

    def create_print_report_content(self, parent_frame):
        """Create the content for the print report."""
        # First repair any inconsistent status values in the database
        self.repair_database_status_values()
        
        # Report title
        ttk.Label(parent_frame, text="WOW Bingo Statistics Report", 
                 font=("Helvetica", 16, "bold")).pack(anchor="w", pady=(0, 10))
        ttk.Label(parent_frame, text=f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                 font=("Helvetica", 10)).pack(anchor="w", pady=(0, 20))
        
        # Summary Section
        ttk.Label(parent_frame, text="Summary Statistics", 
                 font=("Helvetica", 14, "bold")).pack(anchor="w", pady=(0, 10))
        
        # Get summary data
        summary = self.db_session.query(OverallSummary).first()
        if summary:
            # Create a frame for the summary table
            summary_frame = ttk.Frame(parent_frame)
            summary_frame.pack(fill=X, pady=(0, 20))
            
            # Add summary data in a grid
            metrics = [
                ("Total Earnings:", f"{summary.total_earnings:,.2f} ETB"),
                ("Today's Games:", f"{summary.current_daily_games}"),
                ("Today's Earnings:", f"{summary.current_daily_earnings:,.2f} ETB"),
                ("Wallet Balance:", f"{summary.wallet_balance:,.2f} ETB"),
                ("Last Updated:", f"{summary.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")
            ]
            
            for i, (label_text, value_text) in enumerate(metrics):
                ttk.Label(summary_frame, text=label_text, font=("Helvetica", 10, "bold")).grid(
                    row=i, column=0, sticky="w", padx=(0, 10), pady=2)
                ttk.Label(summary_frame, text=value_text, font=("Helvetica", 10)).grid(
                    row=i, column=1, sticky="w", pady=2)
        else:
            ttk.Label(parent_frame, text="No summary data available").pack(anchor="w", pady=(0, 20))
        
        # Earnings Section
        ttk.Label(parent_frame, text=f"{self.time_period_var.get()} Earnings", 
                 font=("Helvetica", 14, "bold")).pack(anchor="w", pady=(0, 10))
        
        # Get earnings data (similar to PDF export)
        time_period = self.time_period_var.get()
        today = datetime.date.today()
        
        # Define the date range (similar to load_earnings_chart)
        if time_period == "Weekly":
            start_date = today - datetime.timedelta(days=6)
        elif time_period == "Monthly":
            start_date = today.replace(day=1)
            start_date = max(start_date, today - datetime.timedelta(days=29))
        elif time_period == "Yearly":
            start_date = today.replace(month=1, day=1)
        else:  # All-time
            earliest_record = self.db_session.query(func.min(DailyStat.date)).scalar()
            start_date = earliest_record if earliest_record else today - datetime.timedelta(days=365)
        
        # Query for earnings data
        stats_query = (
            self.db_session.query(DailyStat.date, DailyStat.earnings)
            .filter(DailyStat.date >= start_date, DailyStat.date <= today)
            .order_by(DailyStat.date)
            .all()
        )
        
        if stats_query:
            # Create a frame for the earnings table
            earnings_frame = ttk.Frame(parent_frame)
            earnings_frame.pack(fill=X, pady=(0, 20))
            
            # Table headers
            ttk.Label(earnings_frame, text="Date", font=("Helvetica", 10, "bold")).grid(
                row=0, column=0, sticky="w", padx=(0, 20), pady=(0, 5))
            ttk.Label(earnings_frame, text="Earnings (ETB)", font=("Helvetica", 10, "bold")).grid(
                row=0, column=1, sticky="e", pady=(0, 5))
            
            # Add separator after headers
            separator = ttk.Separator(earnings_frame, orient="horizontal")
            separator.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 5))
            
            # Add earnings data rows
            total_earnings = 0
            for i, (stat_date, earnings_val) in enumerate(stats_query):
                date_obj = stat_date
                if isinstance(date_obj, datetime.datetime):
                    date_obj = date_obj.date()
                
                formatted_date = date_obj.strftime("%Y-%m-%d")
                earnings = earnings_val if earnings_val is not None else 0.0
                total_earnings += earnings
                
                ttk.Label(earnings_frame, text=formatted_date, font=("Helvetica", 10)).grid(
                    row=i+2, column=0, sticky="w", padx=(0, 20), pady=2)
                ttk.Label(earnings_frame, text=f"{earnings:,.2f}", font=("Helvetica", 10)).grid(
                    row=i+2, column=1, sticky="e", pady=2)
            
            # Add separator before total
            separator = ttk.Separator(earnings_frame, orient="horizontal")
            separator.grid(row=len(stats_query)+2, column=0, columnspan=2, sticky="ew", pady=5)
            
            # Add total row
            ttk.Label(earnings_frame, text="Total:", font=("Helvetica", 10, "bold")).grid(
                row=len(stats_query)+3, column=0, sticky="w", padx=(0, 20), pady=2)
            ttk.Label(earnings_frame, text=f"{total_earnings:,.2f}", font=("Helvetica", 10, "bold")).grid(
                row=len(stats_query)+3, column=1, sticky="e", pady=2)
        else:
            ttk.Label(parent_frame, text="No earnings data available for the selected period").pack(anchor="w", pady=(0, 20))
        
        # Game History Section
        ttk.Label(parent_frame, text="Recent Game History", 
                 font=("Helvetica", 14, "bold")).pack(anchor="w", pady=(0, 10))
        
        # Get recent game history (last 15 games) - using direct SQL to avoid enum issues
        sql_query = """
            SELECT id, username, stake, total_prize, status, date_time 
            FROM game_history 
            ORDER BY date_time DESC 
            LIMIT 15
        """
        history_rows = self.db_session.execute(text(sql_query)).fetchall()
        
        if history_rows:
            # Create a frame for the history table
            history_frame = ttk.Frame(parent_frame)
            history_frame.pack(fill=X, pady=(0, 20))
            
            # Table headers
            headers = ["ID", "Username", "Stake", "Prize", "Status", "Date/Time"]
            for i, header in enumerate(headers):
                ttk.Label(history_frame, text=header, font=("Helvetica", 10, "bold")).grid(
                    row=0, column=i, sticky="w", padx=(0 if i == 0 else 10, 10), pady=(0, 5))
            
            # Add separator after headers
            separator = ttk.Separator(history_frame, orient="horizontal")
            separator.grid(row=1, column=0, columnspan=len(headers), sticky="ew", pady=(0, 5))
            
            # Add history data rows
            for i, row in enumerate(history_rows):
                id, username, stake, total_prize, status, date_time = row
                
                # Format values safely
                stake_display = f"{stake:,.1f} ETB" if stake else "0.0 ETB"
                prize_display = f"{total_prize:,.1f} ETB" if total_prize else "0.0 ETB"
                
                # Handle status display safely
                try:
                    if status:
                        enum_status = GameStatus.from_string(status)
                        status_display = enum_status.value.title()
                    else:
                        status_display = "N/A"
                except Exception:
                    status_display = str(status).title() if status else "N/A"
                
                # Format date safely
                if date_time:
                    if isinstance(date_time, str):
                        try:
                            date_obj = datetime.datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S')
                            date_display = date_obj.strftime("%Y-%m-%d %H:%M")
                        except ValueError:
                            date_display = str(date_time)
                    else:
                        date_display = date_time.strftime("%Y-%m-%d %H:%M")
                else:
                    date_display = "N/A"
                
                cols = [
                    str(id),
                    username,
                    stake_display,
                    prize_display,
                    status_display,
                    date_display
                ]
                
                for j, col in enumerate(cols):
                    ttk.Label(history_frame, text=col, font=("Helvetica", 10)).grid(
                        row=i+2, column=j, sticky="w", padx=(0 if j == 0 else 10, 10), pady=2)
        else:
            ttk.Label(parent_frame, text="No game history available").pack(anchor="w", pady=(0, 20))

    def send_to_printer(self, preview_window, report_frame):
        """Send the report to the printer."""
        try:
            # Temporarily hide the preview window during printing
            preview_window.withdraw()
            
            # Use the system's print dialog
            import os
            import tempfile
            import sys
            
            if sys.platform.startswith('win'):
                # Windows approach using temporary file
                with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                    temp_filename = temp_file.name
                    
                # Print dialog
                os.startfile(temp_filename, 'print')
            else:
                # For macOS and Linux
                import subprocess
                try:
                    subprocess.call(['lpr', '-P', ''])
                except:
                    Messagebox.show_error("Printing not supported on this platform.", "Print Error")
            
            # Restore the preview window
            preview_window.deiconify()
            
            # Show success message
            self.set_status("Print job sent to printer")
            
        except Exception as e:
            print(f"Error printing: {e}")
            import traceback
            traceback.print_exc()
            preview_window.deiconify()  # Restore window if error
            Messagebox.show_error(f"Error printing: {str(e)}", "Print Error")

    @staticmethod
    def update_game_statistics(game_data):
        """
        Update the statistics database with completed game data.
        This static method can be called from the game engine when a game is completed.
        
        Args:
            game_data: Dictionary containing game data
                - winner_name: Name of the winner
                - winner_cartella: Cartella number of the winner
                - claim_type: Type of claim (e.g., 'Full House', 'First Line')
                - game_duration: Duration of the game in seconds
                - player_count: Number of players in the game
                - prize_amount: Prize amount for this game
                - commission_percentage: Commission percentage for this game
                - called_numbers: List of numbers called during the game
                - is_demo_mode: Boolean indicating if the game was in demo mode
        
        Returns:
            bool: True if successful, False otherwise
        """
        # Import necessary modules for this method
        import os
        import datetime
        import time
        # Access global variables properly
        global DATA_DIR, STATS_DB_URL
        
        # Skip demo mode games
        if game_data.get("is_demo_mode", False):
            print(f"⚠️ SKIPPED: Game was in demo mode - statistics not updated: {game_data}")
            return False
        
        print(f"🔵 STARTING: Recording game statistics to database")
        print(f"🔵 GAME DATA: {game_data}")
        
        # Initialize variables outside the try block so they can be accessed in the finally block
        session = None
        conn = None
        
        try:
            # First, ensure the DATA_DIR exists
            if not os.path.exists(DATA_DIR):
                os.makedirs(DATA_DIR, exist_ok=True)
                print(f"✅ Created data directory at: {DATA_DIR}")
            
            # Verify database path exists and is writable
            db_path = os.path.join(DATA_DIR, 'stats.db')
            if os.path.exists(db_path):
                print(f"✅ Database file exists at: {db_path}, size: {os.path.getsize(db_path)} bytes")
                # Check if file is writable
                try:
                    with open(db_path, 'a') as f:
                        # Try to get an exclusive lock to verify database isn't locked
                        import fcntl
                        try:
                            fcntl.flock(f, fcntl.LOCK_EX | fcntl.LOCK_NB)
                            fcntl.flock(f, fcntl.LOCK_UN)
                            print(f"✅ Database file is writable and not locked")
                        except (IOError, ImportError) as lock_e:
                            # Either lock failed or fcntl not available (Windows)
                            print(f"⚠️ Could not verify lock status: {lock_e}")
                except Exception as perm_e:
                    print(f"❌ CRITICAL ERROR: Database file is not writable: {perm_e}")
                    # Try to fix permissions
                    try:
                        os.chmod(db_path, 0o666)  # rw-rw-rw-
                        print(f"✅ Fixed permissions on database file")
                    except Exception as chmod_e:
                        print(f"❌ Failed to fix permissions: {chmod_e}")
            else:
                print(f"⚠️ Database file does not exist at: {db_path}, will be created")
                
            # Create DB engine with explicit connection string, include check_same_thread=False for SQLite
            print(f"🔵 Creating database engine with URL: {STATS_DB_URL}")
            engine = create_engine(STATS_DB_URL, connect_args={"check_same_thread": False})
            
            # Create tables if they don't exist
            Base.metadata.create_all(bind=engine)
            print(f"✅ Ensured database tables exist")
            
            # Create a session
            Session = sessionmaker(bind=engine)
            session = Session()
            print(f"✅ Created database session")
            
            # Extract data from game_data dict with defaults
            winner_name = game_data.get("winner_name", "Unknown")
            winner_cartella = game_data.get("winner_cartella", 0)
            claim_type = game_data.get("claim_type", "Unknown")
            game_duration = game_data.get("game_duration", 0)
            player_count = game_data.get("player_count", 0)
            
            # Get prize amount - default to 0
            prize_amount = game_data.get("prize_amount", 0)
            # If prize_amount is not provided, try other common key names
            if prize_amount == 0:
                prize_amount = game_data.get("total_prize", 0)
            
            # Get commission percentage - default to 20%
            commission_percentage = game_data.get("commission_percentage", 20)
            
            # Calculate fee from prize and commission
            fee = prize_amount * (commission_percentage / 100)
            print(f"✅ Calculated fee: {fee} from prize: {prize_amount} and commission: {commission_percentage}%")
            
            # Get stake amount - default to 0
            stake_amount = game_data.get("stake", 0)
            
            # If stake is zero, try to calculate it from prize and player count
            if stake_amount == 0 and game_data.get("player_count", 0) > 0 and prize_amount > 0:
                # Calculate approximate stake from prize and player count
                stake_amount = (prize_amount / 0.8) / game_data.get("player_count", 1)
                print(f"✅ Fixed: Calculated stake amount: {stake_amount} from prize: {prize_amount} and player count: {game_data.get('player_count')}")
            
            # Get status string - default to "WON" if winner exists, "COMPLETED" otherwise
            status_str = game_data.get("status", "")
            if not status_str:
                if game_data.get("winner_name", "Unknown") != "Unknown":
                    status_str = "WON"
                else:
                    status_str = "COMPLETED"
            
            # Handle status properly - use the from_string method to convert to enum
            try:
                status_enum = GameStatus.from_string(status_str)
                print(f"✅ Converted status string '{status_str}' to enum: {status_enum}")
            except Exception as status_e:
                print(f"⚠️ WARNING: Could not convert status '{status_str}' to enum: {status_e}")
                status_enum = GameStatus.COMPLETED  # Default to COMPLETED
            
            # Create the game history entry
            print(f"🔵 Creating GameHistory record...")
            try:
                print(f"🔵 Adding GameHistory record using direct SQL...")
                from sqlalchemy import text
                
                # Create the record with direct SQL for better reliability
                sql = text("""
                    INSERT INTO game_history 
                    (username, house, stake, players, total_calls, commission_percent, fee, total_prize, date_time, status)
                    VALUES 
                    (:username, :house, :stake, :players, :total_calls, :commission, :fee, :prize, :date_time, :status)
                """)
                
                # Convert status enum to string for SQL
                status_str = status_enum.value if hasattr(status_enum, 'value') else str(status_enum).lower() if status_enum else "pending"
                
                # Execute the insert
                result = session.execute(sql, {
                    "username": game_data.get("winner_name", "Unknown"),
                    "house": game_data.get("house", "Main Hall"),
                    "stake": stake_amount,
                    "players": game_data.get("player_count", 0),
                    "total_calls": len(game_data.get("called_numbers", [])),
                    "commission": commission_percentage,
                    "fee": fee,
                    "prize": prize_amount,
                    "date_time": datetime.datetime.utcnow(),
                    "status": status_str
                })
                
                # Commit immediately to ensure this critical record is saved
                session.commit()
                
                # Get the ID of the inserted record
                id_result = session.execute(text("SELECT last_insert_rowid()"))
                last_id = id_result.scalar()
                
                print(f"✅ Game history record added with ID: {last_id}")
            except Exception as add_e:
                print(f"❌ ERROR: Failed to add GameHistory record: {add_e}")
                session.rollback()
                # Create a new session after rollback
                session.close()
                session = SessionLocal()
                
                # Try again with a simplified approach
                try:
                    print(f"🔵 Retrying with simplified GameHistory SQL...")
                    sql = text("""
                        INSERT INTO game_history 
                        (username, house, stake, players, total_calls, commission_percent, fee, total_prize, date_time, status)
                        VALUES 
                        (:username, 'Main Hall', :stake, :players, 0, 20.0, :fee, :prize, :date_time, 'completed')
                    """)
                    
                    session.execute(sql, {
                        "username": game_data.get("winner_name", "Unknown"),
                        "stake": stake_amount,
                        "players": game_data.get("player_count", 0),
                        "fee": fee,
                        "prize": prize_amount,
                        "date_time": datetime.datetime.utcnow()
                    })
                    
                    session.commit()
                    
                    # Get the ID of the inserted record
                    id_result = session.execute(text("SELECT last_insert_rowid()"))
                    last_id = id_result.scalar()
                    
                    print(f"✅ Simplified GameHistory record added with ID: {last_id}")
                except Exception as retry_e:
                    print(f"❌ ERROR: Failed to add simplified GameHistory record: {retry_e}")
                    session.rollback()
                    # We'll still try to update other stats
            
            # Update daily stats
            print(f"🔵 Updating daily stats...")
            today = datetime.date.today().isoformat()  # Use ISO format for consistent date strings
            try:
                # Use more robust INSERT OR REPLACE syntax
                from sqlalchemy import text
                
                # Use INSERT OR REPLACE (UPSERT) - this is SQLite specific and handles the constraint automatically
                sql = text("""
                    INSERT OR REPLACE INTO daily_stats 
                    (date, earnings, games_played)
                    VALUES 
                    (
                        :today,
                        COALESCE((SELECT earnings FROM daily_stats WHERE date = :today), 0) + :fee,
                        COALESCE((SELECT games_played FROM daily_stats WHERE date = :today), 0) + 1
                    )
                """)
                
                # Execute the query with parameters
                session.execute(sql, {
                    "today": today,
                    "fee": fee
                })
                
                # Commit the daily stats changes immediately
                session.commit()
                print(f"✅ Daily stats committed successfully using INSERT OR REPLACE")
            except Exception as daily_e:
                print(f"❌ ERROR: Failed to update daily stats: {daily_e}")
                session.rollback()
                
                # Try a simpler approach as fallback
                try:
                    print(f"🔵 Retrying daily stats update with simpler approach...")
                    simple_sql = text("""
                        INSERT OR IGNORE INTO daily_stats (date, earnings, games_played)
                        VALUES (:today, :fee, 1)
                    """)
                    session.execute(simple_sql, {"today": today, "fee": fee})
                    
                    # Try to update if insert didn't work (record might already exist)
                    update_sql = text("""
                        UPDATE daily_stats 
                        SET earnings = earnings + :fee, 
                            games_played = games_played + 1
                        WHERE date = :today
                    """)
                    session.execute(update_sql, {"today": today, "fee": fee})
                    
                    # Commit the changes
                    session.commit()
                    print(f"✅ Daily stats updated successfully using fallback approach")
                except Exception as retry_e:
                    print(f"❌ ERROR: Failed to update daily stats with fallback approach: {retry_e}")
                    session.rollback()
                
                print(f"⚠️ Will continue processing despite daily stats error")
            
            # Create a new session for the overall summary update
            session.close()
            session = SessionLocal()
            
            # Update overall summary
            print(f"🔵 Updating overall summary...")
            try:
                # Use INSERT OR REPLACE for overall summary too
                from sqlalchemy import text
                
                # Use INSERT OR REPLACE (UPSERT) to handle the summary record
                sql = text("""
                    INSERT OR REPLACE INTO overall_summary 
                    (id, total_earnings, current_daily_games, current_daily_earnings, wallet_balance, last_updated)
                    VALUES 
                    (
                        1, -- Always use ID 1 for the single summary record
                        COALESCE((SELECT total_earnings FROM overall_summary WHERE id = 1), 0) + :fee,
                        COALESCE((SELECT current_daily_games FROM overall_summary WHERE id = 1), 0) + 1,
                        COALESCE((SELECT current_daily_earnings FROM overall_summary WHERE id = 1), 0) + :fee,
                        COALESCE((SELECT wallet_balance FROM overall_summary WHERE id = 1), 0),
                        :now
                    )
                """)
                
                # Execute the query with parameters
                session.execute(sql, {
                    "fee": fee,
                    "now": datetime.datetime.utcnow()
                })
                
                # Commit the summary changes immediately
                session.commit()
                print(f"✅ Overall summary committed successfully using INSERT OR REPLACE")
            except Exception as summary_e:
                print(f"❌ ERROR: Failed to update overall summary: {summary_e}")
                session.rollback()
                
                # Try a simpler approach as fallback
                try:
                    print(f"🔵 Retrying overall summary update with simpler approach...")
                    # Check if we have any record first
                    result = session.execute(text("SELECT COUNT(*) FROM overall_summary"))
                    has_summary = result.scalar() > 0
                    
                    if has_summary:
                        # Update the existing record
                        update_sql = text("""
                            UPDATE overall_summary 
                            SET total_earnings = total_earnings + :fee,
                                current_daily_games = current_daily_games + 1,
                                current_daily_earnings = current_daily_earnings + :fee,
                                last_updated = :now
                            WHERE id = 1
                        """)
                        session.execute(update_sql, {"fee": fee, "now": datetime.datetime.utcnow()})
                    else:
                        # Create a new record
                        insert_sql = text("""
                            INSERT INTO overall_summary 
                            (id, total_earnings, current_daily_games, current_daily_earnings, wallet_balance, last_updated)
                            VALUES (1, :fee, 1, :fee, 0, :now)
                        """)
                        session.execute(insert_sql, {"fee": fee, "now": datetime.datetime.utcnow()})
                    
                    # Commit the changes
                    session.commit()
                    print(f"✅ Overall summary updated successfully using fallback approach")
                except Exception as retry_e:
                    print(f"❌ ERROR: Failed to update overall summary with fallback approach: {retry_e}")
                    session.rollback()
                
                print(f"⚠️ Will continue processing despite overall summary error")
            
            # CRITICAL FIX: Execute a test query to ensure database is working
            try:
                # Execute a simple query to verify database connection
                test_count = session.query(GameHistory).count()
                print(f"✅ Database connectivity verified - found {test_count} existing GameHistory records")
            except Exception as test_e:
                print(f"❌ ERROR: Database connectivity test failed: {test_e}")
                # Continue processing - don't raise
            
            # Commit any remaining changes with explicit error handling and retry
            max_retries = 3
            retry_count = 0
            committed = False
            
            while not committed and retry_count < max_retries:
                try:
                    print(f"🔵 Committing final transaction (attempt {retry_count + 1}/{max_retries})...")
                    session.commit()
                    committed = True
                    print(f"✅ Final transaction committed successfully")
                except Exception as commit_e:
                    retry_count += 1
                    print(f"❌ ERROR: Failed to commit transaction (attempt {retry_count}/{max_retries}): {commit_e}")
                    
                    if retry_count < max_retries:
                        print(f"⚠️ Retrying commit in 1 second...")
                        import time
                        time.sleep(1)  # Wait 1 second before retrying
                        # Refresh the session to retry
                        session.rollback()
                    else:
                        print(f"❌ ERROR: Maximum retries reached, giving up on commit")
                        session.rollback()  # Final rollback
            
            # Try to update the UI if possible by forcing a refresh
            try:
                # Update any active StatsPage instances - find them using garbage collector
                import gc
                stats_pages_refreshed = 0
                for obj in gc.get_objects():
                    if hasattr(obj, '__class__') and hasattr(obj.__class__, '__name__') and obj.__class__.__name__ == 'StatsPage':
                        if hasattr(obj, 'refresh_data') and callable(obj.refresh_data):
                            print("🔄 Found StatsPage instance, calling refresh_data() to update UI")
                            obj.refresh_data()
                            stats_pages_refreshed += 1
                print(f"🔄 Refreshed {stats_pages_refreshed} StatsPage instances")
            except Exception as refresh_e:
                print(f"⚠️ Could not auto-refresh StatsPage UI: {refresh_e}")
            
            # Force a file system flush to ensure data is written to disk
            try:
                import os
                import platform
                
                # Check for Windows platform
                if platform.system() == 'Windows':
                    # On Windows, we can't use fsync directly, so create a new connection to force flush
                    import sqlite3
                    
                    # Just open and close a connection to ensure writes are flushed
                    temp_conn = sqlite3.connect(db_path)
                    temp_conn.execute("PRAGMA wal_checkpoint(FULL)")
                    temp_conn.close()
                    print(f"✅ Forced Windows filesystem sync for database file")
                else:
                    # On Unix-like systems, we can use fsync
                    fd = os.open(db_path, os.O_RDONLY)
                    os.fsync(fd)
                    os.close(fd)
                    print(f"✅ Forced Unix filesystem sync for database file")
            except Exception as sync_e:
                print(f"⚠️ Could not force filesystem sync: {sync_e}")
            
            # Final verification query
            try:
                new_session = SessionLocal()
                # Use count() to avoid enum conversion issues
                record_count = new_session.query(GameHistory).count()
                
                # Get most recent record with direct SQL to avoid enum conversion issues
                from sqlalchemy import text
                result = new_session.execute(text("SELECT id, username, date_time FROM game_history ORDER BY id DESC LIMIT 1"))
                most_recent = result.fetchone()
                
                print(f"✅ FINAL VERIFICATION: Database contains {record_count} game history records")
                if most_recent:
                    print(f"✅ FINAL VERIFICATION: Most recent record ID: {most_recent[0]}, User: {most_recent[1]}, Time: {most_recent[2]}")
                new_session.close()
            except Exception as verify_e:
                print(f"⚠️ Final verification query failed: {verify_e}")
            
            print(f"✅ SUCCESS: Game statistics update completed")
            return True
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR: Failed to update game statistics: {e}")
            import traceback
            traceback.print_exc()
            
            # Rollback if there was an error
            if session:
                try:
                    session.rollback()
                    print("✅ Session rolled back due to error")
                except Exception as rollback_e:
                    print(f"❌ ERROR: Failed to rollback session: {rollback_e}")
                
                # Always close the session
                try:
                    session.close()
                    print("✅ Database session closed")
                except Exception as close_e:
                    print(f"❌ ERROR: Failed to close session: {close_e}")
            
            print(f"🔵 Finished processing game statistics update")
            return False

    def schedule_auto_refresh(self):
        """Schedule automatic refresh of stats data every 60 seconds."""
        self.auto_refresh_stats()
        # Schedule next refresh after 60 seconds
        self.master.after(60000, self.schedule_auto_refresh)
        
    def auto_refresh_stats(self):
        """Automatically refresh stats data without visual indicators."""
        try:
            # Refresh the SQLAlchemy session to get new data
            self.refresh_session()
            # Reload data
            self.load_summary_data()
            self.load_earnings_chart()
            self.load_game_history()
            print("Auto-refreshed stats data")
        except Exception as e:
            print(f"Error in auto refresh: {e}")
    
    def refresh_session(self):
        """Refresh the SQLAlchemy session to ensure it sees the latest database changes."""
        try:
            # Close the existing session
            self.db_session.close()
            # Create a new session
            self.db_session = SessionLocal()
            print("Refreshed database session")
        except Exception as e:
            print(f"Error refreshing session: {e}")
            # Try to recreate session if there was an error
            try:
                self.db_session = SessionLocal()
            except:
                pass

# --- Main Application Execution ---
def show_stats_page(master=None):
    """Show the stats page."""
    try:
        # Force refresh stats data first
        try:
            import stats_integration
            print("Forcing refresh of all stats data before loading stats page...")
            if hasattr(stats_integration, 'force_refresh_data'):
                stats_integration.force_refresh_data()
                print("Stats data refresh complete")
        except Exception as refresh_e:
            print(f"Note: Could not force refresh stats data: {refresh_e}")
            # Try thread_safe_db direct access as fallback
            try:
                import thread_safe_db
                print("Trying thread_safe_db data refresh...")
                # Close and reopen the connection to ensure fresh data
                thread_safe_db.close_connection()
                conn = thread_safe_db.get_connection()
                print("Refreshed thread_safe_db connection")
            except Exception as db_e:
                print(f"Note: Could not refresh thread_safe_db connection: {db_e}")
        
        # Check if master is a pygame Surface or None
        is_pygame_surface = False
        try:
            # Try to import pygame to check if master is a pygame Surface
            import pygame
            if isinstance(master, pygame.Surface):
                print("Detected pygame Surface as master, creating new tkinter window instead")
                is_pygame_surface = True
        except ImportError:
            # If pygame isn't available, just assume master isn't a pygame Surface
            pass
            
        # Continue with normal stats page loading
        if master is None or is_pygame_surface:
            # Create a new window if no master is provided or if master is a pygame Surface
            root = ttk.Window(themename='darkly', title="WOW Bingo - Statistics")
            # Set a minimum size for good layout
            root.minsize(1000, 600)
        else:
            # Use the provided tkinter master window
            root = master
            
        stats_page = StatsPage(root)
        stats_page.pack(fill=BOTH, expand=True)
        
        if master is None or is_pygame_surface:
            root.mainloop()  # Only start mainloop if we created the window
        
        return stats_page
    except Exception as e:
        print(f"Error showing stats page: {e}")
        import traceback
        traceback.print_exc()
        
        if master is None or (is_pygame_surface if 'is_pygame_surface' in locals() else False):
            # Create a minimal error window
            try:
                root = ttk.Window(themename='darkly', title="Error")
                ttk.Label(root, text=f"Error loading stats page: {e}", foreground="red", padding=20).pack()
                ttk.Button(root, text="Close", command=root.destroy).pack(pady=10)
                root.minsize(400, 200)
                root.mainloop()
            except:
                # Last resort fallback if even the error window fails
                import tkinter as tk
                tk.messagebox.showerror("Critical Error", f"Failed to start stats page: {e}")
        
        return None

if __name__ == "__main__":
    # Set up logging
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join('data', 'stats_page.log')),
            logging.StreamHandler()
        ]
    )
    
    try:
        # Log startup information for diagnostics
        print("=" * 50)
        print("Starting Stats Page")
        print(f"Using database at {STATS_DB_URL}")
        print(f"Current directory: {os.getcwd()}")
        print(f"Data directory: {DATA_DIR}")
        db_path = os.path.join(DATA_DIR, 'stats.db')
        if os.path.exists(db_path):
            print(f"Database file exists, size: {os.path.getsize(db_path)} bytes")
        else:
            print(f"WARNING: Database file not found at {db_path}")
        print("=" * 50)
        
        # Launch the stats page
        show_stats_page()
    except Exception as e:
        print(f"Critical error in stats_page main: {e}")
        import traceback
        traceback.print_exc()
        
        # Show error dialog
        try:
            root = ttk.Window(themename='darkly', title="Critical Error")
            ttk.Label(root, text=f"Critical error starting stats page: {e}", 
                     foreground="red", padding=20, wraplength=400).pack()
            ttk.Button(root, text="Close", command=root.destroy).pack(pady=10)
            root.minsize(500, 250)
            root.mainloop()
        except:
            # Last resort fallback if even the error window fails
            import tkinter as tk
            tk.messagebox.showerror("Critical Error", f"Failed to start stats page: {e}")
