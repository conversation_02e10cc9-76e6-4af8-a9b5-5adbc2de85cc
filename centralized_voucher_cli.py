#!/usr/bin/env python3
"""
Centralized Voucher Manager Command Line Interface

Command-line interface for the centralized voucher generation system.
Provides easy access to all voucher management functions.
"""

import sys
import argparse
import json
from datetime import datetime
from typing import List, Dict

try:
    from centralized_voucher_manager import CentralizedVoucherManager
    MANAGER_AVAILABLE = True
except ImportError:
    MANAGER_AVAILABLE = False
    print("Error: centralized_voucher_manager.py not found")

try:
    from enhanced_uuid_voucher_validator import EnhancedUUIDVoucherValidator
    VALIDATOR_AVAILABLE = True
except ImportError:
    VALIDATOR_AVAILABLE = False
    print("Warning: enhanced_uuid_voucher_validator.py not found")

class CentralizedVoucherCLI:
    """Command-line interface for centralized voucher management."""
    
    def __init__(self):
        """Initialize the CLI."""
        if not MANAGER_AVAILABLE:
            print("Error: Centralized voucher manager not available")
            sys.exit(1)
        
        self.manager = CentralizedVoucherManager()
        
        if VALIDATOR_AVAILABLE:
            self.validator = EnhancedUUIDVoucherValidator()
        else:
            self.validator = None
    
    def register_pc(self, args):
        """Register a new external PC."""
        success = self.manager.register_external_pc(
            args.uuid, args.name, args.description, args.notes
        )
        
        if success:
            print(f"✓ Successfully registered PC: {args.uuid}")
            if args.name:
                print(f"  Name: {args.name}")
            if args.description:
                print(f"  Description: {args.description}")
        else:
            print(f"✗ Failed to register PC: {args.uuid}")
            sys.exit(1)
    
    def list_pcs(self, args):
        """List all registered external PCs."""
        pcs = self.manager.get_registered_pcs()
        
        if not pcs:
            print("No external PCs registered.")
            return
        
        print(f"Registered External PCs ({len(pcs)}):")
        print("-" * 80)
        
        for pc in pcs:
            print(f"UUID: {pc['uuid']}")
            print(f"Name: {pc['name'] or 'Unnamed'}")
            print(f"Description: {pc['description'] or 'No description'}")
            print(f"First seen: {pc['first_seen']}")
            print(f"Total vouchers: {pc['total_vouchers_generated']}")
            print(f"Last voucher: {pc['last_voucher_generated'] or 'Never'}")
            if pc['notes']:
                print(f"Notes: {pc['notes']}")
            print("-" * 80)
    
    def generate_single(self, args):
        """Generate a single voucher."""
        voucher = self.manager.generate_voucher(
            args.uuid, args.amount, args.generator, args.days, args.share, args.notes
        )
        
        if voucher:
            print(f"✓ Voucher generated successfully!")
            print(f"Voucher Code: {voucher}")
            print(f"Target UUID: {args.uuid}")
            print(f"Amount: {args.amount} credits")
            print(f"Share: {args.share}%")
            print(f"Valid for: {args.days} days")
            print(f"Generator: {args.generator}")
            
            if args.output:
                self._save_voucher_to_file(voucher, args.uuid, args.amount, args.share, args.days, args.output)
        else:
            print(f"✗ Failed to generate voucher")
            sys.exit(1)
    
    def generate_batch(self, args):
        """Generate a batch of vouchers."""
        print(f"Generating batch of {args.count} vouchers...")
        
        vouchers = self.manager.generate_batch(
            args.uuid, args.count, args.amount, args.generator, 
            args.days, args.share, args.batch_name, args.output
        )
        
        if vouchers:
            print(f"✓ Successfully generated {len(vouchers)} vouchers!")
            print(f"Target UUID: {args.uuid}")
            print(f"Amount per voucher: {args.amount} credits")
            print(f"Total value: {len(vouchers) * args.amount} credits")
            print(f"Share: {args.share}%")
            print(f"Valid for: {args.days} days")
            print(f"Generator: {args.generator}")
            
            if args.batch_name:
                print(f"Batch name: {args.batch_name}")
            
            if args.output:
                print(f"Exported to: {args.output}")
            
            if args.show_vouchers:
                print("\nGenerated vouchers:")
                for i, voucher in enumerate(vouchers, 1):
                    print(f"{i:3d}. {voucher}")
        else:
            print(f"✗ Failed to generate batch")
            sys.exit(1)
    
    def validate_voucher(self, args):
        """Validate a voucher."""
        if self.validator:
            result = self.validator.validate_voucher_comprehensive(args.voucher, args.uuid)
        else:
            result = self.manager.validate_voucher_for_uuid(args.voucher, args.uuid)
        
        print(f"Voucher Validation Result")
        print("=" * 50)
        print(f"Voucher Code: {args.voucher}")
        print(f"Target UUID: {args.uuid}")
        print(f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        if result.get('valid'):
            print("✓ VOUCHER IS VALID")
            print(f"Amount: {result.get('amount', 'N/A')} credits")
            print(f"Share: {result.get('share', 'N/A')}%")
            
            expiry = result.get('expiry', 0)
            if expiry == 0:
                print("Expiry: Never expires")
            else:
                try:
                    expiry_date = datetime.fromtimestamp(expiry)
                    print(f"Expiry: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"Expiry: {expiry}")
            
            print(f"Message: {result.get('message', 'Valid voucher')}")
            
            if 'validator_used' in result:
                print(f"Validator: {result['validator_used']}")
        else:
            print("✗ VOUCHER IS INVALID")
            print(f"Reason: {result.get('message', 'Unknown error')}")
            
            if result.get('error_type') == 'uuid_mismatch':
                print(f"Expected UUID: {result.get('expected_uuid', 'N/A')}")
                print(f"Provided UUID: {result.get('provided_uuid', 'N/A')}")
    
    def show_history(self, args):
        """Show voucher generation history."""
        history = self.manager.get_voucher_history(args.uuid, args.limit)
        
        if not history:
            print("No voucher history found.")
            return
        
        print(f"Voucher Generation History ({len(history)} records):")
        print("-" * 100)
        
        for record in history:
            print(f"Voucher: {record['voucher_code']}")
            print(f"Target UUID: {record['target_uuid']}")
            print(f"Amount: {record['amount']} credits")
            print(f"Share: {record['share']}%")
            print(f"Generator: {record['generator_type']}")
            print(f"Generated: {record['generated_timestamp']}")
            print(f"Status: {record['status']}")
            if record['notes']:
                print(f"Notes: {record['notes']}")
            print("-" * 100)
    
    def show_statistics(self, args):
        """Show system statistics."""
        stats = self.manager.get_statistics()
        
        print("Centralized Voucher System Statistics")
        print("=" * 50)
        
        for key, value in stats.items():
            formatted_key = key.replace('_', ' ').title()
            print(f"{formatted_key}: {value}")
        
        if self.validator:
            print("\nValidation Statistics:")
            print("-" * 30)
            val_stats = self.validator.get_validation_statistics()
            for key, value in val_stats.items():
                formatted_key = key.replace('_', ' ').title()
                print(f"{formatted_key}: {value}")
    
    def _save_voucher_to_file(self, voucher: str, uuid: str, amount: int, 
                             share: int, days: int, filename: str):
        """Save voucher information to file."""
        try:
            voucher_data = {
                "voucher_code": voucher,
                "target_uuid": uuid,
                "amount": amount,
                "share": share,
                "days_valid": days,
                "generated_timestamp": datetime.now().isoformat(),
                "expiry_date": "Never" if days == 0 else (
                    datetime.now().replace(day=datetime.now().day + days).isoformat()
                )
            }
            
            with open(filename, 'w') as f:
                json.dump(voucher_data, f, indent=2)
            
            print(f"✓ Voucher saved to: {filename}")
            
        except Exception as e:
            print(f"✗ Error saving voucher: {e}")


def create_parser():
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Centralized Voucher Management System CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Register a new PC
  python centralized_voucher_cli.py register-pc --uuid "ABC123..." --name "Office PC"
  
  # List all registered PCs
  python centralized_voucher_cli.py list-pcs
  
  # Generate a single voucher
  python centralized_voucher_cli.py generate --uuid "ABC123..." --amount 100
  
  # Generate a batch of vouchers
  python centralized_voucher_cli.py batch --uuid "ABC123..." --count 10 --amount 50
  
  # Validate a voucher
  python centralized_voucher_cli.py validate --voucher "VOUCHER123" --uuid "ABC123..."
  
  # Show statistics
  python centralized_voucher_cli.py stats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Register PC command
    register_parser = subparsers.add_parser('register-pc', help='Register a new external PC')
    register_parser.add_argument('--uuid', required=True, help='Machine UUID')
    register_parser.add_argument('--name', help='Friendly name for the PC')
    register_parser.add_argument('--description', help='Description of the PC')
    register_parser.add_argument('--notes', help='Additional notes')
    
    # List PCs command
    list_parser = subparsers.add_parser('list-pcs', help='List all registered external PCs')
    
    # Generate single voucher command
    generate_parser = subparsers.add_parser('generate', help='Generate a single voucher')
    generate_parser.add_argument('--uuid', required=True, help='Target machine UUID')
    generate_parser.add_argument('--amount', type=int, default=100, help='Credit amount (default: 100)')
    generate_parser.add_argument('--days', type=int, default=30, help='Days valid (default: 30)')
    generate_parser.add_argument('--share', type=int, default=80, help='Share percentage (default: 80)')
    generate_parser.add_argument('--generator', default='compact', help='Generator type (default: compact)')
    generate_parser.add_argument('--notes', help='Additional notes')
    generate_parser.add_argument('--output', help='Save voucher to file')
    
    # Generate batch command
    batch_parser = subparsers.add_parser('batch', help='Generate a batch of vouchers')
    batch_parser.add_argument('--uuid', required=True, help='Target machine UUID')
    batch_parser.add_argument('--count', type=int, required=True, help='Number of vouchers to generate')
    batch_parser.add_argument('--amount', type=int, default=100, help='Credit amount per voucher (default: 100)')
    batch_parser.add_argument('--days', type=int, default=30, help='Days valid (default: 30)')
    batch_parser.add_argument('--share', type=int, default=80, help='Share percentage (default: 80)')
    batch_parser.add_argument('--generator', default='compact', help='Generator type (default: compact)')
    batch_parser.add_argument('--batch-name', help='Name for the batch')
    batch_parser.add_argument('--output', help='Export batch to file')
    batch_parser.add_argument('--show-vouchers', action='store_true', help='Show all generated vouchers')
    
    # Validate voucher command
    validate_parser = subparsers.add_parser('validate', help='Validate a voucher')
    validate_parser.add_argument('--voucher', required=True, help='Voucher code to validate')
    validate_parser.add_argument('--uuid', required=True, help='Target machine UUID')
    
    # History command
    history_parser = subparsers.add_parser('history', help='Show voucher generation history')
    history_parser.add_argument('--uuid', help='Filter by target UUID')
    history_parser.add_argument('--limit', type=int, default=50, help='Maximum records to show (default: 50)')
    
    # Statistics command
    stats_parser = subparsers.add_parser('stats', help='Show system statistics')
    
    return parser


def main():
    """Main CLI function."""
    if not MANAGER_AVAILABLE:
        print("Error: Centralized voucher manager not available")
        sys.exit(1)
    
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    cli = CentralizedVoucherCLI()
    
    try:
        if args.command == 'register-pc':
            cli.register_pc(args)
        elif args.command == 'list-pcs':
            cli.list_pcs(args)
        elif args.command == 'generate':
            cli.generate_single(args)
        elif args.command == 'batch':
            cli.generate_batch(args)
        elif args.command == 'validate':
            cli.validate_voucher(args)
        elif args.command == 'history':
            cli.show_history(args)
        elif args.command == 'stats':
            cli.show_statistics(args)
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
