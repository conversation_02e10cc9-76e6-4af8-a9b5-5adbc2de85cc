"""
RethinkDB Configuration Module

This module provides configuration settings for connecting to RethinkDB
and synchronizing data between local SQLite and remote RethinkDB.
"""

import os
import json

# RethinkDB connection settings
RETHINKDB_HOST = os.environ.get('RETHINKDB_HOST', 'localhost')
RETHINKDB_PORT = int(os.environ.get('RETHINKDB_PORT', '28015'))
RETHINKDB_DB = os.environ.get('RETHINKDB_DB', 'wow_game_stats')
RETHINKDB_USER = os.environ.get('RETHINKDB_USER', 'admin')
RETHINKDB_PASSWORD = os.environ.get('RETHINKDB_PASSWORD', '')
RETHINKDB_SSL = os.environ.get('RETHINKDB_SSL', 'false').lower() == 'true'

# Sync configuration
SYNC_INTERVAL = int(os.environ.get('SYNC_INTERVAL', '60'))  # seconds
MAX_SYNC_RETRIES = int(os.environ.get('MAX_SYNC_RETRIES', '3'))
SYNC_BATCH_SIZE = int(os.environ.get('SYNC_BATCH_SIZE', '100'))

# Tables to synchronize
SYNC_TABLES = [
    'daily_stats',
    'game_history',
    'wallet_transactions',
    'admin_users',
    'audit_log'
]

# Local cache directory for offline changes
SYNC_CACHE_DIR = os.path.join('data', 'sync_cache')

# Create cache directory if it doesn't exist
os.makedirs(SYNC_CACHE_DIR, exist_ok=True)

def save_config_to_file():
    """Save current configuration to a JSON file"""
    config = {
        'rethinkdb': {
            'host': RETHINKDB_HOST,
            'port': RETHINKDB_PORT,
            'db': RETHINKDB_DB,
            'user': RETHINKDB_USER,
            'password': RETHINKDB_PASSWORD,
            'ssl': RETHINKDB_SSL
        },
        'sync': {
            'interval': SYNC_INTERVAL,
            'max_retries': MAX_SYNC_RETRIES,
            'batch_size': SYNC_BATCH_SIZE,
            'tables': SYNC_TABLES
        }
    }
    
    config_path = os.path.join('data', 'rethink_config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    return config_path

def load_config_from_file():
    """Load configuration from JSON file if it exists"""
    config_path = os.path.join('data', 'rethink_config.json')
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Update global variables
            global RETHINKDB_HOST, RETHINKDB_PORT, RETHINKDB_DB, RETHINKDB_USER, RETHINKDB_PASSWORD, RETHINKDB_SSL
            global SYNC_INTERVAL, MAX_SYNC_RETRIES, SYNC_BATCH_SIZE, SYNC_TABLES
            
            RETHINKDB_HOST = config['rethinkdb']['host']
            RETHINKDB_PORT = config['rethinkdb']['port']
            RETHINKDB_DB = config['rethinkdb']['db']
            RETHINKDB_USER = config['rethinkdb']['user']
            RETHINKDB_PASSWORD = config['rethinkdb']['password']
            RETHINKDB_SSL = config['rethinkdb']['ssl']
            
            SYNC_INTERVAL = config['sync']['interval']
            MAX_SYNC_RETRIES = config['sync']['max_retries']
            SYNC_BATCH_SIZE = config['sync']['batch_size']
            SYNC_TABLES = config['sync']['tables']
            
            return True
        except Exception as e:
            print(f"Error loading config from file: {e}")
            return False
    
    return False

# Try to load config from file
if not load_config_from_file():
    # If file doesn't exist or is invalid, create it with default values
    save_config_to_file()