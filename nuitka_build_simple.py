#!/usr/bin/env python3
"""
WOW Bingo Game - Simple Nuitka Build Script
===========================================

This script creates a standalone executable using Nuitka with the most basic
and reliable settings to avoid any compiler issues.

Usage:
    python nuitka_build_simple.py [--onefile] [--verbose]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def main():
    """Main build function."""
    parser = argparse.ArgumentParser(description="Simple Nuitka build for WOW Bingo Game")
    parser.add_argument('--onefile', action='store_true', help='Create single-file executable')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()

    project_root = Path(__file__).parent.absolute()
    dist_dir = project_root / "dist"
    
    log("=" * 60)
    log("WOW Bingo Game - Simple Nuitka Build")
    log("=" * 60)

    # Check prerequisites
    log("Checking prerequisites...")
    
    if not (project_root / "main.py").exists():
        error("main.py not found!")
    
    if not (project_root / "assets").exists():
        error("assets directory not found!")

    # Check Python version
    if sys.version_info < (3, 7):
        error("Python 3.7 or higher is required")

    log("Prerequisites check passed")

    # Install Nuitka if needed
    try:
        subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                     capture_output=True, check=True)
        log("Nuitka is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        log("Installing Nuitka...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka>=2.4.0'],
                         check=True, capture_output=not args.verbose)
            log("Nuitka installed successfully")
        except subprocess.CalledProcessError as e:
            error(f"Failed to install Nuitka: {e}")

    # Prepare build environment
    log("Preparing build environment...")
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
        log("Cleaned dist directory")
    
    dist_dir.mkdir(exist_ok=True)

    # Build command with minimal, safe settings
    log("Building with Nuitka (simple mode)...")
    
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone' if not args.onefile else '--onefile',
        '--output-filename=WOW_Bingo_Game.exe',
        f'--output-dir={dist_dir}',
        '--assume-yes-for-downloads',
        '--windows-console-mode=disable',
        '--show-progress',
        
        # Windows metadata
        '--windows-company-name=WOW Games',
        '--windows-product-name=WOW Bingo Game',
        '--windows-file-version=1.0.0',
        '--windows-product-version=1.0.0',
    ]

    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.append(f'--windows-icon-from-ico={icon_path}')

    # Include data directories
    if (project_root / "assets").exists():
        cmd.append(f'--include-data-dir={project_root / "assets"}=assets')
        log("Including assets directory")

    if (project_root / "data").exists():
        cmd.append(f'--include-data-dir={project_root / "data"}=data')
        log("Including data directory")

    # Include essential packages
    essential_packages = [
        'pygame',
        'pyperclip',
        'sqlite3',
        'json',
        'datetime',
    ]

    for package in essential_packages:
        cmd.append(f'--include-package={package}')

    # Add main script
    cmd.append('main.py')

    # Execute Nuitka
    log("Executing Nuitka (this may take 10-20 minutes)...")
    if args.verbose:
        log(f"Command: {' '.join(cmd)}")

    start_time = time.time()

    try:
        result = subprocess.run(cmd, cwd=project_root,
                              capture_output=not args.verbose, text=True)

        if result.returncode == 0:
            build_time = time.time() - start_time
            log("Nuitka build completed successfully")
            
            # Find executable
            executable_path = None
            possible_paths = [
                dist_dir / "WOW_Bingo_Game.exe",
                dist_dir / "WOW_Bingo_Game" / "WOW_Bingo_Game.exe",
            ]
            
            for path in possible_paths:
                if path.exists():
                    executable_path = path
                    break
            
            if executable_path:
                size_mb = executable_path.stat().st_size / (1024 * 1024)
                log("=" * 60)
                log("BUILD COMPLETED SUCCESSFULLY!")
                log("=" * 60)
                log(f"Executable: {executable_path}")
                log(f"Size: {size_mb:.1f} MB")
                log(f"Build time: {build_time:.1f} seconds")
                log("=" * 60)
                return True
            else:
                error("Executable not found after build")
        else:
            error(f"Nuitka build failed with return code {result.returncode}")
            if result.stderr and not args.verbose:
                log(f"Error output: {result.stderr}")
            return False

    except Exception as e:
        error(f"Error during Nuitka build: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        log(f"Unexpected error: {e}", "ERROR")
        sys.exit(1)
