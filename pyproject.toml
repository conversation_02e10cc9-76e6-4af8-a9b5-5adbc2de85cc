[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wow-bingo-game"
version = "2.0.0"
description = "WOW Bingo Game - Modern Python Application with Advanced Features"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "WOW Games Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "WOW Games Team", email = "<EMAIL>"}
]
keywords = ["bingo", "game", "pygame", "flet", "desktop", "entertainment"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Games/Entertainment :: Board Games",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.9"
dependencies = [
    # Core UI Framework
    "flet>=0.24.0",
    
    # Game Engine (for compatibility with existing code)
    "pygame>=2.5.0",
    
    # Audio Processing
    "pygame-ce>=2.4.0",
    "pydub>=0.25.1",
    
    # Database Support
    "sqlite3-utils>=3.36.0",
    "rethinkdb>=2.4.10",
    
    # Utilities
    "pyperclip>=1.8.2",
    "psutil>=5.9.0",
    "pillow>=10.0.0",
    "requests>=2.31.0",
    
    # Performance
    "numpy>=1.24.0",
    "numba>=0.58.0",
    
    # Cryptography for voucher system
    "cryptography>=41.0.0",
    
    # Configuration
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    
    # Logging
    "loguru>=0.7.0",
    
    # Development
    "typing-extensions>=4.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
]
build = [
    "nuitka>=2.0.0",
    "auto-py-to-exe>=2.40.0",
    "pyinstaller>=6.0.0",
    "cx-freeze>=6.15.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/wowgames/wow-bingo-game"
Documentation = "https://wow-bingo-game.readthedocs.io/"
Repository = "https://github.com/wowgames/wow-bingo-game.git"
"Bug Tracker" = "https://github.com/wowgames/wow-bingo-game/issues"
Changelog = "https://github.com/wowgames/wow-bingo-game/blob/main/CHANGELOG.md"

[project.scripts]
wow-bingo = "wow_bingo_game.main:main"
wow-bingo-dev = "wow_bingo_game.main:main_dev"

[project.gui-scripts]
"WOW Bingo Game" = "wow_bingo_game.main:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["wow_bingo_game*"]

[tool.setuptools.package-data]
"wow_bingo_game" = [
    "assets/**/*",
    "data/**/*",
    "templates/**/*",
    "config/**/*",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wow_bingo_game"]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pygame.*",
    "rethinkdb.*",
    "flet.*",
    "pydub.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["src/wow_bingo_game"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
