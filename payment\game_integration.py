"""
Game integration for the payment system.

This module integrates the payment system with the main game,
adding credit usage tracking and validation.
"""

import pygame
from .usage_tracker import get_usage_tracker

def integrate_with_game(game):
    """
    Integrate the payment system with the main game.

    Args:
        game: BingoGame instance

    Returns:
        bool: True if integrated successfully, False otherwise
    """
    print("Integrating payment system with game...")

    # Get the usage tracker
    usage_tracker = get_usage_tracker()
    print(f"Got usage tracker: {usage_tracker}")
    print(f"Current credits: {usage_tracker.voucher_manager.credits}")

    # Store original start_game method
    original_start_game = game.start_game
    print("Stored original start_game method")

    # Enhanced start_game method that checks credits
    def enhanced_start_game():
        """Enhanced start_game method that checks credits."""
        print("Enhanced start_game method called")

        # Check if we're in demo mode (2 or fewer players)
        # This should be set correctly in the original start_game method
        is_demo_mode = hasattr(game, 'is_demo_mode') and game.is_demo_mode

        # Skip credit check in demo mode
        if is_demo_mode:
            print("Game started in demo mode - skipping credit check")
            # Set a flag to indicate we're in demo mode
            usage_tracker.in_demo_mode = True
            # Call original start_game method
            return original_start_game()
        else:
            # Make sure the demo mode flag is reset in the tracker
            usage_tracker.in_demo_mode = False

        # Normal mode - check if we have enough credits
        print(f"Starting game with credit tracking. Current tracker state: active={usage_tracker.active}")
        start_result = usage_tracker.start_game()

        if not start_result:
            # Show error message
            game.message = "Insufficient credits. Please recharge to continue."
            game.message_type = "error"
            game.message_timer = 180  # 3 seconds at 60 FPS
            print("Insufficient credits to start game")
            return False

        print(f"Game started with credit tracking. New tracker state: active={usage_tracker.active}, game_id={usage_tracker.current_game_id}")

        # CRITICAL FIX: Store a reference to the usage tracker in the game object
        if not hasattr(game, 'usage_tracker'):
            game.usage_tracker = usage_tracker
            print("CRITICAL FIX: Added usage_tracker reference to game object")

        # Call original start_game method
        return original_start_game()

    # Check if game has a reset_game method
    if hasattr(game, 'reset_game'):
        # Store original reset_game method
        original_reset_game = game.reset_game

        # Enhanced reset_game method that ends tracking
        def enhanced_reset_game():
            """Enhanced reset_game method that ends tracking."""
            # Get the most recent voucher's share percentage
            voucher_history = usage_tracker.voucher_manager.get_voucher_history(1)
            share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

            # Get commission percentage from game settings
            if hasattr(game, 'commission_percentage'):
                commission_percentage = game.commission_percentage
            elif hasattr(game, 'settings') and isinstance(game.settings, dict):
                commission_percentage = game.settings.get('game', {}).get('commission_percentage', 20)
            else:
                commission_percentage = 20  # Default commission percentage

            # Get total bets from game
            total_bets = 0
            if hasattr(game, 'bet_amount') and hasattr(game, 'players'):
                total_bets = game.bet_amount * len(game.players)

            # End tracking with the new parameters
            result = usage_tracker.end_game(
                share_percentage=share_percentage,
                total_bets=total_bets,
                commission_percentage=commission_percentage
            )

            # Show detailed message if tracking was active
            if result.get('success', False):
                if result['credits_used'] > 0:
                    # Create a detailed message showing the calculation
                    message = (
                        f"Game completed. Commission: {result['referee_commission']:.2f} ETB ({result['commission_percentage']}%) | "
                        f"Share: {result['share_percentage']}% | "
                        f"Deducted: {result['credits_used']} credits | "
                        f"Balance: {result['new_balance']} credits"
                    )
                else:
                    message = f"Game completed. No credits deducted. Balance: {result['new_balance']} credits"

                game.message = message
                game.message_type = "info"
                game.message_timer = 300  # 5 seconds at 60 FPS

            # Call original reset_game method
            return original_reset_game()

        # Replace reset_game method
        game.reset_game = enhanced_reset_game

    # Check if game has a game_state attribute with a reset_game method
    if hasattr(game, 'game_state') and hasattr(game.game_state, 'reset_game'):
        # Store original game_state.reset_game method
        original_game_state_reset = game.game_state.reset_game

        # Enhanced game_state.reset_game method that ends tracking
        def enhanced_game_state_reset():
            """Enhanced game_state.reset_game method that ends tracking."""
            # Get the most recent voucher's share percentage
            voucher_history = usage_tracker.voucher_manager.get_voucher_history(1)
            share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

            # Get commission percentage from game settings
            if hasattr(game, 'commission_percentage'):
                commission_percentage = game.commission_percentage
            elif hasattr(game, 'settings') and isinstance(game.settings, dict):
                commission_percentage = game.settings.get('game', {}).get('commission_percentage', 20)
            else:
                commission_percentage = 20  # Default commission percentage

            # Get total bets from game
            total_bets = 0
            if hasattr(game, 'bet_amount') and hasattr(game, 'players'):
                total_bets = game.bet_amount * len(game.players)

            # End tracking with the new parameters
            result = usage_tracker.end_game(
                share_percentage=share_percentage,
                total_bets=total_bets,
                commission_percentage=commission_percentage
            )

            # Show detailed message if tracking was active
            if result.get('success', False):
                if result['credits_used'] > 0:
                    # Create a detailed message showing the calculation
                    message = (
                        f"Game completed. Commission: {result['referee_commission']:.2f} ETB ({result['commission_percentage']}%) | "
                        f"Share: {result['share_percentage']}% | "
                        f"Deducted: {result['credits_used']} credits | "
                        f"Balance: {result['new_balance']} credits"
                    )
                else:
                    message = f"Game completed. No credits deducted. Balance: {result['new_balance']} credits"

                game.message = message
                game.message_type = "info"
                game.message_timer = 300  # 5 seconds at 60 FPS

            # Call original game_state.reset_game method
            return original_game_state_reset()

        # Replace game_state.reset_game method
        game.game_state.reset_game = enhanced_game_state_reset

    # Store original draw method
    original_draw = game.draw

    # Enhanced draw method that shows credit info
    def enhanced_draw(*args):
        """
        Enhanced draw method that shows credit info.
        Compatible with different draw method signatures.
        """
        # Import the global screen variable
        import pygame
        global screen

        try:
            # Check if original draw expects a screen parameter
            if args and len(args) > 0:
                # CRITICAL FIX: Add screen attribute to game object before calling original draw
                # This ensures that the original draw method can access self.screen even when called with parameters
                if not hasattr(game, 'screen'):
                    game.screen = args[0]
                    print("CRITICAL FIX: Added screen attribute to game object")

                # Call original draw method with screen parameter
                original_draw(args[0])
                screen_to_use = args[0]
            else:
                # Get the current display surface before calling original draw
                # This ensures we have a valid reference even if original_draw fails
                try:
                    screen_to_use = pygame.display.get_surface()
                    if screen_to_use is None:
                        # If no surface is available, try to get it from main module
                        import sys
                        if hasattr(sys.modules['__main__'], 'screen'):
                            screen_to_use = sys.modules['__main__'].screen
                except Exception as e:
                    print(f"Warning: Could not get display surface before draw: {e}")
                    # Fallback to global screen if available
                    import sys
                    if hasattr(sys.modules['__main__'], 'screen'):
                        screen_to_use = sys.modules['__main__'].screen
                    else:
                        # If we still don't have a valid screen, we can't proceed
                        print("Error: No valid screen surface available")
                        return

                # Verify screen_to_use is valid before proceeding
                if screen_to_use is None or not isinstance(screen_to_use, pygame.Surface):
                    print("Error: Invalid screen surface")
                    return

                # CRITICAL FIX: Add screen attribute to game object before calling original draw
                # This ensures that the original draw method can access self.screen even when called without parameters
                if not hasattr(game, 'screen'):
                    game.screen = screen_to_use
                    print("CRITICAL FIX: Added screen attribute to game object")

                # Now call original draw method without parameters
                try:
                    original_draw()
                except Exception as e:
                    print(f"Error in original draw method: {e}")
                    # Continue anyway to try to show credit info
                    
                    # Add more specific handling for surface bit depth errors
                    if "unsupported surface bit depth" in str(e) or "Unsupported pixel format" in str(e):
                        print("Detected surface compatibility issue - attempting recovery")
                        try:
                            # Try to recreate the screen with a standard format
                            if hasattr(game, 'screen') and game.screen is not None:
                                print("Attempting to reset screen format")
                                width, height = game.screen.get_size()
                                game.screen = pygame.Surface((width, height))
                                game.screen.fill((10, 30, 45))  # Dark blue fallback
                        except Exception as recovery_error:
                            print(f"Recovery attempt failed: {recovery_error}")

            # Verify screen_to_use is still valid after original_draw
            if screen_to_use is None or not isinstance(screen_to_use, pygame.Surface):
                print("Error: Screen surface became invalid after draw")
                return

            # Make sure the screen has a valid size
            try:
                width, height = screen_to_use.get_size()
                if width <= 0 or height <= 0:
                    print(f"Error: Invalid screen dimensions: {width}x{height}")
                    return
            except Exception as e:
                print(f"Error getting screen size: {e}")
                # Try to recover by creating a new surface with default size
                try:
                    print("Attempting to create a new surface with default size")
                    screen_to_use = pygame.Surface((800, 600))
                    width, height = 800, 600
                except Exception as recovery_error:
                    print(f"Recovery attempt failed: {recovery_error}")
                    return
                return

            # Draw credit info if game is active
            if hasattr(game, 'game_started') and game.game_started:
                try:
                    draw_credit_info(game, screen_to_use, usage_tracker)
                except Exception as e:
                    print(f"Error drawing credit info: {e}")
        except Exception as e:
            print(f"Error in enhanced_draw: {e}")
            # Try to recover by getting a fresh screen reference
            try:
                screen_to_use = pygame.display.get_surface()
                if screen_to_use is not None:
                    # CRITICAL FIX: Update the screen attribute on the game object
                    # This ensures that the screen attribute is always up-to-date
                    game.screen = screen_to_use
                    print("CRITICAL FIX: Updated screen attribute on game object during error recovery")

                    # Draw credit info if game is started
                    if hasattr(game, 'game_started') and game.game_started:
                        draw_credit_info(game, screen_to_use, usage_tracker)
            except Exception as nested_e:
                print(f"Failed to recover from draw error: {nested_e}")

    # Replace methods
    game.start_game = enhanced_start_game
    game.draw = enhanced_draw

    # Add usage tracker to game
    game.usage_tracker = usage_tracker

    # CRITICAL FIX: Add a direct reference to the voucher manager in the game object
    # This ensures the voucher manager is accessible when exiting the game
    if not hasattr(game, 'voucher_manager'):
        game.voucher_manager = usage_tracker.voucher_manager
        print("CRITICAL FIX: Added voucher_manager reference to game object")

    # CRITICAL FIX: Add a method to ensure the usage tracker is active when needed
    def ensure_usage_tracker_active():
        """Ensure the usage tracker is active if the game is started."""
        if hasattr(game, 'game_started') and game.game_started and not usage_tracker.active:
            print("CRITICAL FIX: Game is started but usage tracker is not active. Activating it now.")
            usage_tracker.active = True
            usage_tracker.game_start_time = time.time() - 60  # Assume game has been running for at least 1 minute
            usage_tracker.current_game_id = f"game_{int(time.time())}"
            print(f"Usage tracker activated: active={usage_tracker.active}, game_id={usage_tracker.current_game_id}")

    # Add the method to the game object
    game.ensure_usage_tracker_active = ensure_usage_tracker_active

    # CRITICAL FIX: Override the original start_game method to ensure usage tracker is active
    original_original_start_game = original_start_game

    def super_enhanced_start_game():
        """Super enhanced start_game method that ensures usage tracker is active."""
        result = enhanced_start_game()
        # Ensure usage tracker is active after starting the game
        if result and hasattr(game, 'ensure_usage_tracker_active'):
            game.ensure_usage_tracker_active()
        return result

    # Replace the enhanced_start_game method with the super enhanced version
    game.start_game = super_enhanced_start_game
    print("CRITICAL FIX: Replaced start_game method with super enhanced version")

    return True

def draw_credit_info(game, screen_to_use, usage_tracker):
    """
    Draw credit usage information on the game screen.

    Args:
        game: BingoGame instance
        screen_to_use: Pygame screen surface
        usage_tracker: UsageTracker instance
    """
    try:
        # Validate screen_to_use
        if screen_to_use is None or not hasattr(screen_to_use, 'get_size'):
            print("Error: Invalid screen surface in draw_credit_info")
            return

        # Get screen dimensions with error handling
        try:
            screen_width, screen_height = screen_to_use.get_size()
            if screen_width <= 0 or screen_height <= 0:
                print(f"Error: Invalid screen dimensions: {screen_width}x{screen_height}")
                return
        except Exception as e:
            print(f"Error getting screen size in draw_credit_info: {e}")
            return

        # Validate game object has required attributes
        if not hasattr(game, 'scale_x') or not hasattr(game, 'scale_y'):
            print("Error: Game object missing scale attributes")
            return

        # Check if game info should be displayed - directly from settings manager
        from settings_manager import SettingsManager
        settings_manager = SettingsManager()
        show_game_info = settings_manager.get_setting('game', 'show_game_info', True)

        # If game info is disabled, don't draw anything
        if not show_game_info:
            return

        # Check if we're in demo mode - use both game flag and tracker flag
        is_demo_mode = (hasattr(game, 'is_demo_mode') and game.is_demo_mode) or (hasattr(usage_tracker, 'in_demo_mode') and usage_tracker.in_demo_mode)

        if is_demo_mode:
            try:
                # In demo mode, show a prominent demo mode indicator
                demo_font = pygame.font.SysFont("Arial", int(18 * min(game.scale_x, game.scale_y)), bold=True)
                demo_text = "DEMO MODE - No Credits Used"
                demo_color = (255, 215, 0)  # Gold color

                # Create a background for the demo text
                demo_surface = demo_font.render(demo_text, True, demo_color)
                demo_rect = demo_surface.get_rect()
                demo_rect.topright = (screen_width - int(10 * game.scale_x), int(10 * game.scale_y))

                # Draw a semi-transparent background - use a valid size
                bg_rect = demo_rect.inflate(int(20 * game.scale_x), int(10 * game.scale_y))
                if bg_rect.width > 0 and bg_rect.height > 0:
                    bg_surface = pygame.Surface((bg_rect.width, bg_rect.height), pygame.SRCALPHA)
                    bg_surface.fill((0, 0, 0, 150))  # Semi-transparent black
                    screen_to_use.blit(bg_surface, bg_rect.topleft)

                    # Draw text with shadow for better visibility
                    shadow_offset = int(2 * min(game.scale_x, game.scale_y))
                    shadow_surface = demo_font.render(demo_text, True, (0, 0, 0))
                    screen_to_use.blit(shadow_surface, (demo_rect.x + shadow_offset, demo_rect.y + shadow_offset))
                    screen_to_use.blit(demo_surface, demo_rect)
                else:
                    print(f"Warning: Invalid background rect dimensions: {bg_rect.width}x{bg_rect.height}")
            except Exception as e:
                print(f"Error drawing demo mode indicator: {e}")

            # No need to show credit info in demo mode
            return

        # Normal mode - show credit info
        try:
            # Get total bets from game
            total_bets = 0
            if hasattr(game, 'bet_amount') and hasattr(game, 'players'):
                total_bets = game.bet_amount * len(game.players)

            # Get commission percentage from game settings
            if hasattr(game, 'commission_percentage'):
                commission_percentage = game.commission_percentage
            elif hasattr(game, 'settings') and isinstance(game.settings, dict):
                commission_percentage = game.settings.get('game', {}).get('commission_percentage', 20)
            else:
                commission_percentage = 20  # Default commission percentage

            # Get usage info with the game parameters
            usage_info = usage_tracker.get_estimated_usage(
                total_bets=total_bets,
                commission_percentage=commission_percentage
            )

            # Calculate position for credit info
            info_x = int(10 * game.scale_x)
            info_y = screen_height - int(30 * game.scale_y)

            # Prepare text
            font = pygame.font.SysFont("Arial", int(14 * min(game.scale_x, game.scale_y)))

            if usage_info["active"]:
                # Format duration as minutes:seconds
                minutes = int(usage_info["duration"] / 60)
                seconds = int(usage_info["duration"] % 60)
                duration_str = f"{minutes:02d}:{seconds:02d}"

                if usage_info["total_bets"] > 0:
                    # Create detailed text with commission calculation
                    text = (
                        f"Credits: {usage_info['credits_used']} | "
                        f"Commission: {usage_info['referee_commission']:.2f} ETB ({usage_info['commission_percentage']}%) | "
                        f"Share: {usage_info['share_percentage']}% | "
                        f"Balance: {usage_info['remaining_credits']}"
                    )
                else:
                    # Show simpler text if no bets yet
                    text = f"Credits: 0 | Time: {duration_str} | Balance: {usage_tracker.voucher_manager.credits}"

                text_color = (200, 200, 200)  # Light gray
            else:
                # Show only balance
                text = f"Credits: {usage_tracker.voucher_manager.credits}"
                text_color = (150, 150, 150)  # Darker gray

            # Render text
            text_surface = font.render(text, True, text_color)

            # Draw text with shadow for better visibility
            shadow_offset = int(1 * min(game.scale_x, game.scale_y))
            shadow_surface = font.render(text, True, (0, 0, 0))
            screen_to_use.blit(shadow_surface, (info_x + shadow_offset, info_y + shadow_offset))
            screen_to_use.blit(text_surface, (info_x, info_y))
        except Exception as e:
            print(f"Error drawing credit info details: {e}")
    except Exception as e:
        print(f"Error in draw_credit_info: {e}")
