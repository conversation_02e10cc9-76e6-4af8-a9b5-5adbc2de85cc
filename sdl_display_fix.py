"""
SDL Display Fix for External Monitors

This module must be imported BEFORE pygame to set critical SDL environment variables
that fix external display issues, particularly with HDMI connections.
"""

import os
import sys


def apply_sdl_display_fixes():
    """
    Apply SDL environment variable fixes for external display compatibility.
    This must be called BEFORE pygame.init() or pygame.display.init().
    """
    print("Applying SDL display fixes for external monitors...")
    
    # Core SDL video settings for external display stability
    sdl_fixes = {
        # Video driver and rendering
        'SDL_VIDEODRIVER': 'windows' if sys.platform.startswith('win') else None,
        'SDL_HINT_RENDER_DRIVER': 'direct3d',
        'SDL_HINT_RENDER_VSYNC': '1',
        'SDL_HINT_RENDER_SCALE_QUALITY': '1',
        
        # Window positioning and behavior
        'SDL_VIDEO_WINDOW_POS': 'centered',
        'SDL_VIDEO_CENTERED': '1',
        'SDL_VIDEO_MINIMIZE_ON_FOCUS_LOSS': '0',
        'SDL_VIDEO_ALLOW_SCREENSAVER': '1',
        'SDL_HINT_VIDEO_ALLOW_SCREENSAVER': '1',
        
        # Display mode and refresh
        'SDL_HINT_VIDEO_HIGHDPI_DISABLED': '1',
        'SDL_HINT_WINDOWS_DISABLE_THREAD_NAMING': '1',
        'SDL_HINT_WINDOWS_ENABLE_MESSAGELOOP': '1',
        
        # External display specific fixes
        'SDL_HINT_VIDEO_EXTERNAL_CONTEXT': '1',
        'SDL_HINT_VIDEO_MINIMIZE_ON_FOCUS_LOSS': '0',
        'SDL_HINT_GRAB_KEYBOARD': '0',
        
        # Performance and compatibility
        'SDL_HINT_FRAMEBUFFER_ACCELERATION': '1',
        'SDL_HINT_RENDER_BATCHING': '1',
        'SDL_HINT_THREAD_STACK_SIZE': '1048576',  # 1MB stack size
        
        # HDMI and external monitor specific
        'SDL_HINT_VIDEO_X11_NET_WM_BYPASS_COMPOSITOR': '0',
        'SDL_HINT_VIDEO_X11_FORCE_EGL': '0',
        'SDL_HINT_RENDER_OPENGL_SHADERS': '1',
        
        # Windows-specific external display fixes
        'SDL_HINT_WINDOWS_DPI_AWARENESS': 'permonitorv2',
        'SDL_HINT_WINDOWS_DPI_SCALING': '1',
        'SDL_HINT_MOUSE_FOCUS_CLICKTHROUGH': '1',
        
        # Additional stability settings
        'SDL_HINT_TIMER_RESOLUTION': '1',
        'SDL_HINT_THREAD_PRIORITY_POLICY': '1',
        'SDL_HINT_VIDEO_DOUBLE_BUFFER': '1',
    }
    
    # Apply SDL environment variables
    applied_count = 0
    for key, value in sdl_fixes.items():
        if value is not None and key not in os.environ:
            os.environ[key] = value
            applied_count += 1
            print(f"  Set {key} = {value}")
    
    print(f"Applied {applied_count} SDL display fixes")
    
    # Additional Windows-specific registry-based fixes
    if sys.platform.startswith('win'):
        try:
            apply_windows_display_fixes()
        except Exception as e:
            print(f"Warning: Could not apply Windows-specific fixes: {e}")


def apply_windows_display_fixes():
    """
    Apply Windows-specific display fixes for external monitors
    """
    try:
        import winreg
        print("Applying Windows-specific display fixes...")
        
        # These are read-only checks - we don't modify the registry
        # Just detect potential issues and provide recommendations
        
        # Check for DPI awareness settings
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Control Panel\Desktop", 0, winreg.KEY_READ)
            try:
                dpi_scaling = winreg.QueryValueEx(key, "Win8DpiScaling")[0]
                print(f"  DPI Scaling setting: {dpi_scaling}")
            except FileNotFoundError:
                print("  DPI Scaling setting: Not configured")
            winreg.CloseKey(key)
        except Exception as e:
            print(f"  Could not check DPI settings: {e}")
        
        print("Windows display compatibility check completed")
        
    except ImportError:
        print("Windows registry module not available")
    except Exception as e:
        print(f"Error checking Windows display settings: {e}")


def get_display_recommendations():
    """
    Get recommendations for external display setup
    """
    recommendations = [
        "External Display Setup Recommendations:",
        "1. Ensure HDMI cable is properly connected",
        "2. Set external display as primary in Windows Display Settings",
        "3. Use 'Extend' or 'Duplicate' display mode",
        "4. Update graphics drivers to latest version",
        "5. Try different HDMI ports if available",
        "6. Check display refresh rate compatibility",
        "7. Disable Windows display scaling if issues persist",
    ]
    
    return recommendations


def diagnose_display_issues():
    """
    Diagnose potential display issues and provide solutions
    """
    print("\n" + "="*60)
    print("DISPLAY ISSUE DIAGNOSIS")
    print("="*60)
    
    issues_found = []
    
    # Check SDL environment variables
    critical_vars = ['SDL_VIDEODRIVER', 'SDL_HINT_RENDER_DRIVER', 'SDL_VIDEO_CENTERED']
    for var in critical_vars:
        if var not in os.environ:
            issues_found.append(f"Missing SDL variable: {var}")
    
    # Check platform-specific issues
    if sys.platform.startswith('win'):
        if 'SDL_HINT_WINDOWS_DPI_AWARENESS' not in os.environ:
            issues_found.append("Windows DPI awareness not configured")
    
    # Report findings
    if issues_found:
        print("Potential issues found:")
        for issue in issues_found:
            print(f"  - {issue}")
        print("\nRecommendation: Run apply_sdl_display_fixes() before pygame.init()")
    else:
        print("No obvious SDL configuration issues detected")
    
    # Display recommendations
    print("\n" + "\n".join(get_display_recommendations()))


# Auto-apply fixes when module is imported
if __name__ != "__main__":
    apply_sdl_display_fixes()


if __name__ == "__main__":
    # When run directly, provide diagnostic information
    print("SDL Display Fix Diagnostic Tool")
    print("="*40)
    
    print("\nCurrent SDL Environment Variables:")
    sdl_vars = {k: v for k, v in os.environ.items() if k.startswith('SDL_')}
    if sdl_vars:
        for key, value in sorted(sdl_vars.items()):
            print(f"  {key} = {value}")
    else:
        print("  No SDL environment variables set")
    
    diagnose_display_issues()
    
    print(f"\nTo apply fixes, import this module before pygame:")
    print(f"  import sdl_display_fix")
    print(f"  import pygame")
