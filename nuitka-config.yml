# Nuitka Configuration File
# This file provides detailed configuration for Nuitka builds

# Python module configuration
python-flag: no_site
python-flag: no_warnings

# Include these modules explicitly
include-package: pygame
include-package: json
include-package: datetime
include-package: colorsys
include-package: math
include-package: random
include-package: time

# Plugin configuration
enable-plugin: data-files
enable-plugin: numpy
enable-plugin: multiprocessing
enable-plugin: tk-inter

# Data files to include
include-data-dir: assets=assets
include-data-dir: data=data

# Output configuration
output-dir: build
windows-icon-from-ico: assets/app_logo.ico
windows-company-name: "Bingo Game"
windows-product-name: "Bingo Game"
windows-file-version: 1.0.0
windows-product-version: 1.0.0
file-reference-choice: runtime

# Optimization settings
lto: yes
jobs: 4
clang: yes
mingw64: no
windows-console-mode: disable
assume-yes-for-downloads: yes

# Advanced options
show-memory: yes
show-progress: yes
verbose: yes
follow-imports: yes
prefer-source-code: no

# Experimental features
experimental: use_pgo
experimental: use_ccache
