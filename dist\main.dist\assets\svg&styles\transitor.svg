<?xml version="1.0" encoding="UTF-8"?>
<!-- Modern Widescreen Advertising Board – "WOW BINGO" demo -->
<svg xmlns="http://www.w3.org/2000/svg"
     viewBox="0 0 1400 600" width="100%" height="100%" overflow="visible">

  <!-- ============ DEFINITIONS ============ -->
  <!-- Screen glow behind the whole board -->
  <defs>
    <!-- Outer turquoise glow -->
    <filter id="frameGlow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="25" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="blur"/>
        <feMergeNode in="blur"/>
      </feMerge>
    </filter>

    <!-- Neon text glow -->
    <filter id="neon" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="4" result="blur1"/>
      <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="blur2"/>
      <feMerge>
        <feMergeNode in="blur1"/>
        <feMergeNode in="blur2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradient sweep across the LED screen -->
    <linearGradient id="screenGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%"  stop-color="#101020"/>
      <stop offset="50%" stop-color="#2c2c40" id="midStop">
        <animate attributeName="offset" values="0.5;0.8;0.5" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#101020"/>
      <!-- Optional hue animation -->
      <animate attributeName="x1" values="0;1;0" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="x2" values="1;0;1" dur="8s" repeatCount="indefinite"/>
    </linearGradient>

    <!-- Thin glass reflection highlight -->
    <linearGradient id="glass" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="0" y2="600">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.15"/>
      <stop offset="50%" stop-color="#ffffff" stop-opacity="0.00"/>
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0.20"/>
    </linearGradient>
  </defs>

  <!-- ============ BACKBOARD ============ -->
  <!-- Slight drop glow onto background -->
  <rect x="100" y="80" width="1200" height="440" rx="28" ry="28"
        fill="#0c0c0f" filter="url(#frameGlow)"/>

  <!-- Metal frame with subtle gradient -->
  <rect x="100" y="80" width="1200" height="440" rx="28" ry="28"
        fill="url(#screenGrad)" stroke="#1d1d25" stroke-width="12"/>

  <!-- Glass reflection overlay -->
  <rect x="100" y="80" width="1200" height="440" rx="28" ry="28"
        fill="url(#glass)" pointer-events="none"/>

  <!-- ============ TEXT & CONTENT ============ -->
  <g filter="url(#neon)">
    <!-- Amharic headline -->
    <text x="700" y="260" text-anchor="middle"
          font-family="'Noto Sans Ethiopic', 'Nyala', sans-serif"
          font-size="120" font-weight="700"
          fill="#00f0ff">
      ዋው ቢንጎ
      <animate attributeName="fill" values="#00f0ff;#00bbff;#00f0ff" dur="3s" repeatCount="indefinite"/>
    </text>

    <!-- English subtitle -->
    <text x="700" y="340" text-anchor="middle"
          font-family="Montserrat, sans-serif"
          font-size="56" font-weight="600"
          letter-spacing="2"
          fill="#ff6688">
      WOW BINGO
      <animate attributeName="fill" values="#ff6688;#ff99aa;#ff6688" dur="3s" repeatCount="indefinite"/>
    </text>
  </g>

  <!-- Call‑to‑action strip -->
  <rect x="160" y="380" width="1080" height="90" rx="14" ry="14"
        fill="#ff0066">
    <animate attributeName="fill" values="#ff0066;#ff3388;#ff0066" dur="5s" repeatCount="indefinite"/>
  </rect>

  <text x="700" y="440" text-anchor="middle"
        font-family="Montserrat, sans-serif"
        font-size="38" font-weight="600"
        fill="#ffffff">
    እባኮን በኃላፊነት ይጫወቱ! - (WOW GAMES)
  </text>
</svg>