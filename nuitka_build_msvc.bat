@echo off
:: ================================================================
:: Bingo Game - MSVC Optimized Nuitka Build Script
:: ================================================================
:: This script compiles the Python bingo game using Nuitka with
:: MSVC compiler and performance optimizations.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PROJECT_NAME=WOW Bingo Game"
set "PROJECT_VERSION=1.0.0"
set "MAIN_SCRIPT=main.py"
set "ICON_PATH=assets\app_logo.ico"
set "BUILD_DIR=build_msvc"
set "OUTPUT_NAME=WOWBingoGame"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - MSVC Optimized Compilation%RESET%
echo %CYAN%================================================================%RESET%
echo.

:: Check if main.py exists
if not exist "%MAIN_SCRIPT%" (
    echo %RED%Error: %MAIN_SCRIPT% not found!%RESET%
    pause
    exit /b 1
)

:: Check if icon exists
if not exist "%ICON_PATH%" (
    echo %YELLOW%Warning: Icon file %ICON_PATH% not found. Using default icon.%RESET%
    set "ICON_PATH="
)

:: Clean previous build
if exist "%BUILD_DIR%" (
    echo %YELLOW%Cleaning previous build directory...%RESET%
    rd /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

:: Verify Python and Nuitka
echo %CYAN%Verifying Python and Nuitka installation...%RESET%
python --version
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found!%RESET%
    pause
    exit /b 1
)

python -m nuitka --version
if %errorlevel% neq 0 (
    echo %RED%Error: Nuitka not found!%RESET%
    pause
    exit /b 1
)

:: Check for required assets
echo %CYAN%Verifying required assets...%RESET%
if not exist "assets" (
    echo %RED%Error: assets directory not found!%RESET%
    pause
    exit /b 1
)

if not exist "data" (
    echo %YELLOW%Warning: data directory not found. Creating empty data directory.%RESET%
    mkdir "data"
)

:: Clean app data for fresh build
echo %CYAN%Cleaning app data for fresh build...%RESET%
if exist "clean_app.py" (
    python clean_app.py --silent
    if %errorlevel% neq 0 (
        echo %YELLOW%Warning: Could not clean app data. Build will continue.%RESET%
    ) else (
        echo %GREEN%App data cleaned successfully.%RESET%
    )
)

:: Get CPU count for parallel compilation
set /a CPU_COUNT=%NUMBER_OF_PROCESSORS%
if %CPU_COUNT% lss 1 set CPU_COUNT=1
echo %CYAN%Using %CPU_COUNT% CPU cores for compilation...%RESET%

echo.
echo %GREEN%Starting MSVC optimized Nuitka compilation...%RESET%
echo %CYAN%This may take several minutes depending on your system.%RESET%
echo.

:: Build the optimized executable with MSVC compiler
python -m nuitka ^
    --standalone ^
    --onefile ^
    --output-filename=%OUTPUT_NAME%.exe ^
    --output-dir=%BUILD_DIR% ^
    --windows-icon-from-ico=%ICON_PATH% ^
    --windows-company-name="%PROJECT_NAME%" ^
    --windows-product-name="%PROJECT_NAME%" ^
    --windows-file-version=%PROJECT_VERSION% ^
    --windows-product-version=%PROJECT_VERSION% ^
    --windows-file-description="WOW Bingo Game - Professional Bingo Gaming Application" ^
    --disable-console ^
    --assume-yes-for-downloads ^
    --include-data-dir=assets=assets ^
    --include-data-dir=data=data ^
    --include-package=pygame ^
    --include-package=json ^
    --include-package=datetime ^
    --include-package=sqlite3 ^
    --include-package=pyperclip ^
    --enable-plugin=multiprocessing ^
    --lto=yes ^
    --msvc=latest ^
    --jobs=%CPU_COUNT% ^
    --show-memory ^
    --show-progress ^
    --verbose ^
    --remove-output ^
    %MAIN_SCRIPT%

:: Check build result
if %errorlevel% neq 0 (
    echo.
    echo %RED%================================================================%RESET%
    echo %RED%    BUILD FAILED!%RESET%
    echo %RED%================================================================%RESET%
    echo %RED%The compilation process encountered errors.%RESET%
    echo %YELLOW%Please check the output above for specific error messages.%RESET%
    echo.
    echo %CYAN%Common solutions:%RESET%
    echo - Make sure Visual Studio Build Tools are properly installed
    echo - Try running as administrator
    echo - Check that all dependencies are available
    echo.
    pause
    exit /b 1
)

:: Success message
echo.
echo %GREEN%================================================================%RESET%
echo %GREEN%    BUILD COMPLETED SUCCESSFULLY!%RESET%
echo %GREEN%================================================================%RESET%
echo.

:: Find the generated executable
set "EXECUTABLE_PATH=%BUILD_DIR%\%OUTPUT_NAME%.exe"
if exist "%EXECUTABLE_PATH%" (
    echo %GREEN%Executable created: %EXECUTABLE_PATH%%RESET%

    :: Get file size
    for %%F in ("%EXECUTABLE_PATH%") do (
        set "FILE_SIZE=%%~zF"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1048576"
    )
    echo %CYAN%File size: !FILE_SIZE_MB! MB%RESET%

    :: Get creation time
    for %%F in ("%EXECUTABLE_PATH%") do (
        echo %CYAN%Created: %%~tF%RESET%
    )

    echo.
    echo %YELLOW%Optimizations Applied:%RESET%
    echo - Link-time optimization (LTO)
    echo - MSVC compiler optimizations
    echo - Multi-threaded compilation
    echo - Standalone executable
    echo - Embedded assets and data
    echo - Production-ready build
    echo.

    echo %GREEN%The executable is ready for distribution!%RESET%
    echo %CYAN%You can now run: %EXECUTABLE_PATH%%RESET%
    echo.

    :: Ask if user wants to test the executable
    set /p "TEST_CHOICE=Do you want to test the executable now? (y/n): "
    if /i "!TEST_CHOICE!"=="y" (
        echo %CYAN%Starting the executable...%RESET%
        start "" "%EXECUTABLE_PATH%"
    )

) else (
    echo %RED%Error: Executable not found at expected location!%RESET%
    echo %YELLOW%Check the %BUILD_DIR% directory for the executable.%RESET%
)

echo.
echo %CYAN%Build process completed.%RESET%
pause
