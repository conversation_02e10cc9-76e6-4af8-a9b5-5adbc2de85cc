# -*- mode: python ; coding: utf-8 -*-
"""
WOW Bingo Game - Comprehensive PyInstaller Spec File
===================================================

This spec file creates a standalone executable for the WOW Bingo Game
that can run on any Windows PC without requiring Python installation.

Features:
- Includes all game assets (audio, images, splash screens)
- Bundles all required Python packages
- Creates a single executable file
- Includes proper Windows metadata and icon
- Optimized for distribution

Usage:
    pyinstaller WOW_Bingo_Game.spec
"""

import os
import sys
from pathlib import Path

# Get the project root directory
project_root = Path(__file__).parent.absolute()

# Define paths
assets_dir = project_root / "assets"
data_dir = project_root / "data"
splash_dir = assets_dir / "Splash_screen"
audio_dir = assets_dir / "audio-effects"
cartella_dir = assets_dir / "cartella-announcer"
cancellation_dir = assets_dir / "cartella-cancellation-announcements"

# Collect all data files and directories
datas = []

# Add main asset directories
if assets_dir.exists():
    datas.append((str(assets_dir), 'assets'))

if data_dir.exists():
    datas.append((str(data_dir), 'data'))

# Add specific important files
important_files = [
    'requirements.txt',
    'build_requirements.txt',
    'minimal_requirements.txt',
]

for file_path in important_files:
    if (project_root / file_path).exists():
        datas.append((str(project_root / file_path), '.'))

# Hidden imports - packages that PyInstaller might miss
hiddenimports = [
    # Core game modules
    'pygame',
    'pygame.mixer',
    'pygame.font',
    'pygame.image',
    'pygame.display',
    'pygame.event',
    'pygame.key',
    'pygame.mouse',
    'pygame.time',
    'pygame.transform',
    'pygame.surface',
    'pygame.rect',
    'pygame.color',
    'pygame.gfxdraw',
    
    # Audio processing
    'pydub',
    'pydub.audio_segment',
    
    # System utilities
    'pyperclip',
    'psutil',
    
    # Image processing
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # Database
    'sqlite3',
    'json',
    
    # Cryptography
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'cryptography.hazmat.primitives',
    'cryptography.hazmat.backends',
    
    # Configuration
    'pydantic',
    'pydantic_settings',
    
    # Logging
    'loguru',
    
    # Network
    'requests',
    'urllib3',
    
    # Math and utilities
    'numpy',
    'math',
    'random',
    'colorsys',
    'datetime',
    'time',
    'os',
    'sys',
    'pathlib',
    'threading',
    'multiprocessing',
    
    # Windows-specific
    'win32api',
    'win32con',
    'win32gui',
    'win32process',
    'winsound',
    
    # Optional but commonly used
    'tkinter',
    'tkinter.filedialog',
    'tkinter.messagebox',
    
    # Modern UI (if using Flet)
    'flet',
    'flet_core',
    
    # RethinkDB (if available)
    'rethinkdb',
]

# Binaries - additional DLLs or executables
binaries = []

# Exclude unnecessary modules to reduce size
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
    'sphinx',
    'pytest',
    'setuptools',
    'distutils',
    'email',
    'html',
    'http',
    'urllib',
    'xml',
    'test',
    'tests',
    'testing',
    'unittest',
    'doctest',
    'pdb',
    'profile',
    'pstats',
    'cProfile',
    'trace',
    'traceback',
    'warnings',
    'weakref',
    'pickle',
    'pickletools',
    'shelve',
    'dbm',
    'csv',
    'configparser',
    'argparse',
    'getopt',
    'optparse',
    'logging.handlers',
    'logging.config',
]

# Analysis configuration
a = Analysis(
    ['main.py'],  # Main script
    pathex=[str(project_root)],  # Additional paths
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
    optimize=0,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WOW_Bingo_Game',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Enable UPX compression
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Hide console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    
    # Windows-specific options
    icon=str(assets_dir / 'app_logo.ico') if (assets_dir / 'app_logo.ico').exists() else None,
    version_file=None,  # We'll create this separately if needed
    
    # Metadata
    uac_admin=False,  # Don't require admin privileges
    uac_uiaccess=False,
)

# Optional: Create a COLLECT for debugging (creates a directory instead of single file)
# Uncomment the following lines if you want a directory distribution instead
"""
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='WOW_Bingo_Game_Dir'
)
"""
