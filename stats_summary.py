from stats_db import get_stats_db_manager
try:
    from stats_preloader import get_stats_preloader
    PRELOADER_AVAILABLE = True
except ImportError:
    PRELOADER_AVAILABLE = False
from datetime import datetime

def get_stats_summary():
    """
    Get a summary of statistics for display on the stats page.
    Uses the preloader for improved performance when available.

    Returns:
        dict: Statistics summary
    """
    # Try to use preloader first for better performance
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()
            weekly_stats = preloader.get_cached_data('weekly_stats')
            total_earnings = preloader.get_cached_data('total_earnings')
            daily_earnings = preloader.get_cached_data('daily_earnings')
            daily_games = preloader.get_cached_data('daily_games')
            wallet_balance = preloader.get_cached_data('wallet_balance')
            if weekly_stats and total_earnings is not None:
                return {
                    "weekly_stats": weekly_stats,
                    "total_earnings": total_earnings,
                    "daily_earnings": daily_earnings,
                    "daily_games": daily_games,
                    "wallet_balance": wallet_balance
                }
            preloader.start_preloading()
        except Exception as e:
            print(f"Error using stats preloader: {e}")
    stats_db = get_stats_db_manager()
    today = datetime.now().strftime('%Y-%m-%d')
    weekly_stats = stats_db.get_weekly_stats()
    total_earnings = stats_db.get_total_earnings()
    daily_earnings = stats_db.get_daily_earnings()
    daily_games = stats_db.get_daily_games_played()
    wallet_balance = stats_db.get_wallet_balance()
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()
            preloader.cache.set('weekly_stats', weekly_stats)
            preloader.cache.set('total_earnings', total_earnings)
            preloader.cache.set('daily_earnings', daily_earnings)
            preloader.cache.set('daily_games', daily_games)
            preloader.cache.set('wallet_balance', wallet_balance)
        except Exception as e:
            print(f"Error caching stats in preloader: {e}")
    return {
        "weekly_stats": weekly_stats,
        "total_earnings": total_earnings,
        "daily_earnings": daily_earnings,
        "daily_games": daily_games,
        "wallet_balance": wallet_balance
    } 