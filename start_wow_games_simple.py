"""
Simple WOW Games Launcher

This is a simplified launcher that assumes RethinkDB is already running
and just starts the web dashboard and game.
"""

import os
import sys
import time
import subprocess
import threading
import signal
import webbrowser
from datetime import datetime

class SimpleWOWGamesLauncher:
    """Simple launcher for WOW Games when RethinkDB is already running."""
    
    def __init__(self):
        """Initialize the launcher."""
        self.processes = []
        self.dashboard_process = None
        self.game_process = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
    
    def run_command_background(self, command, description):
        """Run a command in background."""
        try:
            self.log(f"Starting: {description}")
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes.append(process)
            self.log(f"🚀 {description} started in background (PID: {process.pid})")
            return process
            
        except Exception as e:
            self.log(f"❌ Error starting {description}: {e}", "ERROR")
            return None
    
    def check_rethinkdb_status(self):
        """Check if RethinkDB is running."""
        try:
            result = subprocess.run(
                [sys.executable, "setup_rethinkdb.py", "--check"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return "RethinkDB is running" in result.stdout
        except:
            return False
    
    def start_web_dashboard(self):
        """Start the web dashboard."""
        self.log("🌐 Starting Web Dashboard...")
        
        self.dashboard_process = self.run_command_background(
            [sys.executable, "rethink_dashboard_fixed.py"],
            "Web dashboard"
        )
        
        if self.dashboard_process:
            time.sleep(3)
            self.log("🌐 Web dashboard should be available at: http://localhost:5000")
            return True
        
        return False
    
    def start_game(self):
        """Start the main game."""
        self.log("🎮 Starting WOW Games Application...")
        
        self.game_process = self.run_command_background(
            [sys.executable, "main.py"],
            "WOW Games application"
        )
        
        if self.game_process:
            self.log("🎮 WOW Games application started successfully!")
            return True
        
        return False
    
    def open_dashboard_in_browser(self):
        """Open the web dashboard in the default browser."""
        try:
            time.sleep(2)
            webbrowser.open("http://localhost:5000")
            self.log("🌐 Opened web dashboard in browser")
        except Exception as e:
            self.log(f"⚠️ Could not open browser: {e}", "WARNING")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.log("\n🛑 Shutdown signal received. Cleaning up...")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """Clean up all processes."""
        self.log("🧹 Cleaning up processes...")
        
        for process in self.processes:
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                    self.log(f"✅ Terminated process {process.pid}")
            except Exception as e:
                self.log(f"⚠️ Error terminating process: {e}", "WARNING")
        
        self.log("✅ Cleanup completed")
    
    def print_startup_summary(self):
        """Print a summary of the startup process."""
        self.log("=" * 80)
        self.log("🎉 WOW GAMES STARTUP COMPLETED!")
        self.log("=" * 80)
        self.log("")
        self.log("📋 SERVICES RUNNING:")
        self.log("   🗄️  RethinkDB Server: http://localhost:8081 (Admin)")
        self.log("   🌐 Web Dashboard: http://localhost:5000")
        self.log("   🎮 WOW Games: Main application window")
        self.log("")
        self.log("🔧 MANAGEMENT:")
        self.log("   • Use the web dashboard to monitor performance")
        self.log("   • Check RethinkDB admin for database status")
        self.log("   • Press Ctrl+C to shutdown all services")
        self.log("=" * 80)
    
    def run(self):
        """Run the simple startup sequence."""
        self.log("🚀 SIMPLE WOW GAMES LAUNCHER")
        self.log("=" * 60)
        
        # Check if RethinkDB is running
        if not self.check_rethinkdb_status():
            self.log("❌ RethinkDB is not running!", "ERROR")
            self.log("Please run the database fix script first:")
            self.log("   python fix_rethinkdb_database.py")
            return False
        
        self.log("✅ RethinkDB is running")
        
        try:
            # Start web dashboard
            if not self.start_web_dashboard():
                self.log("❌ Failed to start web dashboard", "ERROR")
                return False
            
            # Start the game
            if not self.start_game():
                self.log("❌ Failed to start game", "ERROR")
                return False
            
            # Open dashboard in browser
            threading.Thread(target=self.open_dashboard_in_browser, daemon=True).start()
            
            # Print summary
            self.print_startup_summary()
            
            # Keep the script running
            self.log("✅ All services started successfully!")
            self.log("Press Ctrl+C to shutdown all services...")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            self.log(f"❌ Startup failed: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    """Main entry point."""
    print("WOW Games Simple Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found. Please run this script from the game directory.")
        sys.exit(1)
    
    # Create and run launcher
    launcher = SimpleWOWGamesLauncher()
    success = launcher.run()
    
    if not success:
        print("\n❌ Startup failed. Check the logs above for details.")
        print("\n💡 Try running the database fix script first:")
        print("   python fix_rethinkdb_database.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
