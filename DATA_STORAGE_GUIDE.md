# 📁 WOW Bingo Game - Data Storage Guide

## 🎯 **Current Situation Analysis**

Your WOW Bingo Game currently stores data using **relative paths** in a local `data/` directory. This approach works for development but has limitations for compiled executables and different deployment scenarios.

### **Current Data Storage:**
```
📁 Current Directory/
├── 📁 data/
│   ├── 📄 settings.json          # Game settings
│   ├── 📄 players.json           # Player data
│   ├── 📄 game_settings.json     # Game configuration
│   ├── 📄 bingo_boards.json      # Bingo board data
│   ├── 📄 ui_state.json          # UI state
│   ├── 🗄️ game_stats.db          # Game statistics (SQLite)
│   ├── 🗄️ players.db             # Player database (SQLite)
│   ├── 🗄️ vouchers.db            # Voucher system (SQLite)
│   └── 📁 exports/               # Exported files
```

---

## 🏗️ **Recommended Data Storage Strategy**

### **1. Smart Storage Location Detection**

The app should automatically choose the appropriate storage location based on how it's deployed:

#### **🔧 Development Mode** (Python source)
```
📁 Project Directory/
└── 📁 data/                      # Local development data
```

#### **💼 Portable Executable** (Standalone .exe)
```
📁 Executable Directory/
├── 🎮 WOWBingoGame.exe
├── 📄 portable.txt              # Portable mode indicator
└── 📁 data/                      # Portable data storage
```

#### **🏢 Installed Application** (Via installer)
```
📁 C:\Users\<USER>\AppData\Roaming\WOW Games\WOW Bingo Game\
└── 📁 data/                      # User-specific data storage
```

---

## 🌍 **Cross-Platform Storage Locations**

### **Windows (Recommended)**
```
# Installed Mode
C:\Users\<USER>\AppData\Roaming\WOW Games\WOW Bingo Game\data\

# Portable Mode  
[Executable Directory]\data\
```

### **macOS**
```
# Installed Mode
~/Library/Application Support/WOW Bingo Game/data/

# Portable Mode
[Executable Directory]/data/
```

### **Linux**
```
# Installed Mode
~/.local/share/wow-bingo-game/data/

# Portable Mode
[Executable Directory]/data/
```

---

## 📋 **Data Storage Categories**

### **1. User Settings & Preferences**
```
📄 settings.json                 # Game settings, audio, display
📄 ui_state.json                 # Window positions, UI preferences
📄 game_settings.json            # Game rules, commission rates
```
**Location**: User data directory (persistent across updates)

### **2. Game Data**
```
🗄️ game_stats.db                 # Game history, statistics
🗄️ players.db                    # Player information
📄 players.json                  # Player data backup
📄 bingo_boards.json             # Board configurations
```
**Location**: User data directory (persistent across updates)

### **3. System Data**
```
🗄️ vouchers.db                   # Voucher system
📄 rethink_config.json           # Database configuration
📁 sync_cache/                   # Synchronization cache
```
**Location**: User data directory (persistent across updates)

### **4. Temporary Data**
```
📁 temp/                         # Temporary files
📄 *.tmp                         # Temporary files
📄 *.lock                        # Lock files
📄 *.pid                         # Process ID files
```
**Location**: Temp directory (cleaned automatically)

### **5. Export Data**
```
📁 exports/                      # User exports
📄 *.pdf                         # Exported reports
📄 *.html                        # Exported data
```
**Location**: User data directory or user-chosen location

### **6. Backup Data**
```
📁 backups/                      # Automatic backups
📄 backup_YYYYMMDD_HHMMSS/       # Timestamped backups
```
**Location**: User data directory

---

## 🔧 **Implementation Strategy**

### **1. Data Storage Manager** (Already Created)
```python
from data_storage_manager import get_storage_manager

# Get storage manager
storage = get_storage_manager()

# Get file paths
settings_path = storage.get_config_path("settings.json")
database_path = storage.get_database_path("game_stats.db")
export_path = storage.get_export_dir()
```

### **2. Automatic Migration**
```python
# Migrate from old location if needed
from data_storage_manager import migrate_legacy_data
migrate_legacy_data()
```

### **3. Portable Mode Detection**
```python
# Check if running in portable mode
if storage.is_portable:
    print("Running in portable mode")
else:
    print("Running in installed mode")
```

---

## 🎮 **For Different Deployment Scenarios**

### **Scenario 1: Development/Testing**
```
✅ Use: Project directory/data/
✅ Benefits: Easy access, version control
✅ Suitable for: Development, testing
```

### **Scenario 2: Portable Distribution**
```
✅ Use: Executable directory/data/
✅ Benefits: Self-contained, no installation
✅ Suitable for: USB drives, temporary use
✅ Create: portable.txt indicator file
```

### **Scenario 3: Professional Installation**
```
✅ Use: User AppData directory
✅ Benefits: Proper Windows integration, user isolation
✅ Suitable for: Permanent installation, multiple users
✅ Survives: Application updates, reinstalls
```

### **Scenario 4: Enterprise Deployment**
```
✅ Use: Configurable data directory
✅ Benefits: Network storage, centralized management
✅ Suitable for: Corporate environments
✅ Supports: Group policies, shared data
```

---

## 🛡️ **Data Protection & Backup**

### **Automatic Backup System**
```python
# Create automatic backups
backup_path = storage.create_backup()
print(f"Backup created: {backup_path}")

# Cleanup old backups (keep last 10)
storage.cleanup_old_backups(keep=10)
```

### **Data Validation**
```python
# Validate data integrity
if storage.validate_data_integrity():
    print("Data is valid")
else:
    print("Data corruption detected, restoring from backup")
    storage.restore_from_backup()
```

---

## 📊 **Storage Requirements**

### **Minimum Storage Space**
- **Base Installation**: 100-200 MB
- **User Data**: 10-50 MB
- **Game Statistics**: 5-20 MB (grows over time)
- **Exports/Backups**: 10-100 MB (user-dependent)
- **Total Recommended**: 500 MB free space

### **Performance Considerations**
- **SSD vs HDD**: SSD recommended for database operations
- **Network Storage**: Supported but may impact performance
- **Cloud Sync**: Compatible with OneDrive, Dropbox, etc.

---

## 🔄 **Migration & Updates**

### **Updating Data Storage**
1. **Detect old data location**
2. **Copy data to new location**
3. **Validate migration**
4. **Update application to use new location**
5. **Clean up old location (optional)**

### **Version Compatibility**
- **Forward compatible**: New versions read old data
- **Backward compatible**: Data format versioning
- **Migration scripts**: Automatic data format updates

---

## 🎯 **Best Practices Summary**

### ✅ **DO:**
- Use the DataStorageManager for all file operations
- Detect deployment mode automatically
- Store user data in appropriate system directories
- Create automatic backups
- Validate data integrity
- Support portable mode for flexibility

### ❌ **DON'T:**
- Hardcode file paths
- Store data in Program Files
- Assume write permissions
- Mix user data with application files
- Ignore platform differences

---

## 🚀 **Implementation Steps**

### **1. Integrate Data Storage Manager**
```python
# Replace all hardcoded paths with:
from data_storage_manager import get_data_path, get_database_path

# Old way:
settings_file = "data/settings.json"

# New way:
settings_file = get_data_path("settings.json")
```

### **2. Update Build Scripts**
```python
# Include data storage manager in build
# Update Nuitka build to include data_storage_manager.py
```

### **3. Test All Scenarios**
```bash
# Test development mode
python main.py

# Test portable mode (create portable.txt)
dist/WOWBingoGame.exe

# Test installed mode
# Install via installer and test
```

---

## 🎉 **Benefits of This Approach**

✅ **User-Friendly**: Data persists across updates
✅ **Professional**: Follows platform conventions  
✅ **Flexible**: Supports portable and installed modes
✅ **Reliable**: Automatic backup and recovery
✅ **Scalable**: Handles growing data requirements
✅ **Cross-Platform**: Works on Windows, macOS, Linux
✅ **Enterprise-Ready**: Supports network storage and policies

**Your WOW Bingo Game will have professional-grade data management that works reliably across all deployment scenarios!**
