@echo off
:: WOW Bingo Game Executable Wrapper
:: This script ensures clean startup by handling cleanup before running the game

echo Starting WOW Bingo Game...

:: Kill any existing processes
taskkill /f /im "WOWBingoGame.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *main.py*" >nul 2>&1

:: Clean temporary files
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.tmp" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.lock" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.pid" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.db-wal" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.db-shm" >nul 2>&1

:: Wait a moment for cleanup
timeout /t 2 /nobreak >nul

:: Run the game
echo Launching game...
start "" "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\dist\WOWBingoGame.exe"

:: Exit wrapper
exit
