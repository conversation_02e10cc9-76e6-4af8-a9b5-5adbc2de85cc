"""
Test script for stats page integration.

This script tests the stats page integration by loading data from various sources
and validating it.
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta

# Create data directory if it doesn't exist
os.makedirs('data', exist_ok=True)
os.makedirs(os.path.join('data', 'cache'), exist_ok=True)

# Test database initialization
print("Testing database initialization...")
try:
    import thread_safe_db
    thread_safe_db.ensure_database_exists()
    print("Database initialized successfully")
except Exception as e:
    print(f"Error initializing database: {e}")
    import traceback
    traceback.print_exc()

# Test data generation
print("\nGenerating test data...")
try:
    # Get database connection
    conn = thread_safe_db.get_connection()
    cursor = conn.cursor()
    
    # Clear existing data for testing
    cursor.execute("DELETE FROM daily_stats")
    cursor.execute("DELETE FROM game_history")
    cursor.execute("DELETE FROM wallet_transactions")
    conn.commit()
    
    # Generate daily stats for the past week
    end_date = datetime.now()
    start_date = end_date - timedelta(days=6)
    current_date = start_date
    
    # Ensure no duplicate dates by deleting any existing records for the test range
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        cursor.execute('DELETE FROM daily_stats WHERE date = ?', (date_str,))
        current_date += timedelta(days=1)

    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        
        # Generate random data based on day of week (more on weekends)
        day_of_week = current_date.weekday()
        games_played = 5 + (day_of_week * 2)  # More games on weekends
        earnings = games_played * 10.5  # Simple calculation for test data
        winners = max(1, games_played // 2)
        total_players = games_played * 4
        
        # Insert data
        cursor.execute('''
        INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
        VALUES (?, ?, ?, ?, ?)
        ''', (date_str, games_played, earnings, winners, total_players))
        
        current_date += timedelta(days=1)
    
    # Generate game history
    for i in range(20):
        game_time = datetime.now() - timedelta(hours=i*2)
        date_time_str = game_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Generate random data
        username = f"Player{i+1}"
        house = "Main House"
        stake = 50
        players = 4 + (i % 4)
        total_calls = 30 + (i % 20)
        commission_percent = 20
        fee = stake * players * (commission_percent / 100)
        total_prize = stake * players - fee
        
        # Game details
        details = json.dumps({
            "winner_cartella": i + 1,
            "claim_type": "Full House",
            "game_duration": 120 + (i * 5),
            "called_numbers": list(range(1, total_calls + 1))
        })
        
        # Insert data
        cursor.execute('''
        INSERT INTO game_history (
            date_time, username, house, stake, players, total_calls,
            commission_percent, fee, total_prize, details, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            date_time_str, username, house, stake, players, total_calls,
            commission_percent, fee, total_prize, details, 'Won'
        ))
    
    # Add wallet balance
    cursor.execute('''
    INSERT INTO wallet_transactions (
        date_time, amount, transaction_type, description, balance_after
    ) VALUES (?, ?, ?, ?, ?)
    ''', (
        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        1000,
        'Deposit',
        'Initial deposit',
        1000
    ))
    
    # Commit changes
    conn.commit()
    print("Test data generated successfully")
except Exception as e:
    print(f"Error generating test data: {e}")
    import traceback
    traceback.print_exc()

# Test data loading
print("\nTesting data loading...")

# Test thread_safe_db
print("\nTesting thread_safe_db...")
try:
    # Get weekly stats
    weekly_stats = thread_safe_db.get_weekly_stats()
    print(f"Weekly stats: {len(weekly_stats)} days")
    for day in weekly_stats:
        print(f"  {day['date']}: {day['games_played']} games, {day['earnings']} earnings")
    
    # Get summary stats
    summary_stats = thread_safe_db.get_summary_stats()
    print(f"Summary stats: {summary_stats}")
    
    # Get game history
    history, total_pages = thread_safe_db.get_game_history(0, 10)
    print(f"Game history: {len(history)} entries, {total_pages} total pages")
    for game in history[:3]:  # Show first 3 games
        print(f"  {game['date_time']}: {game['username']} won {game['total_prize']}")
except Exception as e:
    print(f"Error testing thread_safe_db: {e}")
    import traceback
    traceback.print_exc()

# Test GameStatsIntegration
print("\nTesting GameStatsIntegration...")
try:
    from game_stats_integration import GameStatsIntegration
    
    # Get weekly stats
    weekly_stats = GameStatsIntegration.get_weekly_stats()
    print(f"Weekly stats: {len(weekly_stats)} days")
    
    # Get summary stats
    summary_stats = GameStatsIntegration.get_summary_stats()
    print(f"Summary stats: {summary_stats}")
    
    # Get game history
    history, total_pages = GameStatsIntegration.get_game_history(0, 10)
    print(f"Game history: {len(history)} entries, {total_pages} total pages")
except Exception as e:
    print(f"Error testing GameStatsIntegration: {e}")
    import traceback
    traceback.print_exc()

# Test OptimizedStatsLoader
print("\nTesting OptimizedStatsLoader...")
try:
    from optimized_stats_loader import get_optimized_stats_loader
    optimized_loader = get_optimized_stats_loader()
    
    # Get weekly stats
    weekly_stats = optimized_loader.get_weekly_stats()
    print(f"Weekly stats: {len(weekly_stats)} days")
    
    # Get summary stats
    summary_stats = optimized_loader.get_summary_stats()
    print(f"Summary stats: {summary_stats}")
    
    # Get game history
    history, total_pages = optimized_loader.get_game_history_page(0, 10)
    print(f"Game history: {len(history)} entries, {total_pages} total pages")
except Exception as e:
    print(f"Error testing OptimizedStatsLoader: {e}")
    import traceback
    traceback.print_exc()

# Test StatsDataProvider
print("\nTesting StatsDataProvider...")
try:
    from stats_data_provider import get_stats_provider
    stats_provider = get_stats_provider()
    
    # Get weekly stats
    weekly_stats = stats_provider.get_weekly_stats()
    print(f"Weekly stats: {len(weekly_stats)} days")
    
    # Get summary stats
    summary_stats = stats_provider.get_summary_stats()
    print(f"Summary stats: {summary_stats}")
    
    # Get game history
    history = stats_provider.get_game_history(0, 10)
    print(f"Game history: {len(history)} entries")
except Exception as e:
    print(f"Error testing StatsDataProvider: {e}")
    import traceback
    traceback.print_exc()

# After all test runs, add assertions to verify real data is loaded
print("\nVerifying that real data is loaded and not default/placeholder values...")
try:
    # Check summary stats from thread_safe_db
    summary_stats = thread_safe_db.get_summary_stats()
    assert summary_stats['total_earnings'] > 0, "total_earnings should be > 0 (real data)"
    assert summary_stats['daily_games'] > 0, "daily_games should be > 0 (real data)"
    assert summary_stats['wallet_balance'] == 1000, "wallet_balance should be 1000 (real data)"
    print("thread_safe_db: Real data loaded correctly.")

    # Check summary stats from GameStatsIntegration
    from game_stats_integration import GameStatsIntegration
    summary_stats = GameStatsIntegration.get_summary_stats()
    assert summary_stats['total_earnings'] > 0, "total_earnings should be > 0 (real data)"
    assert summary_stats['daily_games'] > 0, "daily_games should be > 0 (real data)"
    assert summary_stats['wallet_balance'] == 1000, "wallet_balance should be 1000 (real data)"
    print("GameStatsIntegration: Real data loaded correctly.")

    # Check summary stats from OptimizedStatsLoader
    from optimized_stats_loader import get_optimized_stats_loader
    optimized_loader = get_optimized_stats_loader()
    summary_stats = optimized_loader.get_summary_stats()
    assert summary_stats['total_earnings'] > 0, "total_earnings should be > 0 (real data)"
    assert summary_stats['daily_games'] > 0, "daily_games should be > 0 (real data)"
    assert summary_stats['wallet_balance'] == 1000, "wallet_balance should be 1000 (real data)"
    print("OptimizedStatsLoader: Real data loaded correctly.")

    # Check summary stats from StatsDataProvider
    from stats_data_provider import get_stats_provider
    stats_provider = get_stats_provider()
    summary_stats = stats_provider.get_summary_stats(force_refresh=True)
    assert summary_stats['total_earnings'] > 0, "total_earnings should be > 0 (real data)"
    assert summary_stats['daily_games'] > 0, "daily_games should be > 0 (real data)"
    assert summary_stats['wallet_balance'] == 1000, "wallet_balance should be 1000 (real data)"
    print("StatsDataProvider: Real data loaded correctly.")
except AssertionError as ae:
    print(f"ASSERTION FAILED: {ae}")
    print("WARNING: Only default/empty data is being returned. Real data is NOT loaded!")
    exit(1)
except Exception as e:
    print(f"Error during real data verification: {e}")
    import traceback
    traceback.print_exc()
    exit(1)
print("\nAll real data checks passed. No placeholder or test data is being shown.\n")

print("\nTest completed. Check the output for any errors.")