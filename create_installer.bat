@echo off
:: ================================================================
:: WOW Bingo Game - Professional Installer Creator (Windows)
:: ================================================================
:: This batch script creates professional Windows installers for
:: the WOW Bingo Game using various installer technologies.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Professional Installer Creator%RESET%
echo %CYAN%================================================================%RESET%
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found in PATH!%RESET%
    echo %YELLOW%Please install Python 3.7+ and add it to your PATH.%RESET%
    pause
    exit /b 1
)

:: Check if executable exists
if not exist "dist\WOWBingoGame.exe" (
    echo %RED%Error: WOWBingoGame.exe not found in dist\ directory!%RESET%
    echo %YELLOW%Please build the executable first using:
    echo   enhanced_build.bat%RESET%
    pause
    exit /b 1
)

:: Check if installer creator script exists
if not exist "create_installer.py" (
    echo %RED%Error: create_installer.py not found!%RESET%
    echo %YELLOW%Please ensure the installer creator script is in the current directory.%RESET%
    pause
    exit /b 1
)

:: Display executable info
for %%F in ("dist\WOWBingoGame.exe") do (
    set "size=%%~zF"
    set /a "sizeMB=!size! / 1048576"
)
echo %GREEN%Found executable: dist\WOWBingoGame.exe (!sizeMB! MB)%RESET%
echo.

:: Display installer options menu
:menu
echo %BLUE%Please select an installer type:%RESET%
echo.
echo %GREEN%1.%RESET% Inno Setup Installer (Recommended)
echo     - Professional Windows installer
echo     - Small download size
echo     - Modern wizard interface
echo     - Automatic uninstaller
echo.
echo %GREEN%2.%RESET% NSIS Installer (Alternative)
echo     - Nullsoft Scriptable Install System
echo     - Highly customizable
echo     - Compact installers
echo.
echo %GREEN%3.%RESET% MSI Installer (Enterprise)
echo     - Microsoft Installer format
echo     - Group Policy deployment
echo     - Enterprise features
echo.
echo %GREEN%4.%RESET% Check Requirements
echo     - Verify installer tools are available
echo     - Download links for missing tools
echo.
echo %GREEN%5.%RESET% Exit
echo.
set /p "choice=Enter your choice (1-5): "

:: Process user choice
if "%choice%"=="1" goto inno_installer
if "%choice%"=="2" goto nsis_installer
if "%choice%"=="3" goto msi_installer
if "%choice%"=="4" goto check_requirements
if "%choice%"=="5" goto exit
echo %RED%Invalid choice. Please try again.%RESET%
echo.
goto menu

:inno_installer
echo %CYAN%Creating Inno Setup installer...%RESET%
echo.
python create_installer.py --type inno
goto installer_complete

:nsis_installer
echo %CYAN%Creating NSIS installer...%RESET%
echo.
echo %YELLOW%NSIS installer creation is not yet implemented.%RESET%
echo %YELLOW%Please use Inno Setup installer (option 1) for now.%RESET%
echo.
pause
goto menu

:msi_installer
echo %CYAN%Creating MSI installer...%RESET%
echo.
echo %YELLOW%MSI installer creation is not yet implemented.%RESET%
echo %YELLOW%Please use Inno Setup installer (option 1) for now.%RESET%
echo.
pause
goto menu

:check_requirements
echo %CYAN%Checking installer requirements...%RESET%
echo.

:: Check for Inno Setup
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo %GREEN%✓ Inno Setup 6 found (x86)%RESET%
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    echo %GREEN%✓ Inno Setup 6 found%RESET%
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    echo %GREEN%✓ Inno Setup 5 found (x86)%RESET%
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    echo %GREEN%✓ Inno Setup 5 found%RESET%
) else (
    echo %RED%✗ Inno Setup not found%RESET%
    echo %YELLOW%  Download from: https://jrsoftware.org/isinfo.php%RESET%
)

:: Check for NSIS
if exist "C:\Program Files (x86)\NSIS\makensis.exe" (
    echo %GREEN%✓ NSIS found (x86)%RESET%
) else if exist "C:\Program Files\NSIS\makensis.exe" (
    echo %GREEN%✓ NSIS found%RESET%
) else (
    echo %RED%✗ NSIS not found%RESET%
    echo %YELLOW%  Download from: https://nsis.sourceforge.io/%RESET%
)

:: Check for WiX Toolset (for MSI)
if exist "C:\Program Files (x86)\WiX Toolset v3.11\bin\candle.exe" (
    echo %GREEN%✓ WiX Toolset found (x86)%RESET%
) else if exist "C:\Program Files\WiX Toolset v3.11\bin\candle.exe" (
    echo %GREEN%✓ WiX Toolset found%RESET%
) else (
    echo %RED%✗ WiX Toolset not found%RESET%
    echo %YELLOW%  Download from: https://wixtoolset.org/%RESET%
)

echo.
echo %BLUE%Installation recommendations:%RESET%
echo %YELLOW%1. For most users: Install Inno Setup (easiest and most reliable)%RESET%
echo %YELLOW%2. For advanced users: Install NSIS for more customization%RESET%
echo %YELLOW%3. For enterprise: Install WiX Toolset for MSI packages%RESET%
echo.
pause
goto menu

:installer_complete
echo.
if %errorlevel% equ 0 (
    echo %GREEN%Installer creation completed successfully!%RESET%
    echo.
    echo %CYAN%Your installer can be found in the 'installer\output' directory.%RESET%
    echo %CYAN%The installer includes:%RESET%
    echo %YELLOW%  • WOWBingoGame.exe (standalone executable)%RESET%
    echo %YELLOW%  • Desktop shortcut creation%RESET%
    echo %YELLOW%  • Start menu entries%RESET%
    echo %YELLOW%  • Automatic uninstaller%RESET%
    echo %YELLOW%  • Professional installer interface%RESET%
    echo.
    echo %BLUE%You can now distribute the installer to end users!%RESET%
    
    :: Ask if user wants to open the output directory
    set /p "open_dir=Do you want to open the installer output directory? (y/n): "
    if /i "!open_dir!"=="y" (
        if exist "installer\output" (
            explorer "installer\output"
        ) else (
            echo %YELLOW%Output directory not found.%RESET%
        )
    )
) else (
    echo %RED%Installer creation failed!%RESET%
    echo %YELLOW%Check the output above for error details.%RESET%
    echo.
    echo %YELLOW%Common solutions:%RESET%
    echo %YELLOW%1. Make sure Inno Setup is installed%RESET%
    echo %YELLOW%2. Verify WOWBingoGame.exe exists in dist\ directory%RESET%
    echo %YELLOW%3. Run as Administrator if you get permission errors%RESET%
)
echo.
echo %BLUE%Press any key to return to menu or Ctrl+C to exit...%RESET%
pause >nul
goto menu

:exit
echo %CYAN%Thank you for using the WOW Bingo Game installer creator!%RESET%
echo.
echo %YELLOW%Remember to test your installer on a clean system before distribution.%RESET%
pause
exit /b 0
