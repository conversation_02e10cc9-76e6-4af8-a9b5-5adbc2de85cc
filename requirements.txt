# WOW Bingo Game - Modern Application Requirements
# ===============================================

# Core UI Framework (Modern Flet-based UI)
flet>=0.24.0

# Game Engine (Backward compatibility with existing pygame code)
pygame>=2.5.0
pygame-ce>=2.4.0

# Audio Processing
pydub>=0.25.1

# Database Support
sqlite3-utils>=3.36.0
rethinkdb>=2.4.10

# Utilities
pyperclip>=1.8.2
psutil>=5.9.0
pillow>=10.0.0
requests>=2.31.0

# Performance and Optimization
numpy>=1.24.0
numba>=0.58.0

# Cryptography for voucher system
cryptography>=41.0.0

# Configuration Management
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Logging
loguru>=0.7.0

# Type hints
typing-extensions>=4.7.0

# Build Tools (Optional - for development)
nuitka>=2.0.0
pyinstaller>=6.0.0
auto-py-to-exe>=2.40.0
cx-freeze>=6.15.0

# Development Tools (Optional)
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
