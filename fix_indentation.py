#!/usr/bin/env python

with open("main.py", "r", encoding="utf-8") as f:
    lines = f.readlines()

# Find the problematic section
start_line = -1
end_line = -1
for i, line in enumerate(lines):
    if "if event.type == pygame.QUIT:" in line:
        start_line = i
    if start_line != -1 and i > start_line and "elif event.type == pygame.VIDEORESIZE:" in line:
        end_line = i
        break

if start_line != -1 and end_line != -1:
    # Extract the problematic section
    print(f"Found problematic section from line {start_line + 1} to {end_line}")
    for i in range(start_line, end_line):
        print(f"{i + 1}: {lines[i]}")
    
    # Replace the problematic section with the correct code
    correct_code = """            if event.type == pygame.QUIT:
                # Check if we're in the main game page and the game has started
                if game.active_nav == "play" and game.game_started:
                    # Show exit confirmation dialog instead of immediately exiting
                    game.ui_handler.show_exit_confirmation = True
                    need_redraw = True
                else:
                    # Exit immediately if not in the main game page or game hasn't started
                    running = False

"""
    
    # Replace the lines
    lines[start_line:end_line] = correct_code.splitlines(True)
    
    # Write the fixed file
    with open("main.py", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    print("Fixed indentation error in main.py")
else:
    print("Could not find the problematic section")