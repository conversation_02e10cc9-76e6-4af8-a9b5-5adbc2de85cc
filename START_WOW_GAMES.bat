@echo off
title WOW Games Complete Launcher
color 0A

echo.
echo ================================================================================
echo                        WOW GAMES COMPLETE LAUNCHER
echo                     with RethinkDB Integration
echo ================================================================================
echo.

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

:: Check if we're in the right directory
if not exist "main.py" (
    echo ❌ Error: main.py not found
    echo Please run this script from the WOW Games directory.
    pause
    exit /b 1
)

echo ✅ Python found
echo ✅ WOW Games directory confirmed
echo.

:: Run the Python launcher
echo 🚀 Starting WOW Games Complete Launcher...
echo.
python start_wow_games_complete.py

:: If we get here, the launcher has finished
echo.
echo ================================================================================
echo                           LAUNCHER FINISHED
echo ================================================================================
echo.
pause
