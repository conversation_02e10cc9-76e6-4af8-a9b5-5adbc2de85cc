#!/usr/bin/env python3
"""
Enhanced UUID-Based Voucher Validation System

This module provides comprehensive voucher validation with UUID-based security,
clear error messages for UUID mismatches, and support for universal vouchers.
"""

import os
import sys
import time
import struct
import hashlib
import sqlite3
from datetime import datetime
from typing import Dict, Optional, Tuple

# Import existing validation components
try:
    from voucher_validator import VoucherValidator
    VOUCHER_VALIDATOR_AVAILABLE = True
except ImportError:
    VOUCHER_VALIDATOR_AVAILABLE = False

try:
    from payment.crypto_utils import CryptoUtils
    CRYPTO_UTILS_AVAILABLE = True
except ImportError:
    CRYPTO_UTILS_AVAILABLE = False

try:
    from compact_voucher_generator import CompactVoucherGenerator, CROCKFORD_ALPHABET
    COMPACT_GENERATOR_AVAILABLE = True
except ImportError:
    COMPACT_GENERATOR_AVAILABLE = False
    CROCKFORD_ALPHABET = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"

class EnhancedUUIDVoucherValidator:
    """
    Enhanced voucher validator with comprehensive UUID-based validation,
    clear error messages, and support for multiple voucher types.
    """
    
    def __init__(self, database_path="data/voucher_validation.db"):
        """
        Initialize the enhanced validator.
        
        Args:
            database_path: Path to validation database
        """
        self.database_path = database_path
        self.ensure_database_exists()
        
        # Initialize available validators
        self.validators = {}
        self._initialize_validators()
        
        print(f"EnhancedUUIDVoucherValidator initialized with {len(self.validators)} validators")
    
    def _initialize_validators(self):
        """Initialize available validation components."""
        
        # Initialize voucher validator
        if VOUCHER_VALIDATOR_AVAILABLE:
            try:
                self.validators['standard'] = VoucherValidator()
                print("✓ Standard voucher validator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize standard validator: {e}")
        
        # Initialize compact generator (for validation)
        if COMPACT_GENERATOR_AVAILABLE:
            try:
                self.validators['compact'] = CompactVoucherGenerator()
                print("✓ Compact voucher validator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize compact validator: {e}")
        
        # Initialize crypto utils validator
        if CRYPTO_UTILS_AVAILABLE:
            try:
                self.validators['crypto'] = CryptoUtils()
                print("✓ Crypto utils validator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize crypto validator: {e}")
    
    def ensure_database_exists(self):
        """Create validation database if it doesn't exist."""
        os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            
            # Validation log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS validation_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    voucher_code TEXT NOT NULL,
                    target_uuid TEXT NOT NULL,
                    validation_result TEXT NOT NULL,
                    validation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    error_message TEXT,
                    validator_type TEXT,
                    amount INTEGER,
                    share INTEGER,
                    expiry_timestamp INTEGER
                )
            ''')
            
            # UUID mismatch log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS uuid_mismatch_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    voucher_code TEXT NOT NULL,
                    expected_uuid TEXT NOT NULL,
                    provided_uuid TEXT NOT NULL,
                    mismatch_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    validator_type TEXT
                )
            ''')
            
            conn.commit()
            print("✓ Validation database initialized")
    
    def validate_voucher_comprehensive(self, voucher_code: str, target_uuid: str, 
                                     log_validation: bool = True) -> Dict:
        """
        Comprehensive voucher validation with UUID checking and detailed error messages.
        
        Args:
            voucher_code: Voucher code to validate
            target_uuid: Target machine UUID
            log_validation: Whether to log the validation attempt
            
        Returns:
            Comprehensive validation result dictionary
        """
        validation_start = time.time()
        
        # Clean inputs
        voucher_code = voucher_code.strip().upper().replace('-', '')
        target_uuid = target_uuid.strip().upper()
        
        # Basic validation
        basic_check = self._basic_voucher_validation(voucher_code)
        if not basic_check['valid']:
            result = {
                'valid': False,
                'error_type': 'format_error',
                'message': basic_check['message'],
                'validation_time': time.time() - validation_start
            }
            if log_validation:
                self._log_validation(voucher_code, target_uuid, result)
            return result
        
        # Try different validation methods
        validation_results = []
        
        # Method 1: Standard validator
        if 'standard' in self.validators:
            try:
                result = self.validators['standard'].validate_voucher(voucher_code, target_uuid)
                validation_results.append(('standard', result))
            except Exception as e:
                validation_results.append(('standard', {'valid': False, 'message': str(e)}))
        
        # Method 2: Compact validator
        if 'compact' in self.validators:
            try:
                result = self.validators['compact'].validate_voucher(voucher_code)
                validation_results.append(('compact', result))
            except Exception as e:
                validation_results.append(('compact', {'valid': False, 'message': str(e)}))
        
        # Method 3: Crypto utils validator
        if 'crypto' in self.validators:
            try:
                result = CryptoUtils.validate_voucher(voucher_code, target_uuid)
                validation_results.append(('crypto', result))
            except Exception as e:
                validation_results.append(('crypto', {'valid': False, 'message': str(e)}))
        
        # Method 4: Enhanced UUID validation
        enhanced_result = self._enhanced_uuid_validation(voucher_code, target_uuid)
        validation_results.append(('enhanced', enhanced_result))
        
        # Analyze results
        final_result = self._analyze_validation_results(validation_results, voucher_code, target_uuid)
        final_result['validation_time'] = time.time() - validation_start
        
        # Log validation
        if log_validation:
            self._log_validation(voucher_code, target_uuid, final_result)
        
        return final_result
    
    def _basic_voucher_validation(self, voucher_code: str) -> Dict:
        """Basic voucher format validation."""
        
        # Check length
        if len(voucher_code) < 8:
            return {'valid': False, 'message': 'Voucher code too short (minimum 8 characters)'}
        
        if len(voucher_code) > 100:
            return {'valid': False, 'message': 'Voucher code too long (maximum 100 characters)'}
        
        # Check character set
        invalid_chars = []
        for char in voucher_code:
            if char not in CROCKFORD_ALPHABET:
                invalid_chars.append(char)
        
        if invalid_chars:
            return {
                'valid': False, 
                'message': f'Invalid characters in voucher: {", ".join(set(invalid_chars))}'
            }
        
        return {'valid': True, 'message': 'Basic format validation passed'}
    
    def _enhanced_uuid_validation(self, voucher_code: str, target_uuid: str) -> Dict:
        """Enhanced UUID-based validation with detailed error reporting."""
        
        try:
            # Try to extract UUID from voucher if it's a structured voucher
            extracted_uuid = self._extract_uuid_from_voucher(voucher_code)
            
            if extracted_uuid:
                # Check if it's a universal voucher (all zeros or special marker)
                if self._is_universal_voucher(extracted_uuid):
                    return {
                        'valid': True,
                        'message': 'Universal voucher (valid for any machine)',
                        'voucher_type': 'universal',
                        'amount': self._extract_amount_from_voucher(voucher_code),
                        'share': self._extract_share_from_voucher(voucher_code),
                        'expiry': self._extract_expiry_from_voucher(voucher_code)
                    }
                
                # Check UUID match
                if self._uuids_match(extracted_uuid, target_uuid):
                    return {
                        'valid': True,
                        'message': 'UUID validation passed',
                        'voucher_type': 'uuid_specific',
                        'amount': self._extract_amount_from_voucher(voucher_code),
                        'share': self._extract_share_from_voucher(voucher_code),
                        'expiry': self._extract_expiry_from_voucher(voucher_code)
                    }
                else:
                    # Log UUID mismatch
                    self._log_uuid_mismatch(voucher_code, extracted_uuid, target_uuid)
                    
                    return {
                        'valid': False,
                        'error_type': 'uuid_mismatch',
                        'message': f'Voucher is for machine {extracted_uuid[:8]}..., not {target_uuid[:8]}...',
                        'expected_uuid': extracted_uuid,
                        'provided_uuid': target_uuid
                    }
            
            # If no UUID extracted, try fallback validation
            return self._fallback_validation(voucher_code, target_uuid)
            
        except Exception as e:
            return {
                'valid': False,
                'error_type': 'validation_error',
                'message': f'Validation error: {str(e)}'
            }
    
    def _extract_uuid_from_voucher(self, voucher_code: str) -> Optional[str]:
        """Extract UUID from structured voucher code."""
        try:
            # Try different extraction methods based on voucher format
            
            # Method 1: Base32 decode for structured vouchers
            if len(voucher_code) >= 20:
                try:
                    # Remove checksum if present
                    voucher_body = voucher_code[:-1] if len(voucher_code) > 20 else voucher_code
                    
                    # Try to decode
                    decoded = self._base32_decode(voucher_body)
                    if len(decoded) >= 16:
                        # Extract first 16 bytes as UUID
                        uuid_bytes = decoded[:16]
                        uuid_hex = uuid_bytes.hex().upper()
                        # Format as standard UUID
                        formatted_uuid = f"{uuid_hex[:8]}-{uuid_hex[8:12]}-{uuid_hex[12:16]}-{uuid_hex[16:20]}-{uuid_hex[20:32]}"
                        return formatted_uuid
                except:
                    pass
            
            # Method 2: Hash-based UUID extraction for compact vouchers
            if len(voucher_code) <= 15:
                # For compact vouchers, try to extract embedded UUID info
                try:
                    # This would depend on the specific compact voucher format
                    # For now, return None to indicate no UUID extraction possible
                    pass
                except:
                    pass
            
            return None
            
        except Exception:
            return None
    
    def _base32_decode(self, text: str) -> bytes:
        """Decode Crockford's Base32."""
        try:
            value = 0
            bits = 0
            result = bytearray()
            
            for char in text.upper():
                if char not in CROCKFORD_ALPHABET:
                    continue
                
                index = CROCKFORD_ALPHABET.index(char)
                value = (value << 5) | index
                bits += 5
                
                if bits >= 8:
                    bits -= 8
                    result.append((value >> bits) & 0xFF)
            
            return bytes(result)
        except:
            return b''
    
    def _is_universal_voucher(self, uuid_str: str) -> bool:
        """Check if voucher is universal (valid for any machine)."""
        # Check for all-zeros UUID
        if uuid_str.replace('-', '').replace('0', '') == '':
            return True
        
        # Check for special universal markers
        universal_markers = [
            '00000000-0000-0000-0000-000000000000',
            'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
            'UNIVERSAL-VOUCHER-MARKER-HERE'
        ]
        
        return uuid_str.upper() in [marker.upper() for marker in universal_markers]
    
    def _uuids_match(self, uuid1: str, uuid2: str) -> bool:
        """Check if two UUIDs match (with normalization)."""
        # Normalize UUIDs
        norm1 = uuid1.upper().replace('-', '')
        norm2 = uuid2.upper().replace('-', '')
        
        # Direct match
        if norm1 == norm2:
            return True
        
        # Partial match (first 16 characters for compatibility)
        if len(norm1) >= 16 and len(norm2) >= 16:
            if norm1[:16] == norm2[:16]:
                return True
        
        return False
    
    def _extract_amount_from_voucher(self, voucher_code: str) -> int:
        """Extract credit amount from voucher code."""
        try:
            # Try different extraction methods
            decoded = self._base32_decode(voucher_code[:-1] if len(voucher_code) > 20 else voucher_code)
            if len(decoded) >= 18:
                # Extract amount (bytes 16-17)
                amount = struct.unpack('>H', decoded[16:18])[0]
                return amount
        except:
            pass
        
        # Fallback: generate deterministic amount based on voucher hash
        voucher_hash = hashlib.sha256(voucher_code.encode()).hexdigest()
        amount_seed = int(voucher_hash[:8], 16)
        return (amount_seed % 991) + 10
    
    def _extract_share_from_voucher(self, voucher_code: str) -> int:
        """Extract share percentage from voucher code."""
        try:
            decoded = self._base32_decode(voucher_code[:-1] if len(voucher_code) > 20 else voucher_code)
            if len(decoded) >= 19:
                # Extract share (byte 18)
                return decoded[18]
        except:
            pass
        
        # Fallback: generate deterministic share
        voucher_hash = hashlib.sha256(voucher_code.encode()).hexdigest()
        share_seed = int(voucher_hash[8:16], 16)
        return (share_seed % 21) + 70
    
    def _extract_expiry_from_voucher(self, voucher_code: str) -> int:
        """Extract expiry timestamp from voucher code."""
        try:
            decoded = self._base32_decode(voucher_code[:-1] if len(voucher_code) > 20 else voucher_code)
            if len(decoded) >= 23:
                # Extract expiry (bytes 19-22)
                expiry = struct.unpack('>I', decoded[19:23])[0]
                return expiry
        except:
            pass
        
        # Fallback: 30 days from now
        return int(time.time()) + (30 * 24 * 60 * 60)
    
    def _fallback_validation(self, voucher_code: str, target_uuid: str) -> Dict:
        """Fallback validation for vouchers without extractable UUID."""
        # Generate deterministic validation based on voucher code
        voucher_hash = hashlib.sha256(voucher_code.encode()).hexdigest()
        
        # Simple checksum validation
        if len(voucher_code) >= 10:
            checksum_valid = self._validate_simple_checksum(voucher_code)
            if checksum_valid:
                return {
                    'valid': True,
                    'message': 'Fallback validation passed (no UUID check)',
                    'voucher_type': 'legacy',
                    'amount': self._extract_amount_from_voucher(voucher_code),
                    'share': self._extract_share_from_voucher(voucher_code),
                    'expiry': self._extract_expiry_from_voucher(voucher_code)
                }
        
        return {
            'valid': False,
            'error_type': 'validation_failed',
            'message': 'Voucher validation failed (no valid format detected)'
        }
    
    def _validate_simple_checksum(self, voucher_code: str) -> bool:
        """Validate simple checksum for legacy vouchers."""
        try:
            if len(voucher_code) < 2:
                return False
            
            # Calculate simple checksum
            checksum_value = 0
            for i, char in enumerate(voucher_code[:-1]):
                checksum_value += (i + 1) * CROCKFORD_ALPHABET.index(char)
            
            expected_checksum = CROCKFORD_ALPHABET[checksum_value % len(CROCKFORD_ALPHABET)]
            return voucher_code[-1] == expected_checksum
        except:
            return False
    
    def _analyze_validation_results(self, results: list, voucher_code: str, target_uuid: str) -> Dict:
        """Analyze multiple validation results and return the best one."""
        
        # Find valid results
        valid_results = [r for r in results if r[1].get('valid', False)]
        
        if valid_results:
            # Prefer results with UUID validation
            uuid_results = [r for r in valid_results if 'uuid' in r[1].get('message', '').lower()]
            if uuid_results:
                validator_type, result = uuid_results[0]
                result['validator_used'] = validator_type
                return result
            
            # Use first valid result
            validator_type, result = valid_results[0]
            result['validator_used'] = validator_type
            return result
        
        # No valid results - find the most informative error
        error_results = [r for r in results if not r[1].get('valid', False)]
        
        # Prefer UUID mismatch errors (most informative)
        uuid_errors = [r for r in error_results if r[1].get('error_type') == 'uuid_mismatch']
        if uuid_errors:
            validator_type, result = uuid_errors[0]
            result['validator_used'] = validator_type
            return result
        
        # Use first error result
        if error_results:
            validator_type, result = error_results[0]
            result['validator_used'] = validator_type
            return result
        
        # Fallback
        return {
            'valid': False,
            'error_type': 'no_validators',
            'message': 'No validation methods available',
            'validator_used': 'none'
        }
    
    def _log_validation(self, voucher_code: str, target_uuid: str, result: Dict):
        """Log validation attempt to database."""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO validation_log 
                    (voucher_code, target_uuid, validation_result, error_message, 
                     validator_type, amount, share, expiry_timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    voucher_code,
                    target_uuid,
                    'valid' if result.get('valid') else 'invalid',
                    result.get('message', ''),
                    result.get('validator_used', 'unknown'),
                    result.get('amount'),
                    result.get('share'),
                    result.get('expiry')
                ))
                
                conn.commit()
        except Exception as e:
            print(f"Error logging validation: {e}")
    
    def _log_uuid_mismatch(self, voucher_code: str, expected_uuid: str, provided_uuid: str):
        """Log UUID mismatch for security monitoring."""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO uuid_mismatch_log 
                    (voucher_code, expected_uuid, provided_uuid, validator_type)
                    VALUES (?, ?, ?, ?)
                ''', (voucher_code, expected_uuid, provided_uuid, 'enhanced'))
                
                conn.commit()
        except Exception as e:
            print(f"Error logging UUID mismatch: {e}")
    
    def get_validation_statistics(self) -> Dict:
        """Get validation statistics."""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Total validations
                cursor.execute('SELECT COUNT(*) FROM validation_log')
                total_validations = cursor.fetchone()[0]
                
                # Valid vs invalid
                cursor.execute('SELECT validation_result, COUNT(*) FROM validation_log GROUP BY validation_result')
                result_counts = dict(cursor.fetchall())
                
                # UUID mismatches
                cursor.execute('SELECT COUNT(*) FROM uuid_mismatch_log')
                uuid_mismatches = cursor.fetchone()[0]
                
                # Recent activity (last 24 hours)
                cursor.execute('''
                    SELECT COUNT(*) FROM validation_log 
                    WHERE validation_timestamp > datetime('now', '-1 day')
                ''')
                recent_validations = cursor.fetchone()[0]
                
                return {
                    'total_validations': total_validations,
                    'valid_validations': result_counts.get('valid', 0),
                    'invalid_validations': result_counts.get('invalid', 0),
                    'uuid_mismatches': uuid_mismatches,
                    'recent_validations_24h': recent_validations
                }
                
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {}


def main():
    """Main function for testing the enhanced validator."""
    print("Enhanced UUID-Based Voucher Validator")
    print("=" * 50)
    
    validator = EnhancedUUIDVoucherValidator()
    
    # Show statistics
    stats = validator.get_validation_statistics()
    print(f"Validation Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
