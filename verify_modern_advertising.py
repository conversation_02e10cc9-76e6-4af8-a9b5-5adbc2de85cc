#!/usr/bin/env python3
"""
WOW Bingo Game - Modern Advertising Verification
===============================================

Quick verification script to check if the modern advertising system
is properly integrated and working in your main.py file.
"""

import os
import sys

def check_integration():
    """Check if modern advertising is properly integrated."""
    print("🔍 Checking Modern Advertising Integration...")
    print("=" * 50)
    
    checks_passed = 0
    total_checks = 6
    
    # Check 1: Files exist
    print("\n1. Checking required files...")
    required_files = [
        "main.py",
        "modern_advertising_integration.py",
        "src/wow_bingo_game/ui/components/modern_advertising.py",
        "settings_manager.py"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MISSING")
            return False
    
    checks_passed += 1
    
    # Check 2: Integration code in main.py
    print("\n2. Checking main.py integration...")
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            main_content = f.read()
        
        integration_checks = [
            ("Import statement", "from modern_advertising_integration import ModernAdvertisingAdapter"),
            ("Initialization", "self.modern_advertising = ModernAdvertisingAdapter"),
            ("Draw method", "self.modern_advertising.draw_advertising_text"),
            ("Cleanup", "self.modern_advertising.cleanup()")
        ]
        
        for check_name, check_code in integration_checks:
            if check_code in main_content:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_code} - NOT FOUND")
                return False
        
        checks_passed += 1
        
    except Exception as e:
        print(f"   ❌ Error reading main.py: {e}")
        return False
    
    # Check 3: Modern advertising component
    print("\n3. Checking modern advertising component...")
    try:
        sys.path.insert(0, "src")
        from wow_bingo_game.ui.components.modern_advertising import ModernAdvertisingComponent
        print("   ✅ Modern advertising component can be imported")
        checks_passed += 1
    except ImportError as e:
        print(f"   ❌ Cannot import modern advertising component: {e}")
        return False
    
    # Check 4: Integration adapter
    print("\n4. Checking integration adapter...")
    try:
        from modern_advertising_integration import ModernAdvertisingAdapter
        print("   ✅ Integration adapter can be imported")
        checks_passed += 1
    except ImportError as e:
        print(f"   ❌ Cannot import integration adapter: {e}")
        return False
    
    # Check 5: Settings configuration
    print("\n5. Checking settings configuration...")
    try:
        from settings_manager import SettingsManager
        sm = SettingsManager()
        
        # Check if modern advertising settings exist
        modern_settings = [
            ('advertising', 'modern_mode'),
            ('advertising', 'gpu_acceleration'),
            ('advertising', 'animation_mode'),
            ('advertising', 'visual_quality')
        ]
        
        for section, key in modern_settings:
            value = sm.get_setting(section, key, None)
            if value is not None:
                print(f"   ✅ {section}.{key} = {value}")
            else:
                print(f"   ⚠️ {section}.{key} = default (not configured)")
        
        checks_passed += 1
        
    except Exception as e:
        print(f"   ❌ Settings check failed: {e}")
        return False
    
    # Check 6: Hardware acceleration availability
    print("\n6. Checking hardware acceleration...")
    try:
        from wow_bingo_game.utils.hardware_acceleration import HardwareAccelerationManager
        print("   ✅ Hardware acceleration module available")
        checks_passed += 1
    except ImportError as e:
        print(f"   ⚠️ Hardware acceleration not available: {e}")
        print("   (This is OK - will use CPU fallback)")
        checks_passed += 1  # Still count as passed since fallback works
    
    # Summary
    print(f"\n📊 Integration Check Results: {checks_passed}/{total_checks} passed")
    
    if checks_passed == total_checks:
        print("\n🎉 SUCCESS! Modern advertising is properly integrated!")
        return True
    else:
        print("\n❌ FAILED! Some integration issues found.")
        return False


def show_next_steps():
    """Show next steps for using the modern advertising system."""
    print("\n" + "="*60)
    print("NEXT STEPS - HOW TO USE YOUR MODERN ADVERTISING")
    print("="*60)
    
    print("""
🚀 Your WOW Bingo Game now has modern GPU-accelerated advertising!

TO START USING:
1. Run your game normally:
   python main.py

2. The modern advertising will automatically:
   ✅ Detect your GPU capabilities
   ✅ Use GPU acceleration if available
   ✅ Fall back to CPU optimization if needed
   ✅ Display beautiful animations and effects

WHAT YOU'LL SEE:
✨ Glass morphism backgrounds with gradients
✨ Smooth 60+ FPS animations
✨ Professional typography with glow effects
✨ Multiple animation modes (scroll, wave, pulse, particles)
✨ Automatic quality adjustment based on performance

TESTING THE SYSTEM:
• Run: python test_hardware_acceleration.py
  (Check what GPU acceleration is available)

• Run: python demo_modern_advertising.py
  (See interactive demo of all features)

• Run: python test_main_integration.py
  (Test the integration thoroughly)

CONFIGURATION:
The system uses your existing settings in settings_manager.py:
• advertising.text - Your advertising message
• advertising.modern_mode - Enable/disable modern features
• advertising.gpu_acceleration - Enable/disable GPU acceleration
• advertising.animation_mode - Animation type (scroll, wave, pulse, etc.)
• advertising.visual_quality - Quality level (auto, ultra, high, medium, low)

TROUBLESHOOTING:
If you see "Using legacy advertising system":
1. Check dependencies: python quick_start_modern.py --install-deps
2. Test hardware: python test_hardware_acceleration.py
3. Check integration: python test_main_integration.py

PERFORMANCE:
The system automatically:
✅ Detects Intel QSV, NVIDIA NVENC, AMD VCE acceleration
✅ Uses basic GPU acceleration for any functional GPU
✅ Monitors performance and adjusts quality in real-time
✅ Falls back to optimized CPU rendering when needed

Enjoy your beautiful, modern, professional advertising system! 🎨✨
""")


def main():
    """Main verification function."""
    print("🎮 WOW Bingo Game - Modern Advertising Verification")
    print("=" * 60)
    
    success = check_integration()
    
    if success:
        show_next_steps()
        print("\n🏁 Verification completed successfully!")
        print("Your modern advertising system is ready to use!")
    else:
        print("\n❌ Verification failed!")
        print("\nTo fix the issues:")
        print("1. Make sure all files are in the correct location")
        print("2. Run: python quick_start_modern.py --install-deps")
        print("3. Check the error messages above")
        print("4. Re-run this verification script")


if __name__ == "__main__":
    main()
