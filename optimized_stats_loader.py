"""
Optimized Stats Loader for the WOW Games application.

This module provides a simplified, high-performance data loading system
for the stats page, with efficient caching and progressive loading.
"""

import os
import json
import time
import sqlite3
import threading
import logging
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'optimized_stats.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Constants
STATS_DB_PATH = os.path.join('data', 'stats.db')
CACHE_DIR = os.path.join('data', 'cache')
CACHE_FILE = os.path.join(CACHE_DIR, 'stats_cache.json')
CACHE_DURATION = 300  # 5 minutes cache duration

# Ensure cache directory exists
os.makedirs(CACHE_DIR, exist_ok=True)

class OptimizedStatsLoader:
    """
    Optimized stats loader with efficient caching and progressive loading.
    """
    
    def __init__(self):
        """Initialize the optimized stats loader."""
        self.cache = {}
        self.cache_times = {}
        self.lock = threading.RLock()
        self.db_connection = None
        self.background_thread = None
        self.stop_event = threading.Event()
        
        # Load cache from disk
        self._load_cache()
        
        # Start background loading
        self._start_background_loading()
        
        logging.info("OptimizedStatsLoader initialized")
    
    def _load_cache(self):
        """Load cache from disk."""
        try:
            if os.path.exists(CACHE_FILE):
                with open(CACHE_FILE, 'r') as f:
                    data = json.load(f)
                    
                with self.lock:
                    self.cache = data.get('cache', {})
                    self.cache_times = data.get('cache_times', {})
                    
                    # Remove expired entries
                    current_time = time.time()
                    expired_keys = []
                    for key, timestamp in self.cache_times.items():
                        if current_time - timestamp > CACHE_DURATION:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        if key in self.cache:
                            del self.cache[key]
                        if key in self.cache_times:
                            del self.cache_times[key]
                
                logging.info(f"Loaded {len(self.cache)} items from cache")
        except Exception as e:
            logging.error(f"Error loading cache: {e}")
            # Initialize empty cache if loading fails
            with self.lock:
                self.cache = {}
                self.cache_times = {}
    
    def _save_cache(self):
        """Save cache to disk."""
        try:
            with self.lock:
                data = {
                    'cache': self.cache,
                    'cache_times': self.cache_times
                }
            
            with open(CACHE_FILE, 'w') as f:
                json.dump(data, f)
            
            logging.info(f"Saved {len(self.cache)} items to cache")
        except Exception as e:
            logging.error(f"Error saving cache: {e}")
    
    def _get_db_connection(self):
        """Get a database connection."""
        if self.db_connection is None:
            try:
                self.db_connection = sqlite3.connect(STATS_DB_PATH)
                # Enable foreign keys
                cursor = self.db_connection.cursor()
                cursor.execute("PRAGMA foreign_keys = ON")
                # Set busy timeout to prevent immediate locking errors
                cursor.execute("PRAGMA busy_timeout = 5000")  # 5 seconds
            except Exception as e:
                logging.error(f"Error connecting to database: {e}")
                return None
        
        return self.db_connection
    
    def _close_db_connection(self):
        """Close the database connection."""
        if self.db_connection:
            try:
                self.db_connection.close()
                self.db_connection = None
            except Exception as e:
                logging.error(f"Error closing database connection: {e}")
    
    def _start_background_loading(self):
        """Start background loading of data."""
        if self.background_thread and self.background_thread.is_alive():
            return  # Already running
        
        self.stop_event.clear()
        self.background_thread = threading.Thread(target=self._background_load_data)
        self.background_thread.daemon = True
        self.background_thread.start()
        
        logging.info("Started background data loading")
    
    def _background_load_data(self):
        """Background thread to load all data."""
        try:
            # Load all data types
            self._load_weekly_stats()
            self._load_summary_data()
            self._load_game_history()
            
            # Save cache after loading all data
            self._save_cache()
            
            logging.info("Background data loading completed")
        except Exception as e:
            logging.error(f"Error in background data loading: {e}")
        finally:
            # Close database connection
            self._close_db_connection()
    
    def _load_weekly_stats(self):
        """Load weekly statistics."""
        try:
            # Get current date
            end_date = datetime.now()
            start_date = end_date - timedelta(days=6)
            
            # Format dates for SQL query
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # Get database connection
            conn = self._get_db_connection()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            # Single optimized query to get all weekly stats
            cursor.execute('''
            SELECT date, games_played, earnings, winners, total_players 
            FROM daily_stats 
            WHERE date BETWEEN ? AND ?
            ORDER BY date
            ''', (start_date_str, end_date_str))
            
            results = cursor.fetchall()
            
            # Convert to dictionary format
            stats_by_date = {}
            for row in results:
                stats_by_date[row[0]] = {
                    'date': row[0],
                    'games_played': row[1],
                    'earnings': row[2],
                    'winners': row[3],
                    'total_players': row[4]
                }
            
            # Generate the full week with default values for missing dates
            weekly_stats = []
            current_date = start_date
            
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                if date_str in stats_by_date:
                    weekly_stats.append(stats_by_date[date_str])
                else:
                    # Default values for dates with no data
                    weekly_stats.append({
                        'date': date_str,
                        'games_played': 0,
                        'earnings': 0,
                        'winners': 0,
                        'total_players': 0
                    })
                current_date += timedelta(days=1)
            
            # Cache the result
            with self.lock:
                self.cache['weekly_stats'] = weekly_stats
                self.cache_times['weekly_stats'] = time.time()
            
            logging.info(f"Loaded weekly stats for {len(weekly_stats)} days")
            
        except Exception as e:
            logging.error(f"Error loading weekly stats: {e}")
    
    def _load_summary_data(self):
        """Load summary statistics data."""
        try:
            # Get database connection
            conn = self._get_db_connection()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            # Get total earnings
            cursor.execute('SELECT SUM(earnings) FROM daily_stats')
            result = cursor.fetchone()
            total_earnings = result[0] if result and result[0] is not None else 0
            
            # Get today's date
            today = datetime.now().strftime('%Y-%m-%d')
            
            # Get daily earnings
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today,))
            result = cursor.fetchone()
            daily_earnings = result[0] if result and result[0] is not None else 0
            
            # Get daily games played
            cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today,))
            result = cursor.fetchone()
            daily_games = result[0] if result and result[0] is not None else 0
            
            # Get wallet balance
            cursor.execute('''
            SELECT balance_after FROM wallet_transactions
            ORDER BY id DESC LIMIT 1
            ''')
            result = cursor.fetchone()
            wallet_balance = result[0] if result and result[0] is not None else 0
            
            # Cache the results
            with self.lock:
                self.cache['total_earnings'] = total_earnings
                self.cache_times['total_earnings'] = time.time()
                
                self.cache['daily_earnings'] = daily_earnings
                self.cache_times['daily_earnings'] = time.time()
                
                self.cache['daily_games'] = daily_games
                self.cache_times['daily_games'] = time.time()
                
                self.cache['wallet_balance'] = wallet_balance
                self.cache_times['wallet_balance'] = time.time()
            
            logging.info("Loaded summary data")
            
        except Exception as e:
            logging.error(f"Error loading summary data: {e}")
    
    def _load_game_history(self):
        """Load game history data."""
        try:
            # Get database connection
            conn = self._get_db_connection()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            # Get total count for pagination
            cursor.execute('SELECT COUNT(*) FROM game_history')
            result = cursor.fetchone()
            total_games = result[0] if result else 0
            
            # Calculate total pages (10 records per page)
            page_size = 10
            total_pages = max(1, (total_games + page_size - 1) // page_size)
            
            # Cache total pages
            with self.lock:
                self.cache['total_history_pages'] = total_pages
                self.cache_times['total_history_pages'] = time.time()
            
            # Load first page of game history
            self._load_game_history_page(0, page_size)
            
            logging.info(f"Loaded game history metadata (total pages: {total_pages})")
            
        except Exception as e:
            logging.error(f"Error loading game history: {e}")
    
    def _load_game_history_page(self, page, page_size=10):
        """
        Load a specific page of game history.
        
        Args:
            page: Page number (0-based)
            page_size: Number of records per page
        """
        try:
            # Calculate offset
            offset = page * page_size
            
            # Get database connection
            conn = self._get_db_connection()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            # Get game history for this page
            cursor.execute('''
            SELECT id, date_time, username, house, stake, players, total_calls,
                   commission_percent, fee, total_prize, details, status
            FROM game_history
            ORDER BY date_time DESC
            LIMIT ? OFFSET ?
            ''', (page_size, offset))
            
            results = cursor.fetchall()
            
            # Convert to list of dictionaries
            history = []
            for row in results:
                history.append({
                    'id': row[0],
                    'date_time': row[1],
                    'username': row[2],
                    'house': row[3],
                    'stake': row[4],
                    'players': row[5],
                    'total_calls': row[6],
                    'commission_percent': row[7],
                    'fee': row[8],
                    'total_prize': row[9],
                    'details': row[10],
                    'status': row[11]
                })
            
            # Cache the result
            cache_key = f'game_history_page_{page}'
            with self.lock:
                self.cache[cache_key] = history
                self.cache_times[cache_key] = time.time()
            
            logging.info(f"Loaded game history page {page} ({len(history)} records)")
            
            return history
            
        except Exception as e:
            logging.error(f"Error loading game history page {page}: {e}")
            return []
    
    def get_weekly_stats(self):
        """
        Get weekly statistics.
        
        Returns:
            list: Weekly statistics
        """
        with self.lock:
            # Check if we have cached data
            if 'weekly_stats' in self.cache:
                # Check if cache is still valid
                cache_time = self.cache_times.get('weekly_stats', 0)
                if time.time() - cache_time <= CACHE_DURATION:
                    return self.cache['weekly_stats']
        
        # If no valid cache, load data
        try:
            self._load_weekly_stats()
            
            with self.lock:
                if 'weekly_stats' in self.cache:
                    return self.cache['weekly_stats']
        except Exception as e:
            logging.error(f"Error getting weekly stats: {e}")
        
        # Return empty list if all else fails
        return []
    
    def get_summary_stats(self):
        """
        Get summary statistics.
        
        Returns:
            dict: Summary statistics
        """
        # Keys to check in cache
        keys = ['total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance']
        
        # Check if all keys are in cache and valid
        all_cached = True
        with self.lock:
            for key in keys:
                if key not in self.cache:
                    all_cached = False
                    break
                
                cache_time = self.cache_times.get(key, 0)
                if time.time() - cache_time > CACHE_DURATION:
                    all_cached = False
                    break
        
        # If all data is cached and valid, return it
        if all_cached:
            with self.lock:
                return {
                    'total_earnings': self.cache.get('total_earnings', 0),
                    'daily_earnings': self.cache.get('daily_earnings', 0),
                    'daily_games': self.cache.get('daily_games', 0),
                    'wallet_balance': self.cache.get('wallet_balance', 0)
                }
        
        # If not all cached, load data
        try:
            self._load_summary_data()
            
            with self.lock:
                return {
                    'total_earnings': self.cache.get('total_earnings', 0),
                    'daily_earnings': self.cache.get('daily_earnings', 0),
                    'daily_games': self.cache.get('daily_games', 0),
                    'wallet_balance': self.cache.get('wallet_balance', 0)
                }
        except Exception as e:
            logging.error(f"Error getting summary stats: {e}")
        
        # Return default values if all else fails
        return {
            'total_earnings': 0,
            'daily_earnings': 0,
            'daily_games': 0,
            'wallet_balance': 0
        }
    
    def get_game_history_page(self, page, page_size=10):
        """
        Get a page of game history.
        
        Args:
            page: Page number (0-based)
            page_size: Number of records per page
            
        Returns:
            tuple: (history_records, total_pages)
        """
        cache_key = f'game_history_page_{page}'
        
        with self.lock:
            # Check if we have cached data for this page
            if cache_key in self.cache and 'total_history_pages' in self.cache:
                # Check if cache is still valid
                cache_time = self.cache_times.get(cache_key, 0)
                pages_cache_time = self.cache_times.get('total_history_pages', 0)
                
                if (time.time() - cache_time <= CACHE_DURATION and 
                    time.time() - pages_cache_time <= CACHE_DURATION):
                    return (self.cache[cache_key], self.cache.get('total_history_pages', 1))
        
        # If no valid cache, load data
        try:
            # Load total pages if not cached
            if 'total_history_pages' not in self.cache:
                self._load_game_history()
            
            # Load specific page
            history = self._load_game_history_page(page, page_size)
            
            with self.lock:
                return (history, self.cache.get('total_history_pages', 1))
        except Exception as e:
            logging.error(f"Error getting game history page {page}: {e}")
        
        # Return empty list if all else fails
        return ([], 1)
    
    def refresh_data(self):
        """
        Force a refresh of all data.
        """
        # Clear cache
        with self.lock:
            self.cache.clear()
            self.cache_times.clear()
        
        # Try to post a refresh_stats event to trigger UI update
        try:
            import pygame
            import time
            if pygame.get_init():
                refresh_event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'refresh_stats',
                    'force': True,
                    'force_reload': True,
                    'source': 'OptimizedStatsLoader.refresh_data',
                    'timestamp': time.time()
                })
                pygame.event.post(refresh_event)
                logging.info("Posted refresh_stats event to trigger UI update")
                print("Posted refresh_stats event to trigger UI update")
        except Exception as e:
            logging.warning(f"Could not post refresh event: {e}")
            print(f"Could not post refresh event: {e}")
        
        # Start background loading
        self._start_background_loading()
        
        logging.info("Forced data refresh")

# Singleton instance
_optimized_stats_loader = None

def get_optimized_stats_loader():
    """
    Get the singleton optimized stats loader instance.
    
    Returns:
        OptimizedStatsLoader: The optimized stats loader instance
    """
    global _optimized_stats_loader
    if _optimized_stats_loader is None:
        _optimized_stats_loader = OptimizedStatsLoader()
    return _optimized_stats_loader

# Initialize loader when module is imported
get_optimized_stats_loader()
