"""
Fix RethinkDB import issues across all files.

This script patches all RethinkDB-related files to use the correct import syntax
and access the RethinkDB API through the proper namespace.
"""

import os
import sys
import glob
import re

def fix_file(file_path):
    """Fix RethinkDB imports in a file."""
    print(f"Fixing imports in {file_path}...")
    
    # Read file content
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix imports
    content = re.sub(
        r'import\s+rethinkdb\s+as\s+r\s*',
        'import rethinkdb\nr = rethinkdb.r\n',
        content
    )
    
    # Fix direct module references
    content = re.sub(
        r'(?<!\.)r\.connect\(',
        'rethinkdb.r.connect(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.db\(',
        'rethinkdb.r.db(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.db_list\(',
        'rethinkdb.r.db_list(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.table\(',
        'rethinkdb.r.table(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.table_list\(',
        'rethinkdb.r.table_list(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.table_create\(',
        'rethinkdb.r.table_create(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.expr\(',
        'rethinkdb.r.expr(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.desc\(',
        'rethinkdb.r.desc(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.asc\(',
        'rethinkdb.r.asc(',
        content
    )
    
    content = re.sub(
        r'(?<!\.)r\.row\[',
        'rethinkdb.r.row[',
        content
    )
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    return True

def fix_all_files():
    """Fix all RethinkDB-related files."""
    files_to_fix = [
        'rethink_db.py',
        'setup_rethinkdb.py',
        'sync_manager.py',
        'db_hybrid.py',
        'hybrid_db_integration.py',
        'rethink_dashboard.py',
        'rethink_dashboard_fixed.py',
        'test_rethinkdb_integration.py'
    ]
    
    success_count = 0
    for file in files_to_fix:
        if os.path.exists(file):
            if fix_file(file):
                success_count += 1
        else:
            print(f"File not found: {file}")
    
    print(f"Fixed {success_count} files successfully.")
    
    return success_count > 0

def create_rethinkdb_wrapper():
    """Create a RethinkDB wrapper module."""
    content = """# RethinkDB wrapper module
import sys
try:
    import rethinkdb
    sys.modules['r'] = rethinkdb.r
    
    # Create a Connect class with the same interface as the original connect function
    class Connect:
        @staticmethod
        def connect(*args, **kwargs):
            return rethinkdb.r.connect(*args, **kwargs)
    
    # Add connect function to the module
    sys.modules['rethinkdb'].connect = Connect.connect
    
    print("RethinkDB wrapper initialized")
except ImportError:
    print("RethinkDB not installed")
except Exception as e:
    print(f"Error initializing RethinkDB wrapper: {e}")
"""
    
    with open('rethinkdb_wrapper.py', 'w') as f:
        f.write(content)
    
    print("Created RethinkDB wrapper module.")
    return True

def main():
    """Main function."""
    print("Fixing RethinkDB import issues...")
    
    # Create RethinkDB wrapper
    create_rethinkdb_wrapper()
    
    # Fix all files
    fix_all_files()
    
    print("\nFix completed. Please restart any running applications.")
    
    return True

if __name__ == '__main__':
    main()