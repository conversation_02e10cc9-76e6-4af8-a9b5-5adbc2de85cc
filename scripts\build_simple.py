#!/usr/bin/env python3
"""
WOW Bingo Game - Simple Build Script (No Dependencies)
======================================================

Simple build script that works without additional dependencies.
This script can be used to build the application using existing tools.

Usage:
    python scripts/build_simple.py [--backend nuitka|pyinstaller] [--optimization low|medium|high]
"""

import os
import sys
import subprocess
import shutil
import argparse
import platform
from pathlib import Path


class SimpleBuildError(Exception):
    """Simple build error."""
    pass


def log(message, level="INFO"):
    """Simple logging function."""
    print(f"[{level}] {message}")


def check_backend_available(backend):
    """Check if a build backend is available."""
    try:
        if backend == "nuitka":
            result = subprocess.run(
                ["python", "-m", "nuitka", "--version"], 
                capture_output=True, text=True, timeout=10
            )
            return result.returncode == 0
        elif backend == "pyinstaller":
            result = subprocess.run(
                ["pyinstaller", "--version"], 
                capture_output=True, text=True, timeout=10
            )
            return result.returncode == 0
        return False
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
        return False


def detect_best_backend():
    """Detect the best available build backend."""
    backends = ["nuitka", "pyinstaller"]
    
    for backend in backends:
        if check_backend_available(backend):
            log(f"Selected build backend: {backend}")
            return backend
    
    raise SimpleBuildError("No suitable build backend found. Please install Nuitka or PyInstaller.")


def prepare_build_environment(project_root):
    """Prepare the build environment."""
    log("Preparing build environment...")
    
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    # Clean previous builds
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # Create directories
    build_dir.mkdir(parents=True, exist_ok=True)
    dist_dir.mkdir(parents=True, exist_ok=True)
    
    log("Build environment prepared")
    return build_dir, dist_dir


def build_with_nuitka(project_root, optimization="medium"):
    """Build with Nuitka."""
    log("Building with Nuitka...")
    
    # Check if main script exists (try both old and new locations)
    main_script_new = project_root / "src/wow_bingo_game/main.py"
    main_script_old = project_root / "main.py"
    
    if main_script_new.exists():
        main_script = main_script_new
        log("Using new main script location")
    elif main_script_old.exists():
        main_script = main_script_old
        log("Using original main script location")
    else:
        raise SimpleBuildError("Main script not found in either location")
    
    # Build arguments
    nuitka_args = [
        "python", "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--output-filename=WOWBingoGame.exe",
        "--output-dir=dist",
        "--assume-yes-for-downloads",
        "--show-progress",
    ]
    
    # Add icon if available
    icon_paths = [
        project_root / "assets/app_logo.ico",
        project_root / "assets/icon.ico",
        project_root / "assets/icons.ico"
    ]
    
    for icon_path in icon_paths:
        if icon_path.exists():
            nuitka_args.append(f"--windows-icon-from-ico={icon_path}")
            log(f"Using icon: {icon_path}")
            break
    
    # Optimization settings
    if optimization in ["medium", "high"]:
        nuitka_args.extend([
            "--lto=yes",
            "--enable-plugin=anti-bloat",
        ])
    
    if optimization == "high":
        nuitka_args.extend([
            "--jobs=0",  # Use all CPU cores
        ])
    
    # Include data directories if they exist
    data_dirs = [
        ("assets", "assets"),
        ("data", "data"),
    ]
    
    for src_dir, dst_dir in data_dirs:
        src_path = project_root / src_dir
        if src_path.exists():
            nuitka_args.append(f"--include-data-dir={src_path}={dst_dir}")
            log(f"Including data directory: {src_dir}")
    
    # Include essential packages
    packages = [
        "pygame",
        "json",
        "datetime",
        "sqlite3",
    ]
    
    for package in packages:
        nuitka_args.append(f"--include-package={package}")
    
    # Disable console on Windows
    if platform.system() == "Windows":
        nuitka_args.append("--disable-console")
    
    # Add main script
    nuitka_args.append(str(main_script))
    
    try:
        log(f"Running Nuitka with {optimization} optimization...")
        log(f"Command: {' '.join(nuitka_args)}")
        
        result = subprocess.run(nuitka_args, cwd=project_root, check=True)
        log("Nuitka build completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        log(f"Nuitka build failed with return code {e.returncode}", "ERROR")
        return False


def build_with_pyinstaller(project_root, optimization="medium"):
    """Build with PyInstaller."""
    log("Building with PyInstaller...")
    
    # Check if main script exists
    main_script_new = project_root / "src/wow_bingo_game/main.py"
    main_script_old = project_root / "main.py"
    
    if main_script_new.exists():
        main_script = main_script_new
        log("Using new main script location")
    elif main_script_old.exists():
        main_script = main_script_old
        log("Using original main script location")
    else:
        raise SimpleBuildError("Main script not found in either location")
    
    # PyInstaller arguments
    pyinstaller_args = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=WOWBingoGame",
        "--distpath=dist",
        "--workpath=build",
        "--specpath=build",
    ]
    
    # Add icon if available
    icon_paths = [
        project_root / "assets/app_logo.ico",
        project_root / "assets/icon.ico",
        project_root / "assets/icons.ico"
    ]
    
    for icon_path in icon_paths:
        if icon_path.exists():
            pyinstaller_args.append(f"--icon={icon_path}")
            log(f"Using icon: {icon_path}")
            break
    
    # Add data directories
    data_dirs = [
        ("assets", "assets"),
        ("data", "data"),
    ]
    
    for src_dir, dst_dir in data_dirs:
        src_path = project_root / src_dir
        if src_path.exists():
            if platform.system() == "Windows":
                pyinstaller_args.append(f"--add-data={src_path};{dst_dir}")
            else:
                pyinstaller_args.append(f"--add-data={src_path}:{dst_dir}")
            log(f"Including data directory: {src_dir}")
    
    # Optimization settings
    if optimization in ["medium", "high"]:
        pyinstaller_args.extend([
            "--optimize=2",
        ])
    
    # Add main script
    pyinstaller_args.append(str(main_script))
    
    try:
        log(f"Running PyInstaller with {optimization} optimization...")
        log(f"Command: {' '.join(pyinstaller_args)}")
        
        result = subprocess.run(pyinstaller_args, cwd=project_root, check=True)
        log("PyInstaller build completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        log(f"PyInstaller build failed with return code {e.returncode}", "ERROR")
        return False


def verify_build(project_root):
    """Verify the build was successful."""
    log("Verifying build...")
    
    exe_name = "WOWBingoGame.exe" if platform.system() == "Windows" else "WOWBingoGame"
    exe_path = project_root / "dist" / exe_name
    
    if not exe_path.exists():
        log(f"Executable not found: {exe_path}", "ERROR")
        return False
    
    # Check file size
    file_size_mb = exe_path.stat().st_size / (1024 * 1024)
    log(f"Executable size: {file_size_mb:.2f} MB")
    
    if file_size_mb < 5:
        log("Warning: Executable seems small, may be missing dependencies", "WARNING")
    elif file_size_mb > 200:
        log("Warning: Executable is very large", "WARNING")
    
    log("Build verification completed")
    return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="WOW Bingo Game - Simple Build Script")
    parser.add_argument("--backend", choices=["nuitka", "pyinstaller"],
                       help="Build backend to use (auto-detect if not specified)")
    parser.add_argument("--optimization", choices=["low", "medium", "high"],
                       default="medium", help="Optimization level")
    
    args = parser.parse_args()
    
    try:
        project_root = Path(__file__).parent.parent
        
        log("WOW Bingo Game - Simple Build Script")
        log("=" * 40)
        
        # Prepare build environment
        build_dir, dist_dir = prepare_build_environment(project_root)
        
        # Select backend
        if args.backend:
            if not check_backend_available(args.backend):
                raise SimpleBuildError(f"Backend '{args.backend}' is not available")
            backend = args.backend
        else:
            backend = detect_best_backend()
        
        # Build with selected backend
        if backend == "nuitka":
            success = build_with_nuitka(project_root, args.optimization)
        elif backend == "pyinstaller":
            success = build_with_pyinstaller(project_root, args.optimization)
        else:
            raise SimpleBuildError(f"Unknown backend: {backend}")
        
        if not success:
            log("Build failed", "ERROR")
            sys.exit(1)
        
        # Verify build
        if not verify_build(project_root):
            log("Build verification failed", "ERROR")
            sys.exit(1)
        
        log("🎉 Build completed successfully!")
        log(f"📁 Executable location: dist/WOWBingoGame.exe")
        
    except SimpleBuildError as e:
        log(f"Build error: {e}", "ERROR")
        sys.exit(1)
    except Exception as e:
        log(f"Unexpected error: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
