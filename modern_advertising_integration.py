#!/usr/bin/env python3
"""
WOW Bingo Game - Modern Advertising Integration
==============================================

Integration adapter to replace the existing advertising system with the new
modern GPU-optimized advertising component while maintaining compatibility
with the existing main.py code.

This integration provides:
1. Drop-in replacement for existing draw_advertising_text() method
2. GPU acceleration with intelligent fallback
3. Modern visual effects and animations
4. Performance monitoring and auto-adjustment
5. Backward compatibility with existing settings
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Optional, Dict, Any

import pygame

# Add the modern application path
modern_app_path = Path(__file__).parent / "src"
sys.path.insert(0, str(modern_app_path))

try:
    from wow_bingo_game.ui.components.modern_advertising import (
        ModernAdvertisingComponent, 
        AdvertisingSettings, 
        AnimationMode, 
        VisualQuality
    )
    from wow_bingo_game.core.config import WOWBingoConfig
    from wow_bingo_game.utils.logger import setup_logging, get_logger
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    logger = get_logger(__name__)
    
    MODERN_ADVERTISING_AVAILABLE = True
except ImportError as e:
    print(f"Modern advertising not available: {e}")
    print("Please run 'python quick_start_modern.py --install-deps' first")
    MODERN_ADVERTISING_AVAILABLE = False


class ModernAdvertisingAdapter:
    """
    Adapter class to integrate modern advertising with existing BingoGame.
    
    This class provides a drop-in replacement for the existing advertising
    system while adding modern GPU-accelerated visual effects.
    """
    
    def __init__(self, settings_manager=None):
        """Initialize the modern advertising adapter.
        
        Args:
            settings_manager: Existing settings manager instance
        """
        self.settings_manager = settings_manager
        self.modern_component: Optional[ModernAdvertisingComponent] = None
        self.initialized = False
        self.fallback_mode = False
        self.last_settings_check = 0
        self.cached_settings = {}
        
        # Initialize if modern advertising is available
        if MODERN_ADVERTISING_AVAILABLE:
            asyncio.run(self._initialize_modern_component())
        else:
            self.fallback_mode = True
            logger.warning("Modern advertising not available, using fallback mode")
    
    async def _initialize_modern_component(self):
        """Initialize the modern advertising component."""
        try:
            logger.info("🚀 Initializing modern advertising component...")
            
            # Create configuration
            config = WOWBingoConfig()
            
            # Create modern component
            self.modern_component = ModernAdvertisingComponent(config)
            
            # Initialize with hardware detection
            success = await self.modern_component.initialize()
            
            if success:
                # Load settings from existing settings manager
                self._load_existing_settings()
                
                self.initialized = True
                logger.info("✅ Modern advertising component initialized successfully")
                
                # Log performance info
                perf_info = self.modern_component.get_performance_info()
                logger.info(f"🎯 GPU Available: {perf_info['gpu_available']}")
                logger.info(f"🎨 Visual Quality: {perf_info['visual_quality']}")
                logger.info(f"🎬 Animation Mode: {perf_info['animation_mode']}")
                
            else:
                logger.warning("Modern advertising initialization failed, using fallback")
                self.fallback_mode = True
                
        except Exception as e:
            logger.error(f"Failed to initialize modern advertising: {e}")
            self.fallback_mode = True
    
    def _load_existing_settings(self):
        """Load settings from existing settings manager."""
        try:
            if not self.settings_manager or not self.modern_component:
                return
            
            # Get advertising settings
            ad_text = self.settings_manager.get_setting('advertising', 'text', "WOW Games - Premium Bingo Experience")
            font_family = self.settings_manager.get_setting('advertising', 'font', "Arial")
            font_size = self.settings_manager.get_setting('advertising', 'font_size', 32)
            text_color_hex = self.settings_manager.get_setting('advertising', 'text_color', "#FFD700")
            animation_speed = self.settings_manager.get_setting('advertising', 'scroll_speed', 2.0)
            glow_enabled = self.settings_manager.get_setting('advertising', 'text_glow', True)
            
            # Convert hex color to RGB
            try:
                text_color = self.settings_manager.hex_to_rgb(text_color_hex)
            except:
                text_color = (255, 215, 0)  # Gold fallback
            
            # Update modern component settings
            self.modern_component.settings.text = ad_text
            self.modern_component.settings.font_family = font_family
            self.modern_component.settings.font_size = font_size
            self.modern_component.settings.text_color = text_color
            self.modern_component.settings.animation_speed = animation_speed
            self.modern_component.settings.glow_enabled = glow_enabled
            
            # Set animation mode based on GPU availability
            if self.modern_component.gpu_available:
                if self.modern_component.visual_quality == VisualQuality.ULTRA:
                    self.modern_component.settings.animation_mode = AnimationMode.PARTICLE
                elif self.modern_component.visual_quality == VisualQuality.HIGH:
                    self.modern_component.settings.animation_mode = AnimationMode.WAVE
                else:
                    self.modern_component.settings.animation_mode = AnimationMode.SCROLL
            else:
                self.modern_component.settings.animation_mode = AnimationMode.SCROLL
            
            logger.info(f"Settings loaded: {ad_text[:30]}...")
            
        except Exception as e:
            logger.error(f"Failed to load existing settings: {e}")
    
    def draw_advertising_text(self, x: int, y: int, width: int, height: int, screen: pygame.Surface):
        """
        Modern replacement for the existing draw_advertising_text method.
        
        This method provides the same interface as the original but with
        modern GPU-accelerated rendering and visual effects.
        
        Args:
            x: X position
            y: Y position  
            width: Width of advertising area
            height: Height of advertising area
            screen: Pygame surface to draw on
        """
        try:
            # Check if advertising is enabled
            if not self._is_advertising_enabled():
                return
            
            # Update settings periodically
            current_time = time.time()
            if current_time - self.last_settings_check > 1.0:  # Check every second
                self._update_settings_from_manager()
                self.last_settings_check = current_time
            
            # Use modern component if available
            if self.modern_component and self.initialized and not self.fallback_mode:
                self._draw_modern_advertising(x, y, width, height, screen)
            else:
                self._draw_fallback_advertising(x, y, width, height, screen)
                
        except Exception as e:
            logger.error(f"Failed to draw advertising: {e}")
            # Try fallback on error
            try:
                self._draw_fallback_advertising(x, y, width, height, screen)
            except:
                pass  # Ignore fallback errors
    
    def _is_advertising_enabled(self) -> bool:
        """Check if advertising is enabled in settings."""
        try:
            if not self.settings_manager:
                return True  # Default to enabled
            
            enabled = self.settings_manager.get_setting('advertising', 'enabled', True)
            hidden = self.settings_manager.get_setting('advertising', 'hidden', False)
            
            return enabled and not hidden
            
        except Exception as e:
            logger.debug(f"Failed to check advertising enabled: {e}")
            return True
    
    def _update_settings_from_manager(self):
        """Update modern component settings from settings manager."""
        try:
            if not self.settings_manager or not self.modern_component:
                return
            
            # Get current settings
            ad_text = self.settings_manager.get_setting('advertising', 'text', self.modern_component.settings.text)
            
            # Update text if changed
            if ad_text != self.modern_component.settings.text:
                self.modern_component.set_text(ad_text)
            
            # Update other settings as needed
            font_size = self.settings_manager.get_setting('advertising', 'font_size', 32)
            if font_size != self.modern_component.settings.font_size:
                self.modern_component.settings.font_size = font_size
                self.modern_component.font_cache.clear()  # Clear cache for new size
            
        except Exception as e:
            logger.debug(f"Failed to update settings: {e}")
    
    def _draw_modern_advertising(self, x: int, y: int, width: int, height: int, screen: pygame.Surface):
        """Draw using modern advertising component."""
        try:
            # Update animation
            current_time = time.time()
            if hasattr(self, '_last_update_time'):
                delta_time = current_time - self._last_update_time
            else:
                delta_time = 1.0 / 60.0  # Assume 60 FPS for first frame
            
            self._last_update_time = current_time
            
            # Update component
            self.modern_component.update(delta_time)
            
            # Create rect for rendering area
            rect = pygame.Rect(x, y, width, height)
            
            # Render modern advertising
            self.modern_component.render(screen, rect)
            
        except Exception as e:
            logger.error(f"Failed to draw modern advertising: {e}")
            raise  # Re-raise to trigger fallback
    
    def _draw_fallback_advertising(self, x: int, y: int, width: int, height: int, screen: pygame.Surface):
        """Draw using simple fallback method."""
        try:
            # Simple fallback implementation
            if not self.settings_manager:
                text = "WOW Games - Premium Bingo Experience"
                color = (255, 215, 0)  # Gold
            else:
                text = self.settings_manager.get_setting('advertising', 'text', "WOW Games - Premium Bingo Experience")
                color_hex = self.settings_manager.get_setting('advertising', 'text_color', "#FFD700")
                try:
                    color = self.settings_manager.hex_to_rgb(color_hex)
                except:
                    color = (255, 215, 0)
            
            # Create simple background
            bg_rect = pygame.Rect(x, y, width, height)
            pygame.draw.rect(screen, (30, 50, 100, 180), bg_rect)
            pygame.draw.rect(screen, (100, 150, 255), bg_rect, 2)
            
            # Render text
            font_size = min(height // 2, 32)
            font = pygame.font.SysFont("Arial", font_size, bold=True)
            text_surf = font.render(text, True, color)
            
            # Center text
            text_x = x + (width - text_surf.get_width()) // 2
            text_y = y + (height - text_surf.get_height()) // 2
            
            screen.blit(text_surf, (text_x, text_y))
            
        except Exception as e:
            logger.debug(f"Fallback advertising failed: {e}")
    
    def get_performance_info(self) -> Dict[str, Any]:
        """Get performance information."""
        try:
            if self.modern_component and self.initialized:
                return self.modern_component.get_performance_info()
            else:
                return {
                    'gpu_available': False,
                    'visual_quality': 'fallback',
                    'animation_mode': 'static',
                    'fallback_mode': self.fallback_mode
                }
        except Exception as e:
            return {'error': str(e)}
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self.modern_component:
                self.modern_component.cleanup()
            logger.info("Modern advertising adapter cleaned up")
        except Exception as e:
            logger.error(f"Failed to cleanup adapter: {e}")


def demonstrate_integration():
    """Demonstrate the modern advertising integration."""
    print("🎮 WOW Bingo Game - Modern Advertising Integration Demo")
    print("=" * 60)
    
    # Create adapter
    adapter = ModernAdvertisingAdapter()
    
    # Show performance info
    perf_info = adapter.get_performance_info()
    print("\n📊 Performance Information:")
    for key, value in perf_info.items():
        print(f"  {key}: {value}")
    
    # Simulate pygame surface
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    print("\n🎨 Rendering Test:")
    try:
        # Test rendering
        adapter.draw_advertising_text(50, 50, 700, 100, screen)
        print("  ✅ Rendering successful")
    except Exception as e:
        print(f"  ❌ Rendering failed: {e}")
    
    # Cleanup
    adapter.cleanup()
    pygame.quit()
    
    print("\n🏁 Integration demo completed!")


def show_integration_instructions():
    """Show integration instructions for existing code."""
    print("\n" + "="*70)
    print("INTEGRATION INSTRUCTIONS FOR EXISTING main.py")
    print("="*70)
    
    print("""
To integrate the modern advertising system with your existing main.py:

1. ADD IMPORT AT THE TOP OF main.py:

```python
# Add after existing imports
try:
    from modern_advertising_integration import ModernAdvertisingAdapter
    MODERN_ADVERTISING_AVAILABLE = True
except ImportError:
    MODERN_ADVERTISING_AVAILABLE = False
    print("Modern advertising not available")
```

2. MODIFY BingoGame.__init__() METHOD:

```python
def __init__(self):
    # ... your existing initialization code ...
    
    # Initialize modern advertising
    if MODERN_ADVERTISING_AVAILABLE:
        self.modern_advertising = ModernAdvertisingAdapter(settings_manager)
        print("🚀 Modern advertising initialized")
    else:
        self.modern_advertising = None
        print("⚠️ Using legacy advertising")
```

3. REPLACE draw_advertising_text() METHOD:

```python
def draw_advertising_text(self, x, y, width, height):
    \"\"\"Draw advertising text with modern GPU acceleration.\"\"\"
    try:
        if self.modern_advertising:
            # Use modern GPU-accelerated advertising
            self.modern_advertising.draw_advertising_text(x, y, width, height, screen)
        else:
            # Fallback to original method
            self._draw_legacy_advertising_text(x, y, width, height)
    except Exception as e:
        print(f"Advertising error: {e}")
        # Fallback to simple text
        font = pygame.font.SysFont("Arial", 24, bold=True)
        text_surf = font.render("WOW Games - Premium Bingo", True, (255, 215, 0))
        screen.blit(text_surf, (x + 10, y + 10))
```

4. ADD PERFORMANCE MONITORING (OPTIONAL):

```python
def show_performance_info(self):
    \"\"\"Show advertising performance information.\"\"\"
    if self.modern_advertising:
        perf_info = self.modern_advertising.get_performance_info()
        print("🎯 Advertising Performance:")
        print(f"  GPU Available: {perf_info.get('gpu_available', False)}")
        print(f"  Visual Quality: {perf_info.get('visual_quality', 'unknown')}")
        print(f"  Animation Mode: {perf_info.get('animation_mode', 'unknown')}")
        print(f"  Current FPS: {perf_info.get('current_fps', 0):.1f}")
```

5. ADD CLEANUP IN GAME EXIT:

```python
def cleanup(self):
    \"\"\"Clean up game resources.\"\"\"
    # ... existing cleanup code ...
    
    # Cleanup modern advertising
    if hasattr(self, 'modern_advertising') and self.modern_advertising:
        self.modern_advertising.cleanup()
```

BENEFITS OF INTEGRATION:
✅ GPU acceleration for smooth 60+ FPS animations
✅ Modern glass morphism backgrounds with gradients
✅ Multiple animation modes (scroll, wave, pulse, particle effects)
✅ Automatic quality adjustment based on hardware
✅ Professional typography with glow effects
✅ Backward compatibility with existing settings
✅ Intelligent fallback to CPU optimization
✅ Performance monitoring and optimization
✅ Drop-in replacement - minimal code changes required

The integration maintains full compatibility with your existing settings
while adding modern GPU-accelerated visual effects!
""")


if __name__ == "__main__":
    # Run the demonstration
    demonstrate_integration()
    
    # Show integration instructions
    show_integration_instructions()
