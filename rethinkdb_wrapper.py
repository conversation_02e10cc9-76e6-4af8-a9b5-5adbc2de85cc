# RethinkDB wrapper module
import sys
try:
    import rethinkdb
    sys.modules['r'] = rethinkdb.r
    
    # Create a Connect class with the same interface as the original connect function
    class Connect:
        @staticmethod
        def connect(*args, **kwargs):
            return rethinkdb.r.connect(*args, **kwargs)
    
    # Add connect function to the module
    sys.modules['rethinkdb'].connect = Connect.connect
    
    print("RethinkDB wrapper initialized")
except ImportError:
    print("RethinkDB not installed")
except Exception as e:
    print(f"Error initializing RethinkDB wrapper: {e}")
