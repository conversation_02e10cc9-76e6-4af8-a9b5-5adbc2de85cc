import sys 
try: 
    import rethinkdb 
    print(dir(rethinkdb)) 
    if not hasattr(rethinkdb, 'connect'): 
        # Fix r namespace 
        print("Fixing RethinkDB import") 
        with open("rethink_db.py", "r") as f: 
            content = f.read() 
        content = content.replace("import rethinkdb as r", "import rethinkdb\r\nr = rethinkdb") 
        with open("rethink_db.py", "w") as f: 
            f.write(content) 
except Exception as e: 
    print(f"Error checking RethinkDB: {e}") 
