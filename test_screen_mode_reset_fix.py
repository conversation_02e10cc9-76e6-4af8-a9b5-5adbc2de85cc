"""
Test script for Screen Mode Reset Fix

This script tests the fix for screen mode inconsistency that occurs when a game session 
is reset and redirects to the board selection page. It verifies that the board selection 
page inherits and maintains the screen mode that was active before the reset.

Test scenarios:
1. Start in windowed mode → switch to fullscreen → reset → verify board selection opens in fullscreen
2. Start in fullscreen mode → switch to windowed → reset → verify board selection opens in windowed
3. Test settings synchronization and persistence
"""

import pygame
import sys
import os
import time
from screen_mode_manager import get_screen_mode_manager
from settings_manager import SettingsManager

def test_screen_mode_inheritance():
    """Test that board selection inherits screen mode from main game"""
    print("=" * 60)
    print("TESTING SCREEN MODE INHERITANCE AFTER RESET")
    print("=" * 60)
    
    # Initialize pygame
    pygame.init()
    
    # Test 1: Windowed → Fullscreen → Reset → Board Selection should be Fullscreen
    print("\n--- Test 1: Windowed → Fullscreen → Reset ---")
    
    # Start in windowed mode
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Screen Mode Reset Test")
    
    # Get screen mode manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Ensure we start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.apply_screen_mode(screen, force_mode=False)
    
    print(f"Initial mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    assert not screen_mode_manager.is_fullscreen(), "Should start in windowed mode"
    
    # Switch to fullscreen (simulating user action during gameplay)
    print("Switching to fullscreen mode...")
    screen, new_mode = screen_mode_manager.toggle_screen_mode(screen)
    print(f"After toggle: {'fullscreen' if new_mode else 'windowed'}")
    assert new_mode == True, "Should be in fullscreen mode after toggle"
    
    # Simulate game reset by calling board selection with from_reset=True
    print("Simulating game reset - calling board selection...")
    try:
        from Board_selection_fixed import show_board_selection
        
        # This should inherit the fullscreen mode
        print("Board selection should open in fullscreen mode...")
        
        # We'll simulate the board selection call without actually running the full UI
        # Just test the screen mode application logic
        test_screen = pygame.display.get_surface()
        
        # Simulate what happens in show_board_selection when from_reset=True
        screen_mode_manager_test = get_screen_mode_manager()
        test_screen = screen_mode_manager_test.ensure_consistent_mode(test_screen)
        
        # Verify the screen mode is maintained
        is_fullscreen_after_reset = screen_mode_manager_test.is_fullscreen()
        print(f"Board selection screen mode: {'fullscreen' if is_fullscreen_after_reset else 'windowed'}")
        
        assert is_fullscreen_after_reset, "Board selection should inherit fullscreen mode"
        print("✓ Test 1 PASSED: Board selection correctly inherited fullscreen mode")
        
    except Exception as e:
        print(f"✗ Test 1 FAILED: {e}")
        return False
    
    # Test 2: Fullscreen → Windowed → Reset → Board Selection should be Windowed
    print("\n--- Test 2: Fullscreen → Windowed → Reset ---")
    
    # Switch to windowed mode (simulating user action during gameplay)
    print("Switching to windowed mode...")
    screen, new_mode = screen_mode_manager.toggle_screen_mode(screen)
    print(f"After toggle: {'fullscreen' if new_mode else 'windowed'}")
    assert new_mode == False, "Should be in windowed mode after toggle"
    
    # Simulate game reset again
    print("Simulating game reset - calling board selection...")
    try:
        # Simulate what happens in show_board_selection when from_reset=True
        test_screen = pygame.display.get_surface()
        screen_mode_manager_test = get_screen_mode_manager()
        test_screen = screen_mode_manager_test.ensure_consistent_mode(test_screen)
        
        # Verify the screen mode is maintained
        is_fullscreen_after_reset = screen_mode_manager_test.is_fullscreen()
        print(f"Board selection screen mode: {'fullscreen' if is_fullscreen_after_reset else 'windowed'}")
        
        assert not is_fullscreen_after_reset, "Board selection should inherit windowed mode"
        print("✓ Test 2 PASSED: Board selection correctly inherited windowed mode")
        
    except Exception as e:
        print(f"✗ Test 2 FAILED: {e}")
        return False
    
    # Clean up
    pygame.quit()
    return True

def test_settings_persistence():
    """Test that screen mode settings persist correctly"""
    print("\n" + "=" * 60)
    print("TESTING SETTINGS PERSISTENCE")
    print("=" * 60)
    
    # Test settings persistence
    settings_manager = SettingsManager()
    
    # Test setting fullscreen mode
    print("Testing fullscreen mode persistence...")
    settings_manager.set_screen_mode(True)
    assert settings_manager.get_current_screen_mode() == True, "Fullscreen setting should persist"
    print("✓ Fullscreen mode setting persisted correctly")
    
    # Test setting windowed mode
    print("Testing windowed mode persistence...")
    settings_manager.set_screen_mode(False)
    assert settings_manager.get_current_screen_mode() == False, "Windowed setting should persist"
    print("✓ Windowed mode setting persisted correctly")
    
    return True

def test_screen_mode_manager_consistency():
    """Test that screen mode manager maintains consistency"""
    print("\n" + "=" * 60)
    print("TESTING SCREEN MODE MANAGER CONSISTENCY")
    print("=" * 60)
    
    # Initialize pygame for this test
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    # Get screen mode manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Test that settings and actual screen mode stay in sync
    print("Testing settings and screen mode synchronization...")
    
    # Set to windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    settings_mode = screen_mode_manager.get_current_screen_mode()
    actual_mode = screen_mode_manager.is_fullscreen()
    
    print(f"Settings mode: {'fullscreen' if settings_mode else 'windowed'}")
    print(f"Actual mode: {'fullscreen' if actual_mode else 'windowed'}")
    
    assert settings_mode == actual_mode, "Settings and actual screen mode should match"
    print("✓ Settings and screen mode are synchronized")
    
    # Test toggle functionality
    print("Testing toggle functionality...")
    original_mode = screen_mode_manager.is_fullscreen()
    screen, new_mode = screen_mode_manager.toggle_screen_mode(screen)
    
    assert new_mode != original_mode, "Toggle should change the mode"
    assert screen_mode_manager.get_current_screen_mode() == new_mode, "Settings should update after toggle"
    print("✓ Toggle functionality works correctly")
    
    # Clean up
    pygame.quit()
    return True

def main():
    """Run all tests"""
    print("SCREEN MODE RESET FIX - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    all_tests_passed = True
    
    try:
        # Test 1: Screen mode inheritance
        if not test_screen_mode_inheritance():
            all_tests_passed = False
        
        # Test 2: Settings persistence
        if not test_settings_persistence():
            all_tests_passed = False
        
        # Test 3: Screen mode manager consistency
        if not test_screen_mode_manager_consistency():
            all_tests_passed = False
        
        # Final results
        print("\n" + "=" * 60)
        print("TEST RESULTS SUMMARY")
        print("=" * 60)
        
        if all_tests_passed:
            print("🎉 ALL TESTS PASSED! ✓")
            print("\nThe screen mode reset fix is working correctly:")
            print("✓ Board selection inherits screen mode from main game after reset")
            print("✓ Settings persistence works correctly")
            print("✓ Screen mode manager maintains consistency")
            print("\nUsers will now experience seamless screen mode transitions")
            print("even when game resets redirect to board selection.")
        else:
            print("❌ SOME TESTS FAILED! ✗")
            print("\nThe screen mode reset fix needs attention.")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
