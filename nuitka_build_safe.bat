@echo off
REM ================================================================
REM WOW Bingo Game - Safe MSVC Nuitka Build Script
REM ================================================================
REM This script uses the safe MSVC Nuitka build script that avoids
REM Clang issues by using MSVC compiler exclusively.
REM ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PROJECT_NAME=WOW Bingo Game"
set "PROJECT_VERSION=1.0.0"
set "MAIN_SCRIPT=main.py"
set "ICON_PATH=assets\app_logo.ico"
set "BUILD_DIR=build_safe"
set "OUTPUT_NAME=WOWBingoGame"

echo ================================================================
echo     WOW Bingo Game - Safe MSVC Nuitka Build
echo ================================================================
echo.
echo This build avoids Clang issues by using MSVC compiler exclusively.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting safe MSVC build process...
echo.

REM Build with safe MSVC settings
echo Building executable with Nuitka (Safe MSVC Mode)...
echo This avoids Clang issues and uses MSVC compiler exclusively.
echo Build time: approximately 10-15 minutes...
echo.

python nuitka_build_msvc_safe.py --clean --verbose

if errorlevel 1 (
    echo.
    echo ================================================================================
    echo BUILD FAILED
    echo ================================================================================
    echo.
    echo The build failed. This could be due to:
    echo 1. Missing Visual Studio Build Tools
    echo 2. Missing dependencies
    echo 3. Insufficient disk space or memory
    echo.
    echo Solutions:
    echo 1. Install Visual Studio Build Tools from:
    echo    https://visualstudio.microsoft.com/visual-cpp-build-tools/
    echo 2. Install dependencies: pip install -r requirements.txt
    echo 3. Free up disk space (need at least 5GB)
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================================
echo.
echo Your executable is ready in the 'dist' directory.
echo The executable can run on any Windows PC without Python installation.
echo.
echo Next steps:
echo 1. Test the executable by running it from the dist folder
echo 2. Copy the entire dist folder to distribute your application
echo 3. The executable includes all assets and dependencies
echo.
pause
