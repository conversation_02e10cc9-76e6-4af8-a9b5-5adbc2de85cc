#!/usr/bin/env python3
"""
WOW Bingo Game - Build Verification Script
==========================================

This script verifies that the build environment is properly set up
and that all required components are available for building the game.

Usage:
    python verify_build.py [options]

Options:
    --fix-issues    Attempt to automatically fix detected issues
    --verbose       Show detailed information about each check
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path
import json
import time

class BuildVerifier:
    """Verifies build environment and requirements."""
    
    def __init__(self, verbose: bool = False, fix_issues: bool = False):
        self.verbose = verbose
        self.fix_issues = fix_issues
        self.project_root = Path(__file__).parent.absolute()
        self.issues_found = []
        self.checks_passed = 0
        self.checks_total = 0
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message."""
        if level == "DEBUG" and not self.verbose:
            return
        
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🔍"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
    
    def check_python_version(self) -> bool:
        """Check Python version compatibility."""
        self.checks_total += 1
        self.log("Checking Python version...", "DEBUG")
        
        if sys.version_info < (3, 7):
            self.log(f"Python {sys.version_info.major}.{sys.version_info.minor} is too old. Requires 3.7+", "ERROR")
            self.issues_found.append("Python version too old")
            return False
        
        self.log(f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} ✓", "SUCCESS")
        self.checks_passed += 1
        return True
    
    def check_pip_availability(self) -> bool:
        """Check if pip is available."""
        self.checks_total += 1
        self.log("Checking pip availability...", "DEBUG")
        
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.log(f"pip available: {result.stdout.strip()}", "SUCCESS")
            self.checks_passed += 1
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("pip not available", "ERROR")
            self.issues_found.append("pip not available")
            return False
    
    def check_required_files(self) -> bool:
        """Check if all required project files exist."""
        self.checks_total += 1
        self.log("Checking required project files...", "DEBUG")
        
        required_files = [
            "main.py",
            "build_executable.py",
            "WOW_Bingo_Game.spec",
            "build_requirements.txt",
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = self.project_root / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            self.log(f"Missing required files: {', '.join(missing_files)}", "ERROR")
            self.issues_found.append(f"Missing files: {', '.join(missing_files)}")
            return False
        
        self.log(f"All required files present ({len(required_files)} files)", "SUCCESS")
        self.checks_passed += 1
        return True
    
    def check_assets_directory(self) -> bool:
        """Check if assets directory exists and has content."""
        self.checks_total += 1
        self.log("Checking assets directory...", "DEBUG")
        
        assets_dir = self.project_root / "assets"
        if not assets_dir.exists():
            self.log("Assets directory not found", "ERROR")
            self.issues_found.append("Assets directory missing")
            return False
        
        # Count files in assets
        asset_files = list(assets_dir.rglob("*"))
        file_count = len([f for f in asset_files if f.is_file()])
        
        if file_count == 0:
            self.log("Assets directory is empty", "WARNING")
            self.issues_found.append("Assets directory empty")
            return False
        
        self.log(f"Assets directory contains {file_count} files", "SUCCESS")
        self.checks_passed += 1
        return True
    
    def check_build_dependencies(self) -> bool:
        """Check if build dependencies are installed."""
        self.checks_total += 1
        self.log("Checking build dependencies...", "DEBUG")
        
        required_packages = [
            'pygame',
            'pyperclip',
            'psutil',
            'PIL',  # Pillow
            'cryptography',
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                spec = importlib.util.find_spec(package)
                if spec is None:
                    missing_packages.append(package)
                else:
                    self.log(f"Package {package} ✓", "DEBUG")
            except (ImportError, ValueError):
                missing_packages.append(package)
        
        if missing_packages:
            self.log(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
            self.issues_found.append(f"Missing packages: {', '.join(missing_packages)}")
            
            if self.fix_issues:
                self.log("Attempting to install missing packages...", "INFO")
                return self._install_missing_packages(missing_packages)
            return False
        
        self.log(f"All required packages available ({len(required_packages)} packages)", "SUCCESS")
        self.checks_passed += 1
        return True
    
    def check_build_tools(self) -> bool:
        """Check if build tools are available."""
        self.checks_total += 1
        self.log("Checking build tools...", "DEBUG")
        
        # Check PyInstaller
        try:
            result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'],
                                  capture_output=True, text=True, check=True)
            pyinstaller_version = result.stdout.strip()
            self.log(f"PyInstaller {pyinstaller_version} ✓", "SUCCESS")
            self.checks_passed += 1
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("PyInstaller not available", "ERROR")
            self.issues_found.append("PyInstaller not installed")
            
            if self.fix_issues:
                self.log("Attempting to install PyInstaller...", "INFO")
                return self._install_pyinstaller()
            return False
    
    def check_disk_space(self) -> bool:
        """Check available disk space."""
        self.checks_total += 1
        self.log("Checking disk space...", "DEBUG")
        
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.project_root)
            free_gb = free / (1024**3)
            
            if free_gb < 2:
                self.log(f"Low disk space: {free_gb:.1f} GB free (need 2+ GB)", "WARNING")
                self.issues_found.append("Low disk space")
                return False
            
            self.log(f"Disk space OK: {free_gb:.1f} GB free", "SUCCESS")
            self.checks_passed += 1
            return True
        except Exception as e:
            self.log(f"Could not check disk space: {e}", "WARNING")
            return True  # Don't fail the check if we can't determine space
    
    def _install_missing_packages(self, packages: list) -> bool:
        """Attempt to install missing packages."""
        try:
            for package in packages:
                # Map package names to pip names
                pip_name = {
                    'PIL': 'Pillow',
                    'pygame': 'pygame',
                    'pyperclip': 'pyperclip',
                    'psutil': 'psutil',
                    'cryptography': 'cryptography',
                }.get(package, package)
                
                self.log(f"Installing {pip_name}...", "INFO")
                subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name],
                             check=True, capture_output=True)
                self.log(f"Successfully installed {pip_name}", "SUCCESS")
            
            self.checks_passed += 1
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install packages: {e}", "ERROR")
            return False
    
    def _install_pyinstaller(self) -> bool:
        """Attempt to install PyInstaller."""
        try:
            self.log("Installing PyInstaller...", "INFO")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'],
                         check=True, capture_output=True)
            self.log("Successfully installed PyInstaller", "SUCCESS")
            self.checks_passed += 1
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install PyInstaller: {e}", "ERROR")
            return False
    
    def run_verification(self) -> bool:
        """Run all verification checks."""
        self.log("Starting build environment verification...", "INFO")
        self.log("=" * 60, "INFO")
        
        # Run all checks
        checks = [
            ("Python Version", self.check_python_version),
            ("Pip Availability", self.check_pip_availability),
            ("Required Files", self.check_required_files),
            ("Assets Directory", self.check_assets_directory),
            ("Build Dependencies", self.check_build_dependencies),
            ("Build Tools", self.check_build_tools),
            ("Disk Space", self.check_disk_space),
        ]
        
        for check_name, check_func in checks:
            self.log(f"Running check: {check_name}", "DEBUG")
            check_func()
        
        # Summary
        self.log("=" * 60, "INFO")
        self.log(f"Verification complete: {self.checks_passed}/{self.checks_total} checks passed", "INFO")
        
        if self.issues_found:
            self.log("Issues found:", "WARNING")
            for issue in self.issues_found:
                self.log(f"  • {issue}", "WARNING")
            
            if not self.fix_issues:
                self.log("Run with --fix-issues to attempt automatic fixes", "INFO")
        
        success = len(self.issues_found) == 0
        if success:
            self.log("✅ Build environment is ready!", "SUCCESS")
            self.log("You can now run: python build_executable.py", "INFO")
        else:
            self.log("❌ Build environment has issues that need to be resolved", "ERROR")
        
        return success


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Verify WOW Bingo Game build environment")
    parser.add_argument('--fix-issues', action='store_true', 
                       help='Attempt to automatically fix detected issues')
    parser.add_argument('--verbose', action='store_true',
                       help='Show detailed information about each check')
    
    args = parser.parse_args()
    
    verifier = BuildVerifier(verbose=args.verbose, fix_issues=args.fix_issues)
    
    try:
        success = verifier.run_verification()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nVerification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error during verification: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
