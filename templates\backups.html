{% extends "layout.html" %}
{% block content %}
    <h2>Backup Management</h2>

    <div class="dashboard-summary">
        <div class="summary-card">
            <h3>Total Backups</h3>
            <div class="value">{{ backups|length }}</div>
        </div>
        <div class="summary-card">
            <h3>Total Size</h3>
            <div class="value">{{ "%.2f"|format(total_size / 1024 / 1024) }} MB</div>
        </div>
        <div class="summary-card">
            <h3>Latest Backup</h3>
            <div class="value">{{ latest_backup.created_at if latest_backup else 'None' }}</div>
        </div>
    </div>

    <h3>Actions</h3>
    <form method="post" action="/backups/create" style="display: inline;">
        <button type="submit">Create Backup Now</button>
    </form>

    <h3>Available Backups</h3>
    <table>
        <thead>
            <tr>
                <th>Name</th>
                <th>Created</th>
                <th>Size</th>
                <th>Type</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for backup in backups %}
            <tr>
                <td>{{ backup.name }}</td>
                <td>{{ backup.created_at }}</td>
                <td>{{ "%.2f"|format(backup.size / 1024 / 1024) }} MB</td>
                <td>{{ backup.type }}</td>
                <td>
                    <form method="post" action="/backups/download/{{ backup.name }}" style="display: inline;">
                        <button type="submit">Download</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}
