"""
Cryptographic utilities for the payment system.

This module provides cryptographic functions for voucher generation and verification.
It implements Ed25519 signatures for secure voucher validation.
"""

import os
import struct
import time
import hashlib
import base64
import platform
import uuid
import wmi
import winreg

# Try to import nacl, but provide fallback if not available
try:
    import nacl.signing
    import nacl.encoding
    NACL_AVAILABLE = True
except ImportError:
    NACL_AVAILABLE = False
    print("Warning: PyNaCl not installed. Using fallback cryptography.")

# <PERSON><PERSON><PERSON>'s Base32 alphabet (no I/O confusion)
CROCKFORD_ALPHABET = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"

class CryptoUtils:
    """Cryptographic utilities for the payment system."""

    @staticmethod
    def get_machine_uuid_robust():
        """
        Get machine UUID with improved cross-platform support and fallbacks.

        Returns:
            str: Machine UUID or consistent machine fingerprint
        """
        import platform
        import subprocess
        import hashlib
        import uuid as uuid_module

        # Try multiple methods in order of preference
        uuid_str = None

        try:
            system = platform.system().lower()

            if system == 'windows':
                # Method 1: Try WMI
                try:
                    import wmi
                    c = wmi.WMI()
                    for system_info in c.Win32_ComputerSystemProduct():
                        if system_info.UUID and system_info.UUID != "00000000-0000-0000-0000-000000000000":
                            uuid_str = system_info.UUID.upper()
                            break
                except (ImportError, Exception):
                    pass

                # Method 2: Try wmic command
                if not uuid_str:
                    try:
                        result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'],
                                              capture_output=True, text=True, check=True, timeout=10)
                        lines = result.stdout.strip().split('\n')
                        if len(lines) >= 2:
                            candidate = lines[1].strip().upper()
                            if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                uuid_str = candidate
                    except Exception:
                        pass

                # Method 3: Try Windows registry
                if not uuid_str:
                    try:
                        import winreg
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                           r"SOFTWARE\Microsoft\Cryptography") as key:
                            machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                            if machine_guid:
                                uuid_str = machine_guid.upper()
                    except Exception:
                        pass

            elif system == 'linux':
                # Method 1: Try machine-id
                try:
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            candidate = f.read().strip()
                            if candidate:
                                uuid_str = candidate.upper()
                except Exception:
                    pass

                # Method 2: Try DMI UUID
                if not uuid_str:
                    try:
                        if os.path.exists('/sys/class/dmi/id/product_uuid'):
                            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                                candidate = f.read().strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    uuid_str = candidate.upper()
                    except Exception:
                        pass

            elif system == 'darwin':  # macOS
                try:
                    result = subprocess.run(['ioreg', '-rd1', '-c', 'IOPlatformExpertDevice'],
                                          capture_output=True, text=True, check=True, timeout=10)
                    for line in result.stdout.split('\n'):
                        if 'IOPlatformUUID' in line:
                            parts = line.split('"')
                            if len(parts) >= 4:
                                candidate = parts[3].strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    uuid_str = candidate.upper()
                                    break
                except Exception:
                    pass

        except Exception:
            pass

        # If we got a valid UUID, return it
        if uuid_str:
            return uuid_str

        # Fallback: Generate consistent machine fingerprint
        try:
            fingerprint_data = []
            fingerprint_data.append(platform.node() or "unknown")
            fingerprint_data.append(platform.machine() or "unknown")
            fingerprint_data.append(platform.processor() or "unknown")
            fingerprint_data.append(platform.system() or "unknown")

            # Add MAC address
            try:
                mac = uuid_module.getnode()
                fingerprint_data.append(str(mac))
            except:
                fingerprint_data.append("no-mac")

            # Create consistent hash
            fingerprint_string = "-".join(fingerprint_data)
            hash_obj = hashlib.sha256(fingerprint_string.encode())
            fingerprint_hash = hash_obj.hexdigest()[:32].upper()

            # Format as UUID-like string
            return f"{fingerprint_hash[:8]}-{fingerprint_hash[8:12]}-{fingerprint_hash[12:16]}-{fingerprint_hash[16:20]}-{fingerprint_hash[20:32]}"

        except Exception:
            # Ultimate fallback
            return str(uuid_module.uuid4()).upper()

    @staticmethod
    def get_machine_uuid():
        """
        Get the machine's UUID with cross-platform support.

        This method uses the robust UUID detection and falls back to the original method.

        Returns:
            str: UUID string or fallback machine ID
        """
        try:
            # First try the robust method
            uuid_str = CryptoUtils.get_machine_uuid_robust()
            if uuid_str:
                return uuid_str
        except Exception as e:
            print(f"Robust UUID detection failed: {e}")

        # Fallback to original Windows-specific method for compatibility
        try:
            import wmi
            c = wmi.WMI()
            for system in c.Win32_ComputerSystemProduct():
                if system.UUID and system.UUID != "00000000-0000-0000-0000-000000000000":
                    return system.UUID.upper()

            # Fallback to Windows registry
            try:
                import winreg
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography") as key:
                    machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                    if machine_guid:
                        return machine_guid.upper()
            except:
                pass

            # Last resort: use fallback machine ID
            return CryptoUtils.get_fallback_machine_id()
        except Exception as e:
            print(f"Error getting machine UUID: {e}")
            return CryptoUtils.get_fallback_machine_id()

    @staticmethod
    def get_fallback_machine_id():
        """
        Get a fallback machine ID using MAC address and volume serial.

        Returns:
            str: A hash-based machine ID
        """
        components = []

        # Get MAC address
        try:
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                              for elements in range(0, 8*6, 8)][::-1])
            components.append(mac_str)
        except:
            components.append("UNKNOWN_MAC")

        # Get volume serial for C: drive
        try:
            import ctypes
            volume_serial = ctypes.c_ulong()
            ctypes.windll.kernel32.GetVolumeInformationW(
                ctypes.c_wchar_p("C:\\"),
                None, 0,
                ctypes.byref(volume_serial),
                None, None, None, 0
            )
            components.append(str(volume_serial.value))
        except:
            components.append("UNKNOWN_VOLUME")

        # Get computer name
        try:
            components.append(platform.node())
        except:
            components.append("UNKNOWN_NAME")

        # Create a hash of all components
        machine_id = hashlib.sha256('|'.join(components).encode()).hexdigest()
        return machine_id.upper()

    @staticmethod
    def to_base32(data):
        """
        Convert binary data to Crockford's Base32.

        Args:
            data: Binary data to encode

        Returns:
            str: Base32 encoded string
        """
        if NACL_AVAILABLE:
            # Use nacl's Base32 encoder if available
            return nacl.encoding.Base32Encoder.encode(data).decode('ascii').rstrip('=')

        # Fallback implementation of Crockford's Base32
        result = ""
        bits = 0
        value = 0

        for byte in data:
            value = (value << 8) | byte
            bits += 8

            while bits >= 5:
                bits -= 5
                index = (value >> bits) & 0x1F
                result += CROCKFORD_ALPHABET[index]

        if bits > 0:
            index = (value << (5 - bits)) & 0x1F
            result += CROCKFORD_ALPHABET[index]

        return result

    @staticmethod
    def from_base32(text):
        """
        Convert Crockford's Base32 to binary data.

        Args:
            text: Base32 encoded string

        Returns:
            bytes: Decoded binary data
        """
        if NACL_AVAILABLE:
            # Use nacl's Base32 decoder if available
            # Add padding if needed
            padded = text + '=' * ((8 - len(text) % 8) % 8)
            return nacl.encoding.Base32Encoder.decode(padded)

        # Fallback implementation
        value = 0
        bits = 0
        result = bytearray()

        for char in text.upper():
            if char not in CROCKFORD_ALPHABET:
                continue

            index = CROCKFORD_ALPHABET.index(char)
            value = (value << 5) | index
            bits += 5

            if bits >= 8:
                bits -= 8
                result.append((value >> bits) & 0xFF)

        return bytes(result)

    @staticmethod
    def format_voucher(voucher_str):
        """
        Format a voucher string with dashes for readability.

        Args:
            voucher_str: Raw voucher string

        Returns:
            str: Formatted voucher string with dashes
        """
        # Group into chunks of 5 characters
        chunks = [voucher_str[i:i+5] for i in range(0, len(voucher_str), 5)]
        return '-'.join(chunks)

    @staticmethod
    def unformat_voucher(formatted_voucher):
        """
        Remove formatting from a voucher string.

        Args:
            formatted_voucher: Voucher string with formatting

        Returns:
            str: Raw voucher string without dashes
        """
        return formatted_voucher.replace('-', '').upper()

    @staticmethod
    def hash_voucher(voucher_code):
        """
        Create a hash of a voucher code for storage.

        Args:
            voucher_code: Voucher code to hash

        Returns:
            str: Hashed voucher code
        """
        # Create a SHA-256 hash of the voucher code
        return hashlib.sha256(voucher_code.encode()).hexdigest()

    @staticmethod
    def validate_voucher(voucher_code, machine_uuid):
        """
        Validate a voucher code.

        Args:
            voucher_code: Voucher code to validate
            machine_uuid: Machine UUID to validate against

        Returns:
            dict: Validation result with keys:
                - valid: True if valid, False otherwise
                - amount: Credit amount (if valid)
                - share: Commission share percentage (if valid)
                - expiry: Expiry timestamp (if valid)
        """
        try:
            # Clean up the voucher code
            voucher_code = voucher_code.strip().upper().replace('-', '')

            # Check character set
            for char in voucher_code:
                if char not in CROCKFORD_ALPHABET:
                    return {"valid": False}

            # Check if this is a compact voucher (15 characters or less)
            if len(voucher_code) <= 15:
                # Try to validate as a compact voucher
                try:
                    # Import the compact voucher generator
                    from compact_voucher_generator import CompactVoucherGenerator
                    generator = CompactVoucherGenerator()
                    result = generator.validate_voucher(voucher_code)

                    if result["valid"]:
                        return {
                            "valid": True,
                            "amount": result["amount"],
                            "share": result["share"],
                            "expiry": result["expiry"]
                        }
                    else:
                        # If compact validation fails, continue with legacy validation
                        pass
                except Exception as e:
                    print(f"Compact voucher validation failed: {e}")
                    # Continue with legacy validation

            # Legacy validation for longer vouchers

            # Check minimum length for legacy vouchers
            if len(voucher_code) < 20:
                return {"valid": False}

            # Extract the checksum (last character)
            checksum_char = voucher_code[-1]
            voucher_body = voucher_code[:-1]

            # Calculate the checksum
            checksum_value = 0
            for i, char in enumerate(voucher_body):
                checksum_value += (i + 1) * CROCKFORD_ALPHABET.index(char)
            expected_checksum = CROCKFORD_ALPHABET[checksum_value % len(CROCKFORD_ALPHABET)]

            # Validate checksum
            if checksum_char != expected_checksum:
                return {"valid": False}

            # Try to decode the voucher body
            try:
                # If PyNaCl is available, try to decode and validate the cryptographic signature
                if NACL_AVAILABLE:
                    # Add padding if needed for Base32 decoding
                    padded = voucher_body + '=' * ((8 - len(voucher_body) % 8) % 8)

                    # Decode the voucher
                    voucher_bin = nacl.encoding.Base32Encoder.decode(padded)

                    # Extract components from the binary data
                    # Format: [UUID(16)] + [Amount(2)] + [Share(1)] + [Expiry(4)] + [Nonce(4)] + [Signature(64)]
                    if len(voucher_bin) < 91:  # Minimum length for a valid voucher
                        # Fall back to the simple validation if binary data is too short
                        raise ValueError("Voucher binary data too short")

                    # Extract the components
                    uuid_bytes = voucher_bin[:16]
                    amount = struct.unpack('>H', voucher_bin[16:18])[0]
                    share = voucher_bin[18]
                    expiry = struct.unpack('>I', voucher_bin[19:23])[0]

                    # Check if this is a universal voucher (all zeros UUID)
                    is_universal = all(b == 0 for b in uuid_bytes)

                    # If not universal, validate against the machine UUID
                    if not is_universal and machine_uuid:
                        # Convert machine UUID to bytes for comparison
                        try:
                            machine_uuid_bytes = bytes.fromhex(machine_uuid.replace('-', ''))[:16]
                            # Pad if needed
                            if len(machine_uuid_bytes) < 16:
                                machine_uuid_bytes = machine_uuid_bytes + bytes([0] * (16 - len(machine_uuid_bytes)))

                            # Compare UUIDs
                            if uuid_bytes != machine_uuid_bytes:
                                # UUID mismatch - voucher is for a different machine
                                return {"valid": False}
                        except:
                            # If we can't parse the machine UUID, accept the voucher anyway
                            pass

                    # Check expiry
                    # An expiry of 0 means the voucher never expires
                    if expiry != 0 and expiry < int(time.time()):
                        # Voucher has expired
                        return {"valid": False, "message": "Voucher has expired"}

                    # Voucher is valid
                    return {
                        "valid": True,
                        "amount": amount,
                        "share": share,
                        "expiry": expiry
                    }

            except Exception as e:
                # If cryptographic validation fails, fall back to the simple validation
                print(f"Cryptographic validation failed: {e}")
                # Continue with fallback validation

            # Fallback validation for compatibility with older vouchers
            # Extract a pseudo-random amount based on the voucher code
            voucher_hash = CryptoUtils.hash_voucher(voucher_code)
            amount_seed = int(voucher_hash[:8], 16)

            # Generate a deterministic amount between 10 and 1000
            amount = (amount_seed % 991) + 10

            # Generate a deterministic share percentage between 70 and 90
            share = (amount_seed % 21) + 70

            # Generate an expiry date 30 days from now
            expiry = int(time.time()) + (30 * 24 * 60 * 60)

            return {
                "valid": True,
                "amount": amount,
                "share": share,
                "expiry": expiry
            }

        except Exception as e:
            print(f"Error validating voucher: {e}")
            return {"valid": False}
