import random

class BingoCard:
    def __init__(self, card_id=None, board_data=None):
        """
        Initialize a new Bingo Card with a unique ID.

        Args:
            card_id: Unique identifier for the card (optional, will use cartela_no if None)
            board_data: Optional pre-defined board data to use instead of generating a random board
        """
        self.card_id = card_id
        self.grid = []
        self.marked = set()  # Set of marked numbers on this card
        self.has_won = False
        self.winning_pattern = None  # For backward compatibility
        self.winning_patterns = []   # List to store all winning patterns

        # Either load from provided data or generate a random card
        if board_data:
            self.load_from_data(board_data)
        else:
            self.generate_card()

    def load_from_data(self, board_data):
        """
        Load a board from pre-defined data

        Args:
            board_data: List of lists representing the board data
                        Format should be a 5x5 grid with columns as the first dimension
        """
        self.grid = []

        # Validate the data format
        if not isinstance(board_data, list) or len(board_data) != 5:
            print(f"Invalid board data format for card {self.card_id}, generating random card instead")
            self.generate_card()
            return

        # Copy the data to ensure we don't modify the original
        for col in board_data:
            if not isinstance(col, list) or len(col) != 5:
                print(f"Invalid column format in board data for card {self.card_id}, generating random card instead")
                self.generate_card()
                return
            self.grid.append(col.copy())

        # Ensure the middle space (N3) is a free space (value 0)
        if self.grid[2][2] != 0:
            print(f"Warning: Middle space not set as free space in board data for card {self.card_id}, fixing")
            self.grid[2][2] = 0

        # Mark the free space
        self.marked.add(0)

        # Debug output to verify the board was loaded correctly
        print(f"Loaded board for card {self.card_id}:")
        print(self.print_card())

    def generate_card(self):
        """Generate a standard 5x5 bingo card with proper number ranges for each column"""
        self.grid = []

        # Standard Bingo columns have specific number ranges:
        # B: 1-15, I: 16-30, N: 31-45, G: 46-60, O: 61-75
        ranges = [
            (1, 15),    # B column
            (16, 30),   # I column
            (31, 45),   # N column
            (46, 60),   # G column
            (61, 75)    # O column
        ]

        # Create each column
        for col_range in ranges:
            start, end = col_range
            # Select 5 unique random numbers from the column's range
            column = random.sample(range(start, end + 1), 5)
            self.grid.append(column)

        # Set the middle space (N3) as free space (value 0)
        self.grid[2][2] = 0
        self.marked.add(0)  # Mark free space automatically

    def get_number_at(self, col, row):
        """Get the number at a specific position on the card"""
        if 0 <= col < 5 and 0 <= row < 5:
            return self.grid[col][row]
        return None

    def mark_number(self, number):
        """Mark a number on the card if it exists"""
        for col in range(5):
            for row in range(5):
                if self.grid[col][row] == number:
                    self.marked.add(number)
                    return True
        return False

    def is_marked(self, col, row):
        """Check if the number at the specified position is marked"""
        number = self.get_number_at(col, row)
        return number in self.marked if number is not None else False

    def check_winning_patterns(self, called_numbers):
        """
        Check if this card has any winning patterns based on called numbers.
        Returns True if at least one winning pattern is found, False otherwise.
        Stores all winning patterns in self.winning_patterns list.

        Standard bingo patterns:
        - Rows: Any complete horizontal line (5 numbers)
        - Columns: Any complete vertical line (5 numbers)
        - Diagonals: Any complete diagonal line from corner to corner
        - Four Corners: All four corner numbers marked
        - Blackout/Coverall: All numbers on the card marked

        Performance optimized version with:
        - Early returns when already won
        - Set-based lookups for called numbers
        - Caching of intermediate results
        """
        # If we've already checked this exact set of called numbers, return cached result
        # This prevents redundant checks when the same numbers are called multiple times
        if hasattr(self, '_last_check_numbers') and hasattr(self, '_last_check_result'):
            # If the called numbers are the same as last time, return the cached result
            if self._last_check_numbers == called_numbers:
                return self._last_check_result

        # Convert called_numbers to a set for O(1) lookups if it's not already a set
        called_set = set(called_numbers) if not isinstance(called_numbers, set) else called_numbers

        # Clear previous winning patterns if not already won
        if not self.has_won:
            self.winning_patterns = []

        # Track if we found any winning patterns
        found_any_pattern = False

        # Check rows (horizontal lines)
        for row in range(5):
            row_complete = True
            for col in range(5):
                num = self.grid[col][row]
                # Consider FREE space (0) as always marked
                if num != 0 and num not in called_set:
                    row_complete = False
                    break

            if row_complete:
                pattern_name = f"Row {row+1}"
                # Only add if not already in the list
                if pattern_name not in self.winning_patterns:
                    self.winning_patterns.append(pattern_name)
                    print(f"Found winning pattern: {pattern_name}")

                # Set the first winning pattern as the primary one for backward compatibility
                if not self.has_won:
                    self.has_won = True
                    self.winning_pattern = pattern_name

                found_any_pattern = True

        # Check columns (vertical lines)
        for col in range(5):
            col_complete = True
            for row in range(5):
                num = self.grid[col][row]
                # Consider FREE space (0) as always marked
                if num != 0 and num not in called_set:
                    col_complete = False
                    break

            if col_complete:
                pattern_name = f"Column {col+1}"
                # Only add if not already in the list
                if pattern_name not in self.winning_patterns:
                    self.winning_patterns.append(pattern_name)
                    print(f"Found winning pattern: {pattern_name}")

                # Set the first winning pattern as the primary one for backward compatibility
                if not self.has_won:
                    self.has_won = True
                    self.winning_pattern = pattern_name

                found_any_pattern = True

        # Check main diagonal (top-left to bottom-right)
        # This is the diagonal from B1 to O5
        diagonal1_complete = True
        for i in range(5):
            num = self.grid[i][i]
            # Consider FREE space (0) as always marked
            if num != 0 and num not in called_set:
                diagonal1_complete = False
                break

        if diagonal1_complete:
            pattern_name = "Diagonal (top-left to bottom-right)"
            # Only add if not already in the list
            if pattern_name not in self.winning_patterns:
                self.winning_patterns.append(pattern_name)
                print(f"Found winning pattern: {pattern_name}")

            # Set the first winning pattern as the primary one for backward compatibility
            if not self.has_won:
                self.has_won = True
                self.winning_pattern = pattern_name

            found_any_pattern = True

        # Check other diagonal (top-right to bottom-left)
        # This is the diagonal from B5 to O1
        diagonal2_complete = True
        for i in range(5):
            num = self.grid[i][4-i]
            # Consider FREE space (0) as always marked
            if num != 0 and num not in called_set:
                diagonal2_complete = False
                break

        if diagonal2_complete:
            pattern_name = "Diagonal (top-right to bottom-left)"
            # Only add if not already in the list
            if pattern_name not in self.winning_patterns:
                self.winning_patterns.append(pattern_name)
                print(f"Found winning pattern: {pattern_name}")

            # Set the first winning pattern as the primary one for backward compatibility
            if not self.has_won:
                self.has_won = True
                self.winning_pattern = pattern_name

            found_any_pattern = True

        # Check four corners
        corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
        corners_complete = True
        for col, row in corners:
            num = self.grid[col][row]
            # Consider FREE space (0) as always marked
            if num != 0 and num not in called_set:
                corners_complete = False
                break

        if corners_complete:
            pattern_name = "Four Corners"
            # Only add if not already in the list
            if pattern_name not in self.winning_patterns:
                self.winning_patterns.append(pattern_name)
                print(f"Found winning pattern: {pattern_name}")

            # Set the first winning pattern as the primary one for backward compatibility
            if not self.has_won:
                self.has_won = True
                self.winning_pattern = pattern_name

            found_any_pattern = True

        # Check full card (blackout/coverall)
        # Only check if we haven't already found a winning pattern to save time
        # since blackout is the most expensive check
        blackout_complete = True
        for col in range(5):
            for row in range(5):
                num = self.grid[col][row]
                if num != 0 and num not in called_set:
                    blackout_complete = False
                    break
            if not blackout_complete:
                break

        if blackout_complete:
            pattern_name = "Blackout/Coverall"
            # Only add if not already in the list
            if pattern_name not in self.winning_patterns:
                self.winning_patterns.append(pattern_name)
                print(f"Found winning pattern: {pattern_name}")

            # Set the first winning pattern as the primary one for backward compatibility
            if not self.has_won:
                self.has_won = True
                self.winning_pattern = pattern_name

            found_any_pattern = True

        # Cache the result for this set of called numbers
        self._last_check_numbers = called_numbers
        self._last_check_result = found_any_pattern

        # If no patterns found, add debug logging
        if not found_any_pattern:
            print("No winning pattern found. Checking board state:")
            for row in range(5):
                row_str = ""
                for col in range(5):
                    num = self.grid[col][row]
                    if num == 0:
                        # Always mark the FREE space with an asterisk
                        row_str += "FREE*"
                    else:
                        marked = "*" if num in called_numbers else " "
                        row_str += f"{num:2d}{marked} "
                print(row_str)

            # Debug diagonal patterns specifically
            diag1 = [self.grid[i][i] for i in range(5)]
            # Consider FREE space (0) as always marked
            diag1_marked = [(num == 0 or num in called_numbers) for num in diag1]
            print(f"Diagonal (top-left to bottom-right): {diag1}")
            print(f"Marked: {diag1_marked}, Complete: {all(diag1_marked)}")

            diag2 = [self.grid[i][4-i] for i in range(5)]
            # Consider FREE space (0) as always marked
            diag2_marked = [(num == 0 or num in called_numbers) for num in diag2]
            print(f"Diagonal (top-right to bottom-left): {diag2}")
            print(f"Marked: {diag2_marked}, Complete: {all(diag2_marked)}")

        return found_any_pattern

    def reset(self):
        """Reset the card for a new game, keeping the same layout"""
        self.marked = set()
        self.marked.add(0)  # Add free space back
        self.has_won = False
        self.winning_pattern = None
        self.winning_patterns = []

    def get_column_letter(self, col_index):
        """Convert column index to BINGO letter"""
        letters = ['B', 'I', 'N', 'G', 'O']
        if 0 <= col_index < 5:
            return letters[col_index]
        return ''

    def print_card(self, called_numbers=None):
        """
        Return a string representation of the card for display/debugging

        Args:
            called_numbers: Optional list of called numbers to check against

        Returns:
            str: String representation of the card
        """
        result = " B   I   N   G   O \n"
        result += "-------------------\n"

        # Use self.marked if called_numbers is None
        numbers_to_check = called_numbers if called_numbers is not None else self.marked

        for row in range(5):
            for col in range(5):
                num = self.grid[col][row]
                if num == 0:
                    # Always mark the FREE space with an asterisk
                    result += "FREE*"
                else:
                    marked = "*" if num in numbers_to_check else " "
                    result += f"{num:2d}{marked} "
            result += "\n"

        return result

    def check_pattern_complete(self, pattern_name, called_numbers):
        """
        Check if a specific pattern is complete based on called numbers.

        Args:
            pattern_name: The name of the pattern to check (e.g., "Row 1", "Column 3", etc.)
            called_numbers: List of called numbers

        Returns:
            bool: True if the pattern is complete, False otherwise
        """
        # Check rows
        if pattern_name.startswith("Row "):
            try:
                row = int(pattern_name.split(" ")[1]) - 1
                if 0 <= row < 5:
                    for col in range(5):
                        num = self.grid[col][row]
                        # Consider FREE space (0) as always marked
                        if num != 0 and num not in called_numbers:
                            return False
                    return True
            except:
                pass

        # Check columns
        elif pattern_name.startswith("Column "):
            try:
                col = int(pattern_name.split(" ")[1]) - 1
                if 0 <= col < 5:
                    for row in range(5):
                        num = self.grid[col][row]
                        # Consider FREE space (0) as always marked
                        if num != 0 and num not in called_numbers:
                            return False
                    return True
            except:
                pass

        # Check main diagonal (top-left to bottom-right)
        elif pattern_name == "Diagonal (top-left to bottom-right)":
            for i in range(5):
                num = self.grid[i][i]
                # Consider FREE space (0) as always marked
                if num != 0 and num not in called_numbers:
                    return False
            return True

        # Check other diagonal (top-right to bottom-left)
        elif pattern_name == "Diagonal (top-right to bottom-left)":
            for i in range(5):
                num = self.grid[i][4-i]
                # Consider FREE space (0) as always marked
                if num != 0 and num not in called_numbers:
                    return False
            return True

        # Check four corners
        elif pattern_name == "Four Corners":
            corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
            for col, row in corners:
                num = self.grid[col][row]
                # Consider FREE space (0) as always marked
                if num != 0 and num not in called_numbers:
                    return False
            return True

        # Check full card (blackout/coverall)
        elif pattern_name == "Blackout/Coverall":
            for col in range(5):
                for row in range(5):
                    num = self.grid[col][row]
                    if num != 0 and num not in called_numbers:
                        return False
            return True

        # Unknown pattern
        return False

    def debug_patterns(self, called_numbers):
        """
        Debug function to check all patterns against called numbers

        Args:
            called_numbers: List of called numbers

        Returns:
            str: Debug information about patterns
        """
        result = "Pattern detection debug:\n"
        result += f"Card ID: {self.card_id}\n"
        result += f"Called numbers: {sorted(called_numbers)}\n\n"

        # Print the full board with marked numbers
        result += "Board layout with marked numbers (marked with *):\n"
        result += self.print_card(called_numbers) + "\n"

        # Check rows
        for row in range(5):
            row_numbers = [self.grid[col][row] for col in range(5)]
            # Consider FREE space (0) as always marked
            row_marked = [(num == 0 or num in called_numbers) for num in row_numbers]
            result += f"Row {row+1}: {row_numbers} - Marked: {row_marked} - Complete: {all(row_marked)}\n"

        result += "\n"

        # Check columns
        for col in range(5):
            col_numbers = [self.grid[col][row] for row in range(5)]
            # Consider FREE space (0) as always marked
            col_marked = [(num == 0 or num in called_numbers) for num in col_numbers]
            result += f"Column {col+1}: {col_numbers} - Marked: {col_marked} - Complete: {all(col_marked)}\n"

        result += "\n"

        # Check diagonals
        diag1 = [self.grid[i][i] for i in range(5)]
        # Consider FREE space (0) as always marked
        diag1_marked = [(num == 0 or num in called_numbers) for num in diag1]
        result += f"Diagonal (top-left to bottom-right): {diag1} - Marked: {diag1_marked} - Complete: {all(diag1_marked)}\n"

        # Print the diagonal positions for clarity
        result += "Positions: "
        for i in range(5):
            result += f"({i},{i}) "
        result += "\n"

        diag2 = [self.grid[i][4-i] for i in range(5)]
        # Consider FREE space (0) as always marked
        diag2_marked = [(num == 0 or num in called_numbers) for num in diag2]
        result += f"Diagonal (top-right to bottom-left): {diag2} - Marked: {diag2_marked} - Complete: {all(diag2_marked)}\n"

        # Print the diagonal positions for clarity
        result += "Positions: "
        for i in range(5):
            result += f"({i},{4-i}) "
        result += "\n"

        result += "\n"

        # Check four corners
        corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
        corner_numbers = [self.grid[col][row] for col, row in corners]
        # Consider FREE space (0) as always marked
        corner_marked = [(num == 0 or num in called_numbers) for num in corner_numbers]
        result += f"Four Corners: {corner_numbers} - Marked: {corner_marked} - Complete: {all(corner_marked)}\n"

        # Print the corner positions for clarity
        result += "Positions: "
        for col, row in corners:
            result += f"({col},{row}) "
        result += "\n"

        return result

    def get_numbers_in_card(self):
        """Return a flat list of all numbers in the card"""
        numbers = []
        for col in range(5):
            for row in range(5):
                num = self.grid[col][row]
                if num != 0:  # Skip free space
                    numbers.append(num)
        return numbers


def create_card_for_player(cartela_no):
    """
    Create a BingoCard instance for a player with the given cartela_no

    This function loads the board data from the bingo_boards.json file.
    If the specific cartela_no doesn't exist, it uses a modulo operation
    to select one of the predefined boards rather than generating a random one.

    Args:
        cartela_no: The cartella number to create a card for

    Returns:
        BingoCard: A new BingoCard instance for the player
    """
    # Try to load the board data from the JSON file
    try:
        import json
        import os

        # Path to the bingo boards file
        boards_path = os.path.join('data', 'bingo_boards.json')

        # Check if the file exists
        if os.path.exists(boards_path):
            with open(boards_path, 'r') as file:
                boards_data = json.load(file)

                # Check if the cartella number exists in the data
                cartella_key = str(cartela_no)
                if cartella_key in boards_data:
                    # Create a card with the loaded board data
                    print(f"Using predefined board for cartella {cartela_no}")
                    return BingoCard(card_id=cartela_no, board_data=boards_data[cartella_key])
                else:
                    # If the specific cartela_no doesn't exist, use modulo to select a predefined board
                    # This ensures we always use a board from the JSON file
                    available_keys = list(boards_data.keys())
                    if available_keys:
                        # Use modulo to select a board based on cartela_no
                        selected_key = available_keys[cartela_no % len(available_keys)]
                        print(f"Cartella {cartela_no} not found in boards data. Using board {selected_key} instead.")
                        return BingoCard(card_id=cartela_no, board_data=boards_data[selected_key])
    except Exception as e:
        print(f"Error loading board data for cartella {cartela_no}: {e}")

    # This should never happen if the JSON file exists and is properly formatted
    print(f"WARNING: Could not load any board from JSON for cartella {cartela_no}. Check if bingo_boards.json exists and is valid.")
    return BingoCard(card_id=cartela_no)