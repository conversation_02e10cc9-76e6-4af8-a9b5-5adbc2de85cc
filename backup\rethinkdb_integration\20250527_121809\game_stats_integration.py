"""
Game Stats Integration Module for the WOW Games application.

This module provides a clean interface between the game and the stats system,
ensuring proper recording of game events and statistics.
It integrates with both optimized_stats_loader and thread_safe_db for backward compatibility.
"""

import os
import json
import logging
import traceback
from datetime import datetime

# Configure logging
logging.basicConfig(
    filename=os.path.join('data', 'game_stats_integration.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Try to import optimized stats loader first
try:
    from optimized_stats_loader import get_optimized_stats_loader
    OPTIMIZED_LOADER_AVAILABLE = True
    optimized_loader = get_optimized_stats_loader()
    logging.info("Using optimized stats loader for integration")
except ImportError:
    OPTIMIZED_LOADER_AVAILABLE = False
    logging.warning("Optimized stats loader not available, falling back to thread-safe database")

# Import thread-safe database as fallback
try:
    import thread_safe_db
    THREAD_SAFE_DB_AVAILABLE = True
except ImportError:
    THREAD_SAFE_DB_AVAILABLE = False
    logging.error("Thread-safe database module not available")

class GameStatsIntegration:
    """
    Handles integration between the game and the stats system.
    Provides methods to record game events and ensure database is properly initialized.
    """

    @staticmethod
    def ensure_database_exists():
        """
        Ensure the stats database exists and has the required tables.
        This should be called at game startup.
        """
        try:
            if THREAD_SAFE_DB_AVAILABLE:
                thread_safe_db.ensure_database_exists()
                logging.info("Stats database initialized successfully")
                return True
            else:
                logging.error("No database module available for initialization")
                print("Error: No database module available for initialization")
                return False
        except Exception as e:
            logging.error(f"Error initializing stats database: {e}")
            print(f"Error initializing stats database: {e}")
            return False

    @staticmethod
    def record_game_started(player_count, bet_amount=50, is_demo_mode=False):
        """
        Record a game start event in the stats database.

        Args:
            player_count: Number of players in the game
            bet_amount: Bet amount per player
            is_demo_mode: Boolean indicating if the game is in demo mode

        Returns:
            bool: True if successful, False otherwise
        """
        # Skip if in demo mode
        if is_demo_mode:
            logging.info("Game started in demo mode - statistics not updated")
            return False

        try:
            # Try using optimized loader first
            if OPTIMIZED_LOADER_AVAILABLE:
                # The optimized loader doesn't have this method directly,
                # so we need to use the thread_safe_db as a fallback
                if THREAD_SAFE_DB_AVAILABLE:
                    result = thread_safe_db.record_game_started(player_count, bet_amount, is_demo_mode)
                    # Force refresh data in the optimized loader to reflect changes
                    optimized_loader.refresh_data()
                    logging.info(f"Game start recorded with optimized integration: {result}")
                    return result

            # Fallback to direct thread_safe_db if optimized loader not available
            if THREAD_SAFE_DB_AVAILABLE:
                result = thread_safe_db.record_game_started(player_count, bet_amount, is_demo_mode)
                logging.info(f"Game start recorded: {result}")
                return result

            logging.error("No stats module available for recording game start")
            return False
        except Exception as e:
            logging.error(f"Error recording game start: {e}")
            logging.error(traceback.format_exc())
            print(f"Error recording game start: {e}")
            return False

    @staticmethod
    def record_game_completed(game_data):
        """
        Record a completed game in the stats database.

        Args:
            game_data: Dictionary containing game data
                - winner_name: Name of the winner
                - winner_cartella: Cartella number of the winner
                - claim_type: Type of claim (e.g., 'Full House', 'First Line')
                - game_duration: Duration of the game in seconds
                - player_count: Number of players in the game
                - prize_amount: Prize amount for this game
                - commission_percentage: Commission percentage for this game
                - called_numbers: List of numbers called during the game
                - is_demo_mode: Boolean indicating if the game was in demo mode
                - date_time: Timestamp for the game (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        # Debug print to help diagnose issues
        print("=" * 80)
        print("RECORDING GAME COMPLETED IN GAME_STATS_INTEGRATION")
        print(f"Game data: {game_data}")
        print("=" * 80)

        # Log to file for better tracking
        logging.info("=" * 80)
        logging.info("RECORDING GAME COMPLETED IN GAME_STATS_INTEGRATION")
        logging.info(f"Game data: {game_data}")
        logging.info("=" * 80)

        # Enhanced demo mode detection
        is_demo_mode = game_data.get("is_demo_mode", False)

        # Additional check based on player count - real games have more than 2 players
        player_count = game_data.get("player_count", 0)
        if player_count > 2:
            # This is definitely a real game, not a demo
            if is_demo_mode:
                print("CRITICAL: Game has more than 2 players but is_demo_mode is True")
                print("Overriding is_demo_mode to False to ensure stats are recorded")
                is_demo_mode = False
                game_data["is_demo_mode"] = False

        # Skip if in demo mode
        if is_demo_mode:
            logging.info("Game completed in demo mode - statistics not updated")
            return False

        # Add timestamp if missing
        if not game_data.get("date_time"):
            game_data["date_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"Added missing timestamp: {game_data['date_time']}")

        try:
            # Force database connection reset for a clean transaction
            if THREAD_SAFE_DB_AVAILABLE:
                try:
                    # Ensure database exists before attempting operations
                    thread_safe_db.ensure_database_exists()
                    logging.info("Verified database schema exists")

                    # Close existing connection to start fresh
                    thread_safe_db.close_connection()
                    logging.info("Closed existing database connection to ensure fresh data")
                except Exception as close_e:
                    logging.warning(f"Could not prepare database: {close_e}")

            # Try to record the game using thread_safe_db
            if THREAD_SAFE_DB_AVAILABLE:
                logging.info("Recording game completion via thread_safe_db")
                result = thread_safe_db.record_game_completed(game_data)
                logging.info(f"Game completion recorded: {result}")

                # Refresh optimized loader to reflect the changes
                if OPTIMIZED_LOADER_AVAILABLE:
                    try:
                        optimized_loader.refresh_data()
                        logging.info("Refreshed optimized loader data after game completion")
                    except Exception as refresh_e:
                        logging.error(f"Error refreshing optimized loader: {refresh_e}")

                # Verify the data was actually saved
                if result:
                    try:
                        # Get a fresh connection for verification
                        conn = thread_safe_db.get_connection()
                        cursor = conn.cursor()

                        # Check total game count
                        cursor.execute('SELECT COUNT(*) FROM game_history')
                        count = cursor.fetchone()[0]
                        logging.info(f"Verified game_history now has {count} records")
                        print(f"VERIFICATION: Database has {count} game history records")

                        # Get the most recent game record
                        cursor.execute('SELECT id, date_time, username, players, stake, fee FROM game_history ORDER BY id DESC LIMIT 1')
                        latest = cursor.fetchone()
                        if latest:
                            logging.info(f"Latest game record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Players={latest[3]}, Stake={latest[4]}, Fee={latest[5]}")
                            print(f"VERIFICATION: Latest game record details: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Players={latest[3]}, Stake={latest[4]}, Fee={latest[5]}")
                        else:
                            logging.error("CRITICAL ERROR: No game records found after insert!")
                            print("CRITICAL ERROR: No game records found after insert!")

                            # Try to force a direct insert as a backup
                            try:
                                print("Attempting emergency direct database write...")
                                # Format current time
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                                # Insert directly into game_history as a last resort - FIXED stake amount
                                stake_amount = game_data.get('stake', 0)
                                if stake_amount == 0:
                                    stake_amount = game_data.get('bet_amount', 25)  # Updated default
                                cursor.execute('''
                                INSERT INTO game_history (
                                    date_time, username, house, stake, players, total_calls,
                                    commission_percent, fee, total_prize, details, status
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    current_time,
                                    game_data.get('winner_name', 'Unknown'),
                                    game_data.get('house', 'Main House'),
                                    stake_amount,
                                    game_data.get('player_count', 4),
                                    len(game_data.get('called_numbers', [])),
                                    game_data.get('commission_percentage', 20),
                                    game_data.get('prize_amount', 0) * (game_data.get('commission_percentage', 20) / 100),
                                    game_data.get('prize_amount', 0),
                                    json.dumps(game_data),
                                    'Won'
                                ))
                                print(f"DEBUG: Emergency insert used stake amount: {stake_amount}")
                                conn.commit()
                                emergency_id = cursor.lastrowid
                                print(f"Emergency direct insert successful, ID={emergency_id}")
                            except Exception as emergency_e:
                                print(f"Emergency direct insert failed: {emergency_e}")
                    except Exception as verify_e:
                        logging.error(f"Error verifying game record: {verify_e}")
                        print(f"Error verifying game record: {verify_e}")
                else:
                    logging.error("CRITICAL ERROR: thread_safe_db.record_game_completed returned False!")
                    print("CRITICAL ERROR: thread_safe_db.record_game_completed returned False!")

                return result
            else:
                logging.error("No database module available for recording game completion")
                return False

        except Exception as e:
            logging.error(f"Error recording game completion: {e}")
            logging.error(traceback.format_exc())
            print(f"Error recording game completion: {e}")
            return False

    @staticmethod
    def get_game_history(page=0, page_size=10):
        """
        Get game history from the database.

        Args:
            page: Page number (0-based)
            page_size: Number of records per page

        Returns:
            tuple: (list of game history records, total pages)
        """
        try:
            # Try optimized loader first for better performance
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    history, total_pages = optimized_loader.get_game_history_page(page, page_size)
                    return history, total_pages
                except Exception as e:
                    logging.warning(f"Error getting game history from optimized loader: {e}, falling back to thread_safe_db")
                    # Fall back to thread_safe_db

            # Use thread-safe database as fallback
            if THREAD_SAFE_DB_AVAILABLE:
                return thread_safe_db.get_game_history(page, page_size)

            logging.error("No database module available for getting game history")
            return [], 1
        except Exception as e:
            logging.error(f"Error getting game history: {e}")
            logging.error(traceback.format_exc())
            print(f"Error getting game history: {e}")
            return [], 1

    @staticmethod
    def get_weekly_stats():
        """
        Get weekly statistics from the database.

        Returns:
            list: List of daily statistics for the week
        """
        try:
            # Try optimized loader first for better performance
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    return optimized_loader.get_weekly_stats()
                except Exception as e:
                    logging.warning(f"Error getting weekly stats from optimized loader: {e}, falling back to thread_safe_db")
                    # Fall back to thread_safe_db

            # Use thread-safe database as fallback
            if THREAD_SAFE_DB_AVAILABLE:
                return thread_safe_db.get_weekly_stats()

            logging.error("No database module available for getting weekly stats")
            return []
        except Exception as e:
            logging.error(f"Error getting weekly stats: {e}")
            logging.error(traceback.format_exc())
            print(f"Error getting weekly stats: {e}")
            return []

    @staticmethod
    def get_summary_stats():
        """
        Get summary statistics from the database.

        Returns:
            dict: Dictionary of summary statistics
        """
        try:
            # Try optimized loader first for better performance
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    return optimized_loader.get_summary_stats()
                except Exception as e:
                    logging.warning(f"Error getting summary stats from optimized loader: {e}, falling back to thread_safe_db")
                    # Fall back to thread_safe_db

            # Use thread-safe database as fallback
            if THREAD_SAFE_DB_AVAILABLE:
                return thread_safe_db.get_summary_stats()

            logging.error("No database module available for getting summary stats")
            return {
                'total_earnings': 0,
                'daily_earnings': 0,
                'daily_games': 0,
                'wallet_balance': 0
            }
        except Exception as e:
            logging.error(f"Error getting summary stats: {e}")
            logging.error(traceback.format_exc())
            print(f"Error getting summary stats: {e}")
            return {
                'total_earnings': 0,
                'daily_earnings': 0,
                'daily_games': 0,
                'wallet_balance': 0
            }

    # Class variable to track recursive calls
    _refresh_in_progress = False

    @staticmethod
    def force_refresh_data():
        """
        Force refresh all cached data.

        Returns:
            bool: True if successful, False otherwise
        """
        print("=" * 80)
        print("FORCE REFRESH DATA CALLED IN GAME_STATS_INTEGRATION")
        print("=" * 80)

        # CRITICAL FIX: Track recursive calls to prevent infinite recursion
        if GameStatsIntegration._refresh_in_progress:
            print("WARNING: Recursive call to force_refresh_data detected, skipping to prevent infinite recursion")
            return True

        # Set the flag to indicate we're inside this method
        GameStatsIntegration._refresh_in_progress = True

        # First try to force a refresh of the thread_safe_db connection
        try:
            import thread_safe_db
            print("=" * 80)
            print("FORCING REFRESH OF ALL STATS DATA")
            print("=" * 80)

            # Close any existing connections
            thread_safe_db.close_connection()
            print("Closed existing database connections")

            # Get a fresh connection to ensure latest data
            conn = thread_safe_db.get_connection()
            print("Created new database connection")

            # Verify the connection works by running a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM game_history")
            count = cursor.fetchone()[0]
            print(f"Verified connection with game history count: {count}")
        except Exception as db_e:
            print(f"Error refreshing thread_safe_db connection: {db_e}")

        success = False

        try:
            # CRITICAL FIX: Skip calling stats_integration.force_refresh_data() to prevent circular dependencies
            # Instead, directly refresh the optimized loader and thread_safe_db

            # Try to refresh optimized loader data
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    print("Refreshing optimized loader data")
                    optimized_loader.refresh_data()
                    logging.info("Forced refresh of optimized loader data")
                    print("Forced refresh of optimized loader data")
                    success = True
                except Exception as e:
                    logging.warning(f"Error refreshing optimized loader data: {e}")
                    print(f"Error refreshing optimized loader data: {e}")
                    import traceback
                    traceback.print_exc()

            # Try to refresh thread_safe_db data
            if THREAD_SAFE_DB_AVAILABLE:
                try:
                    print("Closing and reopening database connection to ensure fresh data")
                    thread_safe_db.close_connection()
                    conn = thread_safe_db.get_connection()
                    logging.info("Forced refresh of thread_safe_db data")
                    print("Forced refresh of thread_safe_db data")
                    success = True
                except Exception as e:
                    logging.warning(f"Error refreshing thread_safe_db data: {e}")
                    print(f"Error refreshing thread_safe_db data: {e}")
                    import traceback
                    traceback.print_exc()

            # Also try to clear preloader cache if available
            try:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()
                if hasattr(preloader, 'clear'):
                    preloader.clear()
                    logging.info("Cleared stats preloader cache")
                    print("Cleared stats preloader cache")
                    success = True
            except Exception as cache_e:
                logging.warning(f"Could not clear preloader cache: {cache_e}")
                print(f"Could not clear preloader cache: {cache_e}")

            # Try to refresh stats_data_provider - without calling back to this method
            try:
                from stats_data_provider import get_stats_provider
                provider = get_stats_provider()
                if hasattr(provider, 'force_refresh'):
                    # CRITICAL FIX: Don't call provider.force_refresh() directly as it can cause recursion
                    # Instead, manually refresh the provider's data
                    if hasattr(provider, 'refresh_data'):
                        print("Directly refreshing stats_data_provider data")
                        provider.refresh_data()
                        logging.info("Directly refreshed stats_data_provider data")
                        print("Directly refreshed stats_data_provider data")
                        success = True
                    elif hasattr(provider, '_data_fresh'):
                        # Set _data_fresh to False to force re-fetch on next access
                        provider._data_fresh = False
                        print("Set stats_provider._data_fresh = False to force refresh on next access")
                        success = True
            except Exception as e:
                logging.warning(f"Could not refresh stats_data_provider: {e}")
                print(f"Could not refresh stats_data_provider: {e}")
                import traceback
                traceback.print_exc()

            # Try to post a refresh_stats event
            try:
                import pygame
                import time
                if pygame.get_init():
                    refresh_event = pygame.event.Event(pygame.USEREVENT, {
                        'stats_type': 'refresh_stats',
                        'force': True,
                        'force_reload': True,
                        'source': 'GameStatsIntegration.force_refresh_data',
                        'timestamp': time.time()
                    })
                    pygame.event.post(refresh_event)
                    logging.info("Posted refresh_stats event")
                    print("Posted refresh_stats event")
                    success = True
            except Exception as event_e:
                logging.warning(f"Could not post refresh event: {event_e}")
                print(f"Could not post refresh event: {event_e}")
                import traceback
                traceback.print_exc()

            if success:
                print("Successfully refreshed data from at least one source")
                # Clear the recursion flag before returning
                GameStatsIntegration._refresh_in_progress = False
                return True
            else:
                # If we got here, no refresh method was available
                logging.warning("No stats refresh method available")
                print("No stats refresh method available")
                # Clear the recursion flag before returning
                GameStatsIntegration._refresh_in_progress = False
                # Clear the recursion flag before returning
                GameStatsIntegration._refresh_in_progress = False
                return False
        except Exception as e:
            logging.error(f"Error forcing data refresh: {e}")
            logging.error(traceback.format_exc())
            print(f"Error forcing data refresh: {e}")
            import traceback
            traceback.print_exc()
            return False

# Ensure database is initialized at startup
GameStatsIntegration.ensure_database_exists()
