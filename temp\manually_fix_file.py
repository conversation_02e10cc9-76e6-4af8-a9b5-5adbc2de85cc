def manually_fix_file():
    # Read the entire file into memory
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # Create a backup of the original file
    with open('Board_selection_fixed.py.bak', 'w', encoding='utf-8') as backup:
        backup.writelines(lines)
    
    # Define the correct code for the problematic section
    correct_code = [
        '        # If not found in the loaded data, try to load directly from the JSON file\n',
        '        # This ensures we always get the latest data, especially for board previews\n',
        '        try:\n',
        '            import json\n',
        '            import os\n',
        '\n',
        '            # Use the same file path as used in other parts of the code\n',
        '            boards_path = os.path.join(\'data\', \'bingo_boards.json\')\n',
        '\n',
        '            if os.path.exists(boards_path):\n',
        '                try:\n',
        '                    with open(boards_path, \'r\') as file:\n',
        '                        boards_data = json.load(file)\n',
        '\n',
        '                        # Check if the board exists in the file\n',
        '                        if number_key in boards_data:\n',
        '                            # Update our cached boards data with this board\n',
        '                            self.bingo_boards[number_key] = boards_data[number_key]\n',
        '                            self._board_cache[number_key] = boards_data[number_key]\n',
        '                            return boards_data[number_key]\n',
        '\n',
        '                        # If the board doesn\'t exist in the file but is in the extended range (101-1200),\n',
        '                        # check if we need to generate extended boards\n',
        '                        if 1 <= number <= 1200 and number > 100:\n',
        '                            print(f"Board for cartella {number} not found in JSON file but is in extended range (101-1200)")\n',
        '                            # We\'ll continue to the deterministic generation below\n',
        '                except Exception as e:\n',
        '                    print(f"Error reading JSON file: {e}")\n',
        '        except Exception as e:\n',
        '            print(f"Error loading board from JSON file: {e}")\n',
        '\n'
    ]
    
    # Find the start and end of the problematic section
    start_line = -1
    end_line = -1
    
    for i, line in enumerate(lines):
        if "# If not found in the loaded data" in line:
            start_line = i
        if start_line != -1 and "Error loading board from JSON file" in line:
            end_line = i
            break
    
    if start_line != -1 and end_line != -1:
        # Replace the problematic section with the correct code
        lines[start_line:end_line+1] = correct_code
        
        # Write the corrected file
        with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
            file.writelines(lines)
        
        print(f"Manually fixed lines {start_line} to {end_line}")
        return True
    else:
        print("Could not find the problematic section")
        return False

if __name__ == "__main__":
    manually_fix_file() 