"""
Power Management Module for WOW Games

This module prevents Windows from dimming the screen, turning off the display,
or putting the system to sleep while the game is running. It ensures the app
screen stays always active and visible.
"""

import sys
import threading
import time
import pygame
from typing import Optional

class PowerManager:
    """
    Manages system power settings to keep the application always active
    """

    def __init__(self):
        self.is_active = False
        self.keep_alive_thread = None
        self.stop_event = threading.Event()
        self.original_power_state = None

        # Windows API constants
        self.ES_CONTINUOUS = 0x80000000
        self.ES_SYSTEM_REQUIRED = 0x00000001
        self.ES_DISPLAY_REQUIRED = 0x00000002
        self.ES_AWAYMODE_REQUIRED = 0x00000040

        # Initialize Windows API if available
        self.user32 = None
        self.kernel32 = None
        self._init_windows_api()

    def _init_windows_api(self):
        """Initialize Windows API libraries"""
        if sys.platform == "win32":
            try:
                import ctypes
                self.user32 = ctypes.windll.user32
                self.kernel32 = ctypes.windll.kernel32
                print("Power Management: Windows API initialized successfully")
            except Exception as e:
                print(f"Power Management: Failed to initialize Windows API: {e}")

    def prevent_screen_sleep(self):
        """
        Prevent the screen from going to sleep or dimming
        Uses Windows SetThreadExecutionState API
        """
        if not self.user32 or not self.kernel32:
            print("Power Management: Windows API not available")
            return False

        try:
            # Prevent system sleep, display sleep, and enable away mode
            execution_state = (
                self.ES_CONTINUOUS |
                self.ES_SYSTEM_REQUIRED |
                self.ES_DISPLAY_REQUIRED |
                self.ES_AWAYMODE_REQUIRED
            )

            result = self.kernel32.SetThreadExecutionState(execution_state)
            if result:
                print("Power Management: Successfully prevented screen sleep")
                return True
            else:
                print("Power Management: Failed to set execution state")
                return False

        except Exception as e:
            print(f"Power Management: Error preventing screen sleep: {e}")
            return False

    def restore_power_settings(self):
        """
        Restore normal power management settings
        """
        if not self.kernel32:
            return False

        try:
            # Reset to normal power management
            result = self.kernel32.SetThreadExecutionState(self.ES_CONTINUOUS)
            if result:
                print("Power Management: Power settings restored to normal")
                return True
            else:
                print("Power Management: Failed to restore power settings")
                return False

        except Exception as e:
            print(f"Power Management: Error restoring power settings: {e}")
            return False

    def keep_window_active(self):
        """
        Keep the pygame window active and in foreground
        """
        if not self.user32:
            return False

        try:
            # Get pygame window handle
            wm_info = pygame.display.get_wm_info()
            if wm_info and "window" in wm_info:
                hwnd = wm_info["window"]
                if hwnd:
                    # Ensure window stays active and visible
                    self.user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                    self.user32.SetForegroundWindow(hwnd)
                    self.user32.BringWindowToTop(hwnd)
                    self.user32.SetActiveWindow(hwnd)
                    return True

        except Exception as e:
            print(f"Power Management: Error keeping window active: {e}")

        return False

    def simulate_user_activity(self):
        """
        Simulate minimal user activity to prevent screen saver
        """
        if not self.user32:
            return False

        try:
            # Move mouse cursor by 1 pixel and back (invisible to user)
            import ctypes
            from ctypes import wintypes

            # Get current cursor position
            point = wintypes.POINT()
            self.user32.GetCursorPos(ctypes.byref(point))

            # Move cursor by 1 pixel
            self.user32.SetCursorPos(point.x + 1, point.y)
            time.sleep(0.01)  # 10ms delay

            # Move cursor back to original position
            self.user32.SetCursorPos(point.x, point.y)

            return True

        except Exception as e:
            print(f"Power Management: Error simulating user activity: {e}")
            return False

    def _keep_alive_worker(self):
        """
        Background thread worker to maintain system activity
        """
        print("Power Management: Keep-alive thread started")

        while not self.stop_event.is_set():
            try:
                # Prevent screen sleep every 30 seconds
                self.prevent_screen_sleep()

                # Keep window active every 60 seconds
                if not self.stop_event.wait(60):
                    self.keep_window_active()

                # Simulate user activity every 5 minutes to prevent screensaver
                if not self.stop_event.wait(240):  # 4 more minutes = 5 total
                    self.simulate_user_activity()

            except Exception as e:
                print(f"Power Management: Keep-alive worker error: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(10)

        print("Power Management: Keep-alive thread stopped")

    def start(self):
        """
        Start power management to keep screen always active
        """
        if self.is_active:
            print("Power Management: Already active")
            return True

        print("Power Management: Starting...")

        # Initial setup
        if not self.prevent_screen_sleep():
            print("Power Management: Warning - Could not prevent screen sleep")

        # Start background keep-alive thread
        self.stop_event.clear()
        self.keep_alive_thread = threading.Thread(
            target=self._keep_alive_worker,
            daemon=True,
            name="PowerManagerKeepAlive"
        )
        self.keep_alive_thread.start()

        self.is_active = True
        print("Power Management: Successfully started - screen will stay active")
        return True

    def stop(self):
        """
        Stop power management and restore normal settings
        """
        if not self.is_active:
            return True

        print("Power Management: Stopping...")

        # Stop background thread
        self.stop_event.set()
        if self.keep_alive_thread and self.keep_alive_thread.is_alive():
            self.keep_alive_thread.join(timeout=5)

        # Restore normal power settings
        self.restore_power_settings()

        self.is_active = False
        print("Power Management: Stopped - normal power management restored")
        return True

    def is_running(self):
        """
        Check if power management is currently active

        Returns:
            bool: True if power management is active
        """
        return self.is_active


# Global power manager instance
_power_manager = None

def get_power_manager():
    """
    Get the global power manager instance

    Returns:
        PowerManager: Global power manager instance
    """
    global _power_manager
    if _power_manager is None:
        _power_manager = PowerManager()
    return _power_manager

def start_power_management():
    """
    Start power management to keep screen always active

    Returns:
        bool: True if started successfully
    """
    return get_power_manager().start()

def stop_power_management():
    """
    Stop power management and restore normal settings

    Returns:
        bool: True if stopped successfully
    """
    return get_power_manager().stop()

def is_power_management_active():
    """
    Check if power management is currently active

    Returns:
        bool: True if power management is active
    """
    return get_power_manager().is_running()
