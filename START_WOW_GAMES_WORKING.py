"""
Working WOW Games Launcher

This launcher handles all the common issues and provides a reliable startup.
"""

import os
import sys
import time
import subprocess
import threading
import signal
import webbrowser
from datetime import datetime

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def check_rethinkdb_status():
    """Check if RethinkDB is running."""
    try:
        result = subprocess.run(
            [sys.executable, "setup_rethinkdb.py", "--check"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return "RethinkDB is running" in result.stdout
    except:
        return False

def start_rethinkdb_if_needed():
    """Start RethinkDB if it's not running."""
    if check_rethinkdb_status():
        log("✅ RethinkDB is already running")
        return True
    
    log("🚀 Starting RethinkDB...")
    
    try:
        # Start RethinkDB in background
        process = subprocess.Popen([
            'rethinkdb',
            '--directory', 'data\\rethinkdb_data',
            '--bind', '127.0.0.1',
            '--http-port', '8081',
            '--driver-port', '28015'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for it to start
        for i in range(10):
            time.sleep(2)
            if check_rethinkdb_status():
                log("✅ RethinkDB started successfully!")
                return True
            log(f"⏳ Waiting for RethinkDB... ({i+1}/10)")
        
        log("❌ RethinkDB failed to start within timeout", "ERROR")
        return False
        
    except Exception as e:
        log(f"❌ Error starting RethinkDB: {e}", "ERROR")
        return False

def start_web_dashboard():
    """Start the web dashboard."""
    log("🌐 Starting Web Dashboard...")
    
    try:
        process = subprocess.Popen([
            sys.executable, "rethink_dashboard_fixed.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(3)
        log("✅ Web dashboard started at: http://localhost:5000")
        return process
        
    except Exception as e:
        log(f"❌ Error starting web dashboard: {e}", "ERROR")
        return None

def start_game():
    """Start the main game."""
    log("🎮 Starting WOW Games...")
    
    try:
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(2)
        log("✅ WOW Games started successfully!")
        return process
        
    except Exception as e:
        log(f"❌ Error starting game: {e}", "ERROR")
        return None

def open_browser():
    """Open the web dashboard in browser."""
    try:
        time.sleep(2)
        webbrowser.open("http://localhost:5000")
        log("🌐 Opened web dashboard in browser")
    except Exception as e:
        log(f"⚠️ Could not open browser: {e}", "WARNING")

def main():
    """Main launcher function."""
    log("🚀 WOW GAMES WORKING LAUNCHER")
    log("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        log("❌ Error: main.py not found. Please run from game directory.", "ERROR")
        return False
    
    processes = []
    
    try:
        # Step 1: Ensure RethinkDB is running
        if not start_rethinkdb_if_needed():
            log("❌ Could not start RethinkDB. Trying database fix...", "ERROR")
            
            # Try to fix the database
            try:
                log("🔧 Running database fix...")
                result = subprocess.run([
                    sys.executable, "fix_rethinkdb_database.py"
                ], input="n\n", text=True, timeout=120)
                
                if result.returncode == 0:
                    log("✅ Database fix completed")
                    time.sleep(5)
                    
                    if not check_rethinkdb_status():
                        log("❌ RethinkDB still not running after fix", "ERROR")
                        return False
                else:
                    log("❌ Database fix failed", "ERROR")
                    return False
                    
            except Exception as e:
                log(f"❌ Error running database fix: {e}", "ERROR")
                return False
        
        # Step 2: Start web dashboard
        dashboard_process = start_web_dashboard()
        if dashboard_process:
            processes.append(dashboard_process)
        
        # Step 3: Start the game
        game_process = start_game()
        if game_process:
            processes.append(game_process)
        
        # Step 4: Open browser
        threading.Thread(target=open_browser, daemon=True).start()
        
        # Success summary
        log("=" * 60)
        log("🎉 WOW GAMES STARTUP COMPLETED!")
        log("=" * 60)
        log("📋 SERVICES RUNNING:")
        log("   🗄️  RethinkDB Server: http://localhost:8081")
        log("   🌐 Web Dashboard: http://localhost:5000")
        log("   🎮 WOW Games: Main application window")
        log("")
        log("Press Ctrl+C to shutdown all services...")
        log("=" * 60)
        
        # Keep running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            log("\n🛑 Shutting down...")
        
        return True
        
    except Exception as e:
        log(f"❌ Startup failed: {e}", "ERROR")
        return False
    
    finally:
        # Cleanup
        log("🧹 Cleaning up processes...")
        for process in processes:
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
            except:
                pass
        log("✅ Cleanup completed")

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Startup failed.")
        print("\n💡 Troubleshooting options:")
        print("   1. Run: python fix_rethinkdb_database.py")
        print("   2. Check if ports 28015, 8081, 5000 are available")
        print("   3. Restart your computer and try again")
        input("\nPress Enter to exit...")
    else:
        print("\n✅ Launcher completed successfully!")
        input("\nPress Enter to exit...")
