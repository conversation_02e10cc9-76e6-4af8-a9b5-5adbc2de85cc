"""
Monitor Stats Page Performance

This script monitors the performance of the stats page by measuring
the time taken for various operations.
"""

import os
import time
import logging
import threading
import json
from datetime import datetime

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'stats_performance.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class StatsPerformanceMonitor:
    """Monitor the performance of stats page operations."""

    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics = {}
        self.lock = threading.RLock()

        # Create metrics directory if it doesn't exist
        self.metrics_dir = os.path.join('data', 'metrics')
        os.makedirs(self.metrics_dir, exist_ok=True)

        # Load previous metrics if available
        self._load_metrics()

        logging.info("Stats performance monitor initialized")

    def _load_metrics(self):
        """Load metrics from disk."""
        try:
            metrics_file = os.path.join(self.metrics_dir, 'stats_metrics.json')
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    loaded_metrics = json.load(f)

                # Validate the loaded metrics structure
                validated_metrics = {}
                for operation, metrics in loaded_metrics.items():
                    if isinstance(metrics, dict) and 'count' in metrics:
                        validated_metrics[operation] = metrics
                    else:
                        logging.warning(f"Invalid metrics structure for operation '{operation}', skipping")

                self.metrics = validated_metrics
                logging.info(f"Loaded {len(validated_metrics)} valid metrics from {metrics_file}")
        except Exception as e:
            logging.error(f"Error loading metrics: {e}")
            self.metrics = {}

    def _save_metrics(self):
        """Save metrics to disk."""
        try:
            metrics_file = os.path.join(self.metrics_dir, 'stats_metrics.json')
            with open(metrics_file, 'w') as f:
                json.dump(self.metrics, f, indent=2)
            logging.info(f"Saved metrics to {metrics_file}")
        except Exception as e:
            logging.error(f"Error saving metrics: {e}")

    def start_timer(self, operation):
        """
        Start a timer for an operation.

        Args:
            operation: Name of the operation being timed

        Returns:
            dict: Timer context with start time
        """
        return {
            'operation': operation,
            'start_time': time.time()
        }

    def stop_timer(self, timer_context):
        """
        Stop a timer and record the elapsed time.

        Args:
            timer_context: Timer context returned by start_timer

        Returns:
            float: Elapsed time in seconds
        """
        operation = timer_context['operation']
        start_time = timer_context['start_time']
        elapsed_time = time.time() - start_time

        with self.lock:
            if operation not in self.metrics:
                self.metrics[operation] = {
                    'count': 0,
                    'total_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'last_time': 0,
                    'last_timestamp': ''
                }

            self.metrics[operation]['count'] += 1
            self.metrics[operation]['total_time'] += elapsed_time
            self.metrics[operation]['min_time'] = min(self.metrics[operation]['min_time'], elapsed_time)
            self.metrics[operation]['max_time'] = max(self.metrics[operation]['max_time'], elapsed_time)
            self.metrics[operation]['last_time'] = elapsed_time
            self.metrics[operation]['last_timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Calculate average
            avg_time = self.metrics[operation]['total_time'] / self.metrics[operation]['count']
            self.metrics[operation]['avg_time'] = avg_time

        # Log the timing
        logging.info(f"{operation}: {elapsed_time:.4f}s")

        # Save metrics periodically (every 10 operations)
        try:
            total_count = sum(m['count'] for m in self.metrics.values() if isinstance(m, dict) and 'count' in m)
            if total_count % 10 == 0:
                self._save_metrics()
        except Exception as e:
            logging.error(f"Error calculating total count for periodic save: {e}")
            # Try to save anyway
            self._save_metrics()

        return elapsed_time

    def get_metrics(self):
        """
        Get all recorded metrics.

        Returns:
            dict: All metrics
        """
        with self.lock:
            return self.metrics.copy()

    def get_operation_metrics(self, operation):
        """
        Get metrics for a specific operation.

        Args:
            operation: Name of the operation

        Returns:
            dict: Metrics for the operation
        """
        with self.lock:
            return self.metrics.get(operation, {}).copy()

    def print_metrics(self):
        """Print all metrics to the console."""
        with self.lock:
            print("\nStats Page Performance Metrics:")
            print("=" * 80)
            print(f"{'Operation':<30} {'Count':<8} {'Avg Time':<12} {'Min Time':<12} {'Max Time':<12} {'Last Time':<12}")
            print("-" * 80)

            for operation, metrics in sorted(self.metrics.items()):
                if isinstance(metrics, dict):
                    count = metrics.get('count', 0)
                    avg_time = metrics.get('avg_time', 0)
                    min_time = metrics.get('min_time', 0)
                    max_time = metrics.get('max_time', 0)
                    last_time = metrics.get('last_time', 0)

                    print(f"{operation:<30} {count:<8} {avg_time:.4f}s      {min_time:.4f}s      {max_time:.4f}s      {last_time:.4f}s")
                else:
                    print(f"{operation:<30} {'INVALID':<8} {'N/A':<12} {'N/A':<12} {'N/A':<12} {'N/A':<12}")

            print("=" * 80)

# Singleton instance
_performance_monitor = None

def get_performance_monitor():
    """
    Get the singleton performance monitor instance.

    Returns:
        StatsPerformanceMonitor: The performance monitor instance
    """
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = StatsPerformanceMonitor()
    return _performance_monitor

# Decorator for timing functions
def time_operation(operation_name=None):
    """
    Decorator to time a function.

    Args:
        operation_name: Name of the operation (defaults to function name)

    Returns:
        function: Decorated function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get operation name
            op_name = operation_name or func.__name__

            # Get performance monitor
            monitor = get_performance_monitor()

            # Start timer
            timer = monitor.start_timer(op_name)

            try:
                # Call the function
                result = func(*args, **kwargs)
                return result
            finally:
                # Stop timer
                monitor.stop_timer(timer)

        return wrapper
    return decorator

# Example usage
if __name__ == "__main__":
    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)

    # Get performance monitor
    monitor = get_performance_monitor()

    # Import required modules
    try:
        from stats_db import get_stats_db_manager
        from stats_integration import get_stats_summary, get_game_history

        # Test weekly stats performance
        print("Testing weekly stats performance...")
        timer = monitor.start_timer("get_weekly_stats")
        stats_db = get_stats_db_manager()
        weekly_stats = stats_db.get_weekly_stats()
        elapsed = monitor.stop_timer(timer)
        print(f"Weekly stats loaded in {elapsed:.4f} seconds")

        # Test stats summary performance
        print("\nTesting stats summary performance...")
        timer = monitor.start_timer("get_stats_summary")
        summary = get_stats_summary()
        elapsed = monitor.stop_timer(timer)
        print(f"Stats summary loaded in {elapsed:.4f} seconds")

        # Test game history performance
        print("\nTesting game history performance...")
        timer = monitor.start_timer("get_game_history")
        history = get_game_history(limit=10, offset=0)
        elapsed = monitor.stop_timer(timer)
        print(f"Game history loaded in {elapsed:.4f} seconds")

        # Print all metrics
        monitor.print_metrics()

    except Exception as e:
        print(f"Error testing performance: {e}")
        logging.error(f"Error testing performance: {e}")
