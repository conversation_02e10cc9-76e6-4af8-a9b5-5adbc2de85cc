"""
Hybrid Database Manager for the WOW Games application.

This module provides a unified interface for database operations that works with
both local SQLite and remote RethinkDB while maintaining offline sync capabilities.
"""

import os
import json
import logging
import threading
from datetime import datetime, timedelta

# Import local and remote database managers
from stats_db import get_stats_db_manager
from rethink_db import get_rethink_db_manager
from sync_manager import get_sync_manager

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'db_hybrid.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class HybridDBManager:
    """
    Unified database manager that combines local SQLite and remote RethinkDB
    with offline-to-online synchronization capabilities.
    """

    def __init__(self):
        """Initialize the hybrid database manager."""
        self.local_db = get_stats_db_manager()
        self.remote_db = None
        self.sync_manager = None
        self.online_mode = False

        # Try to initialize remote DB and sync manager
        try:
            self.remote_db = get_rethink_db_manager()
            self.sync_manager = get_sync_manager()
            
            # Check if we're connected to RethinkDB
            if self.remote_db.is_connected():
                self.online_mode = True
                logging.info("Hybrid DB initialized in online mode")
            else:
                logging.info("Hybrid DB initialized in offline mode (RethinkDB not available)")
        except Exception as e:
            logging.error(f"Error initializing remote DB: {e}")
            logging.info("Hybrid DB initialized in offline mode (error initializing RethinkDB)")

    def is_online(self):
        """
        Check if we're in online mode (connected to RethinkDB).
        
        Returns:
            bool: True if online, False if offline
        """
        if not self.remote_db:
            return False
            
        # Update online status
        self.online_mode = self.remote_db.is_connected()
        return self.online_mode

    def try_connect_online(self):
        """
        Try to connect to RethinkDB and switch to online mode.
        
        Returns:
            bool: True if successfully connected, False otherwise
        """
        if not self.remote_db:
            try:
                self.remote_db = get_rethink_db_manager()
            except Exception as e:
                logging.error(f"Error initializing remote DB: {e}")
                return False
                
        # Try to connect
        if self.remote_db.connect():
            self.online_mode = True
            logging.info("Switched to online mode")
            
            # Initialize sync manager if needed
            if not self.sync_manager:
                try:
                    self.sync_manager = get_sync_manager()
                except Exception as e:
                    logging.error(f"Error initializing sync manager: {e}")
            
            return True
        else:
            logging.warning("Failed to connect to RethinkDB")
            return False

    def force_offline_mode(self):
        """Force the database to operate in offline mode."""
        self.online_mode = False
        logging.info("Forced offline mode")

    def force_online_mode(self):
        """
        Force the database to operate in online mode.
        
        Returns:
            bool: True if successfully switched to online mode, False otherwise
        """
        return self.try_connect_online()

    # ========== Data Access Methods ==========
    
    # These methods will use either local or remote database based on the current mode
    
    def get_daily_stats(self, date_str=None):
        """
        Get daily statistics for a specific date.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            
        Returns:
            dict: Daily statistics
        """
        # Always use local DB for reads to ensure data is available offline
        return self.local_db.get_daily_stats(date_str)

    def update_daily_stats(self, date_str=None, games_played=0, earnings=0, winners=0, total_players=0):
        """
        Update daily statistics for a specific date.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            games_played: Number of games played to add
            earnings: Amount of earnings to add
            winners: Number of winners to add
            total_players: Number of total players to add
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Always update local DB
        result = self.local_db.update_daily_stats(
            date_str, games_played, earnings, winners, total_players
        )
        
        # If online, queue the change for sync
        if self.is_online() and self.sync_manager:
            # Get the updated record
            updated_stats = self.local_db.get_daily_stats(date_str)
            
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'UPDATE', 'daily_stats', updated_stats
            )
            
        return result

    def get_weekly_stats(self, end_date=None):
        """
        Get weekly statistics for the 7 days ending on the specified date.
        
        Args:
            end_date: End date string in format 'YYYY-MM-DD', defaults to today
            
        Returns:
            list: List of daily statistics for the week
        """
        # Always use local DB for reads
        return self.local_db.get_weekly_stats(end_date)

    def add_game_to_history(self, username, house, stake, players, total_calls,
                            commission_percent, fee, total_prize, details, status="completed"):
        """
        Add a game to the history.
        
        Args:
            username: Username of the player
            house: House pattern that won
            stake: Stake amount
            players: Number of players
            total_calls: Total number of calls
            commission_percent: Commission percentage
            fee: Fee amount
            total_prize: Total prize amount
            details: Game details as JSON string
            status: Game status
            
        Returns:
            int: ID of the new game history entry
        """
        # Add to local DB
        game_id = self.local_db.add_game_to_history(
            username, house, stake, players, total_calls,
            commission_percent, fee, total_prize, details, status
        )
        
        # If online, queue the change for sync
        if self.is_online() and self.sync_manager and game_id:
            # Get the new record
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM game_history WHERE id = ?', (game_id,))
                columns = [col[0] for col in cursor.description]
                game_data = dict(zip(columns, cursor.fetchone()))
            
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'INSERT', 'game_history', game_data
            )
            
        return game_id

    def get_game_history(self, page=0, page_size=10):
        """
        Get game history with pagination.
        
        Args:
            page: Page number (0-based)
            page_size: Number of items per page
            
        Returns:
            dict: Dictionary with total_games, total_pages, and games
        """
        # Always use local DB for reads
        return self.local_db.get_game_history(page, page_size)

    def add_wallet_transaction(self, amount, transaction_type, description):
        """
        Add a wallet transaction.
        
        Args:
            amount: Transaction amount
            transaction_type: Type of transaction
            description: Transaction description
            
        Returns:
            int: ID of the new transaction
        """
        # Add to local DB
        transaction_id = self.local_db.add_wallet_transaction(
            amount, transaction_type, description
        )
        
        # If online, queue the change for sync
        if self.is_online() and self.sync_manager and transaction_id:
            # Get the new record
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM wallet_transactions WHERE id = ?', (transaction_id,))
                columns = [col[0] for col in cursor.description]
                transaction_data = dict(zip(columns, cursor.fetchone()))
            
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'INSERT', 'wallet_transactions', transaction_data
            )
            
        return transaction_id

    def get_wallet_balance(self):
        """
        Get the current wallet balance.
        
        Returns:
            float: Current wallet balance
        """
        # Always use local DB for reads
        return self.local_db.get_wallet_balance()

    def get_wallet_transactions(self, limit=50, offset=0):
        """
        Get wallet transactions with pagination.
        
        Args:
            limit: Maximum number of transactions to return
            offset: Offset for pagination
            
        Returns:
            list: List of wallet transactions
        """
        # Always use local DB for reads
        return self.local_db.get_wallet_transactions(limit, offset)

    def get_wallet_summary(self):
        """
        Get a summary of wallet transactions.
        
        Returns:
            dict: Wallet summary
        """
        # Always use local DB for reads
        return self.local_db.get_wallet_summary()

    def get_total_earnings(self):
        """
        Get total earnings from all games.
        
        Returns:
            float: Total earnings
        """
        # Always use local DB for reads
        return self.local_db.get_total_earnings()

    def get_daily_games_played(self, date_str=None):
        """
        Get the number of games played on a specific date.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            
        Returns:
            int: Number of games played
        """
        # Always use local DB for reads
        return self.local_db.get_daily_games_played(date_str)

    def get_daily_earnings(self, date_str=None):
        """
        Get earnings for a specific date.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            
        Returns:
            float: Daily earnings
        """
        # Always use local DB for reads
        return self.local_db.get_daily_earnings(date_str)

    def sync_stats(self):
        """
        Sync statistics manually.
        
        Returns:
            bool: True if successful, False otherwise
        """
        # First do the local sync
        local_result = self.local_db.sync_stats()
        
        # If online, trigger a remote sync
        if self.is_online() and self.sync_manager:
            try:
                # Create a new thread to avoid blocking
                def sync_thread():
                    try:
                        # Force a sync of all tables
                        self.sync_manager._sync_all_tables()
                    except Exception as e:
                        logging.error(f"Error in manual sync: {e}")
                
                thread = threading.Thread(target=sync_thread)
                thread.daemon = True
                thread.start()
                
                return local_result
            except Exception as e:
                logging.error(f"Error starting sync thread: {e}")
                return local_result
        
        return local_result

    # Admin user methods
    
    def get_admin_users(self):
        """
        Get all admin users.
        
        Returns:
            list: List of admin users
        """
        # Always use local DB for reads
        return self.local_db.get_admin_users()

    def add_admin_user(self, username, password, access_level=1):
        """
        Add an admin user.
        
        Args:
            username: Username
            password: Password
            access_level: Access level
            
        Returns:
            int: ID of the new admin user
        """
        # Add to local DB
        user_id = self.local_db.add_admin_user(username, password, access_level)
        
        # If online, queue the change for sync
        if self.is_online() and self.sync_manager and user_id:
            # Get the new record
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_users WHERE id = ?', (user_id,))
                columns = [col[0] for col in cursor.description]
                user_data = dict(zip(columns, cursor.fetchone()))
            
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'INSERT', 'admin_users', user_data
            )
            
        return user_id

    def authenticate_admin_user(self, username, password):
        """
        Authenticate an admin user.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            dict: User data if authenticated, None otherwise
        """
        # Always use local DB for authentication
        return self.local_db.authenticate_admin_user(username, password)

    def delete_admin_user(self, user_id):
        """
        Delete an admin user.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        # First get the user data for sync
        user_data = None
        if self.is_online() and self.sync_manager:
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_users WHERE id = ?', (user_id,))
                columns = [col[0] for col in cursor.description]
                result = cursor.fetchone()
                if result:
                    user_data = dict(zip(columns, result))
        
        # Delete from local DB
        result = self.local_db.delete_admin_user(user_id)
        
        # If online and we have the user data, queue the change for sync
        if self.is_online() and self.sync_manager and user_data:
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'DELETE', 'admin_users', user_data
            )
            
        return result

    def update_admin_user(self, user_id, username=None, password=None, access_level=None):
        """
        Update an admin user.
        
        Args:
            user_id: User ID
            username: New username
            password: New password
            access_level: New access level
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Update in local DB
        result = self.local_db.update_admin_user(user_id, username, password, access_level)
        
        # If online, queue the change for sync
        if self.is_online() and self.sync_manager and result:
            # Get the updated record
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM admin_users WHERE id = ?', (user_id,))
                columns = [col[0] for col in cursor.description]
                user_data = dict(zip(columns, cursor.fetchone()))
            
            # Queue for sync
            self.sync_manager.queue_sync_operation(
                'UPDATE', 'admin_users', user_data
            )
            
        return result

    def get_admin_user(self, user_id):
        """
        Get an admin user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            dict: User data
        """
        # Always use local DB for reads
        return self.local_db.get_admin_user(user_id)

    # ========== Realtime Subscription Methods ==========
    
    def subscribe_to_changes(self, table, callback, filter_func=None):
        """
        Subscribe to real-time changes in a table.
        
        Args:
            table: Table name
            callback: Callback function
            filter_func: Optional filter function
            
        Returns:
            str: Subscription ID or None if not in online mode
        """
        if not self.is_online() or not self.remote_db:
            logging.warning("Cannot subscribe to changes in offline mode")
            return None
            
        try:
            return self.remote_db.subscribe_to_changes(table, callback, filter_func)
        except Exception as e:
            logging.error(f"Error subscribing to changes: {e}")
            return None

    def unsubscribe_from_changes(self, subscription_id):
        """
        Unsubscribe from real-time changes.
        
        Args:
            subscription_id: Subscription ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_online() or not self.remote_db:
            logging.warning("Cannot unsubscribe from changes in offline mode")
            return False
            
        try:
            return self.remote_db.unsubscribe_from_changes(subscription_id)
        except Exception as e:
            logging.error(f"Error unsubscribing from changes: {e}")
            return False

# Singleton instance
_hybrid_db_manager = None

def get_hybrid_db_manager():
    """
    Get the singleton instance of HybridDBManager.
    
    Returns:
        HybridDBManager: Hybrid database manager instance
    """
    global _hybrid_db_manager
    
    if _hybrid_db_manager is None:
        _hybrid_db_manager = HybridDBManager()
        
    return _hybrid_db_manager