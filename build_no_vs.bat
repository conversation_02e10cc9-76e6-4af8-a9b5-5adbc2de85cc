@echo off
REM WOW Bingo Game - Build Without Visual Studio
REM ============================================
REM
REM This script builds your game without requiring Visual Studio Build Tools
REM by using PyInstaller and other alternatives that don't need C++ compilation.

echo.
echo ================================================================================
echo WOW Bingo Game - Build Without Visual Studio Build Tools
echo ================================================================================
echo.
echo This script uses PyInstaller which does NOT require Visual Studio!
echo PyInstaller packages Python bytecode instead of compiling to native code.
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting Visual Studio-free build...
echo.

REM Run the no-VS build script
python build_no_vs.py --verbose

if exist "dist\WOW_Bingo_Game\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo SUCCESS: Build completed WITHOUT Visual Studio Build Tools!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game\WOW_Bingo_Game.exe
    echo.
    echo ✅ NO Visual Studio Build Tools required!
    echo ✅ Works on any Windows PC
    echo ✅ Includes all game assets and dependencies
    echo.
    echo To distribute your game:
    echo 1. Copy the entire 'dist\WOW_Bingo_Game' folder
    echo 2. Users can run WOW_Bingo_Game.exe directly
    echo 3. No Python installation needed on target PCs
    echo.
    pause
    exit /b 0
)

if exist "dist\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo SUCCESS: Single-file build completed WITHOUT Visual Studio!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game.exe
    echo.
    echo ✅ NO Visual Studio Build Tools required!
    echo ✅ Single file distribution
    echo ✅ Works on any Windows PC
    echo.
    pause
    exit /b 0
)

REM Build failed
echo.
echo ================================================================================
echo BUILD FAILED
echo ================================================================================
echo.
echo The Visual Studio-free build did not complete successfully.
echo.
echo This could be due to:
echo 1. Missing Python packages - try: pip install -r requirements.txt
echo 2. Insufficient disk space - need at least 2GB free
echo 3. Antivirus blocking the build process
echo.
echo Alternative solutions:
echo 1. Try the original PyInstaller: python build_executable.py
echo 2. Use an online build service
echo 3. Try on a different Windows PC
echo.
pause
exit /b 1
