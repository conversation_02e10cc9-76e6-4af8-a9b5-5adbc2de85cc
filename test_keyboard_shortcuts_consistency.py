"""
Comprehensive Test for Keyboard Shortcuts Consistency

This test verifies that F key and Escape key functionality works consistently
across both the main game page and board selection page, ensuring identical
behavior and proper integration with the global screen mode manager.
"""

import pygame
import sys
import os
import time

def test_screen_mode_manager_integration():
    """Test that both pages use the same screen mode manager"""
    print("=" * 70)
    print("TESTING SCREEN MODE MANAGER INTEGRATION")
    print("=" * 70)
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Screen Mode Manager Integration Test")
    
    # Test main game integration
    print("\n1. Testing main game screen mode manager integration...")
    from screen_mode_manager import get_screen_mode_manager
    main_game_manager = get_screen_mode_manager()
    main_game_manager.set_screen_reference(screen)
    
    # Test board selection integration
    print("2. Testing board selection screen mode manager integration...")
    board_selection_manager = get_screen_mode_manager()
    board_selection_manager.set_screen_reference(screen)
    
    # Verify they are the same instance (singleton pattern)
    print(f"   Main game manager ID: {id(main_game_manager)}")
    print(f"   Board selection manager ID: {id(board_selection_manager)}")
    assert main_game_manager is board_selection_manager, "Should be the same singleton instance"
    print("   ✓ Both pages use the same screen mode manager instance")
    
    # Test settings synchronization
    print("\n3. Testing settings synchronization...")
    main_game_manager.settings_manager.set_screen_mode(True)
    board_selection_setting = board_selection_manager.get_current_screen_mode()
    
    print(f"   Setting from main game manager: True")
    print(f"   Setting from board selection manager: {board_selection_setting}")
    assert board_selection_setting == True, "Settings should be synchronized"
    print("   ✓ Settings are synchronized between pages")
    
    pygame.quit()
    return True

def test_f_key_consistency():
    """Test F key behavior consistency between pages"""
    print("\n" + "=" * 70)
    print("TESTING F KEY CONSISTENCY")
    print("=" * 70)
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("F Key Consistency Test")
    
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print("\n1. Testing F key behavior...")
    print(f"   Initial mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    
    # Test F key toggle (simulating both main game and board selection behavior)
    print("   Testing F key toggle to fullscreen...")
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    mode_after_f = screen_mode_manager.is_fullscreen()
    print(f"   After F key: {'fullscreen' if mode_after_f else 'windowed'}")
    assert mode_after_f, "F key should toggle to fullscreen"
    
    # Test F key toggle back
    print("   Testing F key toggle back to windowed...")
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    mode_after_second_f = screen_mode_manager.is_fullscreen()
    print(f"   After second F key: {'fullscreen' if mode_after_second_f else 'windowed'}")
    assert not mode_after_second_f, "F key should toggle back to windowed"
    
    print("   ✓ F key behavior is consistent")
    
    pygame.quit()
    return True

def test_escape_key_consistency():
    """Test Escape key behavior consistency between pages"""
    print("\n" + "=" * 70)
    print("TESTING ESCAPE KEY CONSISTENCY")
    print("=" * 70)
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Escape Key Consistency Test")
    
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    print("\n1. Testing Escape key behavior...")
    
    # Test Escape key in windowed mode (should do nothing)
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print(f"   Initial mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    screen, mode_changed = screen_mode_manager.handle_escape_key(screen)
    print(f"   Escape in windowed mode changed: {mode_changed}")
    assert not mode_changed, "Escape should not change mode when in windowed"
    
    # Test Escape key in fullscreen mode (should exit to windowed)
    screen_mode_manager.settings_manager.set_screen_mode(True)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print(f"   Switched to: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    screen, mode_changed = screen_mode_manager.handle_escape_key(screen)
    print(f"   Escape in fullscreen mode changed: {mode_changed}")
    print(f"   Final mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    assert mode_changed, "Escape should change mode when in fullscreen"
    assert not screen_mode_manager.is_fullscreen(), "Should exit to windowed mode"
    
    print("   ✓ Escape key behavior is consistent")
    
    pygame.quit()
    return True

def test_priority_handling_consistency():
    """Test that priority handling works consistently"""
    print("\n" + "=" * 70)
    print("TESTING PRIORITY HANDLING CONSISTENCY")
    print("=" * 70)
    
    print("\n1. Testing F key priority logic...")
    
    # Test the priority logic used in main.py
    def should_handle_f_key(input_active, bet_input_active, reason_input_active):
        return not (input_active or bet_input_active or reason_input_active)
    
    # Test cases
    test_cases = [
        (False, False, False, True, "No inputs active"),
        (True, False, False, False, "Input field active"),
        (False, True, False, False, "Bet input active"),
        (False, False, True, False, "Reason input active"),
        (True, True, True, False, "All inputs active"),
    ]
    
    for input_active, bet_input_active, reason_input_active, expected, description in test_cases:
        result = should_handle_f_key(input_active, bet_input_active, reason_input_active)
        print(f"   {description}: {result} (expected: {expected})")
        assert result == expected, f"Priority logic failed for: {description}"
    
    print("   ✓ Priority handling logic is consistent")
    
    return True

def test_fullscreen_preference():
    """Test that fullscreen is the preferred/primary mode"""
    print("\n" + "=" * 70)
    print("TESTING FULLSCREEN AS PRIMARY MODE")
    print("=" * 70)
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Fullscreen Preference Test")
    
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    print("\n1. Testing F key makes fullscreen the primary mode...")
    
    # Start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print(f"   Starting mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    
    # F key should toggle to fullscreen (making it the "top most" mode)
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    mode_after_f = screen_mode_manager.is_fullscreen()
    
    print(f"   After F key: {'fullscreen' if mode_after_f else 'windowed'}")
    assert mode_after_f, "F key should make fullscreen the active/primary mode"
    
    # Verify that the setting is saved as fullscreen (primary mode)
    saved_setting = screen_mode_manager.get_current_screen_mode()
    print(f"   Saved setting: {'fullscreen' if saved_setting else 'windowed'}")
    assert saved_setting, "Fullscreen should be saved as the primary mode"
    
    print("   ✓ Fullscreen is correctly set as the primary/preferred mode")
    
    pygame.quit()
    return True

def main():
    """Run the comprehensive consistency test suite"""
    print("KEYBOARD SHORTCUTS CONSISTENCY TEST SUITE")
    print("Testing F key and Escape key functionality across all pages")
    print("=" * 70)
    
    all_tests_passed = True
    
    try:
        # Test 1: Screen mode manager integration
        if not test_screen_mode_manager_integration():
            all_tests_passed = False
        
        # Test 2: F key consistency
        if not test_f_key_consistency():
            all_tests_passed = False
        
        # Test 3: Escape key consistency
        if not test_escape_key_consistency():
            all_tests_passed = False
        
        # Test 4: Priority handling consistency
        if not test_priority_handling_consistency():
            all_tests_passed = False
        
        # Test 5: Fullscreen preference
        if not test_fullscreen_preference():
            all_tests_passed = False
        
        # Final results
        print("\n" + "=" * 70)
        print("CONSISTENCY TEST RESULTS")
        print("=" * 70)
        
        if all_tests_passed:
            print("🎉 ALL CONSISTENCY TESTS PASSED! ✓")
            print("\nKeyboard shortcuts are now fully consistent across all pages:")
            print("✓ Both main game and board selection use the same screen mode manager")
            print("✓ F key behavior is identical on both pages")
            print("✓ Escape key behavior is identical on both pages")
            print("✓ Priority handling works consistently")
            print("✓ Fullscreen is properly set as the primary/preferred mode")
            print("\nUsers now have a seamless keyboard shortcut experience!")
        else:
            print("❌ CONSISTENCY TESTS FAILED! ✗")
            print("\nSome inconsistencies remain that need to be addressed.")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n❌ CONSISTENCY TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
