"""
Stats Data Provider for WOW Games application.

This module provides a unified interface for retrieving stats data,
regardless of the backend storage mechanism (database, JSON, etc.).
It uses the optimized stats loader when available and falls back
to other methods when needed.
"""

import os
import json
import logging
import threading
import time
import traceback
from datetime import datetime, timedelta

# Configure logging
os.makedirs('data', exist_ok=True)
logging.basicConfig(
    filename=os.path.join('data', 'stats_data_provider.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Constants
DATA_DIR = os.path.join('data')
STATS_DB_PATH = os.path.join(DATA_DIR, 'stats.db')
STATS_JSON_PATH = os.path.join(DATA_DIR, 'stats.json')
CACHE_DIR = os.path.join(DATA_DIR, 'cache')
CACHE_FILE = os.path.join(CACHE_DIR, 'stats_cache.json')

# Debug flag - set to True to enable debug logging to console
DEBUG_MODE = True

def debug_log(message):
    """Log debug messages with a timestamp."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    print(f"[STATS PROVIDER {timestamp}] {message}")
    
    # Also write to a log file
    try:
        os.makedirs('data', exist_ok=True)
        with open(os.path.join('data', 'stats_data_provider.log'), 'a', encoding='utf-8') as f:
            f.write(f"{timestamp} - {message}\n")
    except Exception as e:
        print(f"Error writing to log file: {e}")

# Try to import our various stats modules
try:
    from game_stats_integration import GameStatsIntegration
    # Verify that the module has the required methods
    test_data = GameStatsIntegration.get_summary_stats()
    if test_data is None:
        raise ImportError("GameStatsIntegration returned None for summary stats")
    STATS_INTEGRATION_AVAILABLE = True
    debug_log(f"Using GameStatsIntegration as primary data source. Test data: {test_data}")
except Exception as e:
    STATS_INTEGRATION_AVAILABLE = False
    debug_log(f"GameStatsIntegration not available: {e}")
    debug_log(traceback.format_exc())

try:
    from optimized_stats_loader import get_optimized_stats_loader
    optimized_loader = get_optimized_stats_loader()
    # Verify that the module works
    test_data = optimized_loader.get_summary_stats()
    if test_data is None:
        raise ImportError("OptimizedStatsLoader returned None for summary stats")
    OPTIMIZED_LOADER_AVAILABLE = True
    debug_log(f"Using OptimizedStatsLoader as secondary data source. Test data: {test_data}")
except Exception as e:
    OPTIMIZED_LOADER_AVAILABLE = False
    debug_log(f"OptimizedStatsLoader not available: {e}")
    debug_log(traceback.format_exc())

try:
    import thread_safe_db
    # Verify that the module works
    test_data = thread_safe_db.get_summary_stats()
    if test_data is None:
        raise ImportError("ThreadSafeDB returned None for summary stats")
    THREAD_SAFE_DB_AVAILABLE = True
    debug_log(f"Using ThreadSafeDB as tertiary data source. Test data: {test_data}")
except Exception as e:
    THREAD_SAFE_DB_AVAILABLE = False
    debug_log(f"ThreadSafeDB not available: {e}")
    debug_log(traceback.format_exc())

debug_log(f"Data source availability: GameStatsIntegration={STATS_INTEGRATION_AVAILABLE}, OptimizedLoader={OPTIMIZED_LOADER_AVAILABLE}, ThreadSafeDB={THREAD_SAFE_DB_AVAILABLE}")

# Stats Data Provider - singleton class
class StatsDataProvider:
    """
    Unified interface for retrieving stats data.
    
    This class provides methods to retrieve stats data from various backends,
    with automatic fallbacks if a backend is unavailable.
    """
    
    _instance = None
    _lock = threading.RLock()
    
    def __new__(cls):
        """Implement singleton pattern."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(StatsDataProvider, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        """Initialize the stats data provider."""
        # Skip initialization if already initialized (singleton pattern)
        if getattr(self, '_initialized', False):
            return
            
        with self._lock:
            debug_log("Initializing StatsDataProvider")
            
            # Initialize cache for faster repeated access
            self.cache = {}
            self.cache_times = {}
            self.cache_duration = 60  # Cache duration in seconds (1 minute)
            
            # Ensure cache directory exists
            os.makedirs(CACHE_DIR, exist_ok=True)
            
            # Load the cache from disk if available
            self._load_cache()
            
            # Initialize loading mechanism
            self.background_thread = None
            self.stop_event = threading.Event()
            self.data_loading = False
            
            # Diagnostic information
            self.last_error = None
            self.error_time = None
            self.successful_loads = {
                'summary_stats': 0,
                'game_history': 0,
                'weekly_stats': 0
            }
            
            # Start background data loading
            self._start_background_loading()
            
            self._initialized = True
            debug_log("StatsDataProvider initialization completed")
    
    def _load_cache(self):
        """Load cache from disk."""
        try:
            if os.path.exists(CACHE_FILE):
                with open(CACHE_FILE, 'r') as f:
                    data = json.load(f)
                    
                self.cache = data.get('cache', {})
                self.cache_times = data.get('cache_times', {})
                
                # Remove expired entries
                current_time = time.time()
                expired_keys = []
                for key, timestamp in self.cache_times.items():
                    if current_time - timestamp > self.cache_duration:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    if key in self.cache:
                        del self.cache[key]
                    if key in self.cache_times:
                        del self.cache_times[key]
                
                debug_log(f"Loaded {len(self.cache)} items from cache")
                # Debug cache contents
                debug_log(f"Cache contains keys: {list(self.cache.keys())}")
        except Exception as e:
            self.last_error = str(e)
            self.error_time = time.time()
            logging.error(f"Error loading cache: {e}")
            debug_log(f"Error loading cache: {e}")
            debug_log(traceback.format_exc())
            # Initialize empty cache if loading fails
            self.cache = {}
            self.cache_times = {}
    
    def _save_cache(self):
        """Save cache to disk."""
        try:
            data = {
                'cache': self.cache,
                'cache_times': self.cache_times
            }
            
            with open(CACHE_FILE, 'w') as f:
                json.dump(data, f)
            
            debug_log(f"Saved {len(self.cache)} items to cache")
        except Exception as e:
            self.last_error = str(e)
            self.error_time = time.time()
            logging.error(f"Error saving cache: {e}")
            debug_log(f"Error saving cache: {e}")
            debug_log(traceback.format_exc())
    
    def _start_background_loading(self):
        """Start background loading of data."""
        if self.background_thread is not None and self.background_thread.is_alive():
            debug_log("Background loading thread already running")
            return  # Already running
        
        debug_log("Starting background data loading thread")
        self.stop_event.clear()
        self.background_thread = threading.Thread(target=self._background_load_data)
        self.background_thread.daemon = True
        self.background_thread.start()
    
    def _background_load_data(self):
        """Background thread to load all data."""
        try:
            self.data_loading = True
            debug_log("Background data loading started")
            
            # Load all data types with detailed error tracking
            try:
                summary_stats = self._load_summary_stats()
                if summary_stats:
                    self.successful_loads['summary_stats'] += 1
                    debug_log(f"Successfully loaded summary stats: {summary_stats}")
            except Exception as e:
                debug_log(f"Error loading summary stats: {e}")
                debug_log(traceback.format_exc())
            
            try:
                history, _ = self._load_game_history()
                if history:
                    self.successful_loads['game_history'] += 1
                    debug_log(f"Successfully loaded game history: {len(history)} entries")
            except Exception as e:
                debug_log(f"Error loading game history: {e}")
                debug_log(traceback.format_exc())
            
            try:
                weekly_stats = self._load_weekly_stats()
                if weekly_stats:
                    self.successful_loads['weekly_stats'] += 1
                    debug_log(f"Successfully loaded weekly stats: {len(weekly_stats)} days")
            except Exception as e:
                debug_log(f"Error loading weekly stats: {e}")
                debug_log(traceback.format_exc())
            
            # Save cache after loading all data
            self._save_cache()
            
            self.data_loading = False
            debug_log("Background data loading completed")
        except Exception as e:
            self.last_error = str(e)
            self.error_time = time.time()
            logging.error(f"Error in background data loading: {e}")
            debug_log(f"Error in background data loading: {e}")
            debug_log(traceback.format_exc())
            self.data_loading = False
    
    def _load_summary_stats(self):
        """Load summary statistics."""
        debug_log("Loading summary statistics")
        
        # First check cache
        if 'summary_stats' in self.cache and time.time() - self.cache_times.get('summary_stats', 0) < self.cache_duration:
            debug_log("Returning summary stats from cache")
            return self.cache['summary_stats']
        
        # Try each data source and log detailed information
        try:
            # Try to get data from our available sources in order of preference
            if STATS_INTEGRATION_AVAILABLE:
                try:
                    debug_log("Attempting to load summary stats from GameStatsIntegration")
                    summary_stats = GameStatsIntegration.get_summary_stats()
                    debug_log(f"Data from GameStatsIntegration: {summary_stats}")
                    
                    if summary_stats is not None:
                        # Store in cache
                        self.cache['summary_stats'] = summary_stats
                        self.cache_times['summary_stats'] = time.time()
                        
                        debug_log("Successfully loaded summary stats from GameStatsIntegration")
                        return summary_stats
                    else:
                        debug_log("GameStatsIntegration returned None for summary stats")
                except Exception as e:
                    debug_log(f"Error loading summary stats from GameStatsIntegration: {e}")
                    debug_log(traceback.format_exc())
            
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    debug_log("Attempting to load summary stats from OptimizedStatsLoader")
                    summary_stats = optimized_loader.get_summary_stats()
                    debug_log(f"Data from OptimizedStatsLoader: {summary_stats}")
                    
                    if summary_stats is not None:
                        # Store in cache
                        self.cache['summary_stats'] = summary_stats
                        self.cache_times['summary_stats'] = time.time()
                        
                        debug_log("Successfully loaded summary stats from OptimizedStatsLoader")
                        return summary_stats
                    else:
                        debug_log("OptimizedStatsLoader returned None for summary stats")
                except Exception as e:
                    debug_log(f"Error loading summary stats from OptimizedStatsLoader: {e}")
                    debug_log(traceback.format_exc())
            
            if THREAD_SAFE_DB_AVAILABLE:
                try:
                    debug_log("Attempting to load summary stats from ThreadSafeDB")
                    summary_stats = thread_safe_db.get_summary_stats()
                    debug_log(f"Data from ThreadSafeDB: {summary_stats}")
                    
                    if summary_stats is not None:
                        # Store in cache
                        self.cache['summary_stats'] = summary_stats
                        self.cache_times['summary_stats'] = time.time()
                        
                        debug_log("Successfully loaded summary stats from ThreadSafeDB")
                        return summary_stats
                    else:
                        debug_log("ThreadSafeDB returned None for summary stats")
                except Exception as e:
                    debug_log(f"Error loading summary stats from ThreadSafeDB: {e}")
                    debug_log(traceback.format_exc())
            
            # If we get here, all data sources failed
            debug_log("All data sources failed, returning default summary stats")
            
            # If no data sources are available, return empty/default data
            default_data = {
                'total_earnings': 0,
                'daily_earnings': 0,
                'daily_games': 0,
                'wallet_balance': 0
            }
            
            # Cache the default data too, but with a shorter duration
            self.cache['summary_stats'] = default_data
            self.cache_times['summary_stats'] = time.time() - (self.cache_duration / 2)  # Expire sooner
            
            return default_data
        except Exception as e:
            self.last_error = str(e)
            self.error_time = time.time()
            logging.error(f"Error loading summary stats: {e}")
            debug_log(f"Error loading summary stats: {e}")
            debug_log(traceback.format_exc())
            
            # Return a safe default
            return {
                'total_earnings': 0,
                'daily_earnings': 0,
                'daily_games': 0,
                'wallet_balance': 0
            }
    
    def _load_game_history(self, page=0, page_size=10):
        """Load game history."""
        try:
            # Try to get data from our available sources in order of preference
            if STATS_INTEGRATION_AVAILABLE:
                history, total_pages = GameStatsIntegration.get_game_history(page, page_size)
                
                # Cache key depends on page and page size
                cache_key = f'game_history_{page}_{page_size}'
                
                # Store in cache
                self.cache[cache_key] = (history, total_pages)
                self.cache_times[cache_key] = time.time()
                
                logging.info(f"Loaded game history page {page} from GameStatsIntegration")
                return history, total_pages
            
            if OPTIMIZED_LOADER_AVAILABLE:
                try:
                    history, total_pages = optimized_loader.get_game_history_page(page, page_size)
                    
                    # Cache key depends on page and page size
                    cache_key = f'game_history_{page}_{page_size}'
                    
                    # Store in cache
                    self.cache[cache_key] = (history, total_pages)
                    self.cache_times[cache_key] = time.time()
                    
                    logging.info(f"Loaded game history page {page} from OptimizedStatsLoader")
                    return history, total_pages
                except:
                    # Fallback to thread_safe_db if optimized loader fails
                    pass
            
            if THREAD_SAFE_DB_AVAILABLE:
                history, total_pages = thread_safe_db.get_game_history(page, page_size)
                
                # Cache key depends on page and page size
                cache_key = f'game_history_{page}_{page_size}'
                
                # Store in cache
                self.cache[cache_key] = (history, total_pages)
                self.cache_times[cache_key] = time.time()
                
                logging.info(f"Loaded game history page {page} from ThreadSafeDB")
                return history, total_pages
            
            # If no data sources are available, return empty data
            return [], 1
        except Exception as e:
            logging.error(f"Error loading game history: {e}")
            return [], 1
    
    def _load_weekly_stats(self):
        """Load weekly statistics."""
        try:
            # Try to get data from our available sources in order of preference
            if STATS_INTEGRATION_AVAILABLE:
                weekly_stats = GameStatsIntegration.get_weekly_stats()
                
                # Store in cache
                self.cache['weekly_stats'] = weekly_stats
                self.cache_times['weekly_stats'] = time.time()
                
                logging.info("Loaded weekly stats from GameStatsIntegration")
                return weekly_stats
            
            if OPTIMIZED_LOADER_AVAILABLE:
                weekly_stats = optimized_loader.get_weekly_stats()
                
                # Store in cache
                self.cache['weekly_stats'] = weekly_stats
                self.cache_times['weekly_stats'] = time.time()
                
                logging.info("Loaded weekly stats from OptimizedStatsLoader")
                return weekly_stats
            
            if THREAD_SAFE_DB_AVAILABLE:
                weekly_stats = thread_safe_db.get_weekly_stats()
                
                # Store in cache
                self.cache['weekly_stats'] = weekly_stats
                self.cache_times['weekly_stats'] = time.time()
                
                logging.info("Loaded weekly stats from ThreadSafeDB")
                return weekly_stats
            
            # If no data sources are available, return empty data
            return []
        except Exception as e:
            logging.error(f"Error loading weekly stats: {e}")
            return []
    
    # Public API methods
    
    def get_summary_stats(self, force_refresh=False):
        """
        Get summary statistics.
        
        Args:
            force_refresh: Whether to force a refresh of the data
            
        Returns:
            dict: Summary statistics
        """
        debug_log(f"get_summary_stats called with force_refresh={force_refresh}")
        
        # Force refresh if requested
        if force_refresh:
            try:
                debug_log("Forcing refresh of summary stats")
                return self._load_summary_stats()
            except Exception as e:
                debug_log(f"Error forcing refresh of summary stats: {e}")
                debug_log(traceback.format_exc())
        
        # Check cache first
        if not force_refresh and 'summary_stats' in self.cache:
            cache_age = time.time() - self.cache_times.get('summary_stats', 0)
            if cache_age < self.cache_duration:
                debug_log(f"Returning summary stats from cache (age: {cache_age:.1f}s)")
                return self.cache['summary_stats']
            else:
                debug_log(f"Cache expired for summary stats (age: {cache_age:.1f}s)")
        
        # Load fresh data
        return self._load_summary_stats()
    
    def get_game_history(self, page=0, page_size=10, force_refresh=False):
        """
        Get game history.
        
        Args:
            page: Page number (0-based)
            page_size: Number of items per page
            force_refresh: Whether to force a refresh of the data
            
        Returns:
            list: List of game history items (for compatibility with stats_page.py)
        """
        # Cache key depends on page and page size
        cache_key = f'game_history_{page}_{page_size}'
        
        # Check if we have cached data and it's still valid
        if not force_refresh and cache_key in self.cache:
            cache_time = self.cache_times.get(cache_key, 0)
            if time.time() - cache_time <= self.cache_duration:
                logging.info(f"Returning cached game history page {page}")
                # The cache stores a tuple (history, total_pages), but we only need history for stats_page.py
                cached_data = self.cache[cache_key]
                if isinstance(cached_data, tuple) and len(cached_data) >= 1:
                    debug_log(f"Returning cached history data: {len(cached_data[0])} entries")
                    return cached_data[0]  # Return just the history list
                return []
        
        # Load data (either directly or by triggering background load)
        history_data, _ = self._load_game_history(page, page_size)
        debug_log(f"Returning freshly loaded history data: {len(history_data)} entries")
        return history_data  # Return just the history list
    
    def get_weekly_stats(self, force_refresh=False):
        """
        Get weekly statistics.
        
        Args:
            force_refresh: Whether to force a refresh of the data
            
        Returns:
            list: List of daily statistics for the week
        """
        # Check if we have cached data and it's still valid
        if not force_refresh and 'weekly_stats' in self.cache:
            cache_time = self.cache_times.get('weekly_stats', 0)
            if time.time() - cache_time <= self.cache_duration:
                logging.info("Returning cached weekly stats")
                return self.cache['weekly_stats']
        
        # Load data (either directly or by triggering background load)
        return self._load_weekly_stats()
    
    def force_refresh(self):
        """
        Force refresh all cached data.
        
        Returns:
            bool: True if successful, False otherwise
        """
        debug_log("Forcing refresh of all stats data")
        try:
            # First try direct access to GameStatsIntegration
            if STATS_INTEGRATION_AVAILABLE:
                try:
                    debug_log("Attempting to force refresh via GameStatsIntegration")
                    GameStatsIntegration.force_refresh_data()
                    debug_log("Force refresh via GameStatsIntegration successful")
                except Exception as e:
                    debug_log(f"Error forcing refresh via GameStatsIntegration: {e}")
                    debug_log(traceback.format_exc())
            
            # Then refresh our own cache
            debug_log("Clearing cached data")
            with self._lock:
                self.cache = {}
                self.cache_times = {}
            
            # Try to post a refresh_stats event to trigger UI update
            try:
                import pygame
                import time
                if pygame.get_init():
                    refresh_event = pygame.event.Event(pygame.USEREVENT, {
                        'stats_type': 'refresh_stats',
                        'force': True,
                        'force_reload': True,
                        'source': 'StatsDataProvider.force_refresh',
                        'timestamp': time.time()
                    })
                    pygame.event.post(refresh_event)
                    debug_log("Posted refresh_stats event to trigger UI update")
            except Exception as event_e:
                debug_log(f"Could not post refresh event: {event_e}")
                debug_log(traceback.format_exc())
            
            # Start background loading
            debug_log("Starting background data reload")
            self._start_background_loading()
            
            return True
        except Exception as e:
            self.last_error = str(e)
            self.error_time = time.time()
            logging.error(f"Error forcing refresh: {e}")
            debug_log(f"Error forcing refresh: {e}")
            debug_log(traceback.format_exc())
            return False
    
    def get_diagnostic_info(self):
        """
        Get diagnostic information about the stats provider.
        
        Returns:
            dict: Diagnostic information
        """
        return {
            'initialized': getattr(self, '_initialized', False),
            'data_loading': self.data_loading,
            'cache_size': len(self.cache),
            'cache_keys': list(self.cache.keys()),
            'last_error': self.last_error,
            'error_time': self.error_time,
            'successful_loads': self.successful_loads,
            'data_sources': {
                'STATS_INTEGRATION_AVAILABLE': STATS_INTEGRATION_AVAILABLE,
                'OPTIMIZED_LOADER_AVAILABLE': OPTIMIZED_LOADER_AVAILABLE,
                'THREAD_SAFE_DB_AVAILABLE': THREAD_SAFE_DB_AVAILABLE
            }
        }
    
    def is_loading(self):
        """
        Check if data is currently being loaded.
        
        Returns:
            bool: True if data is being loaded, False otherwise
        """
        return self.data_loading

# Global function to get the stats data provider instance
def get_stats_provider():
    """
    Get the stats data provider singleton instance.
    
    Returns:
        StatsDataProvider: The stats data provider instance
    """
    provider = StatsDataProvider()
    debug_log(f"get_stats_provider called, returning provider with initialized={getattr(provider, '_initialized', False)}")
    return provider

# Create the singleton instance on module import
try:
    _stats_provider = StatsDataProvider()
    debug_log("Created singleton instance of StatsDataProvider on module import")
    debug_log(f"Diagnostic info: {_stats_provider.get_diagnostic_info()}")
except Exception as e:
    logging.error(f"Error creating StatsDataProvider singleton: {e}")
    debug_log(f"Error creating StatsDataProvider singleton: {e}")
    debug_log(traceback.format_exc()) 