#!/usr/bin/env python3
"""
Comprehensive test script for voucher system cross-platform compatibility.

This script tests the voucher system to ensure it works correctly across
different computers and environments.
"""

import os
import sys
import json
import time
import tempfile
import shutil
from datetime import datetime

def test_uuid_detection():
    """Test machine UUID detection across different methods."""
    print("=" * 60)
    print("TESTING UUID DETECTION")
    print("=" * 60)
    
    results = {}
    
    # Test 1: get_machine_uuid module
    try:
        from get_machine_uuid import get_machine_uuid
        uuid1 = get_machine_uuid()
        results['get_machine_uuid'] = uuid1
        print(f"✓ get_machine_uuid: {uuid1}")
    except Exception as e:
        results['get_machine_uuid'] = f"ERROR: {e}"
        print(f"✗ get_machine_uuid: {e}")
    
    # Test 2: payment.crypto_utils
    try:
        from payment.crypto_utils import CryptoUtils
        uuid2 = CryptoUtils.get_machine_uuid()
        results['crypto_utils'] = uuid2
        print(f"✓ CryptoUtils.get_machine_uuid: {uuid2}")
    except Exception as e:
        results['crypto_utils'] = f"ERROR: {e}"
        print(f"✗ CryptoUtils.get_machine_uuid: {e}")
    
    # Test 3: voucher_processor
    try:
        from payment.voucher_processor import VoucherProcessor
        processor = VoucherProcessor()
        uuid3 = processor.machine_uuid
        results['voucher_processor'] = uuid3
        print(f"✓ VoucherProcessor.machine_uuid: {uuid3}")
    except Exception as e:
        results['voucher_processor'] = f"ERROR: {e}"
        print(f"✗ VoucherProcessor.machine_uuid: {e}")
    
    # Test 4: stats_page method
    try:
        from stats_page import StatsPage
        stats = StatsPage(None, 1024, 768)
        uuid4 = stats.get_machine_uuid()
        results['stats_page'] = uuid4
        print(f"✓ StatsPage.get_machine_uuid: {uuid4}")
    except Exception as e:
        results['stats_page'] = f"ERROR: {e}"
        print(f"✗ StatsPage.get_machine_uuid: {e}")
    
    # Check consistency
    valid_uuids = [v for v in results.values() if not str(v).startswith("ERROR:")]
    if len(set(valid_uuids)) <= 1:
        print(f"\n✓ UUID CONSISTENCY: All methods return the same UUID")
    else:
        print(f"\n✗ UUID INCONSISTENCY: Different methods return different UUIDs")
        for method, uuid in results.items():
            print(f"  {method}: {uuid}")
    
    return results

def test_database_initialization():
    """Test database initialization and path handling."""
    print("\n" + "=" * 60)
    print("TESTING DATABASE INITIALIZATION")
    print("=" * 60)
    
    try:
        from payment.voucher_manager import VoucherManager, get_data_dir
        
        # Test data directory creation
        data_dir = get_data_dir()
        print(f"✓ Data directory: {data_dir}")
        print(f"✓ Data directory exists: {os.path.exists(data_dir)}")
        
        # Test voucher manager initialization
        manager = VoucherManager()
        print(f"✓ VoucherManager initialized")
        print(f"✓ Machine UUID: {manager.machine_uuid}")
        print(f"✓ Current credits: {manager.credits}")
        
        if manager.initialization_errors:
            print(f"⚠ Initialization errors: {len(manager.initialization_errors)}")
            for error in manager.initialization_errors:
                print(f"  - {error}")
        else:
            print(f"✓ No initialization errors")
        
        return True
        
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voucher_processing():
    """Test voucher processing functionality."""
    print("\n" + "=" * 60)
    print("TESTING VOUCHER PROCESSING")
    print("=" * 60)
    
    try:
        from payment.voucher_processor import VoucherProcessor
        
        processor = VoucherProcessor()
        print(f"✓ VoucherProcessor created")
        print(f"✓ Machine UUID: {processor.machine_uuid}")
        
        # Test machine info
        machine_info = processor.get_machine_info()
        print(f"✓ Machine info retrieved:")
        for key, value in machine_info.items():
            print(f"  {key}: {value}")
        
        # Test invalid voucher
        result = processor.process_voucher("INVALID123")
        print(f"✓ Invalid voucher test: {result['success']} - {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Voucher processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_paths():
    """Test file path handling and data persistence."""
    print("\n" + "=" * 60)
    print("TESTING FILE PATHS AND PERSISTENCE")
    print("=" * 60)
    
    try:
        # Test data directory paths
        from payment.voucher_manager import get_data_dir, VOUCHERS_DB_PATH, USAGE_LOG_PATH
        
        data_dir = get_data_dir()
        print(f"✓ Data directory: {data_dir}")
        print(f"✓ Vouchers DB path: {VOUCHERS_DB_PATH}")
        print(f"✓ Usage log path: {USAGE_LOG_PATH}")
        
        # Check if paths are absolute
        print(f"✓ Data dir is absolute: {os.path.isabs(data_dir)}")
        print(f"✓ Vouchers DB is absolute: {os.path.isabs(VOUCHERS_DB_PATH)}")
        print(f"✓ Usage log is absolute: {os.path.isabs(USAGE_LOG_PATH)}")
        
        # Test file creation
        test_file = os.path.join(data_dir, 'test_file.txt')
        with open(test_file, 'w') as f:
            f.write("test")
        
        if os.path.exists(test_file):
            print(f"✓ File creation test passed")
            os.remove(test_file)
        else:
            print(f"✗ File creation test failed")
        
        return True
        
    except Exception as e:
        print(f"✗ File path test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_test_report():
    """Generate a comprehensive test report."""
    print("\n" + "=" * 60)
    print("GENERATING TEST REPORT")
    print("=" * 60)
    
    report = {
        "test_timestamp": datetime.now().isoformat(),
        "system_info": {},
        "test_results": {}
    }
    
    # System information
    import platform
    report["system_info"] = {
        "system": platform.system(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "node": platform.node(),
        "python_version": platform.python_version()
    }
    
    # Run tests
    print("Running UUID detection test...")
    report["test_results"]["uuid_detection"] = test_uuid_detection()
    
    print("Running database initialization test...")
    report["test_results"]["database_init"] = test_database_initialization()
    
    print("Running voucher processing test...")
    report["test_results"]["voucher_processing"] = test_voucher_processing()
    
    print("Running file paths test...")
    report["test_results"]["file_paths"] = test_file_paths()
    
    # Save report
    report_file = f"voucher_test_report_{int(time.time())}.json"
    try:
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n✓ Test report saved to: {report_file}")
    except Exception as e:
        print(f"\n✗ Failed to save test report: {e}")
    
    return report

def main():
    """Main test function."""
    print("VOUCHER SYSTEM CROSS-PLATFORM COMPATIBILITY TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    try:
        report = generate_test_report()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(report["test_results"])
        passed_tests = sum(1 for result in report["test_results"].values() 
                          if isinstance(result, (bool, dict)) and result)
        
        print(f"Total tests: {total_tests}")
        print(f"Passed tests: {passed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! Voucher system should work correctly.")
        else:
            print(f"\n⚠ {total_tests - passed_tests} tests failed. Check the report for details.")
        
        return 0 if passed_tests == total_tests else 1
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
