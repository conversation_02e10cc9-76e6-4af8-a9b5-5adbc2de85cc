"""
Add Test Game Data

This script adds test game data to the SQLite database
to verify that the dashboard can display real-time data.
"""

import os
import sqlite3
from datetime import datetime, timedelta
import random

def add_test_game_data():
    """Add test game data to SQLite database."""
    print("🎮 ADDING TEST GAME DATA")
    print("=" * 50)
    
    # Connect to SQLite database
    db_path = os.path.join('data', 'stats.db')
    
    if not os.path.exists(os.path.dirname(db_path)):
        os.makedirs(os.path.dirname(db_path))
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create table if it doesn't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            house TEXT,
            stake REAL,
            players INTEGER,
            total_calls INTEGER,
            commission_percent REAL,
            fee REAL,
            total_prize REAL,
            details TEXT,
            status TEXT,
            date_time TEXT
        )
    ''')
    
    # Generate test data
    test_games = []
    
    # Add some games from today
    today = datetime.now()
    for i in range(5):
        game_time = today - timedelta(hours=random.randint(0, 12))
        test_games.append({
            'username': f'Player{i+1}',
            'house': f'House{random.randint(1, 3)}',
            'stake': random.randint(50, 200),
            'players': random.randint(3, 8),
            'total_calls': random.randint(15, 45),
            'commission_percent': 20,
            'fee': random.randint(10, 40),
            'total_prize': random.randint(150, 800),
            'details': f'{{"winner": "Player{i+1}", "pattern": "Full House"}}',
            'status': 'completed',
            'date_time': game_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # Add some games from yesterday
    yesterday = today - timedelta(days=1)
    for i in range(3):
        game_time = yesterday - timedelta(hours=random.randint(0, 12))
        test_games.append({
            'username': f'PlayerY{i+1}',
            'house': f'House{random.randint(1, 3)}',
            'stake': random.randint(50, 200),
            'players': random.randint(3, 8),
            'total_calls': random.randint(15, 45),
            'commission_percent': 20,
            'fee': random.randint(10, 40),
            'total_prize': random.randint(150, 800),
            'details': f'{{"winner": "PlayerY{i+1}", "pattern": "Line"}}',
            'status': 'completed',
            'date_time': game_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # Insert test data
    for game in test_games:
        cursor.execute('''
            INSERT INTO game_history 
            (username, house, stake, players, total_calls, commission_percent, 
             fee, total_prize, details, status, date_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            game['username'], game['house'], game['stake'], game['players'],
            game['total_calls'], game['commission_percent'], game['fee'],
            game['total_prize'], game['details'], game['status'], game['date_time']
        ))
    
    conn.commit()
    
    # Get current stats
    cursor.execute('SELECT COUNT(*) FROM game_history')
    total_games = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(total_prize) FROM game_history')
    total_earnings = cursor.fetchone()[0] or 0
    
    # Get today's stats
    today_str = today.strftime('%Y-%m-%d')
    cursor.execute('SELECT COUNT(*) FROM game_history WHERE date(date_time) = ?', (today_str,))
    todays_games = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(total_prize) FROM game_history WHERE date(date_time) = ?', (today_str,))
    todays_earnings = cursor.fetchone()[0] or 0
    
    conn.close()
    
    print(f"✅ Added {len(test_games)} test games to database")
    print(f"📊 Current Stats:")
    print(f"   - Total Games: {total_games}")
    print(f"   - Total Earnings: {total_earnings} ETB")
    print(f"   - Today's Games: {todays_games}")
    print(f"   - Today's Earnings: {todays_earnings} ETB")
    print("")
    print("🌐 Now refresh the dashboard at http://localhost:5000")
    print("   Click the 'Refresh Data' button to see the updated stats!")

def add_real_time_game():
    """Add a single game to simulate real-time activity."""
    print("⚡ ADDING REAL-TIME GAME")
    print("=" * 30)
    
    db_path = os.path.join('data', 'stats.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Add a game with current timestamp
    now = datetime.now()
    game_data = {
        'username': 'RealTimePlayer',
        'house': 'LiveHouse',
        'stake': 100,
        'players': 5,
        'total_calls': 25,
        'commission_percent': 20,
        'fee': 20,
        'total_prize': 400,
        'details': '{"winner": "RealTimePlayer", "pattern": "Full House", "realtime": true}',
        'status': 'completed',
        'date_time': now.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    cursor.execute('''
        INSERT INTO game_history 
        (username, house, stake, players, total_calls, commission_percent, 
         fee, total_prize, details, status, date_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        game_data['username'], game_data['house'], game_data['stake'], 
        game_data['players'], game_data['total_calls'], game_data['commission_percent'], 
        game_data['fee'], game_data['total_prize'], game_data['details'], 
        game_data['status'], game_data['date_time']
    ))
    
    conn.commit()
    conn.close()
    
    print(f"✅ Added real-time game at {now.strftime('%H:%M:%S')}")
    print("🔄 Refresh the dashboard to see the update!")

def main():
    """Main function."""
    print("🧪 TEST DATA GENERATOR")
    print("=" * 50)
    
    choice = input("Choose an option:\n1. Add test game data\n2. Add real-time game\n3. Both\nEnter choice (1/2/3): ").strip()
    
    if choice == '1':
        add_test_game_data()
    elif choice == '2':
        add_real_time_game()
    elif choice == '3':
        add_test_game_data()
        print("\n" + "=" * 50)
        add_real_time_game()
    else:
        print("Invalid choice. Exiting.")
        return
    
    print("\n💡 Tips:")
    print("   - Dashboard URL: http://localhost:5000")
    print("   - Click 'Refresh Data' button to see updates")
    print("   - Data source will show 'SQLite (Fallback)' if RethinkDB is not connected")
    print("   - Run this script again to add more test data")

if __name__ == "__main__":
    main()
