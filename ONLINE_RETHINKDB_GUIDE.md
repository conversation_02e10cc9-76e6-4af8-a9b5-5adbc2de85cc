# Online RethinkDB Integration Guide

This guide explains how to set up and use the online capabilities of the RethinkDB integration.

## How Online Integration Works

The RethinkDB integration works in two modes:

1. **Offline Mode**: All data is stored locally in SQLite. Changes are queued for future synchronization.
2. **Online Mode**: Data is stored locally and synced with a remote RethinkDB server in real-time.

## Setting Up RethinkDB Server

### Option 1: Local Server (Development)

1. **Install RethinkDB** on your computer:
   - Windows: Download from https://rethinkdb.com/docs/install/windows/
   - macOS: `brew install rethinkdb`
   - Linux: Follow instructions at https://rethinkdb.com/docs/install/

2. **Start RethinkDB Server**:
   ```
   rethinkdb --bind all
   ```

3. **Access the Admin Interface**:
   - Open http://localhost:8080 in your browser
   - This web interface allows you to browse and manage your data

4. **Configure Your Application**:
   - Update `rethink_config.py` with your local server details:
     ```
     RETHINKDB_HOST = 'localhost'
     RETHINKDB_PORT = 28015
     RETHINKDB_DB = 'wow_game_stats'
     ```

### Option 2: Remote Server (Production)

1. **Set Up a Server** with RethinkDB installed:
   - You can use a cloud service like AWS, Digital Ocean, or a dedicated server
   - Install RethinkDB following the appropriate OS instructions

2. **Configure RethinkDB for Remote Access**:
   - Edit RethinkDB configuration to allow remote connections
   - Set up authentication (strongly recommended for production)
   - Configure firewall to allow connections on ports 28015 (driver) and 8080 (admin)

3. **Update Your Configuration**:
   - Edit `rethink_config.py`:
     ```
     RETHINKDB_HOST = 'your.server.ip.address'
     RETHINKDB_PORT = 28015
     RETHINKDB_DB = 'wow_game_stats'
     RETHINKDB_USER = 'username'  # If authentication is enabled
     RETHINKDB_PASSWORD = 'password'  # If authentication is enabled
     RETHINKDB_SSL = True  # If SSL is configured
     ```

## Authentication

RethinkDB supports several authentication methods:

1. **No Authentication** (default, not recommended for production):
   - Anyone with network access can connect to your database

2. **Username/Password Authentication**:
   - Set up in the RethinkDB admin interface
   - No account creation needed in the application - credentials are stored in the config file

3. **SSL Certificate Authentication**:
   - For production environments, you can set up SSL certificates
   - Update the configuration to use SSL

## Accessing Your Data Remotely

### Through the Admin Interface

1. **Access the Web Interface**:
   - Open http://your.server.ip.address:8080 in your browser
   - If authentication is enabled, log in with your credentials

2. **Browse Your Data**:
   - Click on the "Tables" tab to see all tables
   - Select a table to browse its contents
   - Use the Data Explorer to run custom queries

### Programmatically

You can access your data from other applications using the RethinkDB driver:

```python
import rethinkdb as r

# Connect to your RethinkDB instance
conn = r.connect(
    host='your.server.ip.address',
    port=28015,
    db='wow_game_stats',
    user='username',  # If authentication is enabled
    password='password'  # If authentication is enabled
)

# Query data
cursor = r.table('game_history').run(conn)
for doc in cursor:
    print(doc)

conn.close()
```

## Switching Between Modes

### Command Line

Use the `rethink_status.py` script to switch modes:

```
# Switch to online mode
python rethink_status.py --online

# Switch to offline mode
python rethink_status.py --offline

# Check current status
python rethink_status.py --status
```

### In the Application

The application will show a connection indicator in the top-right corner:
- **Green**: Online mode (connected to RethinkDB)
- **Red**: Offline mode (using local SQLite only)

## Troubleshooting

### Connection Issues

If you can't connect to your RethinkDB server:

1. **Check Network Connectivity**:
   - Ensure the server is reachable from your network
   - Verify firewall settings allow connections to ports 28015 and 8080

2. **Verify Server Status**:
   - Make sure RethinkDB is running on the server
   - Check server logs for any errors

3. **Test Connection**:
   ```
   python setup_rethinkdb.py --test
   ```

### Sync Issues

If data isn't synchronizing properly:

1. **Force a Manual Sync**:
   ```
   python rethink_status.py --sync
   ```

2. **Check the Logs**:
   - Review `data/rethink_db.log` and `data/sync_manager.log` for errors

## Example: Setting Up a Simple Remote Server

Here's a quick guide to setting up a basic remote server with Digital Ocean:

1. **Create a Droplet** (virtual machine) on Digital Ocean
2. **Install RethinkDB**:
   ```
   sudo apt-get update
   sudo apt-get install rethinkdb
   ```

3. **Configure for Remote Access**:
   - Edit `/etc/rethinkdb/instances.d/instance1.conf`
   - Set `bind=all` to allow remote connections
   - Restart RethinkDB: `sudo systemctl restart rethinkdb`

4. **Set Up a Firewall**:
   ```
   sudo ufw allow 22/tcp    # SSH
   sudo ufw allow 8080/tcp  # Admin interface
   sudo ufw allow 28015/tcp # Driver port
   sudo ufw enable
   ```

5. **Update Your Application Configuration**:
   - Edit `rethink_config.py` with your droplet's IP address

## Security Considerations

For production environments, consider these security practices:

1. **Use Authentication**: Always enable authentication for remote servers
2. **Enable SSL**: Configure SSL for encrypted connections
3. **Use a Firewall**: Restrict access to only trusted IP addresses
4. **Regular Backups**: Back up your data regularly
5. **Keep Updated**: Keep RethinkDB updated with security patches

## Additional Resources

- [RethinkDB Documentation](https://rethinkdb.com/docs/)
- [RethinkDB Security Guide](https://rethinkdb.com/docs/security/)
- [RethinkDB Admin Interface Guide](https://rethinkdb.com/docs/administration-tools/)