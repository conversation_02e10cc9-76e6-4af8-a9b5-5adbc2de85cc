#!/usr/bin/env python3
"""
Comprehensive Nuitka Build Script for WOW Bingo Game
====================================================

This script provides a complete build system for the Python-based bingo game
using Nuitka with automatic dependency detection, asset bundling, and
cross-platform compatibility.

Features:
- Automatic dependency detection and inclusion
- Asset bundling and path resolution
- Error handling and build validation
- Clean build directory management
- Debug/release build modes
- Performance optimization
- Cross-platform compatibility

Usage:
    python nuitka_comprehensive_build.py [options]

Options:
    --mode {debug,release,optimized}  Build mode (default: release)
    --clean                          Clean build directories before building
    --test                           Test the executable after building
    --verbose                        Enable verbose output
    --help                           Show this help message
"""

import os
import sys
import json
import shutil
import subprocess
import argparse
import platform
import time
import tempfile
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple
import importlib.util

class NuitkaBuildSystem:
    """Comprehensive build system for the WOW Bingo Game using Nuitka."""

    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.temp_dir = None

        # Project configuration
        self.project_name = "WOW Bingo Game"
        self.project_version = "1.0.0"
        self.main_script = "main.py"
        self.executable_name = "WOWBingoGame"
        self.icon_path = "assets/app_logo.ico"

        # Build configuration
        self.build_mode = "release"
        self.verbose = False
        self.clean_build = False
        self.test_executable = False

        # Detected dependencies
        self.python_packages = set()
        self.system_modules = set()
        self.local_modules = set()
        self.data_directories = []
        self.asset_files = []

        # Platform-specific settings
        self.is_windows = platform.system() == "Windows"
        self.is_linux = platform.system() == "Linux"
        self.is_macos = platform.system() == "Darwin"

    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")

    def error(self, message: str):
        """Log an error message and exit."""
        self.log(message, "ERROR")
        sys.exit(1)

    def warning(self, message: str):
        """Log a warning message."""
        self.log(message, "WARNING")

    def analyze_project_structure(self):
        """Analyze the project structure to identify all components."""
        self.log("Analyzing project structure...")

        # Check if main script exists
        main_script_path = self.project_root / self.main_script
        if not main_script_path.exists():
            self.error(f"Main script '{self.main_script}' not found!")

        # Analyze Python files for dependencies
        self._analyze_python_dependencies()

        # Identify data directories and assets
        self._identify_assets_and_data()

        # Validate critical components
        self._validate_project_components()

    def _analyze_python_dependencies(self):
        """Analyze Python files to detect all dependencies."""
        self.log("Analyzing Python dependencies...")

        python_files = list(self.project_root.glob("*.py"))
        python_files.extend(self.project_root.glob("**/*.py"))

        # Core Python modules that are always needed
        core_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'math', 'random',
            'sqlite3', 'threading', 'collections', 'itertools', 'functools',
            'operator', 'copy', 'pickle', 'hashlib', 'uuid', 'platform',
            'subprocess', 'pathlib', 'glob', 'shutil', 'tempfile', 'logging',
            'traceback', 'warnings', 'weakref', 'gc', 'ctypes', 'struct',
            'array', 'base64', 'binascii', 'zlib', 'gzip', 'io', 're',
            'string', 'textwrap', 'unicodedata', 'locale', 'calendar',
            'decimal', 'fractions', 'statistics', 'enum', 'types', 'inspect'
        }
        self.system_modules.update(core_modules)

        # Third-party packages that are definitely needed
        known_packages = {
            'pygame', 'pyperclip', 'colorsys'
        }
        self.python_packages.update(known_packages)

        # Analyze import statements in Python files
        for py_file in python_files:
            if py_file.name.startswith('.') or '__pycache__' in str(py_file):
                continue

            try:
                self._extract_imports_from_file(py_file)
            except Exception as e:
                self.warning(f"Could not analyze {py_file}: {e}")

    def _extract_imports_from_file(self, file_path: Path):
        """Extract import statements from a Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Simple regex-based import extraction
            import re

            # Standard imports: import module
            standard_imports = re.findall(r'^import\s+([a-zA-Z_][a-zA-Z0-9_]*)', content, re.MULTILINE)

            # From imports: from module import ...
            from_imports = re.findall(r'^from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import', content, re.MULTILINE)

            all_imports = standard_imports + from_imports

            for imp in all_imports:
                # Skip relative imports
                if imp.startswith('.'):
                    continue

                # Get the top-level module name
                top_level = imp.split('.')[0]

                # Check if it's a local module (exists as .py file in project)
                if (self.project_root / f"{top_level}.py").exists():
                    self.local_modules.add(top_level)
                # Check if it's a known third-party package
                elif top_level in ['pygame', 'pyperclip', 'kivy', 'rethinkdb', 'psutil']:
                    self.python_packages.add(top_level)
                # Check if it's a standard library module
                elif self._is_standard_library_module(top_level):
                    self.system_modules.add(top_level)
                else:
                    # Try to determine if it's available
                    try:
                        spec = importlib.util.find_spec(top_level)
                        if spec is not None:
                            self.python_packages.add(top_level)
                    except (ImportError, ModuleNotFoundError, ValueError):
                        pass

        except Exception as e:
            self.warning(f"Error analyzing {file_path}: {e}")

    def _is_standard_library_module(self, module_name: str) -> bool:
        """Check if a module is part of the Python standard library."""
        try:
            spec = importlib.util.find_spec(module_name)
            if spec is None:
                return False

            # Standard library modules are typically in the Python installation directory
            if spec.origin:
                origin_path = Path(spec.origin)
                python_path = Path(sys.executable).parent.parent
                return str(origin_path).startswith(str(python_path))
            return False
        except (ImportError, ModuleNotFoundError, ValueError):
            return False

    def _identify_assets_and_data(self):
        """Identify all asset files and data directories that need to be included."""
        self.log("Identifying assets and data files...")

        # Essential directories that must be included
        essential_dirs = ['assets', 'data']

        for dir_name in essential_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                self.data_directories.append((str(dir_path), dir_name))
                self.log(f"Found data directory: {dir_name}")
            else:
                self.warning(f"Essential directory '{dir_name}' not found!")

        # Additional directories that might exist (only include if they have files)
        optional_dirs = ['templates', 'keys', 'vouchers', 'instant_loading']

        for dir_name in optional_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                # Check if directory has any files (not just empty)
                has_files = any(dir_path.rglob('*'))
                if has_files:
                    self.data_directories.append((str(dir_path), dir_name))
                    self.log(f"Found optional directory: {dir_name}")
                else:
                    self.log(f"Skipping empty directory: {dir_name}")

        # Special handling for payment module
        payment_dir = self.project_root / 'payment'
        if payment_dir.exists() and payment_dir.is_dir():
            # Check if payment directory has Python files
            has_py_files = any(payment_dir.glob('*.py'))
            if has_py_files:
                self.data_directories.append((str(payment_dir), 'payment'))
                self.log("Found payment module directory")
            else:
                self.log("Skipping payment directory (no Python files found)")

    def _validate_project_components(self):
        """Validate that all critical project components are present."""
        self.log("Validating project components...")

        # Check for main script
        if not (self.project_root / self.main_script).exists():
            self.error(f"Main script '{self.main_script}' not found!")

        # Check for icon file
        icon_path = self.project_root / self.icon_path
        if not icon_path.exists():
            self.warning(f"Icon file '{self.icon_path}' not found. Using default icon.")
            self.icon_path = None

        # Check for essential assets
        assets_dir = self.project_root / "assets"
        if not assets_dir.exists():
            self.error("Assets directory not found!")

        # Check for audio files (critical for the game)
        audio_dirs = [
            assets_dir / "audio-effects",
            assets_dir / "cartella-announcer",
            assets_dir / "cartella-cancellation-announcements"
        ]

        for audio_dir in audio_dirs:
            if not audio_dir.exists():
                self.warning(f"Audio directory '{audio_dir.name}' not found!")

        self.log("Project validation completed")

    def check_prerequisites(self):
        """Check that all build prerequisites are met."""
        self.log("Checking build prerequisites...")

        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 7):
            self.error("Python 3.7 or higher is required!")

        self.log(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

        # Check if Nuitka is installed
        try:
            result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                                  capture_output=True, text=True, check=True)
            nuitka_version = result.stdout.strip()
            self.log(f"Nuitka version: {nuitka_version}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.error("Nuitka is not installed! Install it with: pip install nuitka")

        # Check for required packages
        required_packages = ['pygame', 'pyperclip']
        missing_packages = []

        for package in required_packages:
            try:
                importlib.import_module(package)
                self.log(f"Found required package: {package}")
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            self.error(f"Missing required packages: {', '.join(missing_packages)}")

        # Check available disk space (at least 1GB recommended)
        try:
            disk_usage = shutil.disk_usage(self.project_root)
            free_space_gb = disk_usage.free / (1024**3)
            if free_space_gb < 1.0:
                self.warning(f"Low disk space: {free_space_gb:.1f}GB available")
            else:
                self.log(f"Available disk space: {free_space_gb:.1f}GB")
        except Exception:
            self.warning("Could not check disk space")

        self.log("Prerequisites check completed")

    def prepare_build_environment(self):
        """Prepare the build environment."""
        self.log("Preparing build environment...")

        # Clean build directories if requested
        if self.clean_build:
            self.log("Cleaning build directories...")
            for dir_path in [self.build_dir, self.dist_dir]:
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    self.log(f"Cleaned {dir_path}")

        # Create build directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

        # Create temporary directory for build preparation
        self.temp_dir = Path(tempfile.mkdtemp(prefix="nuitka_build_"))
        self.log(f"Created temporary directory: {self.temp_dir}")

        # Clean application data for fresh build
        self._clean_application_data()

    def _clean_application_data(self):
        """Clean application data for a fresh build."""
        self.log("Cleaning application data...")

        clean_script = self.project_root / "clean_app.py"
        if clean_script.exists():
            try:
                result = subprocess.run([sys.executable, str(clean_script), '--silent'],
                                      capture_output=True, text=True, cwd=self.project_root)
                if result.returncode == 0:
                    self.log("Application data cleaned successfully")
                else:
                    self.warning("Could not clean application data")
            except Exception as e:
                self.warning(f"Error cleaning application data: {e}")
        else:
            self.log("No clean script found, skipping data cleanup")

    def build_executable(self):
        """Build the executable using Nuitka."""
        self.log(f"Building executable in {self.build_mode} mode...")

        # Prepare Nuitka command
        nuitka_cmd = self._prepare_nuitka_command()

        # Execute build
        self.log("Starting Nuitka compilation...")
        self.log(f"Command: {' '.join(nuitka_cmd)}")

        start_time = time.time()

        try:
            # Run Nuitka with real-time output
            process = subprocess.Popen(
                nuitka_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=self.project_root,
                bufsize=1,
                universal_newlines=True
            )

            # Stream output in real-time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    if self.verbose:
                        print(output.strip())
                    elif any(keyword in output.lower() for keyword in ['error', 'warning', 'failed']):
                        print(output.strip())

            return_code = process.poll()

            if return_code != 0:
                self.error(f"Nuitka compilation failed with return code {return_code}")

        except Exception as e:
            self.error(f"Error during compilation: {e}")

        build_time = time.time() - start_time
        self.log(f"Compilation completed in {build_time:.1f} seconds")

        # Verify build output
        self._verify_build_output()

    def _prepare_nuitka_command(self) -> List[str]:
        """Prepare the Nuitka command with all necessary arguments."""
        cmd = [sys.executable, '-m', 'nuitka']

        # Basic compilation options
        cmd.extend([
            '--standalone',
            '--onefile',
            f'--output-filename={self.executable_name}',
            f'--output-dir={self.build_dir}',
            '--assume-yes-for-downloads',
            '--windows-console-mode=disable',  # Updated console option
        ])

        # Platform-specific options
        if self.is_windows:
            if self.icon_path:
                cmd.append(f'--windows-icon-from-ico={self.icon_path}')
            cmd.extend([
                f'--windows-company-name={self.project_name}',
                f'--windows-product-name={self.project_name}',
                f'--windows-file-version={self.project_version}',
                f'--windows-product-version={self.project_version}',
                '--windows-file-description=WOW Bingo Game - Professional Bingo Gaming Application',
            ])

        # Build mode specific options
        if self.build_mode == 'debug':
            cmd.extend([
                '--debug',
                '--show-memory',
                '--show-progress',
                '--verbose'
            ])
        elif self.build_mode == 'optimized':
            cmd.extend([
                '--lto=no',  # Disable LTO to avoid Clang issues
                '--jobs=4',  # Use limited cores to avoid memory issues
                '--show-progress',
            ])
            if self.is_windows:
                cmd.extend([
                    '--msvc=latest',
                ])
        else:  # release mode
            cmd.extend([
                '--show-progress',
                '--jobs=4',  # Use limited cores
                '--lto=no',  # Disable LTO by default
            ])
            if self.is_windows:
                cmd.extend([
                    '--msvc=latest',
                ])

        # Include data directories
        for source_dir, target_dir in self.data_directories:
            cmd.append(f'--include-data-dir={source_dir}={target_dir}')

        # Include Python packages (filter out problematic ones)
        problematic_packages = {
            'kivy', 'flask', 'matplotlib', 'sqlalchemy', 'bcrypt',
            'ttkbootstrap', 'wmi', 'libs', 'winreg', 'PIL'
        }

        safe_packages = self.python_packages - problematic_packages
        for package in sorted(safe_packages):
            # Only include if the package actually exists
            try:
                spec = importlib.util.find_spec(package)
                if spec is not None:
                    cmd.append(f'--include-package={package}')
                else:
                    self.log(f"Skipping non-existent package: {package}", "WARNING")
            except (ImportError, ModuleNotFoundError, ValueError):
                self.log(f"Skipping problematic package: {package}", "WARNING")

        # Include system modules (filter out problematic ones)
        problematic_modules = {
            'winreg', 'py_compile', 'tokenize', 'wmi'
        }

        safe_modules = self.system_modules - problematic_modules
        for module in sorted(safe_modules):
            # Only include if the module actually exists
            try:
                spec = importlib.util.find_spec(module)
                if spec is not None:
                    cmd.append(f'--include-package={module}')
                else:
                    self.log(f"Skipping non-existent module: {module}", "WARNING")
            except (ImportError, ModuleNotFoundError, ValueError):
                self.log(f"Skipping problematic module: {module}", "WARNING")

        # Enable necessary plugins (only non-default ones)
        plugins = []
        if 'tkinter' in self.python_packages or 'tkinter' in self.system_modules:
            plugins.append('tk-inter')

        for plugin in plugins:
            cmd.append(f'--enable-plugin={plugin}')

        # Add main script
        cmd.append(self.main_script)

        return cmd

    def _verify_build_output(self):
        """Verify that the build output is correct and functional."""
        self.log("Verifying build output...")

        # Find the executable
        if self.is_windows:
            executable_name = f"{self.executable_name}.exe"
        else:
            executable_name = self.executable_name

        executable_path = self.build_dir / executable_name

        if not executable_path.exists():
            self.error(f"Executable not found at {executable_path}")

        # Check file size
        file_size = executable_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        self.log(f"Executable size: {file_size_mb:.1f} MB")

        if file_size_mb < 10:
            self.warning("Executable seems unusually small - some dependencies might be missing")
        elif file_size_mb > 500:
            self.warning("Executable is very large - consider optimizing dependencies")

        # Copy to dist directory
        dist_executable = self.dist_dir / executable_name
        shutil.copy2(executable_path, dist_executable)
        self.log(f"Executable copied to: {dist_executable}")

        # Make executable on Unix systems
        if not self.is_windows:
            os.chmod(dist_executable, 0o755)

        self.log("Build verification completed successfully")
        return dist_executable

    def test_executable(self, executable_path: Path):
        """Test the built executable to ensure it works correctly."""
        self.log("Testing executable...")

        if not executable_path.exists():
            self.error(f"Executable not found: {executable_path}")

        # Basic test: try to run the executable with a timeout
        try:
            self.log("Starting executable test (will timeout after 10 seconds)...")

            # Run the executable with a timeout
            process = subprocess.Popen(
                [str(executable_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait for a short time to see if it starts properly
            try:
                stdout, stderr = process.communicate(timeout=10)
                return_code = process.returncode

                if return_code == 0:
                    self.log("Executable test completed successfully")
                else:
                    self.warning(f"Executable exited with code {return_code}")
                    if stderr:
                        self.warning(f"Error output: {stderr}")

            except subprocess.TimeoutExpired:
                # This is expected for a GUI application
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()

                self.log("Executable started successfully (terminated after timeout)")

        except Exception as e:
            self.warning(f"Could not test executable: {e}")

    def cleanup(self):
        """Clean up temporary files and directories."""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
                self.log(f"Cleaned up temporary directory: {self.temp_dir}")
            except Exception as e:
                self.warning(f"Could not clean up temporary directory: {e}")

    def generate_build_report(self, executable_path: Path, build_time: float):
        """Generate a comprehensive build report."""
        report = {
            "build_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "project_name": self.project_name,
                "project_version": self.project_version,
                "build_mode": self.build_mode,
                "build_time_seconds": round(build_time, 2),
                "platform": platform.platform(),
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            },
            "executable_info": {
                "path": str(executable_path),
                "size_bytes": executable_path.stat().st_size if executable_path.exists() else 0,
                "size_mb": round(executable_path.stat().st_size / (1024*1024), 2) if executable_path.exists() else 0
            },
            "dependencies": {
                "python_packages": sorted(list(self.python_packages)),
                "system_modules": sorted(list(self.system_modules)),
                "local_modules": sorted(list(self.local_modules)),
                "data_directories": [{"source": src, "target": tgt} for src, tgt in self.data_directories]
            }
        }

        report_path = self.dist_dir / "build_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)

        self.log(f"Build report saved to: {report_path}")
        return report

    def run_build(self):
        """Run the complete build process."""
        total_start_time = time.time()

        try:
            self.log("="*60)
            self.log(f"Starting {self.project_name} build process")
            self.log("="*60)

            # Step 1: Check prerequisites
            self.check_prerequisites()

            # Step 2: Analyze project
            self.analyze_project_structure()

            # Step 3: Prepare build environment
            self.prepare_build_environment()

            # Step 4: Build executable
            self.build_executable()

            # Step 5: Verify and get executable path
            executable_path = self._verify_build_output()

            # Step 6: Test executable if requested
            if self.test_executable:
                self.test_executable(executable_path)

            # Step 7: Generate build report
            total_build_time = time.time() - total_start_time
            self.generate_build_report(executable_path, total_build_time)

            # Success message
            self.log("="*60)
            self.log("BUILD COMPLETED SUCCESSFULLY!")
            self.log("="*60)
            self.log(f"Executable: {executable_path}")
            self.log(f"Total build time: {total_build_time:.1f} seconds")
            self.log(f"File size: {executable_path.stat().st_size / (1024*1024):.1f} MB")
            self.log("="*60)

            return True

        except Exception as e:
            self.log("="*60)
            self.log("BUILD FAILED!")
            self.log("="*60)
            self.error(f"Build process failed: {e}")

        finally:
            self.cleanup()


def main():
    """Main entry point for the build script."""
    parser = argparse.ArgumentParser(
        description="Comprehensive Nuitka build script for WOW Bingo Game",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python nuitka_comprehensive_build.py                    # Basic release build
  python nuitka_comprehensive_build.py --mode debug      # Debug build with symbols
  python nuitka_comprehensive_build.py --mode optimized  # Optimized build with LTO
  python nuitka_comprehensive_build.py --clean --test    # Clean build and test executable
  python nuitka_comprehensive_build.py --verbose         # Verbose output
        """
    )

    parser.add_argument(
        '--mode',
        choices=['debug', 'release', 'optimized'],
        default='release',
        help='Build mode (default: release)'
    )

    parser.add_argument(
        '--clean',
        action='store_true',
        help='Clean build directories before building'
    )

    parser.add_argument(
        '--test',
        action='store_true',
        help='Test the executable after building'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    # Create build system instance
    build_system = NuitkaBuildSystem()

    # Configure build system
    build_system.build_mode = args.mode
    build_system.clean_build = args.clean
    build_system.test_executable = args.test
    build_system.verbose = args.verbose

    # Run the build
    success = build_system.run_build()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
