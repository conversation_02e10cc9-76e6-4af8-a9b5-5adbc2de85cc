import re

def fix_reset_method():
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # Find the problematic part
    for i in range(len(lines)):
        if "# Reset prize pool" in lines[i]:
            # The next line should be the one with indentation problem
            # Check if it has extra indentation
            if lines[i+1].startswith("                self.calculate_prize_pool()"):
                # Fix indentation
                lines[i+1] = "        self.calculate_prize_pool()\n"
                print(f"Fixed indentation at line {i+1}")
                break
    
    # Write back to file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.writelines(lines)
    
    print("Reset method fixed successfully!")

if __name__ == "__main__":
    fix_reset_method() 