import re

def fix_try_except_blocks():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Fix the try block without except or finally clause (around line 5417)
    pattern1 = r'(try:\s+import json\s+import os\s+)'
    replacement1 = r'\1try:\n            '
    
    # Fix the misplaced except at line 5440
    pattern2 = r'(# We\'ll continue to the deterministic generation below\s+)except Exception as e:'
    replacement2 = r'\1except Exception as e:\n                        pass\n                except Exception as e:'
    
    # Apply the fixes
    new_content = re.sub(pattern1, replacement1, content)
    new_content = re.sub(pattern2, replacement2, new_content)
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Try-except blocks fixed successfully!")

if __name__ == "__main__":
    fix_try_except_blocks() 