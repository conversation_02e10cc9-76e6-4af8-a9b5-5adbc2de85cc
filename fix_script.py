#!/usr/bin/env python

with open("Board_selection_fixed.py", "r", encoding="utf-8") as f:
    lines = f.readlines()

for i, line in enumerate(lines):
    if "self.prize_pool = 0" in line:
        # Insert the missing attribute initialization after this line
        lines.insert(i + 1, "        self.prize_pool_manual_override = False  # Initialize to prevent AttributeError\n")
        break

with open("Board_selection_fixed.py", "w", encoding="utf-8") as f:
    f.writelines(lines)

print("Fix applied successfully!") 