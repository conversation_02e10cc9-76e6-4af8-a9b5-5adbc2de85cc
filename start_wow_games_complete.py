"""
WOW Games Complete Startup Script

This script automatically starts all components of the WOW Games application
with full RethinkDB integration in the correct order:

1. Start RethinkDB server
2. Initialize database
3. Start web dashboard
4. Run the game with full integration

Usage: python start_wow_games_complete.py
"""

import os
import sys
import time
import subprocess
import threading
import signal
import webbrowser
from datetime import datetime

class WOWGamesLauncher:
    """Complete launcher for WOW Games with RethinkDB integration."""

    def __init__(self):
        """Initialize the launcher."""
        self.processes = []
        self.rethinkdb_process = None
        self.dashboard_process = None
        self.game_process = None
        self.startup_log = []

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.startup_log.append(log_entry)

    def run_command(self, command, description, wait=True, timeout=30):
        """
        Run a command and return the result.

        Args:
            command (list): Command to run
            description (str): Description for logging
            wait (bool): Whether to wait for completion
            timeout (int): Timeout in seconds

        Returns:
            subprocess.Popen or bool: Process object or success status
        """
        try:
            self.log(f"Starting: {description}")

            if wait:
                # Run and wait for completion
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )

                if result.returncode == 0:
                    self.log(f"✅ {description} completed successfully")
                    return True
                else:
                    self.log(f"❌ {description} failed: {result.stderr}", "ERROR")
                    return False
            else:
                # Run in background
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                self.processes.append(process)
                self.log(f"🚀 {description} started in background (PID: {process.pid})")
                return process

        except subprocess.TimeoutExpired:
            self.log(f"⏰ {description} timed out after {timeout} seconds", "WARNING")
            return False
        except FileNotFoundError:
            self.log(f"❌ Command not found for: {description}", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Error running {description}: {e}", "ERROR")
            return False

    def check_rethinkdb_status(self):
        """Check if RethinkDB is running."""
        try:
            result = subprocess.run(
                [sys.executable, "setup_rethinkdb.py", "--check"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return "RethinkDB is running" in result.stdout
        except:
            return False

    def wait_for_rethinkdb(self, max_attempts=10):
        """Wait for RethinkDB to be ready."""
        self.log("Waiting for RethinkDB to be ready...")

        for attempt in range(max_attempts):
            if self.check_rethinkdb_status():
                self.log("✅ RethinkDB is ready!")
                return True

            self.log(f"⏳ Waiting for RethinkDB... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)

        self.log("❌ RethinkDB failed to start within timeout", "ERROR")
        return False

    def start_rethinkdb(self):
        """Start RethinkDB server."""
        self.log("=" * 60)
        self.log("STEP 1: Starting RethinkDB Server")
        self.log("=" * 60)

        # Check if already running
        if self.check_rethinkdb_status():
            self.log("✅ RethinkDB is already running")
            return True

        # Start RethinkDB
        success = self.run_command(
            [sys.executable, "setup_rethinkdb.py", "--start"],
            "RethinkDB server startup",
            wait=True,
            timeout=45
        )

        if success:
            # Wait for RethinkDB to be ready
            return self.wait_for_rethinkdb()

        return False

    def initialize_database(self):
        """Initialize RethinkDB database."""
        self.log("=" * 60)
        self.log("STEP 2: Initializing Database")
        self.log("=" * 60)

        # Check if database is already initialized
        try:
            result = subprocess.run(
                [sys.executable, "setup_rethinkdb.py", "--test"],
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                self.log("✅ Database is already initialized")
                return True
        except:
            pass

        # Initialize database with longer timeout
        return self.run_command(
            [sys.executable, "setup_rethinkdb.py", "--init"],
            "Database initialization",
            wait=True,
            timeout=60  # Increased timeout
        )

    def start_web_dashboard(self):
        """Start the web dashboard."""
        self.log("=" * 60)
        self.log("STEP 3: Starting Web Dashboard")
        self.log("=" * 60)

        self.dashboard_process = self.run_command(
            [sys.executable, "rethink_dashboard_fixed.py"],
            "Web dashboard",
            wait=False
        )

        if self.dashboard_process:
            # Wait a moment for dashboard to start
            time.sleep(3)
            self.log("🌐 Web dashboard should be available at: http://localhost:5000")
            return True

        return False

    def start_game(self):
        """Start the main game."""
        self.log("=" * 60)
        self.log("STEP 4: Starting WOW Games Application")
        self.log("=" * 60)

        self.game_process = self.run_command(
            [sys.executable, "main.py"],
            "WOW Games application",
            wait=False
        )

        if self.game_process:
            self.log("🎮 WOW Games application started successfully!")
            return True

        return False

    def open_dashboard_in_browser(self):
        """Open the web dashboard in the default browser."""
        try:
            time.sleep(2)  # Give dashboard time to fully start
            webbrowser.open("http://localhost:5000")
            self.log("🌐 Opened web dashboard in browser")
        except Exception as e:
            self.log(f"⚠️ Could not open browser: {e}", "WARNING")

    def run_validation(self):
        """Run quick validation to ensure everything is working."""
        self.log("=" * 60)
        self.log("RUNNING QUICK VALIDATION")
        self.log("=" * 60)

        try:
            result = subprocess.run(
                [sys.executable, "comprehensive_rethinkdb_test.py"],
                input="2\n",  # Choose quick validation
                capture_output=True,
                text=True,
                timeout=30
            )

            if "🎉 All components are available and ready!" in result.stdout:
                self.log("✅ All components validated successfully!")
                return True
            else:
                self.log("⚠️ Some components may have issues", "WARNING")
                return False

        except Exception as e:
            self.log(f"⚠️ Validation failed: {e}", "WARNING")
            return False

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.log("\n🛑 Shutdown signal received. Cleaning up...")
        self.cleanup()
        sys.exit(0)

    def cleanup(self):
        """Clean up all processes."""
        self.log("🧹 Cleaning up processes...")

        # Terminate all background processes
        for process in self.processes:
            try:
                if process.poll() is None:  # Process is still running
                    process.terminate()
                    process.wait(timeout=5)
                    self.log(f"✅ Terminated process {process.pid}")
            except Exception as e:
                self.log(f"⚠️ Error terminating process: {e}", "WARNING")

        self.log("✅ Cleanup completed")

    def print_startup_summary(self):
        """Print a summary of the startup process."""
        self.log("=" * 80)
        self.log("🎉 WOW GAMES STARTUP COMPLETED!")
        self.log("=" * 80)
        self.log("")
        self.log("📋 SERVICES RUNNING:")
        self.log("   🗄️  RethinkDB Server: http://localhost:8081 (Admin)")
        self.log("   🌐 Web Dashboard: http://localhost:5000")
        self.log("   🎮 WOW Games: Main application window")
        self.log("")
        self.log("🔧 MANAGEMENT:")
        self.log("   • Use the web dashboard to monitor performance")
        self.log("   • Check RethinkDB admin for database status")
        self.log("   • Press Ctrl+C to shutdown all services")
        self.log("")
        self.log("📊 QUICK LINKS:")
        self.log("   • Performance Monitor: http://localhost:5000/performance")
        self.log("   • Backup Manager: http://localhost:5000/backups")
        self.log("   • Sync Status: http://localhost:5000/sync-status")
        self.log("=" * 80)

    def run(self):
        """Run the complete startup sequence."""
        self.log("🚀 STARTING WOW GAMES WITH RETHINKDB INTEGRATION")
        self.log("=" * 80)

        try:
            # Step 1: Start RethinkDB
            if not self.start_rethinkdb():
                self.log("❌ Failed to start RethinkDB. Aborting.", "ERROR")
                return False

            # Step 2: Initialize database
            if not self.initialize_database():
                self.log("❌ Failed to initialize database. Aborting.", "ERROR")
                return False

            # Step 3: Start web dashboard
            if not self.start_web_dashboard():
                self.log("❌ Failed to start web dashboard. Aborting.", "ERROR")
                return False

            # Step 4: Start the game
            if not self.start_game():
                self.log("❌ Failed to start game. Aborting.", "ERROR")
                return False

            # Step 5: Open dashboard in browser
            threading.Thread(target=self.open_dashboard_in_browser, daemon=True).start()

            # Step 6: Run validation
            self.run_validation()

            # Print summary
            self.print_startup_summary()

            # Keep the script running
            self.log("✅ All services started successfully!")
            self.log("Press Ctrl+C to shutdown all services...")

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass

            return True

        except Exception as e:
            self.log(f"❌ Startup failed: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    """Main entry point."""
    print("WOW Games Complete Launcher")
    print("=" * 50)

    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found. Please run this script from the game directory.")
        sys.exit(1)

    # Create and run launcher
    launcher = WOWGamesLauncher()
    success = launcher.run()

    if not success:
        print("\n❌ Startup failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
