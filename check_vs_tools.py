#!/usr/bin/env python3
"""
Visual Studio Build Tools Detection Script
==========================================

This script checks if Visual Studio Build Tools are properly installed
and detectable by Python build systems like Nuitka.
"""

import os
import sys
import subprocess
from pathlib import Path

def log(message, level="INFO"):
    """Log a message."""
    print(f"[{level}] {message}")

def check_vs_installations():
    """Check for Visual Studio installations."""
    log("Checking Visual Studio installations...")
    
    # Common Visual Studio paths
    vs_paths = [
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\BuildTools",
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community",
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional",
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community",
    ]
    
    found_installations = []
    
    for vs_path in vs_paths:
        if Path(vs_path).exists():
            found_installations.append(vs_path)
            log(f"✅ Found: {vs_path}")
    
    if not found_installations:
        log("❌ No Visual Studio installations found in common locations")
        return False
    
    return found_installations

def check_msvc_compiler():
    """Check for MSVC compiler."""
    log("Checking MSVC compiler...")
    
    # Common MSVC paths
    msvc_paths = [
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC",
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC",
    ]
    
    for msvc_path in msvc_paths:
        if Path(msvc_path).exists():
            # List MSVC versions
            versions = [d.name for d in Path(msvc_path).iterdir() if d.is_dir()]
            if versions:
                log(f"✅ Found MSVC at: {msvc_path}")
                log(f"   Versions: {', '.join(versions)}")
                return True
    
    log("❌ MSVC compiler not found")
    return False

def check_windows_sdk():
    """Check for Windows SDK."""
    log("Checking Windows SDK...")
    
    sdk_paths = [
        "C:\\Program Files (x86)\\Windows Kits\\10",
        "C:\\Program Files\\Windows Kits\\10",
    ]
    
    for sdk_path in sdk_paths:
        if Path(sdk_path).exists():
            # Check for include and lib directories
            include_path = Path(sdk_path) / "Include"
            lib_path = Path(sdk_path) / "Lib"
            
            if include_path.exists() and lib_path.exists():
                # List SDK versions
                versions = [d.name for d in include_path.iterdir() if d.is_dir() and d.name.startswith("10.")]
                if versions:
                    log(f"✅ Found Windows SDK at: {sdk_path}")
                    log(f"   Versions: {', '.join(versions)}")
                    return True
    
    log("❌ Windows SDK not found")
    return False

def check_environment_variables():
    """Check relevant environment variables."""
    log("Checking environment variables...")
    
    important_vars = [
        "VS160COMNTOOLS",
        "VS170COMNTOOLS", 
        "VCINSTALLDIR",
        "WindowsSdkDir",
        "PATH",
    ]
    
    for var in important_vars:
        value = os.environ.get(var)
        if value:
            log(f"✅ {var}: {value}")
        else:
            log(f"❌ {var}: Not set")

def check_cl_compiler():
    """Check if cl.exe (MSVC compiler) is accessible."""
    log("Checking cl.exe accessibility...")
    
    try:
        result = subprocess.run(['cl'], capture_output=True, text=True)
        if "Microsoft (R) C/C++ Optimizing Compiler" in result.stderr:
            log("✅ cl.exe is accessible and working")
            return True
        else:
            log("❌ cl.exe found but not working properly")
            return False
    except FileNotFoundError:
        log("❌ cl.exe not found in PATH")
        return False

def check_vcvarsall():
    """Check for vcvarsall.bat."""
    log("Checking vcvarsall.bat...")
    
    vcvarsall_paths = [
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvarsall.bat",
        "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\Build\\vcvarsall.bat",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvarsall.bat",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Auxiliary\\Build\\vcvarsall.bat",
    ]
    
    for vcvarsall_path in vcvarsall_paths:
        if Path(vcvarsall_path).exists():
            log(f"✅ Found vcvarsall.bat at: {vcvarsall_path}")
            return vcvarsall_path
    
    log("❌ vcvarsall.bat not found")
    return None

def test_nuitka_detection():
    """Test if Nuitka can detect the compiler."""
    log("Testing Nuitka compiler detection...")
    
    try:
        # Try to get Nuitka to show compiler info
        result = subprocess.run([
            sys.executable, '-m', 'nuitka', 
            '--show-scons', '--assume-yes-for-downloads'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            log("✅ Nuitka can detect build tools")
            return True
        else:
            log("❌ Nuitka cannot detect build tools")
            log(f"   Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        log("❌ Nuitka detection timed out")
        return False
    except Exception as e:
        log(f"❌ Error testing Nuitka: {e}")
        return False

def generate_fix_suggestions(vs_found, msvc_found, sdk_found, cl_found, vcvarsall_path):
    """Generate fix suggestions based on findings."""
    log("\n" + "="*60)
    log("FIX SUGGESTIONS")
    log("="*60)
    
    if not vs_found:
        log("🔧 Install Visual Studio Build Tools:")
        log("   1. Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
        log("   2. Install 'C++ build tools' workload")
        log("   3. Include 'MSVC v143 - VS 2022 C++ x64/x86 build tools'")
        log("   4. Include 'Windows 10/11 SDK'")
    
    elif not msvc_found:
        log("🔧 MSVC compiler missing:")
        log("   1. Run Visual Studio Installer")
        log("   2. Modify your installation")
        log("   3. Add 'MSVC v143 - VS 2022 C++ x64/x86 build tools'")
    
    elif not sdk_found:
        log("🔧 Windows SDK missing:")
        log("   1. Run Visual Studio Installer")
        log("   2. Modify your installation") 
        log("   3. Add 'Windows 10 SDK' or 'Windows 11 SDK'")
    
    elif not cl_found and vcvarsall_path:
        log("🔧 Compiler not in PATH, but vcvarsall.bat found:")
        log(f"   1. Run this command before building:")
        log(f'      "{vcvarsall_path}" x64')
        log("   2. Then run your Nuitka build in the same command prompt")
        log("   3. Or use the fix script below")
    
    else:
        log("🔧 Try these solutions:")
        log("   1. Restart your command prompt/IDE")
        log("   2. Run as Administrator")
        log("   3. Use the environment setup script below")

def main():
    """Main function."""
    print("="*60)
    print("Visual Studio Build Tools Detection")
    print("="*60)
    
    vs_found = check_vs_installations()
    msvc_found = check_msvc_compiler()
    sdk_found = check_windows_sdk()
    check_environment_variables()
    cl_found = check_cl_compiler()
    vcvarsall_path = check_vcvarsall()
    nuitka_works = test_nuitka_detection()
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Visual Studio Found: {'✅ YES' if vs_found else '❌ NO'}")
    print(f"MSVC Compiler Found: {'✅ YES' if msvc_found else '❌ NO'}")
    print(f"Windows SDK Found: {'✅ YES' if sdk_found else '❌ NO'}")
    print(f"cl.exe Accessible: {'✅ YES' if cl_found else '❌ NO'}")
    print(f"vcvarsall.bat Found: {'✅ YES' if vcvarsall_path else '❌ NO'}")
    print(f"Nuitka Detection: {'✅ WORKS' if nuitka_works else '❌ FAILS'}")
    
    generate_fix_suggestions(vs_found, msvc_found, sdk_found, cl_found, vcvarsall_path)
    
    if vcvarsall_path and not cl_found:
        print("\n" + "="*60)
        print("QUICK FIX SCRIPT")
        print("="*60)
        print("Create a file called 'build_with_vs.bat' with this content:")
        print()
        print(f'@echo off')
        print(f'call "{vcvarsall_path}" x64')
        print(f'python nuitka_build_simple.py --verbose')
        print(f'pause')
        print()
        print("Then run 'build_with_vs.bat' instead of the regular build script.")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
