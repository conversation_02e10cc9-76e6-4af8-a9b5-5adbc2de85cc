"""
Sync Manager for the WOW Games application.

This module handles the synchronization between the local SQLite database
and the remote RethinkDB database, enabling offline-to-online capabilities.
"""

import os
import json
import time
import logging
import threading
import sqlite3
import queue
from datetime import datetime

# Try importing rethinkdb with proper error handling
try:
    import rethinkdb
    r = rethinkdb.r
    from rethinkdb.errors import ReqlDriverError, ReqlTimeoutError
    RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False
    print("RethinkDB module not available for sync manager")

# Import local and remote database managers
from stats_db import get_stats_db_manager
from rethink_db import get_rethink_db_manager

# Import configuration
from rethink_config import (
    RETHINKDB_DB,
    SYNC_INTERVAL,
    MAX_SYNC_RETRIES,
    SYNC_BATCH_SIZE,
    SYNC_TABLES,
    SYNC_CACHE_DIR
)

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'sync_manager.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SyncManager:
    """Manager for synchronizing data between SQLite and RethinkDB."""

    def __init__(self):
        """Initialize the sync manager."""
        self.local_db = get_stats_db_manager()
        self.remote_db = None

        # Only initialize RethinkDB if available
        if RETHINKDB_AVAILABLE:
            try:
                self.remote_db = get_rethink_db_manager()
            except Exception as e:
                logging.error(f"Failed to initialize RethinkDB manager: {e}")
                self.remote_db = None

        self.sync_queue = queue.Queue()
        self.sync_thread = None
        self.sync_running = False
        self.last_sync_time = {}
        self.sync_stats = {
            'total_synced': 0,
            'last_sync_duration': 0,
            'sync_errors': 0,
            'last_error': None
        }

        # Real-time sync features
        self.real_time_enabled = True
        self.batch_sync_enabled = True
        self.conflict_resolution = 'timestamp'  # 'timestamp', 'remote_wins', 'local_wins'

        # Load last sync times from disk
        self._load_sync_metadata()

        # Start background sync thread if RethinkDB is available
        if self.remote_db:
            self._start_sync_thread()

        logging.info(f"Sync manager initialized (RethinkDB available: {RETHINKDB_AVAILABLE})")

    def _load_sync_metadata(self):
        """Load sync metadata from disk."""
        metadata_file = os.path.join(SYNC_CACHE_DIR, 'sync_metadata.json')
        if os.path.exists(metadata_file):
            try:
                with open(metadata_file, 'r') as f:
                    self.last_sync_time = json.load(f)
                logging.info(f"Loaded sync metadata: {self.last_sync_time}")
            except Exception as e:
                logging.error(f"Error loading sync metadata: {e}")
                self.last_sync_time = {table: 0 for table in SYNC_TABLES}
        else:
            self.last_sync_time = {table: 0 for table in SYNC_TABLES}

    def _save_sync_metadata(self):
        """Save sync metadata to disk."""
        metadata_file = os.path.join(SYNC_CACHE_DIR, 'sync_metadata.json')
        try:
            with open(metadata_file, 'w') as f:
                json.dump(self.last_sync_time, f)
            logging.info(f"Saved sync metadata: {self.last_sync_time}")
        except Exception as e:
            logging.error(f"Error saving sync metadata: {e}")

    def _start_sync_thread(self):
        """Start the background sync thread."""
        def sync_worker():
            self.sync_running = True
            logging.info("Sync thread started")

            while self.sync_running:
                try:
                    # Check if we're online
                    if self.remote_db.is_connected():
                        # Process any pending sync operations from the queue
                        self._process_sync_queue()

                        # Perform periodic sync
                        self._sync_all_tables()
                    else:
                        # Try to connect
                        self.remote_db.connect()

                    # Sleep for the sync interval
                    time.sleep(SYNC_INTERVAL)
                except Exception as e:
                    logging.error(f"Error in sync thread: {e}")
                    time.sleep(SYNC_INTERVAL)

            logging.info("Sync thread stopped")

        self.sync_thread = threading.Thread(target=sync_worker, daemon=True)
        self.sync_thread.start()

    def stop_sync_thread(self):
        """Stop the background sync thread."""
        self.sync_running = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        logging.info("Sync thread stopped")

    def _process_sync_queue(self):
        """Process pending sync operations from the queue."""
        try:
            # Process up to SYNC_BATCH_SIZE items at once
            for _ in range(min(self.sync_queue.qsize(), SYNC_BATCH_SIZE)):
                if not self.remote_db.is_connected():
                    logging.warning("Remote database not connected, aborting sync queue processing")
                    break

                try:
                    # Get an item from the queue (non-blocking)
                    item = self.sync_queue.get_nowait()

                    # Process the item
                    self._sync_item(item)

                    # Mark as done
                    self.sync_queue.task_done()
                except queue.Empty:
                    # Queue is empty, break the loop
                    break
                except Exception as e:
                    logging.error(f"Error processing sync item: {e}")
        except Exception as e:
            logging.error(f"Error processing sync queue: {e}")

    def _sync_item(self, item):
        """
        Sync a single item to the remote database.

        Args:
            item (dict): Item to sync containing operation, table, and data
        """
        operation = item.get('operation')
        table = item.get('table')
        data = item.get('data')

        if not operation or not table or not data:
            logging.error(f"Invalid sync item: {item}")
            return

        try:
            if operation == 'INSERT':
                self.remote_db.insert(table, data)
            elif operation == 'UPDATE':
                self.remote_db.update(table, data.get('id'), data)
            elif operation == 'DELETE':
                self.remote_db.delete(table, data.get('id'))
            else:
                logging.error(f"Unknown operation: {operation}")
        except Exception as e:
            logging.error(f"Error syncing item to remote database: {e}")
            # Re-queue the item for later retry
            self.queue_sync_operation(operation, table, data)

    def _sync_all_tables(self):
        """Synchronize all tables between local and remote databases."""
        current_time = time.time()

        for table in SYNC_TABLES:
            try:
                self._sync_table(table)
                # Update last sync time
                self.last_sync_time[table] = current_time
            except Exception as e:
                logging.error(f"Error syncing table {table}: {e}")

        # Save sync metadata
        self._save_sync_metadata()

    def _sync_table(self, table):
        """
        Synchronize a specific table between local and remote databases.

        Args:
            table (str): Table name to sync
        """
        if not self.remote_db.is_connected():
            logging.warning(f"Remote database not connected, aborting sync for table {table}")
            return

        try:
            # Get last sync time for this table
            last_sync = self.last_sync_time.get(table, 0)
            last_sync_str = datetime.fromtimestamp(last_sync).strftime('%Y-%m-%d %H:%M:%S')

            # SQLite query templates for each table
            queries = {
                'daily_stats': {
                    'query': 'SELECT * FROM daily_stats WHERE date > ?',
                    'params': (last_sync_str,)
                },
                'game_history': {
                    'query': 'SELECT * FROM game_history WHERE date_time > ?',
                    'params': (last_sync_str,)
                },
                'wallet_transactions': {
                    'query': 'SELECT * FROM wallet_transactions WHERE date_time > ?',
                    'params': (last_sync_str,)
                },
                'admin_users': {
                    'query': 'SELECT * FROM admin_users WHERE last_login > ?',
                    'params': (last_sync_str,)
                },
                'audit_log': {
                    'query': 'SELECT * FROM audit_log WHERE timestamp > ?',
                    'params': (last_sync_str,)
                }
            }

            if table not in queries:
                logging.warning(f"No sync query defined for table {table}")
                return

            # Get local changes since last sync
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute(queries[table]['query'], queries[table]['params'])

                # Convert to list of dictionaries
                columns = [col[0] for col in cursor.description]
                local_changes = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # Upload local changes to remote
            if local_changes:
                logging.info(f"Syncing {len(local_changes)} local changes to remote for table {table}")
                for change in local_changes:
                    try:
                        # Check if the record exists in the remote database
                        primary_key = 'id'
                        if table == 'daily_stats':
                            primary_key = 'date'

                        remote_record = self.remote_db.get(table, change[primary_key])

                        if remote_record:
                            # Update existing record
                            self.remote_db.update(table, change[primary_key], change)
                        else:
                            # Insert new record
                            self.remote_db.insert(table, change)
                    except Exception as e:
                        logging.error(f"Error syncing change to remote: {e}")

            # Get remote changes since last sync
            try:
                # Query depends on the table's timestamp field
                timestamp_field = 'date'
                if table != 'daily_stats':
                    timestamp_field = 'date_time' if table != 'admin_users' else 'last_login'

                filter_func = lambda doc: rethinkdb.r.expr(doc[timestamp_field]).gt(last_sync_str)
                remote_changes = self.remote_db.query(table, filter_func=filter_func)

                # Apply remote changes to local
                if remote_changes:
                    logging.info(f"Syncing {len(remote_changes)} remote changes to local for table {table}")
                    for change in remote_changes:
                        # SQLite commands depend on the table
                        self._apply_remote_change_to_local(table, change)
            except Exception as e:
                logging.error(f"Error getting remote changes: {e}")
        except Exception as e:
            logging.error(f"Error syncing table {table}: {e}")

    def _apply_remote_change_to_local(self, table, change):
        """
        Apply a remote change to the local database.

        Args:
            table (str): Table name
            change (dict): Change data from remote
        """
        try:
            with self.local_db.get_connection_context() as conn:
                cursor = conn.cursor()

                # Different logic for each table
                if table == 'daily_stats':
                    date = change.get('date')
                    games_played = change.get('games_played', 0)
                    earnings = change.get('earnings', 0)
                    winners = change.get('winners', 0)
                    total_players = change.get('total_players', 0)

                    # Check if record exists
                    cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date,))
                    if cursor.fetchone():
                        # Update
                        cursor.execute('''
                        UPDATE daily_stats
                        SET games_played = ?, earnings = ?, winners = ?, total_players = ?
                        WHERE date = ?
                        ''', (games_played, earnings, winners, total_players, date))
                    else:
                        # Insert
                        cursor.execute('''
                        INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                        VALUES (?, ?, ?, ?, ?)
                        ''', (date, games_played, earnings, winners, total_players))

                elif table == 'game_history':
                    # Extract fields
                    id = change.get('id')
                    date_time = change.get('date_time')
                    username = change.get('username')
                    house = change.get('house')
                    stake = change.get('stake')
                    players = change.get('players')
                    total_calls = change.get('total_calls')
                    commission_percent = change.get('commission_percent')
                    fee = change.get('fee')
                    total_prize = change.get('total_prize')
                    details = change.get('details')
                    status = change.get('status')

                    # Check if record exists
                    cursor.execute('SELECT * FROM game_history WHERE id = ?', (id,))
                    if cursor.fetchone():
                        # Update
                        cursor.execute('''
                        UPDATE game_history
                        SET date_time = ?, username = ?, house = ?, stake = ?,
                            players = ?, total_calls = ?, commission_percent = ?,
                            fee = ?, total_prize = ?, details = ?, status = ?
                        WHERE id = ?
                        ''', (date_time, username, house, stake, players, total_calls,
                             commission_percent, fee, total_prize, details, status, id))
                    else:
                        # Insert
                        cursor.execute('''
                        INSERT INTO game_history
                        (id, date_time, username, house, stake, players, total_calls,
                         commission_percent, fee, total_prize, details, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (id, date_time, username, house, stake, players, total_calls,
                             commission_percent, fee, total_prize, details, status))

                # Similar logic for other tables...
                # Wallet transactions, admin users, and audit log would follow a similar pattern

                conn.commit()
        except Exception as e:
            logging.error(f"Error applying remote change to local: {e}")

    def queue_sync_operation(self, operation, table, data):
        """
        Queue a sync operation to be processed by the sync thread.

        Args:
            operation (str): Operation type (INSERT, UPDATE, DELETE)
            table (str): Table name
            data (dict): Data to sync
        """
        item = {
            'operation': operation,
            'table': table,
            'data': data,
            'timestamp': time.time()
        }

        # Add to queue
        self.sync_queue.put(item)

        # Also save to disk in case app crashes before sync
        self._save_operation_to_disk(item)

        logging.info(f"Queued sync operation: {operation} on {table}")

    def sync_immediately(self, operation, table, data):
        """
        Perform immediate synchronization for critical operations.

        Args:
            operation (str): Operation type (INSERT, UPDATE, DELETE)
            table (str): Table name
            data (dict): Data to sync

        Returns:
            bool: True if sync was successful, False otherwise
        """
        if not self.real_time_enabled or not self.remote_db:
            return False

        try:
            start_time = time.time()

            if not self.remote_db.is_connected():
                if not self.remote_db.connect():
                    logging.warning("Cannot perform immediate sync: RethinkDB not connected")
                    return False

            success = False
            if operation == 'INSERT':
                result = self.remote_db.insert_record(table, data)
                success = result is not None
            elif operation == 'UPDATE':
                primary_key = self._get_primary_key(table)
                record_id = data.get(primary_key)
                if record_id:
                    result = self.remote_db.update_record(table, record_id, data)
                    success = result is not None
            elif operation == 'DELETE':
                primary_key = self._get_primary_key(table)
                record_id = data.get(primary_key)
                if record_id:
                    result = self.remote_db.delete_record(table, record_id)
                    success = result is not None

            duration = time.time() - start_time

            if success:
                self.sync_stats['total_synced'] += 1
                self.sync_stats['last_sync_duration'] = duration
                logging.info(f"Immediate sync successful: {operation} on {table} (took {duration:.2f}s)")
            else:
                self.sync_stats['sync_errors'] += 1
                self.sync_stats['last_error'] = f"Failed {operation} on {table}"
                logging.error(f"Immediate sync failed: {operation} on {table}")

            return success

        except Exception as e:
            self.sync_stats['sync_errors'] += 1
            self.sync_stats['last_error'] = str(e)
            logging.error(f"Error in immediate sync: {e}")
            return False

    def _get_primary_key(self, table):
        """Get the primary key field name for a table."""
        if table == 'daily_stats':
            return 'date'
        else:
            return 'id'

    def get_sync_status(self):
        """
        Get current synchronization status.

        Returns:
            dict: Sync status information
        """
        status = {
            'connected': self.remote_db.is_connected() if self.remote_db else False,
            'queue_size': self.sync_queue.qsize(),
            'last_sync_times': self.last_sync_time.copy(),
            'stats': self.sync_stats.copy(),
            'real_time_enabled': self.real_time_enabled,
            'batch_sync_enabled': self.batch_sync_enabled,
            'conflict_resolution': self.conflict_resolution
        }

        return status

    def enable_real_time_sync(self, enabled=True):
        """Enable or disable real-time synchronization."""
        self.real_time_enabled = enabled
        logging.info(f"Real-time sync {'enabled' if enabled else 'disabled'}")

    def set_conflict_resolution(self, strategy):
        """
        Set conflict resolution strategy.

        Args:
            strategy (str): 'timestamp', 'remote_wins', or 'local_wins'
        """
        if strategy in ['timestamp', 'remote_wins', 'local_wins']:
            self.conflict_resolution = strategy
            logging.info(f"Conflict resolution set to: {strategy}")
        else:
            logging.error(f"Invalid conflict resolution strategy: {strategy}")

    def force_full_sync(self):
        """Force a complete synchronization of all tables."""
        if not self.remote_db or not self.remote_db.is_connected():
            logging.warning("Cannot perform full sync: RethinkDB not connected")
            return False

        try:
            logging.info("Starting forced full synchronization")
            start_time = time.time()

            self._sync_all_tables()

            duration = time.time() - start_time
            logging.info(f"Full sync completed in {duration:.2f} seconds")
            return True

        except Exception as e:
            logging.error(f"Error in full sync: {e}")
            return False

    def _save_operation_to_disk(self, item):
        """
        Save an operation to disk for durability.

        Args:
            item (dict): Sync operation item
        """
        try:
            # Create directory for table if it doesn't exist
            table_dir = os.path.join(SYNC_CACHE_DIR, item['table'])
            os.makedirs(table_dir, exist_ok=True)

            # Generate a filename with timestamp
            filename = f"{item['timestamp']}_{item['operation']}.json"
            filepath = os.path.join(table_dir, filename)

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(item, f)

            logging.debug(f"Saved operation to disk: {filepath}")
        except Exception as e:
            logging.error(f"Error saving operation to disk: {e}")

    def load_pending_operations(self):
        """
        Load pending operations from disk and add them to the sync queue.

        This should be called on startup to ensure no operations are lost.
        """
        operations_loaded = 0

        try:
            # Iterate through all table directories
            for table in SYNC_TABLES:
                table_dir = os.path.join(SYNC_CACHE_DIR, table)
                if not os.path.exists(table_dir):
                    continue

                # Get all operation files
                files = [f for f in os.listdir(table_dir) if f.endswith('.json')]

                for file in files:
                    try:
                        filepath = os.path.join(table_dir, file)

                        # Load the operation
                        with open(filepath, 'r') as f:
                            item = json.load(f)

                        # Add to queue
                        self.sync_queue.put(item)
                        operations_loaded += 1

                        # Delete the file
                        os.remove(filepath)
                    except Exception as e:
                        logging.error(f"Error loading operation from {filepath}: {e}")

            logging.info(f"Loaded {operations_loaded} pending operations from disk")
        except Exception as e:
            logging.error(f"Error loading pending operations: {e}")

        return operations_loaded

# Singleton instance
_sync_manager = None

def get_sync_manager():
    """
    Get the singleton instance of SyncManager.

    Returns:
        SyncManager: Sync manager instance
    """
    global _sync_manager

    if _sync_manager is None:
        _sync_manager = SyncManager()

    return _sync_manager