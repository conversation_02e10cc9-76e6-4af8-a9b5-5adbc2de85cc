"""
RethinkDB Backup and Recovery Manager for WOW Games application.

This module provides comprehensive backup and recovery capabilities
for both SQLite and RethinkDB databases with automated scheduling.
"""

import os
import json
import time
import shutil
import zipfile
import threading
import logging
import schedule
from datetime import datetime, timedelta

# Try importing rethinkdb
try:
    import rethinkdb
    r = rethinkdb.r
    from rethinkdb.errors import ReqlDriverError, ReqlTimeoutError
    RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False

# Import database managers
from stats_db import get_stats_db_manager
from rethink_db import get_rethink_db_manager

class BackupManager:
    """
    Comprehensive backup and recovery manager for WOW Games databases.
    
    Supports both SQLite and RethinkDB with automated scheduling,
    compression, and integrity verification.
    """
    
    def __init__(self):
        """Initialize the backup manager."""
        self.backup_dir = os.path.join('data', 'backups')
        self.sqlite_db_path = os.path.join('data', 'stats.db')
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Backup configuration
        self.config = {
            'auto_backup_enabled': True,
            'backup_interval_hours': 6,
            'max_backups_to_keep': 10,
            'compress_backups': True,
            'verify_backups': True,
            'backup_rethinkdb': True,
            'backup_sqlite': True
        }
        
        # Initialize database managers
        self.sqlite_manager = get_stats_db_manager()
        self.rethink_manager = None
        
        if RETHINKDB_AVAILABLE:
            try:
                self.rethink_manager = get_rethink_db_manager()
            except Exception as e:
                logging.error(f"Failed to initialize RethinkDB manager for backup: {e}")
        
        # Setup logging
        self.logger = logging.getLogger('backup_manager')
        handler = logging.FileHandler(os.path.join('data', 'backup_manager.log'))
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
        # Schedule automatic backups
        self._setup_automatic_backups()
        
        self.logger.info("Backup manager initialized")
    
    def create_full_backup(self, backup_name=None):
        """
        Create a complete backup of both SQLite and RethinkDB databases.
        
        Args:
            backup_name (str): Optional custom backup name
            
        Returns:
            dict: Backup result information
        """
        if backup_name is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"full_backup_{timestamp}"
        
        backup_path = os.path.join(self.backup_dir, backup_name)
        os.makedirs(backup_path, exist_ok=True)
        
        result = {
            'backup_name': backup_name,
            'backup_path': backup_path,
            'timestamp': time.time(),
            'sqlite_backup': None,
            'rethinkdb_backup': None,
            'compressed_file': None,
            'success': False
        }
        
        try:
            self.logger.info(f"Starting full backup: {backup_name}")
            
            # Backup SQLite database
            if self.config['backup_sqlite']:
                sqlite_result = self._backup_sqlite(backup_path)
                result['sqlite_backup'] = sqlite_result
                
            # Backup RethinkDB database
            if self.config['backup_rethinkdb'] and self.rethink_manager:
                rethink_result = self._backup_rethinkdb(backup_path)
                result['rethinkdb_backup'] = rethink_result
            
            # Create backup metadata
            metadata = {
                'backup_name': backup_name,
                'created_at': datetime.now().isoformat(),
                'sqlite_included': self.config['backup_sqlite'],
                'rethinkdb_included': self.config['backup_rethinkdb'],
                'config': self.config.copy()
            }
            
            metadata_path = os.path.join(backup_path, 'backup_metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Compress backup if enabled
            if self.config['compress_backups']:
                compressed_file = self._compress_backup(backup_path)
                result['compressed_file'] = compressed_file
                
                # Remove uncompressed directory
                shutil.rmtree(backup_path)
                result['backup_path'] = compressed_file
            
            # Verify backup integrity
            if self.config['verify_backups']:
                verification_result = self._verify_backup(result['backup_path'])
                result['verification'] = verification_result
            
            result['success'] = True
            self.logger.info(f"Full backup completed successfully: {backup_name}")
            
            # Clean up old backups
            self._cleanup_old_backups()
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"Full backup failed: {e}")
        
        return result
    
    def _backup_sqlite(self, backup_path):
        """
        Backup SQLite database.
        
        Args:
            backup_path (str): Path to backup directory
            
        Returns:
            dict: Backup result
        """
        try:
            if not os.path.exists(self.sqlite_db_path):
                return {'success': False, 'error': 'SQLite database file not found'}
            
            # Copy SQLite database file
            sqlite_backup_path = os.path.join(backup_path, 'stats.db')
            shutil.copy2(self.sqlite_db_path, sqlite_backup_path)
            
            # Get database info
            file_size = os.path.getsize(sqlite_backup_path)
            
            self.logger.info(f"SQLite backup completed: {file_size} bytes")
            
            return {
                'success': True,
                'file_path': sqlite_backup_path,
                'file_size': file_size
            }
            
        except Exception as e:
            self.logger.error(f"SQLite backup failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _backup_rethinkdb(self, backup_path):
        """
        Backup RethinkDB database.
        
        Args:
            backup_path (str): Path to backup directory
            
        Returns:
            dict: Backup result
        """
        try:
            if not self.rethink_manager or not self.rethink_manager.is_connected():
                return {'success': False, 'error': 'RethinkDB not connected'}
            
            rethink_backup_path = os.path.join(backup_path, 'rethinkdb_data')
            os.makedirs(rethink_backup_path, exist_ok=True)
            
            # Get list of tables
            tables = ['daily_stats', 'game_history', 'wallet_transactions', 'admin_users', 'audit_log']
            
            total_records = 0
            table_info = {}
            
            for table in tables:
                try:
                    # Get all records from table
                    records = self.rethink_manager.get_all_records(table)
                    
                    if records:
                        # Save records to JSON file
                        table_file = os.path.join(rethink_backup_path, f'{table}.json')
                        with open(table_file, 'w') as f:
                            json.dump(records, f, indent=2, default=str)
                        
                        table_info[table] = {
                            'records': len(records),
                            'file_size': os.path.getsize(table_file)
                        }
                        total_records += len(records)
                        
                        self.logger.info(f"Backed up {len(records)} records from table {table}")
                    else:
                        table_info[table] = {'records': 0, 'file_size': 0}
                        
                except Exception as e:
                    self.logger.error(f"Error backing up table {table}: {e}")
                    table_info[table] = {'error': str(e)}
            
            # Save table information
            info_file = os.path.join(rethink_backup_path, 'table_info.json')
            with open(info_file, 'w') as f:
                json.dump(table_info, f, indent=2)
            
            self.logger.info(f"RethinkDB backup completed: {total_records} total records")
            
            return {
                'success': True,
                'backup_path': rethink_backup_path,
                'total_records': total_records,
                'table_info': table_info
            }
            
        except Exception as e:
            self.logger.error(f"RethinkDB backup failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _compress_backup(self, backup_path):
        """
        Compress backup directory into a ZIP file.
        
        Args:
            backup_path (str): Path to backup directory
            
        Returns:
            str: Path to compressed file
        """
        zip_path = f"{backup_path}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, backup_path)
                    zipf.write(file_path, arcname)
        
        self.logger.info(f"Backup compressed: {zip_path}")
        return zip_path
    
    def _verify_backup(self, backup_path):
        """
        Verify backup integrity.
        
        Args:
            backup_path (str): Path to backup file or directory
            
        Returns:
            dict: Verification result
        """
        try:
            if backup_path.endswith('.zip'):
                # Verify ZIP file
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    bad_files = zipf.testzip()
                    if bad_files:
                        return {'success': False, 'error': f'Corrupted files: {bad_files}'}
                    
                    file_list = zipf.namelist()
                    return {'success': True, 'files_count': len(file_list)}
            else:
                # Verify directory
                if not os.path.exists(backup_path):
                    return {'success': False, 'error': 'Backup path does not exist'}
                
                files_count = sum(len(files) for _, _, files in os.walk(backup_path))
                return {'success': True, 'files_count': files_count}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _cleanup_old_backups(self):
        """Remove old backups based on retention policy."""
        try:
            # Get all backup files
            backup_files = []
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                if os.path.isfile(item_path) and item.endswith('.zip'):
                    backup_files.append((item_path, os.path.getmtime(item_path)))
                elif os.path.isdir(item_path) and item.startswith('full_backup_'):
                    backup_files.append((item_path, os.path.getmtime(item_path)))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups
            if len(backup_files) > self.config['max_backups_to_keep']:
                for backup_path, _ in backup_files[self.config['max_backups_to_keep']:]:
                    if os.path.isfile(backup_path):
                        os.remove(backup_path)
                    else:
                        shutil.rmtree(backup_path)
                    
                    self.logger.info(f"Removed old backup: {backup_path}")
                    
        except Exception as e:
            self.logger.error(f"Error cleaning up old backups: {e}")
    
    def _setup_automatic_backups(self):
        """Setup automatic backup scheduling."""
        if self.config['auto_backup_enabled']:
            schedule.every(self.config['backup_interval_hours']).hours.do(self._run_scheduled_backup)
            
            # Start scheduler thread
            def run_scheduler():
                while True:
                    schedule.run_pending()
                    time.sleep(60)  # Check every minute
            
            scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
            scheduler_thread.start()
            
            self.logger.info(f"Automatic backups scheduled every {self.config['backup_interval_hours']} hours")
    
    def _run_scheduled_backup(self):
        """Run a scheduled backup."""
        self.logger.info("Running scheduled backup")
        result = self.create_full_backup()
        
        if result['success']:
            self.logger.info("Scheduled backup completed successfully")
        else:
            self.logger.error(f"Scheduled backup failed: {result.get('error', 'Unknown error')}")
    
    def list_backups(self):
        """
        List all available backups.
        
        Returns:
            list: List of backup information
        """
        backups = []
        
        try:
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                if item.endswith('.zip') or (os.path.isdir(item_path) and item.startswith('full_backup_')):
                    backup_info = {
                        'name': item,
                        'path': item_path,
                        'size': self._get_size(item_path),
                        'created_at': datetime.fromtimestamp(os.path.getmtime(item_path)).isoformat(),
                        'type': 'compressed' if item.endswith('.zip') else 'directory'
                    }
                    backups.append(backup_info)
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error listing backups: {e}")
        
        return backups
    
    def _get_size(self, path):
        """Get size of file or directory."""
        if os.path.isfile(path):
            return os.path.getsize(path)
        else:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            return total_size

# Global instance
_backup_manager = None
_backup_lock = threading.RLock()

def get_backup_manager():
    """
    Get the global backup manager instance.
    
    Returns:
        BackupManager: The backup manager instance
    """
    global _backup_manager
    
    with _backup_lock:
        if _backup_manager is None:
            _backup_manager = BackupManager()
        
        return _backup_manager
