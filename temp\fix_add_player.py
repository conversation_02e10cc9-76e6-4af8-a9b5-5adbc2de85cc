import re

def fix_add_player():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Define the pattern to match the incorrect save_ui_state call
    pattern = r'save_ui_state\(self\.selected_cartella_numbers, self\.prize_pool, self\.prize_pool_manual_override, self\.bet_amount\)'
    
    # Define the replacement with the correct parameter order
    replacement = r'save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)'
    
    # Replace the function call in the content
    updated_content = re.sub(pattern, replacement, content)
    
    # Check if any changes were made
    if updated_content == content:
        print("No changes made. Pattern not found or already updated.")
        return False
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print(f"Updated add_player method in Board_selection_fixed.py")
    return True

if __name__ == "__main__":
    fix_add_player() 