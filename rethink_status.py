"""
RethinkDB Status Utility for the WOW Games application.

This script provides a simple command-line interface to check the status
of the RethinkDB connection, switch between online and offline modes,
and view synchronization status.
"""

import os
import sys
import json
import time
import argparse
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'rethink_status.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Try importing required modules
try:
    from rethink_config import (
        RETHINKDB_HOST,
        RETHINKDB_PORT,
        RETHINKDB_DB,
        SYNC_INTERVAL,
        SYNC_TABLES
    )
    from rethink_db import get_rethink_db_manager
    from sync_manager import get_sync_manager
    from db_hybrid import get_hybrid_db_manager
    from hybrid_db_integration import get_hybrid_db_integration
    
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Error importing required modules: {e}")
    MODULES_AVAILABLE = False

def check_status():
    """Check the status of RethinkDB connection and sync."""
    if not MODULES_AVAILABLE:
        print("Required modules are not available. Please ensure all files are installed.")
        return False
        
    try:
        # Get managers
        hybrid_db = get_hybrid_db_manager()
        rethink_db = get_rethink_db_manager()
        sync_manager = get_sync_manager()
        
        # Check online status
        is_online = hybrid_db.is_online()
        print(f"\nConnection Status: {'ONLINE' if is_online else 'OFFLINE'}")
        print(f"RethinkDB Host: {RETHINKDB_HOST}:{RETHINKDB_PORT}")
        print(f"Database: {RETHINKDB_DB}")
        
        # If online, get more details
        if is_online:
            conn = rethink_db.get_connection()
            
            # Get server info
            try:
                server_info = conn.server_info
                print(f"Server Version: {server_info.get('version', 'Unknown')}")
            except:
                print("Could not retrieve server info")
            
            # Get table stats
            print("\nTable Statistics:")
            for table in SYNC_TABLES:
                try:
                    count = conn.db(RETHINKDB_DB).table(table).count().run()
                    print(f"  {table}: {count} records")
                except Exception as e:
                    print(f"  {table}: Error - {e}")
        
        # Get sync status
        print("\nSync Status:")
        sync_metadata = os.path.join('data', 'sync_cache', 'sync_metadata.json')
        if os.path.exists(sync_metadata):
            try:
                with open(sync_metadata, 'r') as f:
                    metadata = json.load(f)
                    
                for table, timestamp in metadata.items():
                    if timestamp > 0:
                        last_sync = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"  {table}: Last synced at {last_sync}")
                    else:
                        print(f"  {table}: Never synced")
            except Exception as e:
                print(f"  Error reading sync metadata: {e}")
        else:
            print("  No sync metadata available")
        
        # Get pending sync operations
        print("\nPending Sync Operations:")
        sync_cache_dir = os.path.join('data', 'sync_cache')
        if os.path.exists(sync_cache_dir):
            total_pending = 0
            for table in SYNC_TABLES:
                table_dir = os.path.join(sync_cache_dir, table)
                if os.path.exists(table_dir):
                    files = [f for f in os.listdir(table_dir) if f.endswith('.json')]
                    print(f"  {table}: {len(files)} pending operations")
                    total_pending += len(files)
                else:
                    print(f"  {table}: 0 pending operations")
            print(f"\nTotal Pending Operations: {total_pending}")
        else:
            print("  No sync cache directory found")
            
        return True
    except Exception as e:
        print(f"Error checking status: {e}")
        logging.error(f"Error checking status: {e}")
        return False

def switch_mode(mode):
    """Switch between online and offline modes."""
    if not MODULES_AVAILABLE:
        print("Required modules are not available. Please ensure all files are installed.")
        return False
        
    try:
        # Get hybrid DB integration
        db_integration = get_hybrid_db_integration()
        
        # Get current mode
        current_mode = "online" if db_integration.is_online() else "offline"
        print(f"Current mode: {current_mode}")
        
        # Switch mode if different
        if mode == current_mode:
            print(f"Already in {mode} mode")
            return True
            
        if mode == "online":
            # Try to switch to online mode
            result = db_integration.force_online_mode()
            if result:
                print("Successfully switched to online mode")
                return True
            else:
                print("Failed to switch to online mode - RethinkDB may not be accessible")
                return False
        else:  # offline mode
            # Switch to offline mode
            db_integration.force_offline_mode()
            print("Successfully switched to offline mode")
            return True
    except Exception as e:
        print(f"Error switching mode: {e}")
        logging.error(f"Error switching mode: {e}")
        return False

def force_sync():
    """Force synchronization of data."""
    if not MODULES_AVAILABLE:
        print("Required modules are not available. Please ensure all files are installed.")
        return False
        
    try:
        # Get hybrid DB integration
        db_integration = get_hybrid_db_integration()
        
        # Check if we're online
        if not db_integration.is_online():
            print("Cannot sync in offline mode - please switch to online mode first")
            return False
            
        print("Forcing synchronization...")
        
        # Force refresh
        result = db_integration.force_refresh_data()
        
        if result:
            print("Synchronization initiated successfully")
            print("Waiting for sync to complete...")
            
            # Wait a bit for sync to complete
            time.sleep(5)
            
            # Check status after sync
            check_status()
            
            return True
        else:
            print("Failed to initiate synchronization")
            return False
    except Exception as e:
        print(f"Error forcing sync: {e}")
        logging.error(f"Error forcing sync: {e}")
        return False

def main():
    """Main function for the RethinkDB status utility."""
    parser = argparse.ArgumentParser(description='RethinkDB Status Utility for WOW Games')
    
    # Define command-line arguments
    parser.add_argument('--status', action='store_true', help='Check RethinkDB connection and sync status')
    parser.add_argument('--online', action='store_true', help='Switch to online mode')
    parser.add_argument('--offline', action='store_true', help='Switch to offline mode')
    parser.add_argument('--sync', action='store_true', help='Force synchronization of data')
    
    args = parser.parse_args()
    
    # If no arguments provided, show status by default
    if len(sys.argv) == 1:
        return check_status()
    
    # Process command-line arguments
    success = True
    
    if args.status:
        success = success and check_status()
    
    if args.online:
        success = success and switch_mode("online")
    
    if args.offline:
        success = success and switch_mode("offline")
    
    if args.sync:
        success = success and force_sync()
    
    return success

if __name__ == '__main__':
    sys.exit(0 if main() else 1)