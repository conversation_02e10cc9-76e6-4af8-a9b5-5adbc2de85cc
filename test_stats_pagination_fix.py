#!/usr/bin/env python3
"""
Test script to verify the stats page pagination fix is working
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pagination_fix():
    """Test that the stats page pagination fix is working correctly"""
    
    print("=" * 80)
    print("TESTING STATS PAGE PAGINATION FIX")
    print("=" * 80)
    
    # Test 1: Verify database has multiple records
    print("\n1. Verifying database content...")
    try:
        import sqlite3
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f"✅ Database contains {total_count} total records")
        
        if total_count >= 5:
            print(f"✅ Database has sufficient records for pagination testing")
        else:
            print(f"⚠️  Database only has {total_count} records - may not trigger pagination")
        
        # Show all records
        cursor.execute('''
        SELECT id, username, date_time, total_prize 
        FROM game_history 
        ORDER BY date_time DESC
        ''')
        
        records = cursor.fetchall()
        print(f"✅ All records in database:")
        for i, (id, username, date_time, prize) in enumerate(records, 1):
            print(f"   {i}. ID:{id} - {username} - {date_time} - {prize} ETB")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False
    
    # Test 2: Test stats integration with different page sizes
    print("\n2. Testing stats integration with different page sizes...")
    try:
        from stats_integration import get_game_history
        
        # Test with small page size (should trigger pagination)
        history_small, total_pages_small = get_game_history(page=0, page_size=3)
        print(f"✅ Page size 3: {len(history_small)} records, {total_pages_small} pages")
        
        # Test with large page size (should get all records)
        history_large, total_pages_large = get_game_history(page=0, page_size=20)
        print(f"✅ Page size 20: {len(history_large)} records, {total_pages_large} pages")
        
        if total_pages_small > 1:
            print(f"✅ PAGINATION TRIGGERED: Small page size created {total_pages_small} pages")
        else:
            print(f"⚠️  No pagination with small page size")
        
        if len(history_large) >= len(history_small):
            print(f"✅ LARGER PAGE SIZE WORKS: Got {len(history_large)} vs {len(history_small)} records")
        else:
            print(f"❌ ISSUE: Larger page size got fewer records")
        
    except Exception as e:
        print(f"❌ Error with stats integration: {e}")
        return False
    
    # Test 3: Test pagination navigation
    print("\n3. Testing pagination navigation...")
    try:
        from stats_integration import get_game_history
        
        # Get first page
        page1, total_pages = get_game_history(page=0, page_size=3)
        print(f"✅ Page 1: {len(page1)} records")
        
        if total_pages > 1:
            # Get second page
            page2, _ = get_game_history(page=1, page_size=3)
            print(f"✅ Page 2: {len(page2)} records")
            
            # Check that pages have different records
            if page1 and page2:
                page1_ids = [r.get('id') for r in page1]
                page2_ids = [r.get('id') for r in page2]
                
                if set(page1_ids).isdisjoint(set(page2_ids)):
                    print(f"✅ PAGINATION WORKS: Pages have different records")
                else:
                    print(f"❌ PAGINATION ISSUE: Pages have overlapping records")
            else:
                print(f"⚠️  One of the pages is empty")
        else:
            print(f"⚠️  Only 1 page available - pagination not needed")
        
    except Exception as e:
        print(f"❌ Error testing pagination navigation: {e}")
        return False
    
    print(f"\n{'=' * 80}")
    print("STATS PAGE PAGINATION FIX TEST COMPLETE")
    print("✅ The pagination controls should now be visible and functional!")
    print("✅ Navigate to the stats page in the game to verify the fix.")
    print("✅ You should see 'Previous' and 'Next' buttons if there are multiple pages.")
    print(f"{'=' * 80}")
    
    return True

if __name__ == "__main__":
    test_pagination_fix()
