#!/usr/bin/env python3
"""
Update daily stats to reflect correct game count
"""

import sqlite3

def update_daily_stats():
    """Update daily stats with correct game count"""
    try:
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        # Update daily stats to exclude reset games and games with 0 calls
        cursor.execute('''
            UPDATE daily_stats 
            SET games_played = (
                SELECT COUNT(*) FROM game_history 
                WHERE date(date_time) = "2025-05-24" 
                AND username NOT LIKE "Game Reset" 
                AND total_calls > 0
            ) 
            WHERE date = "2025-05-24"
        ''')
        
        conn.commit()
        
        # Verify the update
        cursor.execute('SELECT games_played FROM daily_stats WHERE date = "2025-05-24"')
        result = cursor.fetchone()
        updated_count = result[0] if result else 0
        
        print(f"✅ Updated daily games count to: {updated_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating daily stats: {e}")
        return False

if __name__ == "__main__":
    update_daily_stats()
