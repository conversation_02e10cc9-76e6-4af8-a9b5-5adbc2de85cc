@echo off
echo ========================================================
echo     WOW Games with RethinkDB - Automatic Startup
echo ========================================================
echo.

REM Install required packages
echo Step 1: Installing required packages...
python -m pip install rethinkdb
if %ERRORLEVEL% NEQ 0 (
    echo Error installing packages. Please try again.
    pause
    exit /b 1
)
echo Packages installed successfully.
echo.

REM Integrate RethinkDB
echo Step 2: Integrating RethinkDB with the application...
python integrate_rethinkdb.py --all
if %ERRORLEVEL% NEQ 0 (
    echo Error integrating RethinkDB. Please check the logs.
    pause
    exit /b 1
)
echo Integration completed successfully.
echo.

REM Start RethinkDB server
echo Step 3: Starting RethinkDB server...
python setup_rethinkdb.py --start
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Could not start RethinkDB server. The application will run in offline mode.
) else (
    echo RethinkDB server started successfully.
)
echo.

REM Initialize database
echo Step 4: Initializing RethinkDB database...
python setup_rethinkdb.py --init
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Could not initialize database. The application will run in offline mode.
) else (
    echo Database initialized successfully.
)
echo.

REM Migrate data
echo Step 5: Migrating data from SQLite to RethinkDB...
python setup_rethinkdb.py --migrate
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Could not migrate data. The application will continue with existing data.
) else (
    echo Data migration completed successfully.
)
echo.

REM Check status
echo Step 6: Checking RethinkDB status...
python rethink_status.py --status
echo.

REM Start the game
echo Step 7: Starting the game...
echo.
echo ========================================================
echo     WOW Games is starting with RethinkDB integration
echo     You can check the connection status in the top-right
echo     corner of the application (green = online, red = offline)
echo ========================================================
echo.

timeout /t 5

python main.py