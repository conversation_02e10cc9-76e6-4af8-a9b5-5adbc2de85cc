## Changes to update Board_selection_fixed.py to use the new UI state management system

### 1. In __init__ method (around line 949):

```python
# Load remembered cartella numbers if setting is enabled
if self.remember_cartella_checkbox:
    try:
        from player_storage import load_ui_state
        state = load_ui_state()
        remembered_numbers = state.get('cartella_numbers', [])
        remembered_prize_pool = state.get('prize_pool', None)
        remembered_override = state.get('prize_pool_manual_override', False)
        remembered_bet_amount = state.get('bet_amount', None)
        if remembered_numbers:
            self.selected_cartella_numbers = remembered_numbers
            self.called_numbers = remembered_numbers.copy()
            if remembered_bet_amount is not None:
                self.bet_amount = remembered_bet_amount
                self.bet_input_text = str(self.bet_amount)
            self.prize_pool_manual_override = remembered_override
            if self.prize_pool_manual_override and remembered_prize_pool is not None:
                self.prize_pool = remembered_prize_pool
            else:
                self.calculate_prize_pool()
        else:
            # Save empty state to clear remembered values
            from player_storage import save_ui_state
            save_ui_state([], self.bet_amount, None, False)
            print("Cleared UI state")
    except Exception as e:
        print(f"Error loading UI state: {e}")
```

### 2. In add_player method (around line 3327):

```python
# Save UI state if option is enabled
if self.remember_cartella_checkbox:
    try:
        from player_storage import save_ui_state
        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
        print(f"Updated UI state after adding player: {len(self.selected_cartella_numbers)} numbers")
    except Exception as e:
        print(f"Error saving UI state after adding player: {e}")
```

### 3. In handle_direct_interaction method (around line 2740) where a cartella is removed:

```python
# Save updated UI state if option is enabled
if self.remember_cartella_checkbox:
    try:
        from player_storage import save_ui_state
        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
        print(f"Updated UI state after removing player: {len(self.selected_cartella_numbers)} numbers")
    except Exception as e:
        print(f"Error saving UI state after removing player: {e}")
```

### 4. In handle_direct_interaction method (around line 2815) where a cartella is also removed:

```python
# Save UI state if option is enabled
if self.remember_cartella_checkbox:
    try:
        from player_storage import save_ui_state
        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
        print(f"Updated UI state after removing player: {len(self.selected_cartella_numbers)} numbers")
    except Exception as e:
        print(f"Error saving UI state after removing player: {e}")
```

### 5. In _apply_bet_input method (around line 3494):

```python
# Save UI state if option is enabled
if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
    try:
        from player_storage import save_ui_state
        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
        print(f"Updated UI state after changing bet amount: {self.bet_amount}")
    except Exception as e:
        print(f"Error saving UI state after changing bet amount: {e}")
```

### 6. In reset_all_cartellas method (around line 4848):

```python
# Also clear or restore UI state if option is enabled
if self.remember_cartella_checkbox:
    try:
        from player_storage import load_ui_state, save_ui_state
        state = load_ui_state()
        remembered_numbers = state.get('cartella_numbers', [])
        remembered_prize_pool = state.get('prize_pool', None)
        remembered_override = state.get('prize_pool_manual_override', False)
        remembered_bet_amount = state.get('bet_amount', None)
        
        if remembered_numbers:
            self.selected_cartella_numbers = remembered_numbers
            self.called_numbers = remembered_numbers.copy()
            
            if remembered_bet_amount is not None:
                self.bet_amount = remembered_bet_amount
                self.bet_input_text = str(self.bet_amount)
            
            self.prize_pool_manual_override = remembered_override
            if self.prize_pool_manual_override and remembered_prize_pool is not None:
                self.prize_pool = remembered_prize_pool
            else:
                self.calculate_prize_pool()
        else:
            # Save empty state to clear remembered values
            save_ui_state([], self.bet_amount, None, False)
            print("Cleared UI state")
    except Exception as e:
        print(f"Error loading/saving UI state: {e}")
```

### 7. In set_prize_pool_manually method (around line 5676):

```python
self.prize_pool = value
self.prize_pool_manual_override = True

# Save UI state if option is enabled
if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
    try:
        from player_storage import save_ui_state
        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, True)
        print(f"Updated UI state after setting prize pool manually: {self.prize_pool}")
    except Exception as e:
        print(f"Error saving UI state after setting prize pool manually: {e}")
        
self.show_message(f"Prize pool set manually to {value} ETB", "success")
``` 