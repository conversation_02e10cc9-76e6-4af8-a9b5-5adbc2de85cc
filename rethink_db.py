"""
RethinkDB Database Manager for WOW Games application.

This module provides a database manager for RethinkDB operations,
including connection management, query execution, and data synchronization.
"""

import os
import json
import time
import logging
import threading
from datetime import datetime
from contextlib import contextmanager

# Try importing rethinkdb
try:
    import rethinkdb
    r = rethinkdb.r
    from rethinkdb.errors import ReqlDriverError, ReqlTimeoutError
    RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False
    print("RethinkDB module not available")

# Import configuration
try:
    from rethink_config import (
        RETHINKDB_HOST,
        RETHINKDB_PORT,
        RETH<PERSON><PERSON>DB_DB,
        RET<PERSON><PERSON>KDB_USER,
        R<PERSON>H<PERSON>KDB_PASSWORD,
        RETHINKDB_SSL
    )
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    # Default configuration
    RETHINKDB_HOST = 'localhost'
    RETHINKDB_PORT = 28015
    RETHINKDB_DB = 'wow_game_stats'
    RETHINKDB_USER = 'admin'
    RETHINKDB_PASSWORD = ''
    RETHINKDB_SSL = False

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'rethink_db.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class RethinkDBManager:
    """
    RethinkDB database manager for the WOW Games application.

    This class provides methods for connecting to RethinkDB, executing queries,
    and managing database operations.
    """

    def __init__(self):
        """Initialize the RethinkDB manager."""
        self.connection = None
        self.connection_lock = threading.RLock()
        self.is_connected_flag = False
        self.last_connection_attempt = 0
        self.connection_retry_delay = 5  # seconds

        # Initialize connection if RethinkDB is available
        if RETHINKDB_AVAILABLE and CONFIG_AVAILABLE:
            self.connect()

    def connect(self):
        """
        Connect to the RethinkDB server.

        Returns:
            bool: True if connected successfully, False otherwise
        """
        if not RETHINKDB_AVAILABLE:
            logging.warning("RethinkDB module not available")
            return False

        # Avoid rapid reconnection attempts
        current_time = time.time()
        if current_time - self.last_connection_attempt < self.connection_retry_delay:
            return self.is_connected_flag

        self.last_connection_attempt = current_time

        with self.connection_lock:
            try:
                # Close existing connection if any
                if self.connection:
                    try:
                        self.connection.close()
                    except:
                        pass

                # Create new connection
                connection_params = {
                    'host': RETHINKDB_HOST,
                    'port': RETHINKDB_PORT,
                    'user': RETHINKDB_USER,
                    'password': RETHINKDB_PASSWORD,
                    'db': RETHINKDB_DB
                }

                if RETHINKDB_SSL:
                    connection_params['ssl'] = {"ca_certs": None}

                self.connection = r.connect(**connection_params)
                self.is_connected_flag = True

                logging.info(f"Connected to RethinkDB at {RETHINKDB_HOST}:{RETHINKDB_PORT}")
                return True

            except (ReqlDriverError, ReqlTimeoutError) as e:
                logging.error(f"Failed to connect to RethinkDB: {e}")
                self.connection = None
                self.is_connected_flag = False
                return False
            except Exception as e:
                logging.error(f"Unexpected error connecting to RethinkDB: {e}")
                self.connection = None
                self.is_connected_flag = False
                return False

    def disconnect(self):
        """Disconnect from the RethinkDB server."""
        with self.connection_lock:
            if self.connection:
                try:
                    self.connection.close()
                    logging.info("Disconnected from RethinkDB")
                except:
                    pass
                finally:
                    self.connection = None
                    self.is_connected_flag = False

    def is_connected(self):
        """
        Check if connected to RethinkDB.

        Returns:
            bool: True if connected, False otherwise
        """
        if not self.is_connected_flag or not self.connection:
            return False

        try:
            # Test the connection with a simple query
            r.expr(1).run(self.connection)
            return True
        except:
            self.is_connected_flag = False
            return False

    @contextmanager
    def get_connection_context(self):
        """
        Get a connection context manager.

        Yields:
            Connection: RethinkDB connection object
        """
        if not self.is_connected():
            if not self.connect():
                raise Exception("Could not connect to RethinkDB")

        try:
            yield self.connection
        except Exception as e:
            logging.error(f"Error in RethinkDB operation: {e}")
            raise

    def execute_query(self, query):
        """
        Execute a RethinkDB query.

        Args:
            query: RethinkDB query object

        Returns:
            Query result or None if failed
        """
        try:
            with self.get_connection_context() as conn:
                return query.run(conn)
        except Exception as e:
            logging.error(f"Error executing RethinkDB query: {e}")
            return None

    def insert_record(self, table_name, record):
        """
        Insert a record into a RethinkDB table.

        Args:
            table_name (str): Name of the table
            record (dict): Record to insert

        Returns:
            dict: Insert result or None if failed
        """
        try:
            query = r.table(table_name).insert(record)
            return self.execute_query(query)
        except Exception as e:
            logging.error(f"Error inserting record into {table_name}: {e}")
            return None

    def get_all_records(self, table_name, limit=None):
        """
        Get all records from a RethinkDB table.

        Args:
            table_name (str): Name of the table
            limit (int): Maximum number of records to return

        Returns:
            list: List of records
        """
        try:
            query = r.table(table_name)
            if limit:
                query = query.limit(limit)

            result = self.execute_query(query)
            return list(result) if result else []
        except Exception as e:
            logging.error(f"Error getting records from {table_name}: {e}")
            return []

# Global instance
_rethink_db_manager = None
_manager_lock = threading.RLock()

def get_rethink_db_manager():
    """
    Get the global RethinkDB manager instance.

    Returns:
        RethinkDBManager: The RethinkDB manager instance
    """
    global _rethink_db_manager

    with _manager_lock:
        if _rethink_db_manager is None:
            _rethink_db_manager = RethinkDBManager()

        return _rethink_db_manager

def test_rethink_connection():
    """
    Test the RethinkDB connection.

    Returns:
        bool: True if connection is working, False otherwise
    """
    try:
        manager = get_rethink_db_manager()
        return manager.is_connected()
    except Exception as e:
        logging.error(f"Error testing RethinkDB connection: {e}")
        return False