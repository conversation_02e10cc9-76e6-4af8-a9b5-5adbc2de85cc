#!/usr/bin/env python3
"""
WOW Bingo Game - Quick Start Script for Modern Application
==========================================================

Quick start script to set up and run the modern WOW Bingo Game application.

This script will:
1. Check system requirements
2. Set up virtual environment (optional)
3. Install dependencies
4. Run the application

Usage:
    python quick_start_modern.py [--dev] [--build] [--install-deps]
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def check_python_version():
    """Check if Python version meets requirements."""
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version_info.major}.{sys.version_info.minor}")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ["flet", "pygame", "loguru", "pydantic"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    return missing_packages


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def install_package_dev():
    """Install the package in development mode."""
    print("\n📦 Installing package in development mode...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."], check=True)
        print("✅ Package installed in development mode")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install package: {e}")
        return False


def run_application(dev_mode=False):
    """Run the WOW Bingo Game application."""
    print(f"\n🚀 Starting WOW Bingo Game {'(Development Mode)' if dev_mode else ''}...")
    
    try:
        if dev_mode:
            # Run in development mode
            subprocess.run([sys.executable, "-m", "wow_bingo_game.main", "--dev"], check=True)
        else:
            # Run normally
            subprocess.run([sys.executable, "-m", "wow_bingo_game.main"], check=True)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to run application: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Application stopped by user")
        return True
    
    return True


def build_application():
    """Build the application executable."""
    print("\n🔨 Building application executable...")
    
    try:
        subprocess.run([sys.executable, "scripts/build_app.py", "--optimization", "high"], check=True)
        print("✅ Application built successfully")
        print("📁 Executable can be found in the 'dist' directory")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build application: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="WOW Bingo Game - Quick Start")
    parser.add_argument("--dev", action="store_true", help="Run in development mode")
    parser.add_argument("--build", action="store_true", help="Build executable instead of running")
    parser.add_argument("--install-deps", action="store_true", help="Force install dependencies")
    parser.add_argument("--check-only", action="store_true", help="Only check requirements")
    
    args = parser.parse_args()
    
    print("🎯 WOW Bingo Game - Modern Application Quick Start")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ Error: pyproject.toml not found")
        print("Please run this script from the project root directory")
        sys.exit(1)
    
    # Check dependencies
    missing_packages = check_dependencies()
    
    if args.check_only:
        if missing_packages:
            print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
            print("Run with --install-deps to install them")
            sys.exit(1)
        else:
            print("\n✅ All requirements met!")
            sys.exit(0)
    
    # Install dependencies if needed or requested
    if missing_packages or args.install_deps:
        if not install_dependencies():
            sys.exit(1)
        
        # Install package in development mode
        if not install_package_dev():
            sys.exit(1)
    
    # Build or run application
    if args.build:
        if not build_application():
            sys.exit(1)
    else:
        if not run_application(dev_mode=args.dev):
            sys.exit(1)
    
    print("\n🎉 Quick start completed successfully!")


if __name__ == "__main__":
    main()
