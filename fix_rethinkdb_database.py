"""
RethinkDB Database Fix Script

This script fixes corrupted RethinkDB database by:
1. Stopping any running RethinkDB processes
2. Backing up existing data (if any)
3. Removing corrupted database files
4. Creating a fresh database
5. Testing the new database
"""

import os
import sys
import shutil
import subprocess
import time
from datetime import datetime

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def run_command(command, description, timeout=30):
    """Run a command and return success status."""
    try:
        log(f"Running: {description}")
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            log(f"✅ {description} completed successfully")
            return True
        else:
            log(f"❌ {description} failed: {result.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log(f"⏰ {description} timed out", "WARNING")
        return False
    except Exception as e:
        log(f"❌ Error in {description}: {e}", "ERROR")
        return False

def stop_rethinkdb_processes():
    """Stop any running RethinkDB processes."""
    log("Stopping any running RethinkDB processes...")
    
    try:
        # Try to stop gracefully first
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'rethinkdb.exe'], 
                         capture_output=True, timeout=10)
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'rethinkdb'], 
                         capture_output=True, timeout=10)
        
        log("✅ RethinkDB processes stopped")
        time.sleep(2)  # Give processes time to clean up
        
    except Exception as e:
        log(f"⚠️ Error stopping processes: {e}", "WARNING")

def backup_existing_data():
    """Backup existing RethinkDB data if it exists."""
    data_dir = os.path.join('data', 'rethinkdb_data')
    
    if not os.path.exists(data_dir):
        log("No existing RethinkDB data to backup")
        return True
    
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join('data', f'rethinkdb_backup_{timestamp}')
        
        log(f"Backing up existing data to: {backup_dir}")
        shutil.copytree(data_dir, backup_dir)
        
        log(f"✅ Data backed up to: {backup_dir}")
        return True
        
    except Exception as e:
        log(f"❌ Error backing up data: {e}", "ERROR")
        return False

def remove_corrupted_data():
    """Remove corrupted RethinkDB data directory."""
    data_dir = os.path.join('data', 'rethinkdb_data')
    
    if not os.path.exists(data_dir):
        log("No corrupted data to remove")
        return True
    
    try:
        log("Removing corrupted RethinkDB data...")
        shutil.rmtree(data_dir)
        log("✅ Corrupted data removed")
        return True
        
    except Exception as e:
        log(f"❌ Error removing corrupted data: {e}", "ERROR")
        return False

def create_fresh_database():
    """Create a fresh RethinkDB database."""
    log("Creating fresh RethinkDB database...")
    
    # Create data directory
    data_dir = os.path.join('data', 'rethinkdb_data')
    os.makedirs(data_dir, exist_ok=True)
    
    # Start RethinkDB with fresh data
    try:
        log("Starting RethinkDB with fresh database...")
        
        # Use a simpler command that's more likely to work
        cmd = [
            'rethinkdb',
            '--directory', data_dir,
            '--bind', '127.0.0.1',
            '--http-port', '8081',
            '--driver-port', '28015'
        ]
        
        log(f"Command: {' '.join(cmd)}")
        
        # Start RethinkDB in background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for it to start
        log("Waiting for RethinkDB to start...")
        time.sleep(10)
        
        # Check if it's running
        if process.poll() is None:
            log("✅ RethinkDB started successfully!")
            
            # Test connection
            time.sleep(5)
            connection_test = run_command(
                [sys.executable, "setup_rethinkdb.py", "--check"],
                "Connection test",
                timeout=15
            )
            
            if connection_test:
                log("✅ RethinkDB connection test passed!")
                
                # Initialize database
                init_result = run_command(
                    [sys.executable, "setup_rethinkdb.py", "--init"],
                    "Database initialization",
                    timeout=30
                )
                
                if init_result:
                    log("✅ Database initialized successfully!")
                    return True
                else:
                    log("❌ Database initialization failed", "ERROR")
                    return False
            else:
                log("❌ Connection test failed", "ERROR")
                return False
        else:
            log("❌ RethinkDB failed to start", "ERROR")
            return False
            
    except Exception as e:
        log(f"❌ Error creating fresh database: {e}", "ERROR")
        return False

def main():
    """Main function to fix RethinkDB database."""
    log("🔧 RETHINKDB DATABASE FIX SCRIPT")
    log("=" * 60)
    
    # Step 1: Stop any running processes
    stop_rethinkdb_processes()
    
    # Step 2: Backup existing data
    if not backup_existing_data():
        log("❌ Failed to backup data. Continuing anyway...", "WARNING")
    
    # Step 3: Remove corrupted data
    if not remove_corrupted_data():
        log("❌ Failed to remove corrupted data. Aborting.", "ERROR")
        return False
    
    # Step 4: Create fresh database
    if not create_fresh_database():
        log("❌ Failed to create fresh database.", "ERROR")
        return False
    
    log("=" * 60)
    log("🎉 RETHINKDB DATABASE FIX COMPLETED!")
    log("=" * 60)
    log("")
    log("✅ RethinkDB is now running with a fresh database")
    log("✅ You can now use the automated launcher:")
    log("   • Windows: START_WOW_GAMES.bat")
    log("   • Python: python start_wow_games_complete.py")
    log("")
    log("🌐 Access points:")
    log("   • RethinkDB Admin: http://localhost:8081")
    log("   • Web Dashboard: http://localhost:5000 (after starting)")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Database fix failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Database fix completed successfully!")
        
        # Ask if user wants to start the full launcher
        try:
            choice = input("\nWould you like to start the full WOW Games launcher now? (y/n): ").strip().lower()
            if choice in ['y', 'yes']:
                print("\n🚀 Starting WOW Games launcher...")
                subprocess.run([sys.executable, "start_wow_games_complete.py"])
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        except Exception as e:
            print(f"\n⚠️ Could not start launcher: {e}")
            print("You can start it manually with: python start_wow_games_complete.py")
