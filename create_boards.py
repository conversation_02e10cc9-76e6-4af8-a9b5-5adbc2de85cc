import json
import os
import random

# Ensure data directory exists
os.makedirs('data', exist_ok=True)

# Create empty boards dictionary
boards = {}

# Generate 100 boards
for i in range(1, 101):
    # Create a new board
    board = []
    
    # For each column (B, I, N, G, O)
    for col in range(5):
        # Create a new column
        column = []
        
        # Define the range for this column
        start = col * 15 + 1
        end = start + 14
        
        # Generate 5 random numbers for this column
        nums = random.sample(range(start, end + 1), 5)
        column.extend(nums)
        
        # Add the column to the board
        board.append(column)
    
    # Set the middle square (free space) to 0
    board[2][2] = 0
    
    # Add the board to the boards dictionary
    boards[str(i)] = board
    
    print(f"Generated board for cartella {i}")

# Save the boards to a JSON file
with open('data/bingo_boards.json', 'w') as f:
    json.dump(boards, f, indent=2)

print(f"Successfully saved {len(boards)} boards to data/bingo_boards.json")
