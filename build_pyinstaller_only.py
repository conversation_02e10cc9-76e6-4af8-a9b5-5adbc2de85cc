#!/usr/bin/env python3
"""
WOW Bingo Game - PyInstaller Only Build
=======================================

This script uses ONLY PyInstaller to build the game.
PyInstaller does NOT require Visual Studio Build Tools.

Usage:
    python build_pyinstaller_only.py [--onefile]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="PyInstaller-only build (no Visual Studio needed)")
    parser.add_argument('--onefile', action='store_true', help='Create single-file executable')
    args = parser.parse_args()

    print("=" * 60)
    print("WOW Bingo Game - PyInstaller Build")
    print("=" * 60)
    print("✅ NO Visual Studio Build Tools required!")
    print("✅ Works on any Windows PC with Python")
    print("=" * 60)

    # Check prerequisites
    if not Path("main.py").exists():
        print("ERROR: main.py not found!")
        return False

    if not Path("assets").exists():
        print("ERROR: assets directory not found!")
        return False

    # Install PyInstaller if needed
    try:
        import PyInstaller
        print("✅ PyInstaller is available")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        print("✅ PyInstaller installed")

    # Clean previous builds
    for folder in ['dist', 'build']:
        if Path(folder).exists():
            shutil.rmtree(folder)
            print(f"Cleaned {folder} directory")

    # Build command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile' if args.onefile else '--onedir',
        '--windowed',
        '--name=WOW_Bingo_Game',
        '--clean',
        '--noconfirm',
    ]

    # Add icon
    if Path("assets/app_logo.ico").exists():
        cmd.extend(['--icon', 'assets/app_logo.ico'])

    # Add data
    cmd.extend([
        '--add-data', 'assets;assets',
        '--add-data', 'data;data',
    ])

    # Add hidden imports
    imports = ['pygame', 'pyperclip', 'sqlite3', 'json', 'datetime', 'pathlib']
    for imp in imports:
        cmd.extend(['--hidden-import', imp])

    cmd.append('main.py')

    # Build
    print("Building with PyInstaller...")
    print("This will take 5-10 minutes...")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=True, text=True)
    build_time = time.time() - start_time

    if result.returncode == 0:
        print("=" * 60)
        print("BUILD SUCCESS!")
        print("=" * 60)
        
        if args.onefile:
            exe_path = Path("dist/WOW_Bingo_Game.exe")
        else:
            exe_path = Path("dist/WOW_Bingo_Game/WOW_Bingo_Game.exe")
        
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"Executable: {exe_path}")
            print(f"Size: {size_mb:.1f} MB")
            print(f"Build time: {build_time:.1f} seconds")
            print("=" * 60)
            print("✅ NO Visual Studio Build Tools were needed!")
            print("✅ Your game is ready for distribution!")
            return True
    
    print("BUILD FAILED!")
    print("Error:", result.stderr)
    return False

if __name__ == "__main__":
    success = main()
    input("Press Enter to exit...")
    sys.exit(0 if success else 1)
