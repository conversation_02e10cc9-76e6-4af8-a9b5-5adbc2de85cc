{% extends "layout.html" %}
{% block content %}
    <h2>Wallet Transactions</h2>

    <div class="summary-card">
        <h3>Current Balance</h3>
        <div class="value">{{ balance }} ETB</div>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date/Time</th>
                <th>Amount</th>
                <th>Type</th>
                <th>Description</th>
                <th>Balance After</th>
            </tr>
        </thead>
        <tbody>
            {% for transaction in transactions %}
            <tr>
                <td>{{ transaction.id }}</td>
                <td>{{ transaction.date_time }}</td>
                <td>{{ transaction.amount }} ETB</td>
                <td>{{ transaction.transaction_type }}</td>
                <td>{{ transaction.description }}</td>
                <td>{{ transaction.balance_after }} ETB</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="pagination">
        {% if page > 1 %}
            <a href="/wallet?page={{ page - 1 }}">&laquo; Previous</a>
        {% endif %}

        <span>Page {{ page }} of {{ total_pages }}</span>

        {% if page < total_pages %}
            <a href="/wallet?page={{ page + 1 }}">Next &raquo;</a>
        {% endif %}
    </div>
{% endblock %}
