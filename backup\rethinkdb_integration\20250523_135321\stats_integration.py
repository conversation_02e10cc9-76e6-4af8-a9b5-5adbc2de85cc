"""
Stats Integration Module for the WOW Games application.

This module integrates the stats database with the game logic,
providing methods to record game events and retrieve statistics.
It uses the stats preloader for improved performance when available.
"""

import os
import json
import time
import pygame
from datetime import datetime
from stats_db import get_stats_db_manager

# Try to import stats preloader
try:
    from stats_preloader import get_stats_preloader
    PRELOADER_AVAILABLE = True
    print("Stats preloader available for integration")
except ImportError:
    PRELOADER_AVAILABLE = False
    print("Stats preloader not available, using direct database access")

def record_game_completed(game_data):
    """
    Record a completed game in the stats database.

    Args:
        game_data: Dictionary containing game data
            - winner_name: Name of the winner
            - winner_cartella: Cartella number of the winner
            - claim_type: Type of claim (e.g., 'Full House', 'First Line')
            - game_duration: Duration of the game in seconds
            - player_count: Number of players in the game
            - prize_amount: Prize amount for this game
            - commission_percentage: Commission percentage for this game
            - called_numbers: List of numbers called during the game
            - is_demo_mode: <PERSON><PERSON><PERSON> indicating if the game was in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if game_data.get("is_demo_mode", False):
        print("Game was in demo mode - statistics not updated")
        return False

    # Get stats database manager
    stats_db = get_stats_db_manager()

    # Calculate fee based on prize amount and commission percentage
    prize_amount = game_data.get("prize_amount", 0)
    commission_percentage = game_data.get("commission_percentage", 20)
    fee = prize_amount * (commission_percentage / 100)

    # Add game to history
    game_id = stats_db.add_game_to_history(
        username=game_data.get("winner_name", "Unknown"),
        house="Main House",
        stake=game_data.get("bet_amount", 50),
        players=game_data.get("player_count", 0),
        total_calls=len(game_data.get("called_numbers", [])),
        commission_percent=commission_percentage,
        fee=fee,
        total_prize=prize_amount,
        details=json.dumps({
            "winner_cartella": game_data.get("winner_cartella", 0),
            "claim_type": game_data.get("claim_type", "Unknown"),
            "game_duration": game_data.get("game_duration", 0),
            "called_numbers": game_data.get("called_numbers", [])
        }),
        status="Won"
    )

    # Add enhanced logging for game winner recording
    print(f"Game winner recorded via stats_integration.py - ID: {game_id}, Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
    try:
        import logging
        logging.info(f"Game winner recorded via stats_integration.py - ID: {game_id}, Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
    except ImportError:
        pass

    # Update wallet with fee
    if fee > 0:
        stats_db.add_wallet_transaction(
            amount=fee,
            transaction_type="fee",
            description=f"Commission from game #{game_id}"
        )

    return game_id > 0

def force_refresh_data():
    """
    Force a refresh of all statistics data from the database and trigger UI updates.
    
    This function fetches fresh data from the database, bypassing any caching,
    and posts a refresh_stats event to notify any listening UI components
    (like the StatsPage) to update their displays.
    
    Returns:
        bool: True if refresh was successful, False otherwise
    """
    print("=" * 80)
    print("FORCING REFRESH OF ALL STATS DATA")
    print("=" * 80)

    try:
        # Get stats database manager instance in the main thread
        stats_db = get_stats_db_manager()
        
        # Clear any stats caches first
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()
            if hasattr(preloader, 'clear'):
                clear_result = preloader.clear()
                print(f"Cleared stats preloader cache: {clear_result}")
            else:
                print("Preloader clear method not available, using direct cache clear")
                # Try to clear the cache directly
                if hasattr(preloader, 'cache') and hasattr(preloader.cache, 'clear'):
                    preloader.cache.clear()
                    print("Cleared preloader cache directly")
        except Exception as e:
            print(f"Note: Could not clear preloader cache: {e}")
            import traceback
            traceback.print_exc()
        
        # Create a thread for database operations to avoid SQLite thread issues
        import threading
        
        def sync_stats_in_thread():
            try:
                # Get a fresh stats_db manager in this thread
                thread_stats_db = get_stats_db_manager()
                
                # Use the new sync_stats method to ensure all data is properly synchronized
                print("Synchronizing all stats data to ensure consistency...")
                sync_result = thread_stats_db.sync_stats()
                print(f"Stats synchronization result: {sync_result}")
                
                # Add a small delay to ensure all database writes are complete
                try:
                    import time
                    time.sleep(0.5)  # Half second delay
                    print("Added delay to ensure database writes are complete")
                except Exception as delay_e:
                    print(f"Note: Could not add delay: {delay_e}")
            except Exception as e:
                print(f"Error in sync_stats_in_thread: {e}")
                import traceback
                traceback.print_exc()
        
        # Execute the sync in a separate thread
        sync_thread = threading.Thread(target=sync_stats_in_thread)
        sync_thread.daemon = True
        sync_thread.start()
        sync_thread.join(timeout=5.0)  # Wait up to 5 seconds for the thread to complete
        
        # Post a refresh_stats event to update any UI components
        try:
            import pygame
            import time
            
            # Check if pygame is initialized before posting events
            pygame_initialized = pygame.get_init()
            print(f"Pygame initialized: {pygame_initialized}")
            
            if pygame_initialized:
                # Create a refresh event with appropriate attributes
                refresh_event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'refresh_stats',
                    'force': True,
                    'force_reload': True,
                    'source': 'force_refresh_data',
                    'timestamp': time.time()
                })
                
                # Post the event
                pygame.event.post(refresh_event)
                print("Posted refresh_stats event with fresh data")
            else:
                print("Pygame not initialized, skipping event posting")
            
            # Add a short delay to allow for event processing
            import time
            time.sleep(0.1)
            
            # Start preloading again to refresh the cache after we've cleared it
            try:
                preloader = get_stats_preloader()
                preloader.start_preloading()
                print("Started preloader data refresh")
            except Exception as e:
                print(f"Could not start preloader refresh: {e}")
            
            # Also post a direct stats_loaded event with the actual data
            # We'll do this in a separate thread to avoid SQLite thread issues
            def post_stats_loaded_event():
                try:
                    # Import here to ensure we use the right thread context
                    import thread_safe_db
                    import pygame
                    import time
                    
                    # Get fresh data for the stats_loaded event
                    weekly_stats = thread_safe_db.get_weekly_stats()
                    total_earnings = thread_safe_db.get_total_earnings()
                    daily_earnings = thread_safe_db.get_daily_earnings()
                    daily_games = thread_safe_db.get_daily_games_played()
                    wallet_balance = thread_safe_db.get_wallet_balance()
                    game_history, total_pages = thread_safe_db.get_game_history(page=0, page_size=10)
                    
                    # Only post the event if pygame is initialized
                    if pygame.get_init():
                        stats_loaded_event = pygame.event.Event(pygame.USEREVENT, {
                            'stats_type': 'stats_loaded',
                            'weekly_stats': weekly_stats,
                            'summary': {
                                'total_earnings': total_earnings,
                                'daily_earnings': daily_earnings,
                                'daily_games': daily_games,
                                'wallet_balance': wallet_balance
                            },
                            'game_history': game_history,
                            'total_pages': total_pages,
                            'source': 'force_refresh_data',
                            'timestamp': time.time()
                        })
                        pygame.event.post(stats_loaded_event)
                        print("Posted direct stats_loaded event with fresh data")
                    else:
                        print("Pygame not initialized in stats thread, skipping stats_loaded event")
                except Exception as e:
                    print(f"Error posting stats_loaded event: {e}")
                    import traceback
                    traceback.print_exc()
            
            # Start thread to post stats_loaded event
            stats_thread = threading.Thread(target=post_stats_loaded_event)
            stats_thread.daemon = True
            stats_thread.start()
            
            return True
            
        except Exception as e:
            print(f"Error posting refresh event: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"Error during forced refresh: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_weekly_stats():
    """
    Get weekly statistics from the database.
    
    Returns:
        list: List of daily stats for the past week
    """
    stats_db = get_stats_db_manager()
    return stats_db.get_weekly_stats()

def record_game_started(player_count, bet_amount=50, is_demo_mode=False):
    """
    Record a game start event in the stats database.

    Args:
        player_count: Number of players in the game
        bet_amount: Bet amount per player
        is_demo_mode: Boolean indicating if the game is in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if is_demo_mode:
        print("Game started in demo mode - statistics not updated")
        return False

    # Get stats database manager
    stats_db = get_stats_db_manager()

    # Update daily stats with player count
    today = datetime.now().strftime('%Y-%m-%d')
    return stats_db.update_daily_stats(
        date_str=today,
        total_players=player_count
    )

def get_stats_summary():
    """
    Get a summary of statistics for display on the stats page.
    Uses the preloader for improved performance when available.

    Returns:
        dict: Statistics summary
    """
    # Try to use preloader first for better performance
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()

            # Get cached data from preloader
            weekly_stats = preloader.get_cached_data('weekly_stats')
            total_earnings = preloader.get_cached_data('total_earnings')
            daily_earnings = preloader.get_cached_data('daily_earnings')
            daily_games = preloader.get_cached_data('daily_games')
            wallet_balance = preloader.get_cached_data('wallet_balance')

            # If we have all the data from the preloader, return it
            if weekly_stats and total_earnings is not None:
                return {
                    "weekly_stats": weekly_stats,
                    "total_earnings": total_earnings,
                    "daily_earnings": daily_earnings,
                    "daily_games": daily_games,
                    "wallet_balance": wallet_balance
                }

            # Otherwise, start preloading in the background for next time
            preloader.start_preloading()

        except Exception as e:
            print(f"Error using stats preloader: {e}")
            # Fall back to direct database access

    # Get stats database manager for direct access
    stats_db = get_stats_db_manager()

    # Get current date
    today = datetime.now().strftime('%Y-%m-%d')

    # Get weekly stats
    weekly_stats = stats_db.get_weekly_stats()

    # Calculate totals
    total_earnings = stats_db.get_total_earnings()
    daily_earnings = stats_db.get_daily_earnings()
    daily_games = stats_db.get_daily_games_played()
    wallet_balance = stats_db.get_wallet_balance()

    # Cache the results in the preloader if available
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()
            preloader.cache.set('weekly_stats', weekly_stats)
            preloader.cache.set('total_earnings', total_earnings)
            preloader.cache.set('daily_earnings', daily_earnings)
            preloader.cache.set('daily_games', daily_games)
            preloader.cache.set('wallet_balance', wallet_balance)
        except Exception as e:
            print(f"Error caching stats in preloader: {e}")

    return {
        "weekly_stats": weekly_stats,
        "total_earnings": total_earnings,
        "daily_earnings": daily_earnings,
        "daily_games": daily_games,
        "wallet_balance": wallet_balance
    }

def migrate_legacy_stats():
    """
    Migrate legacy stats from JSON to SQLite database.

    Returns:
        bool: True if successful, False otherwise
    """
    stats_db = get_stats_db_manager()
    return stats_db.migrate_from_json()

def add_wallet_transaction(amount, transaction_type, description):
    """
    Add a transaction to the wallet.

    Args:
        amount: Transaction amount
        transaction_type: Type of transaction (deposit, withdrawal, fee, etc.)
        description: Transaction description

    Returns:
        int: Transaction ID, or -1 if failed
    """
    stats_db = get_stats_db_manager()
    return stats_db.add_wallet_transaction(amount, transaction_type, description)

def get_game_history(page=0, page_size=10):
    """
    Get game history.
    Uses the preloader for improved performance when available.

    Args:
        page: Page number (0-based)
        page_size: Number of records per page

    Returns:
        tuple: (list of game history records, total pages)
    """
    # Try to use preloader first for better performance
    if PRELOADER_AVAILABLE:  # Preloader caches pages
        try:
            preloader = get_stats_preloader()

            # Only use cache for small page sizes
            if page_size <= 10:
                history, total_pages = preloader.get_game_history_page(page, page_size)
                if history:
                    return history, total_pages

            # Start preloading in the background for next time
            preloader.start_preloading()

        except Exception as e:
            print(f"Error using preloader for game history: {e}")
            # Fall back to direct database access

    # Use direct database access
    stats_db = get_stats_db_manager()
    return stats_db.get_game_history(page, page_size)
