#!/usr/bin/env python3
"""
Simple build test script
"""

import subprocess
import sys
from pathlib import Path

def test_simple_build():
    """Test the simple build script."""
    print("Testing simple build...")
    
    try:
        result = subprocess.run([
            sys.executable, 'nuitka_simple_build.py', '--debug'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("Simple build test PASSED")
            return True
        else:
            print("Simple build test FAILED")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("Build test timed out")
        return False
    except Exception as e:
        print(f"Build test error: {e}")
        return False

if __name__ == "__main__":
    test_simple_build()
