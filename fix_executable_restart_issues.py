#!/usr/bin/env python3
"""
Fix Executable Restart Issues
=============================

This script fixes common issues that prevent compiled executables
from running on subsequent launches, specifically for the WOW Bingo Game.

Issues addressed:
1. Corrupted settings.json file (encoding issues)
2. Pygame initialization conflicts
3. Audio device locks
4. Database connection issues
5. Temporary file cleanup
6. Process cleanup
7. Registry/settings reset
"""

import os
import sys
import json
import shutil
import sqlite3
import subprocess
import time
from pathlib import Path
from typing import Dict, Any

class ExecutableRestartFixer:
    """Fix issues preventing executable restart."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.data_dir = self.project_root / "data"
        self.fixes_applied = []
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with level indicator."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def fix_corrupted_settings(self) -> bool:
        """Fix corrupted settings.json file."""
        self.log("Fixing corrupted settings.json...")
        
        settings_file = self.data_dir / "settings.json"
        backup_file = self.data_dir / "settings.json.backup"
        
        # Create backup of current file
        if settings_file.exists():
            try:
                shutil.copy2(settings_file, backup_file)
                self.log(f"Created backup: {backup_file}")
            except Exception as e:
                self.log(f"Could not create backup: {e}", "WARNING")
        
        # Create clean settings file
        clean_settings = {
            "game": {
                "number_call_delay": 4.5,
                "strict_claim_timing": True,
                "shuffle_duration": 3.0,
                "commission_percentage": 20.0,
                "show_total_selected": True,
                "remember_cartella_numbers": True,
                "show_game_info": True
            },
            "boards": {
                "presets": [],
                "current_preset": ""
            },
            "display": {
                "fullscreen": True,
                "resolution": "1280x720",
                "animations_enabled": True,
                "show_recent_calls": True,
                "recent_calls_count": 5,
                "ui_theme": "dark",
                "ui_accent_color": "blue"
            },
            "animations": {
                "transition_animation_enabled": True,
                "transition_animation_duration": 0.3
            },
            "audio": {
                "sound_effects_enabled": True,
                "sound_effects_volume": 0.7,
                "music_enabled": True,
                "music_volume": 0.5,
                "voice_enabled": True,
                "voice_volume": 0.8,
                "cartella_announcements_enabled": True,
                "announcer_language": "Default"
            },
            "language": {
                "current_language": "English",
                "available_languages": [
                    "English", "Oromo", "Amharic", "Tigrinya", "Somali", "Agew"
                ],
                "custom_language_path": ""
            },
            "import_export": {
                "last_import_path": "",
                "last_export_path": "",
                "auto_backup": True,
                "backup_interval": 20,
                "default_export_format": "pdf",
                "export_location": "",
                "include_game_history": True,
                "include_credit_history": True,
                "include_summary_data": True,
                "include_notifications": True,
                "auto_open_exported_file": True
            },
            "notifications": {
                "enabled": True,
                "low_credit_threshold": 500,
                "expiry_warning_days": 5,
                "show_low_credit_warning": True,
                "show_expiry_warning": True
            },
            "advertising": {
                "enabled": True,
                "hidden": False,
                "text": "WOW Games - WOW Bingo",  # Use ASCII text to avoid encoding issues
                "font": "Arial",
                "font_size": 24,
                "text_color": "#FFFF00",
                "scroll_speed": 2,
                "bold": True,
                "italic": False,
                "led_style": True,
                "led_pixel_size": 4,
                "rainbow_text": True,
                "text_glow": True
            },
            "power_management": {
                "enabled": True,
                "prevent_screen_sleep": True,
                "keep_window_active": True,
                "simulate_user_activity": True,
                "check_interval": 300,
                "auto_start": True
            }
        }
        
        try:
            # Write clean settings with UTF-8 encoding
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(clean_settings, f, indent=4, ensure_ascii=False)
                
            self.log("✓ Created clean settings.json file")
            self.fixes_applied.append("Fixed corrupted settings.json")
            return True
            
        except Exception as e:
            self.log(f"✗ Could not create clean settings: {e}", "ERROR")
            return False
            
    def fix_database_issues(self) -> bool:
        """Fix database-related issues."""
        self.log("Fixing database issues...")
        
        db_files = [
            self.data_dir / "game_stats.db",
            self.data_dir / "players.db",
            self.data_dir / "vouchers.db"
        ]
        
        fixes = []
        
        for db_file in db_files:
            if db_file.exists():
                try:
                    # Remove WAL and SHM files
                    wal_file = db_file.with_suffix('.db-wal')
                    shm_file = db_file.with_suffix('.db-shm')
                    
                    for temp_file in [wal_file, shm_file]:
                        if temp_file.exists():
                            temp_file.unlink()
                            fixes.append(f"Removed {temp_file.name}")
                            
                    # Test database connection and repair if needed
                    conn = sqlite3.connect(str(db_file))
                    conn.execute("PRAGMA integrity_check")
                    conn.execute("PRAGMA optimize")
                    conn.close()
                    
                    fixes.append(f"Optimized {db_file.name}")
                    
                except Exception as e:
                    self.log(f"✗ Database issue with {db_file.name}: {e}", "WARNING")
                    
        if fixes:
            self.fixes_applied.extend(fixes)
            self.log(f"✓ Applied {len(fixes)} database fixes")
            return True
        return False
        
    def fix_pygame_audio_conflicts(self) -> bool:
        """Fix pygame and audio device conflicts."""
        self.log("Fixing pygame/audio conflicts...")
        
        # Create a script to reset audio devices
        reset_script = """
import pygame
import sys
import time

try:
    # Initialize pygame
    pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
    pygame.mixer.init()
    
    # Quit pygame to release audio devices
    pygame.mixer.quit()
    pygame.quit()
    
    print("Audio devices reset successfully")
    sys.exit(0)
    
except Exception as e:
    print(f"Audio reset failed: {e}")
    sys.exit(1)
"""
        
        try:
            # Run the audio reset script
            result = subprocess.run([sys.executable, '-c', reset_script], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log("✓ Audio devices reset successfully")
                self.fixes_applied.append("Reset audio devices")
                return True
            else:
                self.log(f"✗ Audio reset failed: {result.stderr}", "WARNING")
                
        except Exception as e:
            self.log(f"✗ Could not reset audio devices: {e}", "WARNING")
            
        return False
        
    def create_executable_wrapper(self) -> bool:
        """Create a wrapper script that handles cleanup before running executable."""
        self.log("Creating executable wrapper...")
        
        wrapper_script = f'''@echo off
:: WOW Bingo Game Executable Wrapper
:: This script ensures clean startup by handling cleanup before running the game

echo Starting WOW Bingo Game...

:: Kill any existing processes
taskkill /f /im "WOWBingoGame.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *main.py*" >nul 2>&1

:: Clean temporary files
del /q "{self.data_dir}\\*.tmp" >nul 2>&1
del /q "{self.data_dir}\\*.lock" >nul 2>&1
del /q "{self.data_dir}\\*.pid" >nul 2>&1
del /q "{self.data_dir}\\*.db-wal" >nul 2>&1
del /q "{self.data_dir}\\*.db-shm" >nul 2>&1

:: Wait a moment for cleanup
timeout /t 2 /nobreak >nul

:: Run the game
echo Launching game...
start "" "{self.project_root}\\dist\\WOWBingoGame.exe"

:: Exit wrapper
exit
'''
        
        wrapper_file = self.project_root / "RunWOWBingoGame.bat"
        
        try:
            with open(wrapper_file, 'w') as f:
                f.write(wrapper_script)
                
            self.log(f"✓ Created wrapper script: {wrapper_file}")
            self.fixes_applied.append("Created executable wrapper")
            return True
            
        except Exception as e:
            self.log(f"✗ Could not create wrapper: {e}", "ERROR")
            return False
            
    def create_cleanup_script(self) -> bool:
        """Create a cleanup script for manual use."""
        self.log("Creating cleanup script...")
        
        cleanup_script = f'''@echo off
:: WOW Bingo Game Cleanup Script
:: Run this if the game won't start

echo WOW Bingo Game - Emergency Cleanup
echo ===================================

echo Stopping all game processes...
taskkill /f /im "WOWBingoGame.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *main.py*" >nul 2>&1

echo Cleaning temporary files...
del /q "{self.data_dir}\\*.tmp" >nul 2>&1
del /q "{self.data_dir}\\*.lock" >nul 2>&1
del /q "{self.data_dir}\\*.pid" >nul 2>&1
del /q "{self.data_dir}\\*.db-wal" >nul 2>&1
del /q "{self.data_dir}\\*.db-shm" >nul 2>&1

echo Resetting audio devices...
python -c "import pygame; pygame.mixer.quit(); pygame.quit()" >nul 2>&1

echo Cleanup completed!
echo You can now try running the game again.
pause
'''
        
        cleanup_file = self.project_root / "CleanupWOWBingoGame.bat"
        
        try:
            with open(cleanup_file, 'w') as f:
                f.write(cleanup_script)
                
            self.log(f"✓ Created cleanup script: {cleanup_file}")
            self.fixes_applied.append("Created cleanup script")
            return True
            
        except Exception as e:
            self.log(f"✗ Could not create cleanup script: {e}", "ERROR")
            return False
            
    def apply_all_fixes(self) -> bool:
        """Apply all available fixes."""
        self.log("=" * 60)
        self.log("WOW Bingo Game - Executable Restart Issue Fixer")
        self.log("=" * 60)
        
        fixes = [
            ("Corrupted Settings", self.fix_corrupted_settings),
            ("Database Issues", self.fix_database_issues),
            ("Pygame/Audio Conflicts", self.fix_pygame_audio_conflicts),
            ("Executable Wrapper", self.create_executable_wrapper),
            ("Cleanup Script", self.create_cleanup_script)
        ]
        
        success_count = 0
        
        for fix_name, fix_func in fixes:
            self.log(f"\n--- {fix_name} ---")
            try:
                if fix_func():
                    self.log(f"{fix_name}: FIXED ✓")
                    success_count += 1
                else:
                    self.log(f"{fix_name}: NO ACTION NEEDED")
            except Exception as e:
                self.log(f"{fix_name}: FIX FAILED - {e}", "ERROR")
                
        return success_count > 0
        
    def generate_report(self) -> None:
        """Generate a fix report."""
        self.log("\n" + "=" * 60)
        self.log("FIX REPORT")
        self.log("=" * 60)
        
        if self.fixes_applied:
            self.log(f"Fixes applied ({len(self.fixes_applied)}):")
            for fix in self.fixes_applied:
                self.log(f"  • {fix}")
                
            self.log("\n✅ FIXES COMPLETED!")
            self.log("\nHow to run the game now:")
            self.log("1. Use the wrapper: RunWOWBingoGame.bat (RECOMMENDED)")
            self.log("2. Or run directly: dist/WOWBingoGame.exe")
            self.log("3. If issues persist: CleanupWOWBingoGame.bat")
            
        else:
            self.log("No fixes were needed or applied")

def main():
    """Main entry point."""
    try:
        fixer = ExecutableRestartFixer()
        
        # Apply all fixes
        fixer.apply_all_fixes()
        
        # Generate report
        fixer.generate_report()
        
        print("\n" + "=" * 60)
        print("RESTART ISSUE FIXES COMPLETED!")
        print("=" * 60)
        print("Your executable should now run properly on subsequent launches.")
        print("\nRecommended usage:")
        print("• Use RunWOWBingoGame.bat for best results")
        print("• If problems persist, run CleanupWOWBingoGame.bat")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\nFix process interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fix process failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
