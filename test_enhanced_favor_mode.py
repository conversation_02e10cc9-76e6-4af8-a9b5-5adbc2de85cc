"""
Comprehensive Test Suite for Enhanced Favor Mode

This test suite validates all the enhanced favor mode functionality including:
- Enhanced hotkey handling
- Real-time switching
- Proper animated color indicators
- Clean reset functionality
- Turn off mode functionality
"""

import pygame
import sys
import os
import time

def test_enhanced_activation():
    """Test enhanced activation with animations"""
    print("=" * 70)
    print("TESTING ENHANCED ACTIVATION")
    print("=" * 70)

    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Enhanced Favor Mode Test")

    # Create a mock game object
    class MockGame:
        def __init__(self):
            self.game_started = False
            self.bingo_caller = None

    game = MockGame()

    # Import and test favor mode
    from bingo_favor_mode import BingoFavorMode
    favor_mode = BingoFavorMode(game)

    print("1. Testing basic activation...")
    result = favor_mode.activate(5)
    assert result == True, "Activation should succeed"
    assert favor_mode.active == True, "Should be active"
    assert favor_mode.target_cartella == 5, "Should target cartella 5"
    assert favor_mode._show_activation_animation == True, "Should show activation animation"
    print("   ✓ Basic activation works")

    print("2. Testing real-time switching...")
    result = favor_mode.switch_target(10)
    assert result == True, "Switch should succeed"
    assert favor_mode.target_cartella == 10, "Should now target cartella 10"
    assert favor_mode._previous_target == 5, "Should remember previous target"
    assert favor_mode._show_switch_animation == True, "Should show switch animation"
    print("   ✓ Real-time switching works")

    print("3. Testing toggle functionality...")
    # Toggle off
    result = favor_mode.toggle_favor(10)
    assert favor_mode.active == False, "Should be deactivated"
    assert favor_mode._show_deactivation_animation == True, "Should show deactivation animation"

    # Toggle back on
    result = favor_mode.toggle_favor(15)
    assert favor_mode.active == True, "Should be activated"
    assert favor_mode.target_cartella == 15, "Should target cartella 15"
    print("   ✓ Toggle functionality works")

    print("4. Testing force deactivate...")
    result = favor_mode.force_deactivate()
    assert result == True, "Force deactivate should succeed"
    assert favor_mode.active == False, "Should be deactivated"
    assert favor_mode._last_hotkey_action == "force_deactivated", "Should record action"
    print("   ✓ Force deactivate works")

    pygame.quit()
    return True

def test_enhanced_indicators():
    """Test enhanced visual indicators"""
    print("\n" + "=" * 70)
    print("TESTING ENHANCED VISUAL INDICATORS")
    print("=" * 70)

    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Enhanced Indicators Test")

    # Create a mock game object
    class MockGame:
        def __init__(self):
            self.game_started = False
            self.bingo_caller = None

    game = MockGame()

    from bingo_favor_mode import BingoFavorMode
    favor_mode = BingoFavorMode(game)

    print("1. Testing activation animation...")
    favor_mode.activate(7)
    assert favor_mode._show_activation_animation == True, "Should show activation animation"
    assert favor_mode.indicator_alpha == 255, "Should have full alpha"
    assert favor_mode._indicator_visible == True, "Should be visible"
    print("   ✓ Activation animation initialized")

    print("2. Testing switch animation...")
    favor_mode.switch_target(12)
    assert favor_mode._show_switch_animation == True, "Should show switch animation"
    assert favor_mode._previous_target == 7, "Should remember previous target"
    print("   ✓ Switch animation initialized")

    print("3. Testing deactivation animation...")
    favor_mode.deactivate()
    assert favor_mode._show_deactivation_animation == True, "Should show deactivation animation"
    print("   ✓ Deactivation animation initialized")

    print("4. Testing indicator drawing (animations only, no persistent indicator)...")
    try:
        # Test that drawing doesn't crash and only shows temporary animations
        favor_mode.activate(3)
        favor_mode.draw_indicator(screen)
        print("   ✓ Indicator drawing works (temporary animations only)")
    except Exception as e:
        print(f"   ✗ Indicator drawing failed: {e}")
        return False

    pygame.quit()
    return True

def test_enhanced_reset():
    """Test enhanced reset functionality"""
    print("\n" + "=" * 70)
    print("TESTING ENHANCED RESET FUNCTIONALITY")
    print("=" * 70)

    # Create a mock game object
    class MockGame:
        def __init__(self):
            self.game_started = True
            self.bingo_caller = None

    game = MockGame()

    from bingo_favor_mode import BingoFavorMode
    favor_mode = BingoFavorMode(game)

    print("1. Setting up favor mode with various states...")
    favor_mode.activate(8)
    favor_mode.switch_target(15)
    favor_mode._hotkey_feedback_timer = time.time()
    favor_mode._last_hotkey_action = "test_action"

    # Verify setup
    assert favor_mode.active == True, "Should be active before reset"
    assert favor_mode.target_cartella == 15, "Should have target before reset"

    print("2. Testing comprehensive reset...")
    favor_mode.reset()

    # Verify all states are cleared
    assert favor_mode.active == False, "Should be inactive after reset"
    assert favor_mode.target_cartella == None, "Should have no target after reset"
    assert favor_mode._show_activation_animation == False, "Should clear activation animation"
    assert favor_mode._show_deactivation_animation == False, "Should clear deactivation animation"
    assert favor_mode._show_switch_animation == False, "Should clear switch animation"
    assert favor_mode._hotkey_feedback_timer == 0, "Should clear hotkey timer"
    assert favor_mode._last_hotkey_action == None, "Should clear last action"
    assert favor_mode._previous_target == None, "Should clear previous target"
    assert favor_mode.indicator_alpha == 0, "Should reset indicator alpha"

    print("   ✓ Comprehensive reset works correctly")

    return True

def test_status_info():
    """Test status information functionality"""
    print("\n" + "=" * 70)
    print("TESTING STATUS INFORMATION")
    print("=" * 70)

    # Create a mock game object
    class MockGame:
        def __init__(self):
            self.game_started = False
            self.bingo_caller = None

    game = MockGame()

    from bingo_favor_mode import BingoFavorMode
    favor_mode = BingoFavorMode(game)

    print("1. Testing status info when inactive...")
    status = favor_mode.get_status_info()
    assert status['active'] == False, "Should report inactive"
    assert status['target_cartella'] == None, "Should report no target"
    print("   ✓ Inactive status info correct")

    print("2. Testing status info when active...")
    favor_mode.activate(20)
    status = favor_mode.get_status_info()
    assert status['active'] == True, "Should report active"
    assert status['target_cartella'] == 20, "Should report correct target"
    print("   ✓ Active status info correct")

    print("3. Testing status info after switch...")
    favor_mode.switch_target(25)
    status = favor_mode.get_status_info()
    assert status['target_cartella'] == 25, "Should report new target"
    assert status['previous_target'] == 20, "Should report previous target"
    print("   ✓ Switch status info correct")

    return True

def test_hotkey_simulation():
    """Test hotkey functionality simulation"""
    print("\n" + "=" * 70)
    print("TESTING HOTKEY FUNCTIONALITY SIMULATION")
    print("=" * 70)

    # Create a mock game object
    class MockGame:
        def __init__(self):
            self.game_started = False
            self.bingo_caller = None
            self.favor_mode = None

    game = MockGame()

    from bingo_favor_mode import BingoFavorMode
    favor_mode = BingoFavorMode(game)
    game.favor_mode = favor_mode

    print("1. Testing Ctrl+Shift+[number] toggle...")
    # Simulate Ctrl+Shift+5
    result = favor_mode.toggle_favor(5)
    assert result == True, "Toggle should succeed"
    assert favor_mode.active == True, "Should be active"
    assert favor_mode.target_cartella == 5, "Should target cartella 5"

    # Toggle again to deactivate
    result = favor_mode.toggle_favor(5)
    assert favor_mode.active == False, "Should be deactivated"
    print("   ✓ Ctrl+Shift+[number] toggle simulation works")

    print("2. Testing Ctrl+Alt+[number] quick switch...")
    # First activate
    favor_mode.activate(10)
    # Then switch
    result = favor_mode.switch_target(7)
    assert result == True, "Switch should succeed"
    assert favor_mode.target_cartella == 7, "Should switch to cartella 7"
    assert favor_mode._previous_target == 10, "Should remember previous"
    print("   ✓ Ctrl+Alt+[number] quick switch simulation works")

    print("3. Testing Ctrl+Shift+D force deactivate...")
    result = favor_mode.force_deactivate()
    assert result == True, "Force deactivate should succeed"
    assert favor_mode.active == False, "Should be deactivated"
    print("   ✓ Ctrl+Shift+D force deactivate simulation works")

    print("4. Testing Alt+F emergency toggle...")
    result = favor_mode.toggle_favor(1)
    assert result == True, "Emergency toggle should succeed"
    assert favor_mode.target_cartella == 1, "Should target cartella 1"
    print("   ✓ Alt+F emergency toggle simulation works")

    return True

def main():
    """Run the comprehensive enhanced favor mode test suite"""
    print("ENHANCED FAVOR MODE - COMPREHENSIVE TEST SUITE")
    print("=" * 70)

    all_tests_passed = True

    try:
        # Test 1: Enhanced activation
        if not test_enhanced_activation():
            all_tests_passed = False

        # Test 2: Enhanced indicators
        if not test_enhanced_indicators():
            all_tests_passed = False

        # Test 3: Enhanced reset
        if not test_enhanced_reset():
            all_tests_passed = False

        # Test 4: Status info
        if not test_status_info():
            all_tests_passed = False

        # Test 5: Hotkey simulation
        if not test_hotkey_simulation():
            all_tests_passed = False

        # Final results
        print("\n" + "=" * 70)
        print("ENHANCED FAVOR MODE TEST RESULTS")
        print("=" * 70)

        if all_tests_passed:
            print("🎉 ALL ENHANCED FAVOR MODE TESTS PASSED! ✓")
            print("\nThe enhanced favor mode is working correctly:")
            print("✓ Enhanced activation with animations")
            print("✓ Real-time switching between cartellas")
            print("✓ Clean temporary animations only (no persistent indicator)")
            print("✓ Clean reset functionality")
            print("✓ Force deactivation capability")
            print("✓ Comprehensive status information")
            print("✓ Enhanced hotkey handling")
            print("\nUsers now have a clean, unobtrusive favor mode experience!")
        else:
            print("❌ SOME ENHANCED FAVOR MODE TESTS FAILED! ✗")
            print("\nThe enhanced favor mode needs additional work.")

        return all_tests_passed

    except Exception as e:
        print(f"\n❌ ENHANCED FAVOR MODE TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
