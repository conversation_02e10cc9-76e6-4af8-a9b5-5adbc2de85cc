### Update for set_prize_pool_manually method ###

# Original method
def set_prize_pool_manually(self, value):
    """Set the prize pool manually and activate override mode"""
    try:
        value = int(value)
        if value < 0:
            value = 0
        self.prize_pool = value
        self.prize_pool_manual_override = True
        self.show_message(f"Prize pool set manually to {value} ETB", "success")
    except ValueError:
        self.show_message("Invalid prize pool value", "error")

# Updated method with UI state saving
def set_prize_pool_manually(self, value):
    """Set the prize pool manually and activate override mode"""
    try:
        value = int(value)
        if value < 0:
            value = 0
        self.prize_pool = value
        self.prize_pool_manual_override = True
        
        # Save UI state if option is enabled
        if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
            try:
                from player_storage import save_ui_state
                save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, True)
                print(f"Updated UI state after setting prize pool manually: {self.prize_pool}")
            except Exception as e:
                print(f"Error saving UI state after setting prize pool manually: {e}")
                
        self.show_message(f"Prize pool set manually to {value} ETB", "success")
    except ValueError:
        self.show_message("Invalid prize pool value", "error") 