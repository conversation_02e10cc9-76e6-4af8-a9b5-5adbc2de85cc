import re

def fix_syntax_error():
    # Read the original file
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Make a backup
    with open('Board_selection_fixed.py.bak2', 'w', encoding='utf-8') as backup:
        backup.write(content)
    
    # Fix the missing newline
    pattern = r'self\.show_message\("Invalid prize pool value", "error"\)def show_board_selection'
    replacement = r'self.show_message("Invalid prize pool value", "error")\n\ndef show_board_selection'
    
    updated_content = content.replace(pattern, replacement)
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print("Fixed syntax error in Board_selection_fixed.py")

if __name__ == "__main__":
    fix_syntax_error() 