"""
Chart Renderer Module for the WOW Games application.

This module provides modern, animated chart rendering capabilities
for data visualization in the stats page.
"""

import pygame
import math
from ui_theme import get_ui_theme
from ui_animation import get_animation_manager

class ChartRenderer:
    """
    Renders various chart types with animation support.
    """
    
    def __init__(self):
        self.theme = get_ui_theme()
        self.animation_manager = get_animation_manager()
        self.cached_surfaces = {}
    
    def clear_cache(self):
        """Clear the surface cache"""
        self.cached_surfaces = {}
    
    def render_bar_chart(self, surface, rect, data, labels=None, title=None, 
                        animate=True, animation_id=None, max_value=None):
        """
        Render a bar chart with optional animation.
        
        Args:
            surface: Surface to draw on
            rect: Rectangle defining the chart area
            data: List of data values
            labels: List of labels for each bar
            title: Chart title
            animate: Whether to animate the chart
            animation_id: ID for animation tracking
            max_value: Maximum value for scaling (auto-calculated if None)
        """
        if not data:
            return
        
        # Calculate dimensions
        x, y, width, height = rect
        chart_height = height - 60  # Reserve space for labels and title
        
        # Draw background and border
        pygame.draw.rect(surface, self.theme.get_color('card_bg'), rect, border_radius=10)
        pygame.draw.rect(surface, self.theme.get_color('border_light'), rect, border_radius=10, width=1)
        
        # Draw title if provided
        if title:
            title_font = pygame.font.Font(None, 24)
            self.theme.draw_text(surface, title, title_font, 
                               self.theme.get_color('text_primary'),
                               (x + width // 2, y + 10), align="center")
        
        # Calculate max value if not provided
        if max_value is None:
            max_value = max(data) if data else 1
            # Add 10% padding to max value
            max_value *= 1.1
        
        # Ensure max_value is not zero
        max_value = max(max_value, 1)
        
        # Calculate bar width and spacing
        num_bars = len(data)
        bar_spacing = 10
        bar_width = (width - (num_bars + 1) * bar_spacing) / num_bars
        
        # Draw grid lines
        grid_color = self.theme.get_color('chart_grid')
        num_grid_lines = 5
        for i in range(num_grid_lines + 1):
            grid_y = y + 40 + (chart_height - 40) * (1 - i / num_grid_lines)
            pygame.draw.line(surface, grid_color, (x + 10, grid_y), (x + width - 10, grid_y), 1)
            
            # Draw grid value
            grid_value = max_value * i / num_grid_lines
            value_text = f"{grid_value:,.1f}"
            grid_font = pygame.font.Font(None, 16)
            self.theme.draw_text(surface, value_text, grid_font, 
                               self.theme.get_color('text_muted'),
                               (x + 8, grid_y), align="left")
        
        # Draw bars
        for i, value in enumerate(data):
            # Calculate bar height based on value
            bar_height = (value / max_value) * (chart_height - 40)
            
            # Apply animation if enabled
            if animate and animation_id:
                animation_progress = 1.0  # Default to fully visible
                
                # Check if we have an animation for this bar
                bar_anim_id = f"{animation_id}_bar_{i}"
                if self.animation_manager.is_animating(bar_anim_id, 'progress'):
                    animation_progress = self.animation_manager.get_value(bar_anim_id, 'progress', 1.0)
                elif not self.animation_manager.completed_animations:
                    # Start a new animation for this bar
                    delay = i * 0.05  # Stagger the animations
                    self.animation_manager.add_animation(
                        bar_anim_id, 'progress', 0, 1, 
                        duration=0.5, delay=delay, 
                        easing="ease_out_cubic"
                    )
                    animation_progress = 0
                
                # Apply animation progress
                bar_height *= animation_progress
            
            # Calculate bar position
            bar_x = x + bar_spacing + i * (bar_width + bar_spacing)
            bar_y = y + chart_height - bar_height
            
            # Draw bar with gradient
            bar_rect = pygame.Rect(bar_x, bar_y, bar_width, bar_height)
            
            # Get color based on index
            day_colors = [
                self.theme.get_color('sunday_color'),
                self.theme.get_color('monday_color'),
                self.theme.get_color('tuesday_color'),
                self.theme.get_color('wednesday_color'),
                self.theme.get_color('thursday_color'),
                self.theme.get_color('friday_color'),
                self.theme.get_color('saturday_color')
            ]
            
            bar_color = day_colors[i % len(day_colors)]
            
            # Create gradient for bar
            gradient_surface = self.theme.get_gradient_surface(
                int(bar_width), int(bar_height),
                bar_color, (bar_color[0], bar_color[1], bar_color[2], 180)
            )
            
            # Draw bar
            surface.blit(gradient_surface, bar_rect)
            
            # Draw bar border
            pygame.draw.rect(surface, bar_color, bar_rect, width=1)
            
            # Draw value on top of bar
            value_font = pygame.font.Font(None, 18)
            value_text = f"{value:,.1f}"
            self.theme.draw_text(surface, value_text, value_font, 
                               self.theme.get_color('text_primary'),
                               (bar_x + bar_width // 2, bar_y - 20), align="center")
            
            # Draw label if provided
            if labels and i < len(labels):
                label_font = pygame.font.Font(None, 18)
                self.theme.draw_text(surface, labels[i], label_font, 
                                   self.theme.get_color('text_secondary'),
                                   (bar_x + bar_width // 2, y + chart_height + 5), align="center")
    
    def render_line_chart(self, surface, rect, data, labels=None, title=None, 
                         animate=True, animation_id=None, max_value=None,
                         fill=True, line_color=None, fill_color=None):
        """
        Render a line chart with optional animation.
        
        Args:
            surface: Surface to draw on
            rect: Rectangle defining the chart area
            data: List of data values
            labels: List of labels for each point
            title: Chart title
            animate: Whether to animate the chart
            animation_id: ID for animation tracking
            max_value: Maximum value for scaling (auto-calculated if None)
            fill: Whether to fill the area under the line
            line_color: Color of the line
            fill_color: Color of the fill
        """
        if not data:
            return
        
        # Calculate dimensions
        x, y, width, height = rect
        chart_height = height - 60  # Reserve space for labels and title
        
        # Draw background and border
        pygame.draw.rect(surface, self.theme.get_color('card_bg'), rect, border_radius=10)
        pygame.draw.rect(surface, self.theme.get_color('border_light'), rect, border_radius=10, width=1)
        
        # Draw title if provided
        if title:
            title_font = pygame.font.Font(None, 24)
            self.theme.draw_text(surface, title, title_font, 
                               self.theme.get_color('text_primary'),
                               (x + width // 2, y + 10), align="center")
        
        # Calculate max value if not provided
        if max_value is None:
            max_value = max(data) if data else 1
            # Add 10% padding to max value
            max_value *= 1.1
        
        # Ensure max_value is not zero
        max_value = max(max_value, 1)
        
        # Set default colors if not provided
        if line_color is None:
            line_color = self.theme.get_color('chart_line')
        if fill_color is None:
            fill_color = self.theme.get_color('chart_fill')
        
        # Draw grid lines
        grid_color = self.theme.get_color('chart_grid')
        num_grid_lines = 5
        for i in range(num_grid_lines + 1):
            grid_y = y + 40 + (chart_height - 40) * (1 - i / num_grid_lines)
            pygame.draw.line(surface, grid_color, (x + 10, grid_y), (x + width - 10, grid_y), 1)
            
            # Draw grid value
            grid_value = max_value * i / num_grid_lines
            value_text = f"{grid_value:,.1f}"
            grid_font = pygame.font.Font(None, 16)
            self.theme.draw_text(surface, value_text, grid_font, 
                               self.theme.get_color('text_muted'),
                               (x + 8, grid_y), align="left")
        
        # Calculate point positions
        num_points = len(data)
        point_spacing = (width - 40) / (num_points - 1) if num_points > 1 else 0
        points = []
        
        for i, value in enumerate(data):
            # Calculate point position
            point_x = x + 20 + i * point_spacing
            
            # Apply animation if enabled
            if animate and animation_id:
                animation_progress = 1.0  # Default to fully visible
                
                # Check if we have an animation for this chart
                if self.animation_manager.is_animating(animation_id, 'progress'):
                    animation_progress = self.animation_manager.get_value(animation_id, 'progress', 1.0)
                    # Only show points up to the current animation progress
                    if i > animation_progress * num_points:
                        continue
                elif not self.animation_manager.completed_animations:
                    # Start a new animation for this chart
                    self.animation_manager.add_animation(
                        animation_id, 'progress', 0, 1, 
                        duration=1.0, delay=0, 
                        easing="ease_out_cubic"
                    )
                    # Only show the first point initially
                    if i > 0:
                        continue
            
            # Calculate point height based on value
            point_height = (value / max_value) * (chart_height - 40)
            point_y = y + chart_height - point_height
            
            points.append((point_x, point_y))
            
            # Draw point
            pygame.draw.circle(surface, line_color, (point_x, point_y), 4)
            
            # Draw label if provided
            if labels and i < len(labels):
                label_font = pygame.font.Font(None, 18)
                self.theme.draw_text(surface, labels[i], label_font, 
                                   self.theme.get_color('text_secondary'),
                                   (point_x, y + chart_height + 5), align="center")
        
        # Draw fill if requested and we have at least 2 points
        if fill and len(points) >= 2:
            # Create a polygon for the fill
            fill_points = points.copy()
            fill_points.append((points[-1][0], y + chart_height))  # Bottom right
            fill_points.append((points[0][0], y + chart_height))   # Bottom left
            
            # Draw fill
            pygame.draw.polygon(surface, fill_color, fill_points)
        
        # Draw line connecting points
        if len(points) >= 2:
            pygame.draw.lines(surface, line_color, False, points, 2)
    
    def render_pie_chart(self, surface, rect, data, labels=None, title=None, 
                        animate=True, animation_id=None, colors=None):
        """
        Render a pie chart with optional animation.
        
        Args:
            surface: Surface to draw on
            rect: Rectangle defining the chart area
            data: List of data values
            labels: List of labels for each slice
            title: Chart title
            animate: Whether to animate the chart
            animation_id: ID for animation tracking
            colors: List of colors for each slice
        """
        if not data:
            return
        
        # Calculate dimensions
        x, y, width, height = rect
        
        # Draw background and border
        pygame.draw.rect(surface, self.theme.get_color('card_bg'), rect, border_radius=10)
        pygame.draw.rect(surface, self.theme.get_color('border_light'), rect, border_radius=10, width=1)
        
        # Draw title if provided
        if title:
            title_font = pygame.font.Font(None, 24)
            self.theme.draw_text(surface, title, title_font, 
                               self.theme.get_color('text_primary'),
                               (x + width // 2, y + 10), align="center")
        
        # Calculate total value
        total = sum(data)
        if total == 0:
            total = 1  # Avoid division by zero
        
        # Set default colors if not provided
        if colors is None:
            colors = [
                self.theme.get_color('sunday_color'),
                self.theme.get_color('monday_color'),
                self.theme.get_color('tuesday_color'),
                self.theme.get_color('wednesday_color'),
                self.theme.get_color('thursday_color'),
                self.theme.get_color('friday_color'),
                self.theme.get_color('saturday_color')
            ]
        
        # Calculate center and radius
        center_x = x + width // 2
        center_y = y + height // 2
        radius = min(width, height) // 2 - 20
        
        # Draw pie slices
        start_angle = 0
        for i, value in enumerate(data):
            # Calculate slice angle
            slice_angle = 360 * (value / total)
            
            # Apply animation if enabled
            if animate and animation_id:
                animation_progress = 1.0  # Default to fully visible
                
                # Check if we have an animation for this chart
                if self.animation_manager.is_animating(animation_id, 'progress'):
                    animation_progress = self.animation_manager.get_value(animation_id, 'progress', 1.0)
                    # Scale the slice angle by the animation progress
                    slice_angle *= animation_progress
                elif not self.animation_manager.completed_animations:
                    # Start a new animation for this chart
                    self.animation_manager.add_animation(
                        animation_id, 'progress', 0, 1, 
                        duration=0.8, delay=0, 
                        easing="ease_out_cubic"
                    )
                    slice_angle = 0  # Start with no slice
            
            # Get color for this slice
            color = colors[i % len(colors)]
            
            # Draw slice
            if slice_angle > 0:
                end_angle = start_angle + slice_angle
                
                # Convert angles to radians
                start_rad = math.radians(start_angle)
                end_rad = math.radians(end_angle)
                
                # Calculate points for the slice
                points = [(center_x, center_y)]
                
                # Add the start point on the edge of the circle
                points.append((
                    center_x + radius * math.cos(start_rad),
                    center_y - radius * math.sin(start_rad)
                ))
                
                # Add points along the arc
                num_arc_points = max(1, int(slice_angle / 5))  # One point every 5 degrees
                for j in range(1, num_arc_points + 1):
                    arc_angle = start_rad + (end_rad - start_rad) * (j / num_arc_points)
                    points.append((
                        center_x + radius * math.cos(arc_angle),
                        center_y - radius * math.sin(arc_angle)
                    ))
                
                # Draw the slice
                pygame.draw.polygon(surface, color, points)
                
                # Draw slice border
                pygame.draw.line(surface, (255, 255, 255), 
                               (center_x, center_y), 
                               (center_x + radius * math.cos(start_rad), 
                                center_y - radius * math.sin(start_rad)), 1)
                pygame.draw.line(surface, (255, 255, 255), 
                               (center_x, center_y), 
                               (center_x + radius * math.cos(end_rad), 
                                center_y - radius * math.sin(end_rad)), 1)
                
                # Draw label if provided
                if labels and i < len(labels):
                    # Calculate position for label
                    label_angle = start_rad + (end_rad - start_rad) / 2
                    label_distance = radius * 0.7
                    label_x = center_x + label_distance * math.cos(label_angle)
                    label_y = center_y - label_distance * math.sin(label_angle)
                    
                    # Draw label
                    label_font = pygame.font.Font(None, 18)
                    self.theme.draw_text(surface, labels[i], label_font, 
                                       (255, 255, 255),
                                       (label_x, label_y), align="center")
                    
                    # Draw percentage
                    percentage = value / total * 100
                    percentage_text = f"{percentage:.1f}%"
                    percentage_font = pygame.font.Font(None, 16)
                    self.theme.draw_text(surface, percentage_text, percentage_font, 
                                       (255, 255, 255),
                                       (label_x, label_y + 20), align="center")
            
            # Update start angle for next slice
            start_angle += slice_angle


# Create a singleton instance
_chart_renderer = None

def get_chart_renderer():
    """Get the singleton chart renderer instance"""
    global _chart_renderer
    if _chart_renderer is None:
        _chart_renderer = ChartRenderer()
    return _chart_renderer