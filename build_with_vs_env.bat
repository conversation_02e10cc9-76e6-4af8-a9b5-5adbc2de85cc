@echo off
REM WOW Bingo Game - Build with Visual Studio Environment
REM ====================================================
REM
REM This script properly sets up the Visual Studio environment
REM before running Nuitka to ensure compiler detection works.

echo.
echo ================================================================================
echo WOW Bingo Game - Build with Visual Studio Environment Setup
echo ================================================================================
echo.

REM Try to find and setup Visual Studio environment
echo Setting up Visual Studio environment...

REM Try different Visual Studio versions and paths
set "VCVARSALL_FOUND="

REM VS 2022 BuildTools
if exist "C:\Program Files\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VCVARSALL_PATH=C:\Program Files\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvarsall.bat"
    set "VCVARSALL_FOUND=1"
    echo Found VS 2022 BuildTools
    goto :setup_env
)

REM VS 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VCVARSALL_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat"
    set "VCVARSALL_FOUND=1"
    echo Found VS 2022 Community
    goto :setup_env
)

REM VS 2022 BuildTools (x86 path)
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VCVARSALL_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvarsall.bat"
    set "VCVARSALL_FOUND=1"
    echo Found VS 2022 BuildTools (x86)
    goto :setup_env
)

REM VS 2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvarsall.bat" (
    set "VCVARSALL_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvarsall.bat"
    set "VCVARSALL_FOUND=1"
    echo Found VS 2019 BuildTools
    goto :setup_env
)

REM If no vcvarsall.bat found
if not defined VCVARSALL_FOUND (
    echo.
    echo ERROR: Visual Studio Build Tools not found!
    echo.
    echo Please install Visual Studio Build Tools:
    echo 1. Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
    echo 2. Install "C++ build tools" workload
    echo 3. Include "MSVC v143 - VS 2022 C++ x64/x86 build tools"
    echo 4. Include "Windows 10/11 SDK"
    echo.
    echo Or run the diagnostic script: python check_vs_tools.py
    echo.
    pause
    exit /b 1
)

:setup_env
echo Setting up Visual Studio environment from: %VCVARSALL_PATH%
echo.

REM Setup the Visual Studio environment
call "%VCVARSALL_PATH%" x64

if errorlevel 1 (
    echo.
    echo ERROR: Failed to setup Visual Studio environment
    echo Try running as Administrator or check your Visual Studio installation
    echo.
    pause
    exit /b 1
)

echo Visual Studio environment setup complete.
echo.

REM Verify compiler is now available
echo Verifying compiler availability...
cl >nul 2>&1
if errorlevel 1 (
    echo WARNING: cl.exe still not found after environment setup
    echo This might still work with Nuitka, continuing...
) else (
    echo ✅ cl.exe compiler is now available
)

echo.
echo ================================================================================
echo Starting Nuitka Build with Visual Studio Environment
echo ================================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found in PATH
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found. Run this from the project directory.
    pause
    exit /b 1
)

REM Run Nuitka build with proper environment
echo Running Nuitka build with Visual Studio environment...
python nuitka_build_simple.py --verbose

REM Check if build succeeded
if exist "dist\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo BUILD SUCCESS with Visual Studio Environment!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game.exe
    goto :success
)

if exist "dist\WOW_Bingo_Game\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo BUILD SUCCESS with Visual Studio Environment!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game\WOW_Bingo_Game.exe
    goto :success
)

REM Build failed
echo.
echo ================================================================================
echo BUILD FAILED
echo ================================================================================
echo.
echo Even with Visual Studio environment setup, the build failed.
echo.
echo Try these alternatives:
echo 1. Use PyInstaller instead: python build_fallback.py
echo 2. Check the diagnostic: python check_vs_tools.py
echo 3. Try running as Administrator
echo 4. Restart your computer and try again
echo.
pause
exit /b 1

:success
echo.
echo ✅ Visual Studio Build Tools were successfully detected and used!
echo ✅ Your high-performance Nuitka executable is ready!
echo.
echo The executable includes all game assets and can run on any Windows PC.
echo.
pause
exit /b 0
