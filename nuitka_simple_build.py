#!/usr/bin/env python3
"""
Simple Nuitka Build Script for WOW Bingo Game
=============================================

This is a simplified version that focuses on essential packages only
to avoid complex dependency issues.

Usage:
    python nuitka_simple_build.py [--debug] [--clean]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def check_prerequisites():
    """Check build prerequisites."""
    log("Checking prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        error("Python 3.7 or higher is required!")
    
    log(f"Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check Nuitka
    try:
        result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'], 
                              capture_output=True, text=True, check=True)
        log(f"Nuitka version: {result.stdout.strip()}")
    except subprocess.CalledProcessError:
        error("Nuitka not found! Install with: pip install nuitka")
    
    # Check required packages
    required = ['pygame', 'pyperclip']
    for pkg in required:
        try:
            __import__(pkg)
            log(f"Found package: {pkg}")
        except ImportError:
            error(f"Missing package: {pkg}. Install with: pip install {pkg}")

def prepare_build_environment(clean=False):
    """Prepare build environment."""
    log("Preparing build environment...")
    
    project_root = Path(__file__).parent
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    if clean:
        log("Cleaning build directories...")
        for dir_path in [build_dir, dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                log(f"Cleaned {dir_path}")
    
    build_dir.mkdir(exist_ok=True)
    dist_dir.mkdir(exist_ok=True)
    
    return project_root, build_dir, dist_dir

def build_executable(project_root, build_dir, debug=False):
    """Build the executable with essential packages only."""
    log(f"Building executable ({'debug' if debug else 'release'} mode)...")
    
    # Essential packages only
    essential_packages = [
        'pygame', 'pyperclip', 'json', 'datetime', 'sqlite3',
        'os', 'sys', 'time', 'math', 'random', 'threading',
        'collections', 'itertools', 'functools', 'copy',
        'hashlib', 'uuid', 'platform', 'subprocess', 'pathlib',
        'glob', 'shutil', 'tempfile', 'logging', 'traceback',
        'warnings', 'ctypes', 'struct', 'array', 'base64',
        'binascii', 'zlib', 'gzip', 'io', 're', 'string',
        'textwrap', 'unicodedata', 'locale', 'calendar',
        'decimal', 'fractions', 'statistics', 'enum', 'types',
        'inspect', 'importlib', 'pickle', 'operator', 'colorsys'
    ]
    
    # Build Nuitka command
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',
        '--onefile',
        '--output-filename=WOWBingoGame',
        f'--output-dir={build_dir}',
        '--assume-yes-for-downloads',
        '--windows-console-mode=disable',
        '--show-progress',
    ]
    
    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.append(f'--windows-icon-from-ico={icon_path}')
    
    # Add Windows metadata
    if os.name == 'nt':
        cmd.extend([
            '--windows-company-name=WOW Bingo Game',
            '--windows-product-name=WOW Bingo Game',
            '--windows-file-version=1.0.0',
            '--windows-product-version=1.0.0',
            '--windows-file-description=WOW Bingo Game - Professional Bingo Gaming Application',
        ])
    
    # Debug or release specific options
    if debug:
        cmd.extend(['--debug', '--verbose'])
    else:
        cmd.extend(['--lto=yes', '--jobs=0'])
        if os.name == 'nt':
            cmd.append('--msvc=latest')
    
    # Include essential data directories
    data_dirs = ['assets', 'data']
    for dir_name in data_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            cmd.append(f'--include-data-dir={dir_path}={dir_name}')
            log(f"Including directory: {dir_name}")
    
    # Include payment module if it exists
    payment_dir = project_root / 'payment'
    if payment_dir.exists() and any(payment_dir.glob('*.py')):
        cmd.append(f'--include-data-dir={payment_dir}=payment')
        log("Including payment module")
    
    # Include essential packages
    for package in essential_packages:
        cmd.append(f'--include-package={package}')
    
    # Add main script
    cmd.append('main.py')
    
    log(f"Nuitka command: {' '.join(cmd)}")
    
    # Execute build
    start_time = time.time()
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        build_time = time.time() - start_time
        log(f"Build completed in {build_time:.1f} seconds")
        return True
    except subprocess.CalledProcessError as e:
        log(f"Build failed with return code {e.returncode}", "ERROR")
        return False

def verify_and_copy_executable(project_root, build_dir, dist_dir):
    """Verify build output and copy to dist directory."""
    log("Verifying build output...")
    
    # Find executable
    executable_name = "WOWBingoGame.exe" if os.name == 'nt' else "WOWBingoGame"
    executable_path = build_dir / executable_name
    
    if not executable_path.exists():
        error(f"Executable not found: {executable_path}")
    
    # Check file size
    file_size = executable_path.stat().st_size
    file_size_mb = file_size / (1024 * 1024)
    log(f"Executable size: {file_size_mb:.1f} MB")
    
    # Copy to dist directory
    dist_executable = dist_dir / executable_name
    shutil.copy2(executable_path, dist_executable)
    log(f"Executable copied to: {dist_executable}")
    
    # Make executable on Unix systems
    if os.name != 'nt':
        os.chmod(dist_executable, 0o755)
    
    return dist_executable

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Simple Nuitka build for WOW Bingo Game")
    parser.add_argument('--debug', action='store_true', help='Build in debug mode')
    parser.add_argument('--clean', action='store_true', help='Clean build directories first')
    
    args = parser.parse_args()
    
    try:
        log("=" * 60)
        log("WOW Bingo Game - Simple Build")
        log("=" * 60)
        
        # Check prerequisites
        check_prerequisites()
        
        # Prepare environment
        project_root, build_dir, dist_dir = prepare_build_environment(args.clean)
        
        # Build executable
        if build_executable(project_root, build_dir, args.debug):
            # Verify and copy
            executable_path = verify_and_copy_executable(project_root, build_dir, dist_dir)
            
            # Success
            log("=" * 60)
            log("BUILD COMPLETED SUCCESSFULLY!")
            log("=" * 60)
            log(f"Executable: {executable_path}")
            log(f"File size: {executable_path.stat().st_size / (1024*1024):.1f} MB")
            log("=" * 60)
            
        else:
            error("Build failed!")
            
    except KeyboardInterrupt:
        log("Build interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
