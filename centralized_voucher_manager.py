#!/usr/bin/env python3
"""
Centralized Voucher Management System

This system allows a manager PC to generate voucher codes for any external PC
by specifying the target machine's UUID. It provides centralized voucher authority
with cross-platform compatibility and UUID-based validation.
"""

import os
import sys
import json
import time
import sqlite3
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple

# Import existing voucher generators
try:
    from compact_voucher_generator import CompactVoucherGenerator
    COMPACT_GENERATOR_AVAILABLE = True
except ImportError:
    COMPACT_GENERATOR_AVAILABLE = False
    print("Warning: CompactVoucherGenerator not available")

try:
    from external_voucher_generator import ExternalVoucherGenerator
    EXTERNAL_GENERATOR_AVAILABLE = True
except ImportError:
    EXTERNAL_GENERATOR_AVAILABLE = False
    print("Warning: ExternalVoucherGenerator not available")

try:
    from payment.voucher_generator import VoucherGenerator
    PAYMENT_GENERATOR_AVAILABLE = True
except ImportError:
    PAYMENT_GENERATOR_AVAILABLE = False
    print("Warning: Payment VoucherGenerator not available")

class CentralizedVoucherManager:
    """
    Centralized voucher management system for generating and tracking vouchers
    for multiple external PCs using their UUIDs.
    """
    
    def __init__(self, database_path="data/centralized_vouchers.db"):
        """
        Initialize the centralized voucher manager.
        
        Args:
            database_path: Path to the centralized voucher database
        """
        self.database_path = database_path
        self.ensure_database_exists()
        
        # Initialize available generators
        self.generators = {}
        self._initialize_generators()
        
        print(f"CentralizedVoucherManager initialized with {len(self.generators)} generators")
    
    def _initialize_generators(self):
        """Initialize available voucher generators."""
        
        # Initialize compact generator
        if COMPACT_GENERATOR_AVAILABLE:
            try:
                self.generators['compact'] = CompactVoucherGenerator()
                print("✓ Compact voucher generator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize compact generator: {e}")
        
        # Initialize external generator
        if EXTERNAL_GENERATOR_AVAILABLE:
            try:
                self.generators['external'] = ExternalVoucherGenerator()
                print("✓ External voucher generator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize external generator: {e}")
        
        # Initialize payment generator
        if PAYMENT_GENERATOR_AVAILABLE:
            try:
                self.generators['payment'] = VoucherGenerator()
                print("✓ Payment voucher generator initialized")
            except Exception as e:
                print(f"✗ Failed to initialize payment generator: {e}")
        
        if not self.generators:
            print("⚠ Warning: No voucher generators available!")
    
    def ensure_database_exists(self):
        """Create the centralized voucher database if it doesn't exist."""
        os.makedirs(os.path.dirname(self.database_path), exist_ok=True)
        
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            
            # External PCs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS external_pcs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    uuid TEXT UNIQUE NOT NULL,
                    name TEXT,
                    description TEXT,
                    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_voucher_generated TIMESTAMP,
                    total_vouchers_generated INTEGER DEFAULT 0,
                    notes TEXT
                )
            ''')
            
            # Generated vouchers table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS generated_vouchers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    voucher_code TEXT UNIQUE NOT NULL,
                    target_uuid TEXT NOT NULL,
                    generator_type TEXT NOT NULL,
                    amount INTEGER NOT NULL,
                    share INTEGER NOT NULL,
                    expiry_timestamp INTEGER,
                    generated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    generated_by TEXT,
                    status TEXT DEFAULT 'active',
                    redeemed_timestamp TIMESTAMP,
                    notes TEXT
                )
            ''')
            
            # Voucher batches table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS voucher_batches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    batch_name TEXT NOT NULL,
                    target_uuid TEXT NOT NULL,
                    generator_type TEXT NOT NULL,
                    voucher_count INTEGER NOT NULL,
                    total_amount INTEGER NOT NULL,
                    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    export_path TEXT,
                    notes TEXT
                )
            ''')
            
            conn.commit()
            print("✓ Centralized voucher database initialized")
    
    def register_external_pc(self, uuid: str, name: str = None, description: str = None, notes: str = None) -> bool:
        """
        Register an external PC in the system.
        
        Args:
            uuid: Machine UUID of the external PC
            name: Friendly name for the PC
            description: Description of the PC
            notes: Additional notes
            
        Returns:
            bool: True if registered successfully
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO external_pcs 
                    (uuid, name, description, notes, first_seen)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (uuid, name, description, notes))
                
                conn.commit()
                print(f"✓ Registered external PC: {uuid} ({name})")
                return True
                
        except Exception as e:
            print(f"✗ Failed to register external PC: {e}")
            return False
    
    def get_registered_pcs(self) -> List[Dict]:
        """
        Get list of all registered external PCs.
        
        Returns:
            List of PC information dictionaries
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT uuid, name, description, first_seen, 
                           last_voucher_generated, total_vouchers_generated, notes
                    FROM external_pcs
                    ORDER BY name, uuid
                ''')
                
                pcs = []
                for row in cursor.fetchall():
                    pcs.append({
                        'uuid': row[0],
                        'name': row[1],
                        'description': row[2],
                        'first_seen': row[3],
                        'last_voucher_generated': row[4],
                        'total_vouchers_generated': row[5],
                        'notes': row[6]
                    })
                
                return pcs
                
        except Exception as e:
            print(f"✗ Failed to get registered PCs: {e}")
            return []
    
    def generate_voucher(self, target_uuid: str, amount: int, generator_type: str = 'compact',
                        days_valid: int = 30, share: int = 80, notes: str = None) -> Optional[str]:
        """
        Generate a single voucher for a specific external PC.
        
        Args:
            target_uuid: UUID of the target machine
            amount: Credit amount
            generator_type: Type of generator to use ('compact', 'external', 'payment')
            days_valid: Number of days the voucher is valid
            share: Commission share percentage
            notes: Additional notes
            
        Returns:
            Generated voucher code or None if failed
        """
        if generator_type not in self.generators:
            print(f"✗ Generator type '{generator_type}' not available")
            return None
        
        try:
            generator = self.generators[generator_type]
            
            # Generate voucher based on generator type
            if generator_type == 'compact':
                voucher_code = generator.make_voucher(amount, days_valid, share)
            elif generator_type == 'external':
                voucher_code = generator.make_external_voucher(amount, target_uuid, days_valid, share)
            elif generator_type == 'payment':
                voucher_code = generator.make_voucher(target_uuid, amount, share, days_valid)
            else:
                print(f"✗ Unknown generator type: {generator_type}")
                return None
            
            # Calculate expiry timestamp
            expiry_timestamp = int(time.time()) + (days_valid * 24 * 60 * 60) if days_valid > 0 else 0
            
            # Store in database
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO generated_vouchers 
                    (voucher_code, target_uuid, generator_type, amount, share, 
                     expiry_timestamp, generated_by, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (voucher_code, target_uuid, generator_type, amount, share,
                      expiry_timestamp, "centralized_manager", notes))
                
                # Update PC statistics
                cursor.execute('''
                    UPDATE external_pcs 
                    SET last_voucher_generated = CURRENT_TIMESTAMP,
                        total_vouchers_generated = total_vouchers_generated + 1
                    WHERE uuid = ?
                ''', (target_uuid,))
                
                conn.commit()
            
            print(f"✓ Generated voucher for {target_uuid}: {voucher_code}")
            return voucher_code
            
        except Exception as e:
            print(f"✗ Failed to generate voucher: {e}")
            return None
    
    def generate_batch(self, target_uuid: str, count: int, amount: int, 
                      generator_type: str = 'compact', days_valid: int = 30, 
                      share: int = 80, batch_name: str = None, 
                      export_path: str = None) -> List[str]:
        """
        Generate a batch of vouchers for a specific external PC.
        
        Args:
            target_uuid: UUID of the target machine
            count: Number of vouchers to generate
            amount: Credit amount per voucher
            generator_type: Type of generator to use
            days_valid: Number of days the vouchers are valid
            share: Commission share percentage
            batch_name: Name for the batch
            export_path: Path to export the batch
            
        Returns:
            List of generated voucher codes
        """
        if generator_type not in self.generators:
            print(f"✗ Generator type '{generator_type}' not available")
            return []
        
        vouchers = []
        batch_name = batch_name or f"Batch_{target_uuid[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"Generating batch of {count} vouchers for {target_uuid}...")
        
        try:
            # Generate vouchers
            for i in range(count):
                voucher = self.generate_voucher(target_uuid, amount, generator_type, days_valid, share,
                                              f"Batch: {batch_name} ({i+1}/{count})")
                if voucher:
                    vouchers.append(voucher)
                
                # Progress indicator
                if count > 10 and (i + 1) % 10 == 0:
                    print(f"Generated {i + 1}/{count} vouchers...")
            
            # Record batch in database
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO voucher_batches 
                    (batch_name, target_uuid, generator_type, voucher_count, 
                     total_amount, created_by, export_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (batch_name, target_uuid, generator_type, len(vouchers),
                      len(vouchers) * amount, "centralized_manager", export_path))
                
                conn.commit()
            
            # Export if requested
            if export_path and vouchers:
                self._export_batch(vouchers, target_uuid, batch_name, export_path, amount, share, days_valid)
            
            print(f"✓ Generated batch of {len(vouchers)} vouchers")
            return vouchers
            
        except Exception as e:
            print(f"✗ Failed to generate batch: {e}")
            return vouchers
    
    def _export_batch(self, vouchers: List[str], target_uuid: str, batch_name: str,
                     export_path: str, amount: int, share: int, days_valid: int):
        """Export a batch of vouchers to file."""
        try:
            os.makedirs(os.path.dirname(export_path), exist_ok=True)
            
            export_data = {
                "batch_info": {
                    "batch_name": batch_name,
                    "target_uuid": target_uuid,
                    "generated_timestamp": datetime.now().isoformat(),
                    "voucher_count": len(vouchers),
                    "amount_per_voucher": amount,
                    "share_percentage": share,
                    "days_valid": days_valid,
                    "total_value": len(vouchers) * amount
                },
                "vouchers": []
            }
            
            expiry_date = "Never" if days_valid == 0 else (
                datetime.now() + timedelta(days=days_valid)).strftime("%Y-%m-%d")
            
            for i, voucher in enumerate(vouchers):
                export_data["vouchers"].append({
                    "voucher_code": voucher,
                    "sequence": i + 1,
                    "amount": amount,
                    "share": share,
                    "expiry": expiry_date,
                    "target_uuid": target_uuid
                })
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            print(f"✓ Exported batch to: {export_path}")
            
        except Exception as e:
            print(f"✗ Failed to export batch: {e}")
    
    def get_voucher_history(self, target_uuid: str = None, limit: int = 100) -> List[Dict]:
        """
        Get voucher generation history.
        
        Args:
            target_uuid: Filter by target UUID (optional)
            limit: Maximum number of records to return
            
        Returns:
            List of voucher history records
        """
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                if target_uuid:
                    cursor.execute('''
                        SELECT voucher_code, target_uuid, generator_type, amount, share,
                               expiry_timestamp, generated_timestamp, status, notes
                        FROM generated_vouchers
                        WHERE target_uuid = ?
                        ORDER BY generated_timestamp DESC
                        LIMIT ?
                    ''', (target_uuid, limit))
                else:
                    cursor.execute('''
                        SELECT voucher_code, target_uuid, generator_type, amount, share,
                               expiry_timestamp, generated_timestamp, status, notes
                        FROM generated_vouchers
                        ORDER BY generated_timestamp DESC
                        LIMIT ?
                    ''', (limit,))
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        'voucher_code': row[0],
                        'target_uuid': row[1],
                        'generator_type': row[2],
                        'amount': row[3],
                        'share': row[4],
                        'expiry_timestamp': row[5],
                        'generated_timestamp': row[6],
                        'status': row[7],
                        'notes': row[8]
                    })
                
                return history
                
        except Exception as e:
            print(f"✗ Failed to get voucher history: {e}")
            return []
    
    def validate_voucher_for_uuid(self, voucher_code: str, target_uuid: str) -> Dict:
        """
        Validate if a voucher code is valid for a specific UUID.
        
        Args:
            voucher_code: Voucher code to validate
            target_uuid: Target machine UUID
            
        Returns:
            Validation result dictionary
        """
        try:
            # Check in database first
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT target_uuid, amount, share, expiry_timestamp, status
                    FROM generated_vouchers
                    WHERE voucher_code = ?
                ''', (voucher_code,))
                
                row = cursor.fetchone()
                if row:
                    stored_uuid, amount, share, expiry, status = row
                    
                    # Check UUID match
                    if stored_uuid != target_uuid:
                        return {
                            "valid": False,
                            "message": f"Voucher is for UUID {stored_uuid[:8]}..., not {target_uuid[:8]}..."
                        }
                    
                    # Check status
                    if status != 'active':
                        return {
                            "valid": False,
                            "message": f"Voucher status: {status}"
                        }
                    
                    # Check expiry
                    if expiry > 0 and expiry < int(time.time()):
                        return {
                            "valid": False,
                            "message": "Voucher has expired"
                        }
                    
                    return {
                        "valid": True,
                        "amount": amount,
                        "share": share,
                        "expiry": expiry,
                        "message": "Voucher is valid"
                    }
            
            # If not in database, try generator validation
            for generator_type, generator in self.generators.items():
                if hasattr(generator, 'validate_voucher'):
                    try:
                        result = generator.validate_voucher(voucher_code)
                        if result.get("valid"):
                            return result
                    except Exception as e:
                        print(f"Generator {generator_type} validation failed: {e}")
            
            return {
                "valid": False,
                "message": "Voucher not found or invalid"
            }
            
        except Exception as e:
            print(f"✗ Validation error: {e}")
            return {
                "valid": False,
                "message": f"Validation error: {e}"
            }
    
    def get_statistics(self) -> Dict:
        """Get system statistics."""
        try:
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                
                # Total PCs
                cursor.execute('SELECT COUNT(*) FROM external_pcs')
                total_pcs = cursor.fetchone()[0]
                
                # Total vouchers
                cursor.execute('SELECT COUNT(*) FROM generated_vouchers')
                total_vouchers = cursor.fetchone()[0]
                
                # Active vouchers
                cursor.execute('SELECT COUNT(*) FROM generated_vouchers WHERE status = "active"')
                active_vouchers = cursor.fetchone()[0]
                
                # Total value
                cursor.execute('SELECT SUM(amount) FROM generated_vouchers WHERE status = "active"')
                total_value = cursor.fetchone()[0] or 0
                
                # Recent activity
                cursor.execute('''
                    SELECT COUNT(*) FROM generated_vouchers 
                    WHERE generated_timestamp > datetime('now', '-7 days')
                ''')
                recent_vouchers = cursor.fetchone()[0]
                
                return {
                    'total_external_pcs': total_pcs,
                    'total_vouchers_generated': total_vouchers,
                    'active_vouchers': active_vouchers,
                    'total_active_value': total_value,
                    'vouchers_last_7_days': recent_vouchers,
                    'available_generators': list(self.generators.keys())
                }
                
        except Exception as e:
            print(f"✗ Failed to get statistics: {e}")
            return {}


def main():
    """Main function for testing the centralized voucher manager."""
    print("Centralized Voucher Management System")
    print("=" * 50)
    
    # Initialize manager
    manager = CentralizedVoucherManager()
    
    # Show statistics
    stats = manager.get_statistics()
    print(f"\nSystem Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Show registered PCs
    pcs = manager.get_registered_pcs()
    print(f"\nRegistered External PCs: {len(pcs)}")
    for pc in pcs[:5]:  # Show first 5
        print(f"  {pc['uuid'][:8]}... - {pc['name']} ({pc['total_vouchers_generated']} vouchers)")


if __name__ == "__main__":
    main()
