{% extends "layout.html" %}
{% block content %}
    <h2>RethinkDB Settings</h2>

    {% if message %}
        <div class="alert {{ message_class }}">
            {{ message }}
        </div>
    {% endif %}

    <form method="post" action="/settings">
        <div class="form-group">
            <label for="host">Host:</label>
            <input type="text" id="host" name="host" value="{{ config.host }}">
        </div>

        <div class="form-group">
            <label for="port">Port:</label>
            <input type="number" id="port" name="port" value="{{ config.port }}">
        </div>

        <div class="form-group">
            <label for="db">Database:</label>
            <input type="text" id="db" name="db" value="{{ config.db }}">
        </div>

        <div class="form-group">
            <label for="user">Username:</label>
            <input type="text" id="user" name="user" value="{{ config.user }}">
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="{{ config.password }}">
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="ssl" {% if config.ssl %}checked{% endif %}>
                Use SSL
            </label>
        </div>

        <div class="form-group">
            <label for="sync_interval">Sync Interval (seconds):</label>
            <input type="number" id="sync_interval" name="sync_interval" value="{{ config.sync_interval }}">
        </div>

        <button type="submit">Save Settings</button>
    </form>

    <h3>Test Connection</h3>
    <form method="post" action="/test-connection">
        <button type="submit">Test Connection</button>
    </form>

    <h3>Sync Now</h3>
    <form method="post" action="/sync-now">
        <button type="submit">Sync Now</button>
    </form>
{% endblock %}
