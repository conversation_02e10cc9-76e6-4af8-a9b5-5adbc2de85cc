Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFC8B10000 ntdll.dll
7FFFC7BA0000 KERNEL32.DLL
7FFFC5F60000 KERNELBASE.dll
7FFFC7C70000 USER32.dll
7FFFC6500000 win32u.dll
7FFFC7A60000 GDI32.dll
7FFFC5C30000 gdi32full.dll
7FFFC5D50000 msvcp_win.dll
7FFFC66A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFC8620000 advapi32.dll
7FFFC7AD0000 msvcrt.dll
7FFFC84F0000 sechost.dll
7FFFC5DF0000 bcrypt.dll
7FFFC6D00000 RPCRT4.dll
7FFFC53C0000 CRYPTBASE.DLL
7FFFC5EE0000 bcryptPrimitives.dll
7FFFC7A90000 IMM32.DLL
