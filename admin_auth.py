"""
Admin Authentication Module for the WOW Games application.

This module handles admin user authentication and session management.
"""

import os
import json
import time
import hashlib
import logging
from datetime import datetime, timedelta
import bcrypt
import re
import random
import threading
from stats_db import get_stats_db_manager

# Load configuration
try:
    with open('config/admin_config.json', 'r') as f:
        admin_config = json.load(f)
except Exception as e:
    admin_config = {
        "session_timeout_minutes": 30,
        "lockout_threshold": 5,
        "lockout_duration_minutes": 15,
        "min_password_length": 8
    }
    logging.error(f"Error loading admin config: {e}, using defaults")

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'admin_auth.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Session storage
_sessions = {}
_session_lock = threading.Lock()

# Failed login attempts tracking
_failed_attempts = {}
_failed_attempts_lock = threading.Lock()

class AdminSession:
    """Class to manage admin user sessions."""
    
    def __init__(self):
        """Initialize the admin session manager."""
        self.sessions = {}
        self.session_file = os.path.join('data', 'admin_sessions.json')
        self.load_sessions()
        
    def load_sessions(self):
        """Load sessions from file."""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'r') as f:
                    self.sessions = json.load(f)
                    
                # Clean up expired sessions
                self._clean_expired_sessions()
            except Exception as e:
                logging.error(f"Error loading sessions: {e}")
                self.sessions = {}
        
    def save_sessions(self):
        """Save sessions to file."""
        try:
            # Ensure data directory exists
            os.makedirs(os.path.dirname(self.session_file), exist_ok=True)
            
            with open(self.session_file, 'w') as f:
                json.dump(self.sessions, f)
        except Exception as e:
            logging.error(f"Error saving sessions: {e}")
    
    def _clean_expired_sessions(self):
        """Remove expired sessions."""
        current_time = time.time()
        expired_sessions = []
        
        for token, session in self.sessions.items():
            if session.get('expiry', 0) < current_time:
                expired_sessions.append(token)
                
        for token in expired_sessions:
            del self.sessions[token]
            
        if expired_sessions:
            self.save_sessions()
            logging.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def create_session(self, user_data, duration_hours=24):
        """
        Create a new session for a user.
        
        Args:
            user_data: User data dictionary
            duration_hours: Session duration in hours
            
        Returns:
            str: Session token
        """
        # Generate a unique token
        token = hashlib.sha256(os.urandom(32)).hexdigest()
        
        # Calculate expiry time
        expiry = time.time() + (duration_hours * 3600)
        
        # Create session
        self.sessions[token] = {
            'user_id': user_data['id'],
            'username': user_data['username'],
            'access_level': user_data['access_level'],
            'created': time.time(),
            'expiry': expiry,
            'last_activity': time.time()
        }
        
        # Save sessions
        self.save_sessions()
        
        logging.info(f"Created session for user {user_data['username']}")
        return token
    
    def validate_session(self, token):
        """
        Validate a session token.
        
        Args:
            token: Session token
            
        Returns:
            dict: Session data if valid, None otherwise
        """
        # Clean up expired sessions
        self._clean_expired_sessions()
        
        if token not in self.sessions:
            return None
            
        session = self.sessions[token]
        
        # Check if session is expired
        if session.get('expiry', 0) < time.time():
            del self.sessions[token]
            self.save_sessions()
            return None
            
        # Update last activity
        session['last_activity'] = time.time()
        self.save_sessions()
        
        return session
    
    def end_session(self, token):
        """
        End a session.
        
        Args:
            token: Session token
            
        Returns:
            bool: True if session was ended, False otherwise
        """
        if token in self.sessions:
            del self.sessions[token]
            self.save_sessions()
            return True
        return False

def authenticate_admin(username, password):
    """
    Authenticate an admin user.
    
    Args:
        username: Username
        password: Password
        
    Returns:
        tuple: (success, token or error message)
    """
    try:
        # Get stats database manager
        stats_db = get_stats_db_manager()
        
        # Authenticate user
        user_data = stats_db.authenticate_admin_user(username, password)
        
        if not user_data:
            return (False, "Invalid username or password")
            
        # Create session
        session_manager = AdminSession()
        token = session_manager.create_session(user_data)
        
        return (True, token)
    except Exception as e:
        logging.error(f"Error authenticating admin: {e}")
        return (False, f"Authentication error: {str(e)}")

def validate_admin_session(token):
    """
    Validate an admin session.
    
    Args:
        token: Session token
        
    Returns:
        tuple: (success, session data or error message)
    """
    try:
        session_manager = AdminSession()
        session_data = session_manager.validate_session(token)
        
        if not session_data:
            return (False, "Invalid or expired session")
            
        return (True, session_data)
    except Exception as e:
        logging.error(f"Error validating admin session: {e}")
        return (False, f"Session validation error: {str(e)}")

def end_admin_session(token):
    """
    End an admin session.
    
    Args:
        token: Session token
        
    Returns:
        bool: True if session was ended, False otherwise
    """
    try:
        session_manager = AdminSession()
        return session_manager.end_session(token)
    except Exception as e:
        logging.error(f"Error ending admin session: {e}")
        return False

def create_default_admin():
    """
    Create a default admin user if no admin users exist.
    
    Returns:
        bool: True if default admin was created, False otherwise
    """
    try:
        # Get stats database manager
        stats_db = get_stats_db_manager()
        
        # Check if any admin users exist
        admin_users = stats_db.get_admin_users()
        
        if admin_users:
            return False
            
        # Create default admin user
        user_id = stats_db.add_admin_user(
            username="admin",
            password="admin123",
            access_level=3  # Admin level
        )
        
        if user_id > 0:
            logging.info(f"Created default admin user (ID: {user_id})")
            return True
        else:
            return False
    except Exception as e:
        logging.error(f"Error creating default admin: {e}")
        return False
