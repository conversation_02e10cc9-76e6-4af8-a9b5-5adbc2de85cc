@echo off
REM WOW Bingo Game - Simple Nuitka Build Only
REM =========================================
REM
REM This script runs only the simple Nuitka build with proper error checking.
REM Use this if you want to focus on just the Nuitka approach.

echo.
echo ================================================================================
echo WOW Bingo Game - Simple Nuitka Build
echo ================================================================================
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Check main.py
if not exist "main.py" (
    echo ERROR: main.py not found. Run this from the project directory.
    pause
    exit /b 1
)

REM Check assets
if not exist "assets" (
    echo ERROR: assets directory not found.
    pause
    exit /b 1
)

echo All checks passed. Starting Nuitka build...
echo This will take 10-20 minutes depending on your system.
echo.

REM Run the simple Nuitka build
python nuitka_build_simple.py --verbose

REM Check if executable was created
if exist "dist\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo BUILD SUCCESS!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game.exe
    for %%F in ("dist\WOW_Bingo_Game.exe") do (
        set /a "size_mb=%%~zF / 1048576"
    )
    echo Size: !size_mb! MB
    echo.
    echo Your game is ready! Test it by running the executable.
    echo.
    pause
    exit /b 0
)

if exist "dist\WOW_Bingo_Game\WOW_Bingo_Game.exe" (
    echo.
    echo ================================================================================
    echo BUILD SUCCESS!
    echo ================================================================================
    echo.
    echo Executable: dist\WOW_Bingo_Game\WOW_Bingo_Game.exe
    echo.
    echo Your game is ready! Test it by running the executable.
    echo.
    pause
    exit /b 0
)

REM Build failed
echo.
echo ================================================================================
echo BUILD FAILED
echo ================================================================================
echo.
echo The Nuitka build did not complete successfully.
echo Check the error messages above for details.
echo.
echo Common solutions:
echo 1. Install Visual Studio Build Tools
echo 2. Run: pip install -r requirements.txt
echo 3. Free up disk space (need 5+ GB)
echo 4. Try the PyInstaller fallback: python build_fallback.py
echo.
pause
exit /b 1
