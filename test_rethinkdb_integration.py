"""
Test script for the RethinkDB integration with offline sync.

This script tests the basic functionality of the RethinkDB integration,
including connection, CRUD operations, and offline-to-online sync.
"""

import os
import sys
import time
import json
import random
import datetime
import subprocess
import argparse
from contextlib import contextmanager

# Try importing required modules
try:
    import rethinkdb
r = rethinkdb.r
from rethink_config import RET<PERSON>INKDB_HOST, RETHINKDB_PORT, RETHINKDB_DB
    from rethink_db import get_rethink_db_manager
    from db_hybrid import get_hybrid_db_manager
    from sync_manager import get_sync_manager
    from hybrid_db_integration import get_hybrid_db_integration
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Error importing required modules: {e}")
    MODULES_AVAILABLE = False

# Constants
TEST_DATA_SIZE = 10  # Number of test records to create
WAIT_INTERVAL = 2    # Seconds to wait between operations

# Test data generator functions
def generate_daily_stats():
    """Generate random daily stats data."""
    today = datetime.datetime.now()
    return {
        'date': today.strftime('%Y-%m-%d'),
        'games_played': random.randint(1, 20),
        'earnings': random.uniform(10, 1000),
        'winners': random.randint(0, 10),
        'total_players': random.randint(10, 50)
    }

def generate_game_history():
    """Generate random game history data."""
    return {
        'date_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'username': f"player_{random.randint(1, 100)}",
        'house': f"pattern_{random.randint(1, 5)}",
        'stake': random.uniform(10, 100),
        'players': random.randint(5, 20),
        'total_calls': random.randint(20, 50),
        'commission_percent': 10.0,
        'fee': random.uniform(1, 10),
        'total_prize': random.uniform(50, 500),
        'details': json.dumps({"numbers_called": [random.randint(1, 90) for _ in range(10)]}),
        'status': "completed"
    }

@contextmanager
def simulate_offline_mode():
    """
    Context manager to simulate offline mode by temporarily forcing the
    hybrid DB manager to operate in offline mode.
    """
    db_integration = get_hybrid_db_integration()
    was_online = db_integration.is_online()
    
    # Force offline mode
    db_integration.force_offline_mode()
    print("Forced offline mode")
    
    try:
        yield
    finally:
        # Restore previous mode
        if was_online:
            db_integration.force_online_mode()
            print("Restored online mode")

def check_rethinkdb_installed():
    """Check if RethinkDB is installed."""
    try:
        result = subprocess.run(
            ['rethinkdb', '--version'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_rethinkdb():
    """Install RethinkDB if it's not already installed."""
    if not check_rethinkdb_installed():
        print("RethinkDB is not installed. Please install it manually.")
        print("Visit https://rethinkdb.com/docs/install/ for installation instructions.")
        sys.exit(1)

def start_rethinkdb_server():
    """Start a RethinkDB server for testing."""
    # Import the setup utility
    try:
        from setup_rethinkdb import start_rethinkdb
        process = start_rethinkdb()
        return process
    except Exception as e:
        print(f"Error starting RethinkDB server: {e}")
        return None

def initialize_database():
    """Initialize the RethinkDB database for testing."""
    # Import the setup utility
    try:
        from setup_rethinkdb import initialize_database
        return initialize_database()
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def test_connection():
    """Test the connection to RethinkDB."""
    print("\n=== Testing RethinkDB Connection ===")
    
    try:
        # Connect to RethinkDB
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            db=RETHINKDB_DB
        )
        
        # Test the connection
        print(f"Connected to RethinkDB at {RETHINKDB_HOST}:{RETHINKDB_PORT}")
        print(f"Server info: {conn.server_info.get('name', 'unknown')}")
        
        # Check if the database exists
        databases = rethinkdb.r.db_list().run(conn)
        if RETHINKDB_DB in databases:
            print(f"Database '{RETHINKDB_DB}' exists")
            
            # Check tables
            conn.use(RETHINKDB_DB)
            tables = rethinkdb.r.table_list().run(conn)
            print(f"Tables in database: {', '.join(tables)}")
        else:
            print(f"Database '{RETHINKDB_DB}' does not exist")
            
        conn.close()
        return True
    except Exception as e:
        print(f"Error connecting to RethinkDB: {e}")
        return False

def test_hybrid_db_manager():
    """Test the hybrid database manager."""
    print("\n=== Testing Hybrid DB Manager ===")
    
    try:
        # Get the hybrid database manager
        db = get_hybrid_db_manager()
        
        # Test basic operations
        print("Testing basic operations...")
        
        # Check if we're online
        if db.is_online():
            print("Application is in online mode")
        else:
            print("Application is in offline mode")
            
        # Get daily stats
        stats = db.get_daily_stats()
        print(f"Daily stats: {stats}")
        
        # Update daily stats
        result = db.update_daily_stats(
            games_played=1,
            earnings=50,
            winners=1,
            total_players=10
        )
        print(f"Updated daily stats: {result}")
        
        # Get updated daily stats
        stats = db.get_daily_stats()
        print(f"Updated daily stats: {stats}")
        
        # Get weekly stats
        weekly_stats = db.get_weekly_stats()
        print(f"Weekly stats: {len(weekly_stats)} days")
        
        # Get wallet balance
        balance = db.get_wallet_balance()
        print(f"Wallet balance: {balance}")
        
        return True
    except Exception as e:
        print(f"Error testing hybrid DB manager: {e}")
        return False

def test_crud_operations():
    """Test CRUD operations with the hybrid database."""
    print("\n=== Testing CRUD Operations ===")
    
    try:
        # Get the hybrid database manager
        db = get_hybrid_db_manager()
        
        # Test game history operations
        print("Testing game history operations...")
        
        # Add a game to history
        game_data = generate_game_history()
        game_id = db.add_game_to_history(
            username=game_data['username'],
            house=game_data['house'],
            stake=game_data['stake'],
            players=game_data['players'],
            total_calls=game_data['total_calls'],
            commission_percent=game_data['commission_percent'],
            fee=game_data['fee'],
            total_prize=game_data['total_prize'],
            details=game_data['details'],
            status=game_data['status']
        )
        print(f"Added game to history with ID: {game_id}")
        
        # Get game history
        history = db.get_game_history()
        print(f"Game history: {history['total_games']} games")
        
        # Test wallet operations
        print("Testing wallet operations...")
        
        # Add a wallet transaction
        transaction_id = db.add_wallet_transaction(
            amount=100,
            transaction_type="deposit",
            description="Test deposit"
        )
        print(f"Added wallet transaction with ID: {transaction_id}")
        
        # Get wallet transactions
        transactions = db.get_wallet_transactions()
        print(f"Wallet transactions: {len(transactions)} transactions")
        
        return True
    except Exception as e:
        print(f"Error testing CRUD operations: {e}")
        return False

def test_offline_sync():
    """Test offline-to-online synchronization."""
    print("\n=== Testing Offline-to-Online Sync ===")
    
    try:
        # Get the hybrid database integration
        db_integration = get_hybrid_db_integration()
        
        # Check if we're online
        if not db_integration.is_online():
            print("Application is already in offline mode, cannot test sync")
            return False
            
        print("Starting offline sync test...")
        
        # Simulate offline mode
        with simulate_offline_mode():
            # Perform operations offline
            print("Performing operations while offline...")
            
            # Add a game to history
            game_data = generate_game_history()
            game_id = db_integration.add_game_to_history(
                username=game_data['username'],
                house=game_data['house'],
                stake=game_data['stake'],
                players=game_data['players'],
                total_calls=game_data['total_calls'],
                commission_percent=game_data['commission_percent'],
                fee=game_data['fee'],
                total_prize=game_data['total_prize'],
                details=game_data['details'],
                status=game_data['status']
            )
            print(f"Added game to history while offline with ID: {game_id}")
            
            # Update daily stats
            stats_data = generate_daily_stats()
            result = db_integration.update_daily_stats(
                games_played=stats_data['games_played'],
                earnings=stats_data['earnings'],
                winners=stats_data['winners'],
                total_players=stats_data['total_players']
            )
            print(f"Updated daily stats while offline: {result}")
            
            # Wait a bit to simulate offline period
            print("Waiting for offline period to end...")
            time.sleep(WAIT_INTERVAL)
        
        # Now we're back online
        print("Back online, forcing synchronization...")
        
        # Force synchronization
        result = db_integration.force_refresh_data()
        print(f"Forced sync result: {result}")
        
        # Wait for sync to complete
        print("Waiting for sync to complete...")
        time.sleep(WAIT_INTERVAL * 2)
        
        # Check if data was synced to RethinkDB
        rethink_db = get_rethink_db_manager()
        conn = rethink_db.get_connection()
        
        # Check game history
        try:
            games = list(rethinkdb.r.table('game_history').run(conn))
            print(f"Games in RethinkDB after sync: {len(games)}")
        except Exception as e:
            print(f"Error checking games in RethinkDB: {e}")
        
        # Check daily stats
        try:
            stats = list(rethinkdb.r.table('daily_stats').run(conn))
            print(f"Daily stats in RethinkDB after sync: {len(stats)}")
        except Exception as e:
            print(f"Error checking stats in RethinkDB: {e}")
        
        return True
    except Exception as e:
        print(f"Error testing offline sync: {e}")
        return False

def test_real_time_updates():
    """Test real-time updates."""
    print("\n=== Testing Real-Time Updates ===")
    
    try:
        # Get the hybrid database integration
        db_integration = get_hybrid_db_integration()
        
        # Check if we're online
        if not db_integration.is_online():
            print("Application is in offline mode, cannot test real-time updates")
            return False
            
        print("Starting real-time updates test...")
        
        # Create a flag to signal when updates are received
        received_update = {'value': False}
        
        # Define a handler function
        def on_game_added(change):
            print(f"Real-time update received: {change['new_val']['id'] if 'new_val' in change else 'Unknown'}")
            received_update['value'] = True
        
        # Register the handler
        print("Registering event handler...")
        handler_id = db_integration.register_event_handler('game_added', on_game_added)
        
        # Wait a bit for the subscription to be established
        print("Waiting for subscription to be established...")
        time.sleep(WAIT_INTERVAL)
        
        # Add a game to trigger an update
        print("Adding a game to trigger an update...")
        game_data = generate_game_history()
        game_id = db_integration.add_game_to_history(
            username=game_data['username'],
            house=game_data['house'],
            stake=game_data['stake'],
            players=game_data['players'],
            total_calls=game_data['total_calls'],
            commission_percent=game_data['commission_percent'],
            fee=game_data['fee'],
            total_prize=game_data['total_prize'],
            details=game_data['details'],
            status=game_data['status']
        )
        print(f"Added game with ID: {game_id}")
        
        # Wait a bit for the update to be received
        print("Waiting for update to be received...")
        time.sleep(WAIT_INTERVAL * 2)
        
        # Check if we received the update
        if received_update['value']:
            print("Successfully received real-time update!")
        else:
            print("Did not receive real-time update")
        
        # Unregister the handler
        print("Unregistering event handler...")
        db_integration.unregister_event_handler('game_added', handler_id)
        
        return received_update['value']
    except Exception as e:
        print(f"Error testing real-time updates: {e}")
        return False

def cleanup():
    """Clean up after testing."""
    print("\n=== Cleaning Up ===")
    
    try:
        # Stop RethinkDB server
        from setup_rethinkdb import stop_rethinkdb
        stop_rethinkdb()
        
        return True
    except Exception as e:
        print(f"Error during cleanup: {e}")
        return False

def main():
    """Main function for the test script."""
    parser = argparse.ArgumentParser(description='Test RethinkDB Integration')
    
    # Define command-line arguments
    parser.add_argument('--skip-install', action='store_true', help='Skip RethinkDB installation check')
    parser.add_argument('--skip-server', action='store_true', help='Skip starting RethinkDB server')
    parser.add_argument('--skip-init', action='store_true', help='Skip database initialization')
    parser.add_argument('--skip-cleanup', action='store_true', help='Skip cleanup after testing')
    
    args = parser.parse_args()
    
    # Check if required modules are available
    if not MODULES_AVAILABLE:
        print("Required modules are not available. Please install them first.")
        return False
    
    # Install RethinkDB if needed
    if not args.skip_install:
        install_rethinkdb()
    
    # Start RethinkDB server
    server_process = None
    if not args.skip_server:
        server_process = start_rethinkdb_server()
        if not server_process:
            print("Failed to start RethinkDB server. Exiting.")
            return False
        
        # Wait for server to start
        print("Waiting for RethinkDB server to start...")
        time.sleep(WAIT_INTERVAL * 2)
    
    # Initialize database
    if not args.skip_init:
        if not initialize_database():
            print("Failed to initialize database. Exiting.")
            return False
    
    # Run tests
    success = True
    success = success and test_connection()
    success = success and test_hybrid_db_manager()
    success = success and test_crud_operations()
    success = success and test_offline_sync()
    success = success and test_real_time_updates()
    
    # Clean up
    if not args.skip_cleanup:
        cleanup()
    
    # Print result
    if success:
        print("\n=== All tests passed! ===")
    else:
        print("\n=== Some tests failed. ===")
        
    return success

if __name__ == '__main__':
    sys.exit(0 if main() else 1)