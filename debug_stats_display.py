#!/usr/bin/env python3
"""
Debug script to check what the stats page is actually displaying
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_stats_display():
    """Debug what the stats page is actually getting"""
    
    print("=" * 80)
    print("DEBUGGING STATS PAGE DISPLAY")
    print("=" * 80)
    
    # Test 1: Check what stats_integration returns
    print("\n1. Testing stats_integration.get_game_history()...")
    try:
        from stats_integration import get_game_history
        
        history, total_pages = get_game_history(page=0, page_size=10)
        print(f"✅ Stats integration returned {len(history)} records, {total_pages} total pages")
        
        if history:
            print("\nFirst 3 records from stats_integration:")
            for i, record in enumerate(history[:3]):
                print(f"  Record {i+1}: {record}")
        else:
            print("❌ No records returned from stats_integration")
            
    except Exception as e:
        print(f"❌ Error with stats_integration: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Check what GameStatsIntegration returns
    print("\n2. Testing GameStatsIntegration.get_game_history()...")
    try:
        from game_stats_integration import GameStatsIntegration
        
        history, total_pages = GameStatsIntegration.get_game_history(page=0, page_size=10)
        print(f"✅ GameStatsIntegration returned {len(history)} records, {total_pages} total pages")
        
        if history:
            print("\nFirst 3 records from GameStatsIntegration:")
            for i, record in enumerate(history[:3]):
                print(f"  Record {i+1}: {record}")
        else:
            print("❌ No records returned from GameStatsIntegration")
            
    except Exception as e:
        print(f"❌ Error with GameStatsIntegration: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Check what the stats data provider returns
    print("\n3. Testing StatsDataProvider...")
    try:
        from stats_data_provider import get_stats_provider
        
        provider = get_stats_provider()
        history = provider.get_game_history(page=0, page_size=10)
        print(f"✅ StatsDataProvider returned {len(history)} records")
        
        if history:
            print("\nFirst 3 records from StatsDataProvider:")
            for i, record in enumerate(history[:3]):
                print(f"  Record {i+1}: {record}")
        else:
            print("❌ No records returned from StatsDataProvider")
            
    except Exception as e:
        print(f"❌ Error with StatsDataProvider: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 4: Check what the stats page itself uses
    print("\n4. Testing StatsPage.get_game_history()...")
    try:
        from stats_page import StatsPage
        
        # Create a dummy stats page instance
        import pygame
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        
        stats_page = StatsPage(screen, lambda: None)
        history, total_pages = stats_page.get_game_history(page=0, page_size=10)
        print(f"✅ StatsPage returned {len(history)} records, {total_pages} total pages")
        
        if history:
            print("\nFirst 3 records from StatsPage:")
            for i, record in enumerate(history[:3]):
                print(f"  Record {i+1}: {record}")
        else:
            print("❌ No records returned from StatsPage")
            
        pygame.quit()
            
    except Exception as e:
        print(f"❌ Error with StatsPage: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 5: Check cache content
    print("\n5. Testing cache content...")
    try:
        import json
        
        # Check stats cache
        try:
            with open('data/cache/stats_cache.json', 'r') as f:
                cache_data = json.load(f)
            
            print(f"✅ Cache contains {len(cache_data)} items")
            print(f"Cache keys: {list(cache_data.keys())}")
            
            if 'game_history_page_0' in cache_data:
                cached_history = cache_data['game_history_page_0']
                print(f"Cached game history has {len(cached_history)} records")
                
                if cached_history:
                    print("\nFirst 3 cached records:")
                    for i, record in enumerate(cached_history[:3]):
                        print(f"  Cached Record {i+1}: {record}")
            else:
                print("❌ No game_history_page_0 in cache")
                
        except FileNotFoundError:
            print("❌ Stats cache file not found")
        except Exception as cache_e:
            print(f"❌ Error reading cache: {cache_e}")
            
    except Exception as e:
        print(f"❌ Error checking cache: {e}")

if __name__ == "__main__":
    debug_stats_display()
