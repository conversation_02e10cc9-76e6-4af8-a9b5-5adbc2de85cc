"""
RethinkDB Dashboard for the WOW Games application.

This script provides a simple web dashboard for viewing RethinkDB data remotely.
It allows you to browse data from the RethinkDB database without needing to
access the RethinkDB admin interface directly.
"""

import os
import json
import logging
import threading
import datetime
from flask import Flask, render_template, jsonify, request, redirect, url_for

# Import RethinkDB configuration
try:
    from rethink_config import (
        RETHINKDB_HOST,
        RETHINKDB_PORT,
        RETHINKDB_DB,
        RETHINKDB_USER,
        RETHINKDB_PASSWORD,
        RETHINKDB_SSL,
        SYNC_TABLES
    )
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    # Default configuration
    RETHINKDB_HOST = 'localhost'
    RETHINKDB_PORT = 28015
    RETHINKDB_DB = 'wow_game_stats'
    RETHINKDB_USER = 'admin'
    RETHINKDB_PASSWORD = ''
    RETHINKDB_SSL = False
    SYNC_TABLES = ['daily_stats', 'game_history', 'wallet_transactions', 'admin_users', 'audit_log']

# Import RethinkDB
try:
    import rethinkdb
r = rethinkdb.r
RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False

# Create Flask app
app = Flask(__name__)

# Create templates directory and files if they don't exist
templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
os.makedirs(templates_dir, exist_ok=True)

# Create a basic layout template
layout_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WOW Games RethinkDB Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav {
            background-color: #444;
            overflow: hidden;
        }
        .nav a {
            float: left;
            display: block;
            color: white;
            text-align: center;
            padding: 14px 16px;
            text-decoration: none;
        }
        .nav a:hover {
            background-color: #555;
        }
        .nav a.active {
            background-color: #4CAF50;
        }
        .content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status.online {
            background-color: #4CAF50;
        }
        .status.offline {
            background-color: #f44336;
        }
        .dashboard-summary {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        .summary-card {
            flex: 1;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin: 10px;
            padding: 20px;
            min-width: 200px;
        }
        .summary-card h3 {
            margin-top: 0;
            color: #444;
        }
        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination a {
            display: inline-block;
            padding: 8px 16px;
            text-decoration: none;
            background-color: #f2f2f2;
            color: black;
            border-radius: 5px;
            margin: 0 5px;
        }
        .pagination a.active {
            background-color: #4CAF50;
            color: white;
        }
        .pagination a:hover:not(.active) {
            background-color: #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"], input[type="number"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .alert-danger {
            background-color: #f2dede;
            color: #a94442;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WOW Games RethinkDB Dashboard</h1>
    </div>
    <div class="nav">
        <a href="/" {% if active_page == 'home' %}class="active"{% endif %}>Home</a>
        <a href="/daily-stats" {% if active_page == 'daily-stats' %}class="active"{% endif %}>Daily Stats</a>
        <a href="/game-history" {% if active_page == 'game-history' %}class="active"{% endif %}>Game History</a>
        <a href="/wallet" {% if active_page == 'wallet' %}class="active"{% endif %}>Wallet</a>
        <a href="/settings" {% if active_page == 'settings' %}class="active"{% endif %}>Settings</a>
    </div>
    <div class="container">
        {% if connection_status == True %}
            <div class="status online"></div> Connected to RethinkDB
        {% else %}
            <div class="status offline"></div> Not connected to RethinkDB
        {% endif %}
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
    </div>
</body>
</html>
"""

# Create index template
index_template = """{% extends "layout.html" %}
{% block content %}
    <h2>Dashboard</h2>
    
    <div class="dashboard-summary">
        <div class="summary-card">
            <h3>Total Games</h3>
            <div class="value">{{ summary.total_games }}</div>
        </div>
        <div class="summary-card">
            <h3>Total Earnings</h3>
            <div class="value">{{ summary.total_earnings }} ETB</div>
        </div>
        <div class="summary-card">
            <h3>Today's Games</h3>
            <div class="value">{{ summary.todays_games }}</div>
        </div>
        <div class="summary-card">
            <h3>Today's Earnings</h3>
            <div class="value">{{ summary.todays_earnings }} ETB</div>
        </div>
    </div>
    
    <h3>Sync Status</h3>
    <table>
        <thead>
            <tr>
                <th>Table</th>
                <th>Last Sync</th>
                <th>Records</th>
                <th>Pending Operations</th>
            </tr>
        </thead>
        <tbody>
            {% for table in tables %}
            <tr>
                <td>{{ table.name }}</td>
                <td>{{ table.last_sync }}</td>
                <td>{{ table.records }}</td>
                <td>{{ table.pending }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}
"""

# Create daily stats template
daily_stats_template = """{% extends "layout.html" %}
{% block content %}
    <h2>Daily Statistics</h2>
    
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Games Played</th>
                <th>Earnings</th>
                <th>Winners</th>
                <th>Total Players</th>
            </tr>
        </thead>
        <tbody>
            {% for stat in stats %}
            <tr>
                <td>{{ stat.date }}</td>
                <td>{{ stat.games_played }}</td>
                <td>{{ stat.earnings }} ETB</td>
                <td>{{ stat.winners }}</td>
                <td>{{ stat.total_players }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}
"""

# Create game history template
game_history_template = """{% extends "layout.html" %}
{% block content %}
    <h2>Game History</h2>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date/Time</th>
                <th>Username</th>
                <th>House</th>
                <th>Stake</th>
                <th>Players</th>
                <th>Prize</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for game in games %}
            <tr>
                <td>{{ game.id }}</td>
                <td>{{ game.date_time }}</td>
                <td>{{ game.username }}</td>
                <td>{{ game.house }}</td>
                <td>{{ game.stake }} ETB</td>
                <td>{{ game.players }}</td>
                <td>{{ game.total_prize }} ETB</td>
                <td>{{ game.status }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div class="pagination">
        {% if page > 1 %}
            <a href="/game-history?page={{ page - 1 }}">&laquo; Previous</a>
        {% endif %}
        
        <span>Page {{ page }} of {{ total_pages }}</span>
        
        {% if page < total_pages %}
            <a href="/game-history?page={{ page + 1 }}">Next &raquo;</a>
        {% endif %}
    </div>
{% endblock %}
"""

# Create wallet template
wallet_template = """{% extends "layout.html" %}
{% block content %}
    <h2>Wallet Transactions</h2>
    
    <div class="summary-card">
        <h3>Current Balance</h3>
        <div class="value">{{ balance }} ETB</div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date/Time</th>
                <th>Amount</th>
                <th>Type</th>
                <th>Description</th>
                <th>Balance After</th>
            </tr>
        </thead>
        <tbody>
            {% for transaction in transactions %}
            <tr>
                <td>{{ transaction.id }}</td>
                <td>{{ transaction.date_time }}</td>
                <td>{{ transaction.amount }} ETB</td>
                <td>{{ transaction.transaction_type }}</td>
                <td>{{ transaction.description }}</td>
                <td>{{ transaction.balance_after }} ETB</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div class="pagination">
        {% if page > 1 %}
            <a href="/wallet?page={{ page - 1 }}">&laquo; Previous</a>
        {% endif %}
        
        <span>Page {{ page }} of {{ total_pages }}</span>
        
        {% if page < total_pages %}
            <a href="/wallet?page={{ page + 1 }}">Next &raquo;</a>
        {% endif %}
    </div>
{% endblock %}
"""

# Create settings template
settings_template = """{% extends "layout.html" %}
{% block content %}
    <h2>RethinkDB Settings</h2>
    
    {% if message %}
        <div class="alert {{ message_class }}">
            {{ message }}
        </div>
    {% endif %}
    
    <form method="post" action="/settings">
        <div class="form-group">
            <label for="host">Host:</label>
            <input type="text" id="host" name="host" value="{{ config.host }}">
        </div>
        
        <div class="form-group">
            <label for="port">Port:</label>
            <input type="number" id="port" name="port" value="{{ config.port }}">
        </div>
        
        <div class="form-group">
            <label for="db">Database:</label>
            <input type="text" id="db" name="db" value="{{ config.db }}">
        </div>
        
        <div class="form-group">
            <label for="user">Username:</label>
            <input type="text" id="user" name="user" value="{{ config.user }}">
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="{{ config.password }}">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="ssl" {% if config.ssl %}checked{% endif %}>
                Use SSL
            </label>
        </div>
        
        <div class="form-group">
            <label for="sync_interval">Sync Interval (seconds):</label>
            <input type="number" id="sync_interval" name="sync_interval" value="{{ config.sync_interval }}">
        </div>
        
        <button type="submit">Save Settings</button>
    </form>
    
    <h3>Test Connection</h3>
    <form method="post" action="/test-connection">
        <button type="submit">Test Connection</button>
    </form>
    
    <h3>Sync Now</h3>
    <form method="post" action="/sync-now">
        <button type="submit">Sync Now</button>
    </form>
{% endblock %}
"""

# Write templates to files
with open(os.path.join(templates_dir, 'layout.html'), 'w') as f:
    f.write(layout_template)

with open(os.path.join(templates_dir, 'index.html'), 'w') as f:
    f.write(index_template)

with open(os.path.join(templates_dir, 'daily_stats.html'), 'w') as f:
    f.write(daily_stats_template)

with open(os.path.join(templates_dir, 'game_history.html'), 'w') as f:
    f.write(game_history_template)

with open(os.path.join(templates_dir, 'wallet.html'), 'w') as f:
    f.write(wallet_template)

with open(os.path.join(templates_dir, 'settings.html'), 'w') as f:
    f.write(settings_template)

def connect_to_rethinkdb():
    """Connect to RethinkDB server."""
    try:
        if not RETHINKDB_AVAILABLE:
            return None
            
        conn = rethinkdb.r.connect(
            host=RETHINKDB_HOST,
            port=RETHINKDB_PORT,
            db=RETHINKDB_DB,
            user=RETHINKDB_USER,
            password=RETHINKDB_PASSWORD,
            ssl={"ca_certs": None} if RETHINKDB_SSL else None
        )
        
        return conn
    except Exception as e:
        print(f"Error connecting to RethinkDB: {e}")
        return None

def get_connection_status():
    """Check if connected to RethinkDB."""
    conn = connect_to_rethinkdb()
    status = conn is not None
    
    if conn:
        conn.close()
        
    return status

def get_table_stats():
    """Get statistics for each table."""
    conn = connect_to_rethinkdb()
    if not conn:
        return []
        
    try:
        tables = []
        
        for table_name in SYNC_TABLES:
            try:
                # Get record count
                count = rethinkdb.r.table(table_name).count().run(conn)
                
                # Get last sync time
                sync_metadata = os.path.join('data', 'sync_cache', 'sync_metadata.json')
                last_sync = "Never"
                
                if os.path.exists(sync_metadata):
                    try:
                        with open(sync_metadata, 'r') as f:
                            metadata = json.load(f)
                            
                        if table_name in metadata and metadata[table_name] > 0:
                            last_sync = datetime.datetime.fromtimestamp(metadata[table_name]).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                
                # Get pending operations count
                pending = 0
                sync_cache_dir = os.path.join('data', 'sync_cache', table_name)
                if os.path.exists(sync_cache_dir):
                    pending = len([f for f in os.listdir(sync_cache_dir) if f.endswith('.json')])
                
                tables.append({
                    'name': table_name,
                    'records': count,
                    'last_sync': last_sync,
                    'pending': pending
                })
            except Exception as e:
                print(f"Error getting stats for table {table_name}: {e}")
                tables.append({
                    'name': table_name,
                    'records': 0,
                    'last_sync': "Error",
                    'pending': 0
                })
        
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting table stats: {e}")
        if conn:
            conn.close()
        return []

def get_dashboard_summary():
    """Get summary data for the dashboard."""
    conn = connect_to_rethinkdb()
    if not conn:
        return {
            'total_games': 0,
            'total_earnings': 0,
            'todays_games': 0,
            'todays_earnings': 0
        }
        
    try:
        # Get total games
        total_games = rethinkdb.r.table('game_history').count().run(conn)
        
        # Get total earnings
        total_earnings = rethinkdb.r.table('daily_stats').sum('earnings').run(conn) or 0
        
        # Get today's stats
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        todays_stats = rethinkdb.r.table('daily_stats').get(today).run(conn) or {
            'games_played': 0,
            'earnings': 0
        }
        
        conn.close()
        
        return {
            'total_games': total_games,
            'total_earnings': total_earnings,
            'todays_games': todays_stats.get('games_played', 0),
            'todays_earnings': todays_stats.get('earnings', 0)
        }
    except Exception as e:
        print(f"Error getting dashboard summary: {e}")
        if conn:
            conn.close()
        return {
            'total_games': 0,
            'total_earnings': 0,
            'todays_games': 0,
            'todays_earnings': 0
        }

def get_daily_stats():
    """Get daily statistics."""
    conn = connect_to_rethinkdb()
    if not conn:
        return []
        
    try:
        # Get all daily stats ordered by date
        stats = list(rethinkdb.r.table('daily_stats').order_by(rethinkdb.r.desc('date')).limit(30).run(conn))
        
        conn.close()
        return stats
    except Exception as e:
        print(f"Error getting daily stats: {e}")
        if conn:
            conn.close()
        return []

def get_game_history(page=1, page_size=20):
    """Get game history with pagination."""
    conn = connect_to_rethinkdb()
    if not conn:
        return {
            'games': [],
            'total_games': 0,
            'total_pages': 1
        }
        
    try:
        # Get total games
        total_games = rethinkdb.r.table('game_history').count().run(conn)
        
        # Calculate total pages
        total_pages = max(1, (total_games + page_size - 1) // page_size)
        
        # Get games for current page
        skip = (page - 1) * page_size
        games = list(rethinkdb.r.table('game_history')
            .order_by(rethinkdb.r.desc('date_time'))
            .skip(skip)
            .limit(page_size)
            .run(conn))
        
        conn.close()
        
        return {
            'games': games,
            'total_games': total_games,
            'total_pages': total_pages
        }
    except Exception as e:
        print(f"Error getting game history: {e}")
        if conn:
            conn.close()
        return {
            'games': [],
            'total_games': 0,
            'total_pages': 1
        }

def get_wallet_transactions(page=1, page_size=20):
    """Get wallet transactions with pagination."""
    conn = connect_to_rethinkdb()
    if not conn:
        return {
            'transactions': [],
            'total_transactions': 0,
            'total_pages': 1,
            'balance': 0
        }
        
    try:
        # Get total transactions
        total_transactions = rethinkdb.r.table('wallet_transactions').count().run(conn)
        
        # Calculate total pages
        total_pages = max(1, (total_transactions + page_size - 1) // page_size)
        
        # Get transactions for current page
        skip = (page - 1) * page_size
        transactions = list(rethinkdb.r.table('wallet_transactions')
            .order_by(rethinkdb.r.desc('date_time'))
            .skip(skip)
            .limit(page_size)
            .run(conn))
        
        # Get current balance
        balance = 0
        if total_transactions > 0:
            latest = rethinkdb.r.table('wallet_transactions').order_by(rethinkdb.r.desc('date_time')).limit(1).run(conn)
            latest_list = list(latest)
            if latest_list:
                balance = latest_list[0].get('balance_after', 0)
        
        conn.close()
        
        return {
            'transactions': transactions,
            'total_transactions': total_transactions,
            'total_pages': total_pages,
            'balance': balance
        }
    except Exception as e:
        print(f"Error getting wallet transactions: {e}")
        if conn:
            conn.close()
        return {
            'transactions': [],
            'total_transactions': 0,
            'total_pages': 1,
            'balance': 0
        }

def save_settings(settings):
    """Save RethinkDB settings."""
    try:
        # Update global variables
        global RETHINKDB_HOST, RETHINKDB_PORT, RETHINKDB_DB, RETHINKDB_USER, RETHINKDB_PASSWORD, RETHINKDB_SSL
        
        RETHINKDB_HOST = settings.get('host', 'localhost')
        RETHINKDB_PORT = int(settings.get('port', 28015))
        RETHINKDB_DB = settings.get('db', 'wow_game_stats')
        RETHINKDB_USER = settings.get('user', 'admin')
        RETHINKDB_PASSWORD = settings.get('password', '')
        RETHINKDB_SSL = settings.get('ssl', False) == 'on'
        
        # Save to config file
        if CONFIG_AVAILABLE:
            from rethink_config import save_config_to_file
            save_config_to_file()
        
        return True
    except Exception as e:
        print(f"Error saving settings: {e}")
        return False

def test_connection():
    """Test connection to RethinkDB."""
    conn = connect_to_rethinkdb()
    if not conn:
        return False
        
    try:
        # Test a simple query
        rethinkdb.r.expr(1).run(conn)
        conn.close()
        return True
    except Exception as e:
        print(f"Error testing connection: {e}")
        if conn:
            conn.close()
        return False

def trigger_sync():
    """Trigger a synchronization."""
    try:
        # Try to import hybrid DB integration
        from hybrid_db_integration import get_hybrid_db_integration
        
        # Get the hybrid DB integration
        db_integration = get_hybrid_db_integration()
        
        # Force a refresh
        result = db_integration.force_refresh_data()
        
        return result
    except Exception as e:
        print(f"Error triggering sync: {e}")
        return False

@app.route('/')
def index():
    """Home page."""
    connection_status = get_connection_status()
    summary = get_dashboard_summary()
    tables = get_table_stats()
    
    return render_template('index.html',
        active_page='home',
        connection_status=connection_status,
        summary=summary,
        tables=tables)

@app.route('/daily-stats')
def daily_stats():
    """Daily statistics page."""
    connection_status = get_connection_status()
    stats = get_daily_stats()
    
    return render_template('daily_stats.html',
        active_page='daily-stats',
        connection_status=connection_status,
        stats=stats)

@app.route('/game-history')
def game_history():
    """Game history page."""
    connection_status = get_connection_status()
    page = int(request.args.get('page', 1))
    
    history = get_game_history(page)
    
    return render_template('game_history.html',
        active_page='game-history',
        connection_status=connection_status,
        games=history['games'],
        total_pages=history['total_pages'],
        page=page)

@app.route('/wallet')
def wallet():
    """Wallet transactions page."""
    connection_status = get_connection_status()
    page = int(request.args.get('page', 1))
    
    wallet_data = get_wallet_transactions(page)
    
    return render_template('wallet.html',
        active_page='wallet',
        connection_status=connection_status,
        transactions=wallet_data['transactions'],
        total_pages=wallet_data['total_pages'],
        page=page,
        balance=wallet_data['balance'])

@app.route('/settings', methods=['GET', 'POST'])
def settings():
    """Settings page."""
    connection_status = get_connection_status()
    message = None
    message_class = ""
    
    if request.method == 'POST':
        # Save settings
        settings_data = {
            'host': request.form.get('host'),
            'port': request.form.get('port'),
            'db': request.form.get('db'),
            'user': request.form.get('user'),
            'password': request.form.get('password'),
            'ssl': request.form.get('ssl'),
            'sync_interval': request.form.get('sync_interval')
        }
        
        if save_settings(settings_data):
            message = "Settings saved successfully"
            message_class = "alert-success"
        else:
            message = "Error saving settings"
            message_class = "alert-danger"
    
    # Get current settings
    config = {
        'host': RETHINKDB_HOST,
        'port': RETHINKDB_PORT,
        'db': RETHINKDB_DB,
        'user': RETHINKDB_USER,
        'password': RETHINKDB_PASSWORD,
        'ssl': RETHINKDB_SSL,
        'sync_interval': SYNC_INTERVAL if 'SYNC_INTERVAL' in globals() else 60
    }
    
    return render_template('settings.html',
        active_page='settings',
        connection_status=connection_status,
        config=config,
        message=message,
        message_class=message_class)

@app.route('/test-connection', methods=['POST'])
def test_connection_route():
    """Test connection route."""
    result = test_connection()
    
    if result:
        message = "Connection successful"
        message_class = "alert-success"
    else:
        message = "Connection failed"
        message_class = "alert-danger"
    
    # Get current settings
    config = {
        'host': RETHINKDB_HOST,
        'port': RETHINKDB_PORT,
        'db': RETHINKDB_DB,
        'user': RETHINKDB_USER,
        'password': RETHINKDB_PASSWORD,
        'ssl': RETHINKDB_SSL,
        'sync_interval': SYNC_INTERVAL if 'SYNC_INTERVAL' in globals() else 60
    }
    
    return render_template('settings.html',
        active_page='settings',
        connection_status=get_connection_status(),
        config=config,
        message=message,
        message_class=message_class)

@app.route('/sync-now', methods=['POST'])
def sync_now():
    """Trigger synchronization route."""
    result = trigger_sync()
    
    if result:
        message = "Synchronization triggered successfully"
        message_class = "alert-success"
    else:
        message = "Error triggering synchronization"
        message_class = "alert-danger"
    
    # Get current settings
    config = {
        'host': RETHINKDB_HOST,
        'port': RETHINKDB_PORT,
        'db': RETHINKDB_DB,
        'user': RETHINKDB_USER,
        'password': RETHINKDB_PASSWORD,
        'ssl': RETHINKDB_SSL,
        'sync_interval': SYNC_INTERVAL if 'SYNC_INTERVAL' in globals() else 60
    }
    
    return render_template('settings.html',
        active_page='settings',
        connection_status=get_connection_status(),
        config=config,
        message=message,
        message_class=message_class)

def run_dashboard():
    """Run the dashboard in a separate thread."""
    print("\nStarting RethinkDB Dashboard...")
    print("Open http://localhost:5000 in your browser to access the dashboard")
    print("Press Ctrl+C to stop the dashboard")
    
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == '__main__':
    # Check if Flask is installed
    try:
        import flask
    except ImportError:
        print("Flask is not installed. Installing...")
        import subprocess
        subprocess.run([sys.executable, "-m", "pip", "install", "flask"])
        print("Flask installed successfully. Please run this script again.")
        sys.exit(0)
    
    run_dashboard()