#!/usr/bin/env python3
"""
Script to check database structure and insert data into all relevant databases.
"""

import sqlite3
import os
from datetime import datetime

def check_database_structure(db_path):
    """Check the structure of a database file."""
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📊 Database: {db_path}")
        print(f"   Tables: {[table[0] for table in tables]}")
        
        # Check if daily_stats table exists
        if ('daily_stats',) in tables:
            cursor.execute("PRAGMA table_info(daily_stats)")
            columns = cursor.fetchall()
            print(f"   daily_stats columns: {[col[1] for col in columns]}")
            
            # Check existing data
            cursor.execute("SELECT COUNT(*) FROM daily_stats")
            count = cursor.fetchone()[0]
            print(f"   daily_stats records: {count}")
            
            conn.close()
            return True
        else:
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Error checking {db_path}: {e}")
        return None

def insert_data_to_database(db_path):
    """Insert the historical data to a specific database."""
    stats_data = [
        ('2025-05-26', 500.0),  # Monday
        ('2025-05-27', 500.0),  # Tuesday
        ('2025-05-28', 0.0),    # Wednesday (zero)
        ('2025-05-29', 180.0),  # Thursday
        ('2025-05-30', 600.0),  # Friday
        ('2025-05-31', 750.0),  # Saturday
        ('2025-06-01', 950.0),  # Sunday
    ]

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check the schema of daily_stats table
        cursor.execute("PRAGMA table_info(daily_stats)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        print(f"   📝 Inserting data into {db_path}...")
        print(f"   📋 Schema: {column_names}")

        for date_str, earnings in stats_data:
            # Check if record already exists
            cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date_str,))
            existing = cursor.fetchone()

            if existing:
                # Update existing record based on schema
                if 'winners' in column_names and 'total_players' in column_names:
                    # Full schema (stats.db)
                    cursor.execute('''
                    UPDATE daily_stats
                    SET earnings = ?, games_played = CASE WHEN ? > 0 THEN 1 ELSE 0 END
                    WHERE date = ?
                    ''', (earnings, earnings, date_str))
                else:
                    # Simple schema (stats_new.db)
                    cursor.execute('''
                    UPDATE daily_stats
                    SET earnings = ?, games_played = CASE WHEN ? > 0 THEN 1 ELSE 0 END
                    WHERE date = ?
                    ''', (earnings, earnings, date_str))
            else:
                # Insert new record based on schema
                games_played = 1 if earnings > 0 else 0

                if 'winners' in column_names and 'total_players' in column_names:
                    # Full schema (stats.db)
                    cursor.execute('''
                    INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                    VALUES (?, ?, ?, ?, ?)
                    ''', (date_str, games_played, earnings, 0, 0))
                else:
                    # Simple schema (stats_new.db)
                    cursor.execute('''
                    INSERT INTO daily_stats (date, games_played, earnings)
                    VALUES (?, ?, ?)
                    ''', (date_str, games_played, earnings))

        conn.commit()
        conn.close()
        print(f"   ✅ Data inserted successfully into {db_path}")
        return True

    except Exception as e:
        print(f"   ❌ Error inserting data into {db_path}: {e}")
        return False

def main():
    """Main function."""
    print("🔍 Database Structure Check and Data Insertion")
    print("=" * 60)
    
    # List of potential database files
    db_files = [
        'data/stats.db',
        'data/stats_new.db'
    ]
    
    databases_with_daily_stats = []
    
    # Check each database
    for db_path in db_files:
        has_daily_stats = check_database_structure(db_path)
        if has_daily_stats:
            databases_with_daily_stats.append(db_path)
    
    # Insert data into databases that have daily_stats table
    print(f"\n📊 Inserting data into {len(databases_with_daily_stats)} database(s)...")
    
    for db_path in databases_with_daily_stats:
        insert_data_to_database(db_path)
    
    print("\n🎉 Database check and data insertion completed!")

if __name__ == "__main__":
    main()
