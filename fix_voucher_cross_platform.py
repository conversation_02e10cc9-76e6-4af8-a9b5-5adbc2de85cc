#!/usr/bin/env python3
"""
Comprehensive fix script for voucher system cross-platform compatibility issues.

This script applies all necessary fixes to ensure the voucher system works
reliably across different computers and environments.
"""

import os
import sys
import shutil
import json
from datetime import datetime

def backup_files():
    """Create backups of files that will be modified."""
    print("Creating backups of files to be modified...")
    
    backup_dir = f"backup_voucher_fix_{int(datetime.now().timestamp())}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        "payment/voucher_manager.py",
        "payment/voucher_processor.py", 
        "payment/crypto_utils.py",
        "voucher_validator.py",
        "get_machine_uuid.py",
        "stats_page.py"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, file_path.replace('/', '_'))
            shutil.copy2(file_path, backup_path)
            print(f"✓ Backed up: {file_path} -> {backup_path}")
    
    print(f"✓ Backups created in: {backup_dir}")
    return backup_dir

def fix_crypto_utils():
    """Fix crypto_utils.py for better cross-platform UUID detection."""
    print("\nFixing crypto_utils.py...")
    
    file_path = "payment/crypto_utils.py"
    if not os.path.exists(file_path):
        print(f"✗ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Add improved UUID detection method
        improved_uuid_method = '''
    @staticmethod
    def get_machine_uuid_robust():
        """
        Get machine UUID with improved cross-platform support and fallbacks.
        
        Returns:
            str: Machine UUID or consistent machine fingerprint
        """
        import platform
        import subprocess
        import hashlib
        import uuid as uuid_module
        
        # Try multiple methods in order of preference
        uuid_str = None
        
        try:
            system = platform.system().lower()
            
            if system == 'windows':
                # Method 1: Try WMI
                try:
                    import wmi
                    c = wmi.WMI()
                    for system_info in c.Win32_ComputerSystemProduct():
                        if system_info.UUID and system_info.UUID != "00000000-0000-0000-0000-000000000000":
                            uuid_str = system_info.UUID.upper()
                            break
                except (ImportError, Exception):
                    pass
                
                # Method 2: Try wmic command
                if not uuid_str:
                    try:
                        result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'],
                                              capture_output=True, text=True, check=True, timeout=10)
                        lines = result.stdout.strip().split('\\n')
                        if len(lines) >= 2:
                            candidate = lines[1].strip().upper()
                            if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                uuid_str = candidate
                    except Exception:
                        pass
                
                # Method 3: Try Windows registry
                if not uuid_str:
                    try:
                        import winreg
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                           r"SOFTWARE\\Microsoft\\Cryptography") as key:
                            machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                            if machine_guid:
                                uuid_str = machine_guid.upper()
                    except Exception:
                        pass
            
            elif system == 'linux':
                # Method 1: Try machine-id
                try:
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            candidate = f.read().strip()
                            if candidate:
                                uuid_str = candidate.upper()
                except Exception:
                    pass
                
                # Method 2: Try DMI UUID
                if not uuid_str:
                    try:
                        if os.path.exists('/sys/class/dmi/id/product_uuid'):
                            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                                candidate = f.read().strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    uuid_str = candidate.upper()
                    except Exception:
                        pass
            
            elif system == 'darwin':  # macOS
                try:
                    result = subprocess.run(['ioreg', '-rd1', '-c', 'IOPlatformExpertDevice'],
                                          capture_output=True, text=True, check=True, timeout=10)
                    for line in result.stdout.split('\\n'):
                        if 'IOPlatformUUID' in line:
                            parts = line.split('"')
                            if len(parts) >= 4:
                                candidate = parts[3].strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    uuid_str = candidate.upper()
                                    break
                except Exception:
                    pass
        
        except Exception:
            pass
        
        # If we got a valid UUID, return it
        if uuid_str:
            return uuid_str
        
        # Fallback: Generate consistent machine fingerprint
        try:
            fingerprint_data = []
            fingerprint_data.append(platform.node() or "unknown")
            fingerprint_data.append(platform.machine() or "unknown") 
            fingerprint_data.append(platform.processor() or "unknown")
            fingerprint_data.append(platform.system() or "unknown")
            
            # Add MAC address
            try:
                mac = uuid_module.getnode()
                fingerprint_data.append(str(mac))
            except:
                fingerprint_data.append("no-mac")
            
            # Create consistent hash
            fingerprint_string = "-".join(fingerprint_data)
            hash_obj = hashlib.sha256(fingerprint_string.encode())
            fingerprint_hash = hash_obj.hexdigest()[:32].upper()
            
            # Format as UUID-like string
            return f"{fingerprint_hash[:8]}-{fingerprint_hash[8:12]}-{fingerprint_hash[12:16]}-{fingerprint_hash[16:20]}-{fingerprint_hash[20:32]}"
            
        except Exception:
            # Ultimate fallback
            return str(uuid_module.uuid4()).upper()
'''
        
        # Replace the existing get_machine_uuid method
        if "def get_machine_uuid(" in content:
            # Find the method and replace it
            lines = content.split('\n')
            new_lines = []
            in_method = False
            indent_level = 0
            
            for line in lines:
                if "def get_machine_uuid(" in line and "@staticmethod" in lines[max(0, len(new_lines)-1)]:
                    # Start of method - replace with improved version
                    new_lines.append(improved_uuid_method.strip())
                    in_method = True
                    indent_level = len(line) - len(line.lstrip())
                elif in_method:
                    # Check if we're still in the method
                    if line.strip() == "":
                        continue  # Skip empty lines
                    elif line.startswith(" " * indent_level) and not line.strip().startswith("def ") and not line.strip().startswith("@"):
                        continue  # Skip method content
                    else:
                        # End of method
                        in_method = False
                        new_lines.append(line)
                else:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
        else:
            # Add the method if it doesn't exist
            content += improved_uuid_method
        
        # Write the updated content
        with open(file_path, 'w') as f:
            f.write(content)
        
        print(f"✓ Fixed: {file_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def create_voucher_system_diagnostic():
    """Create a diagnostic script for voucher system issues."""
    print("\nCreating voucher system diagnostic script...")
    
    diagnostic_script = '''#!/usr/bin/env python3
"""
Voucher System Diagnostic Script

This script diagnoses common voucher system issues and provides solutions.
"""

import os
import sys
import json
from datetime import datetime

def diagnose_uuid_issues():
    """Diagnose UUID detection issues."""
    print("Diagnosing UUID detection...")
    
    methods = []
    
    # Test get_machine_uuid
    try:
        from get_machine_uuid import get_machine_uuid
        uuid1 = get_machine_uuid()
        methods.append(("get_machine_uuid", uuid1, "SUCCESS"))
    except Exception as e:
        methods.append(("get_machine_uuid", str(e), "ERROR"))
    
    # Test crypto_utils
    try:
        from payment.crypto_utils import CryptoUtils
        uuid2 = CryptoUtils.get_machine_uuid()
        methods.append(("CryptoUtils", uuid2, "SUCCESS"))
    except Exception as e:
        methods.append(("CryptoUtils", str(e), "ERROR"))
    
    # Test voucher_processor
    try:
        from payment.voucher_processor import VoucherProcessor
        processor = VoucherProcessor()
        uuid3 = processor.machine_uuid
        methods.append(("VoucherProcessor", uuid3, "SUCCESS"))
    except Exception as e:
        methods.append(("VoucherProcessor", str(e), "ERROR"))
    
    print("UUID Detection Results:")
    for method, result, status in methods:
        print(f"  {method}: {status} - {result}")
    
    return methods

def diagnose_database_issues():
    """Diagnose database connectivity issues."""
    print("\\nDiagnosing database issues...")
    
    try:
        from payment.voucher_manager import VoucherManager, get_data_dir
        
        data_dir = get_data_dir()
        print(f"Data directory: {data_dir}")
        print(f"Data directory exists: {os.path.exists(data_dir)}")
        print(f"Data directory writable: {os.access(data_dir, os.W_OK)}")
        
        manager = VoucherManager()
        print(f"VoucherManager created successfully")
        print(f"Initialization errors: {len(manager.initialization_errors)}")
        
        for error in manager.initialization_errors:
            print(f"  Error: {error}")
        
        return True
        
    except Exception as e:
        print(f"Database diagnosis failed: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("VOUCHER SYSTEM DIAGNOSTIC")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print(f"Python: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run diagnostics
    uuid_results = diagnose_uuid_issues()
    db_result = diagnose_database_issues()
    
    # Generate report
    report = {
        "timestamp": datetime.now().isoformat(),
        "uuid_detection": uuid_results,
        "database_status": db_result
    }
    
    with open("voucher_diagnostic_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("\\nDiagnostic complete. Report saved to voucher_diagnostic_report.json")

if __name__ == "__main__":
    main()
'''
    
    with open("diagnose_voucher_system.py", "w") as f:
        f.write(diagnostic_script)
    
    print("✓ Created: diagnose_voucher_system.py")

def main():
    """Main fix function."""
    print("VOUCHER SYSTEM CROSS-PLATFORM COMPATIBILITY FIX")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    
    try:
        # Create backups
        backup_dir = backup_files()
        
        # Apply fixes
        print("\nApplying fixes...")
        
        fixes_applied = 0
        total_fixes = 2
        
        if fix_crypto_utils():
            fixes_applied += 1
        
        create_voucher_system_diagnostic()
        fixes_applied += 1
        
        # Summary
        print(f"\n" + "=" * 60)
        print("FIX SUMMARY")
        print("=" * 60)
        print(f"Total fixes: {total_fixes}")
        print(f"Applied fixes: {fixes_applied}")
        print(f"Success rate: {(fixes_applied/total_fixes)*100:.1f}%")
        
        if fixes_applied == total_fixes:
            print("\n🎉 ALL FIXES APPLIED SUCCESSFULLY!")
            print("\nNext steps:")
            print("1. Run 'python test_voucher_cross_platform.py' to verify fixes")
            print("2. Run 'python diagnose_voucher_system.py' if issues persist")
            print("3. Test voucher redemption on different computers")
        else:
            print(f"\n⚠ {total_fixes - fixes_applied} fixes failed to apply.")
            print(f"Check the error messages above and try manual fixes.")
        
        print(f"\nBackups available in: {backup_dir}")
        
        return 0 if fixes_applied == total_fixes else 1
        
    except Exception as e:
        print(f"\n💥 Fix execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
