In recursion: removing file 'D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data\tmp'
warn: Trying to delete non-existent file 'D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data\tmp'
Initializing directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: --daemon not implemented on windows
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
Listening for administrative HTTP connections on port 8081
Listening on cluster address: 127.0.0.1
Listening on driver address: 127.0.0.1
Listening on http address: 127.0.0.1
To fully expose RethinkDB on the network, bind to all addresses by running rethinkdb with the `--bind all` command line option.
Server ready, "DESKTOP_HD6UHPP_8n3" 227a5a3a-25cc-4a65-833a-8ae8b20b38f7
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Listening for client driver connections on port 28015
Listening for administrative HTTP connections on port 8081
Listening on cluster address: 127.0.0.1
Listening on driver address: 127.0.0.1
Listening on http address: 127.0.0.1
To fully expose RethinkDB on the network, bind to all addresses by running rethinkdb with the `--bind all` command line option.
Server ready, "DESKTOP_HD6UHPP_8n3" 227a5a3a-25cc-4a65-833a-8ae8b20b38f7
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Error in thread 1 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 0 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 3 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Error in thread 2 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Backtrace:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Backtrace:
error: Tue May 27 13:21:04 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
error: Tue May 27 13:21:04 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
error: Tue May 27 13:21:04 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Error in thread 1 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Error in thread 3 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 0 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 2 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Backtrace:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Tue May 27 13:23:05 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Tue May 27 13:23:05 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Tue May 27 13:23:05 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
error: Exiting.
error: Exiting.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Error in thread 1 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 0 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 3 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 2 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Backtrace:
error: Backtrace:
error: Tue May 27 13:24:01 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Backtrace:
error: Tue May 27 13:24:01 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
error: Exiting.
error: Tue May 27 13:24:01 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
Running on 10.0.22631 (Windows 10, Server 2016)
Loading data from directory D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\rethinkdb_data
Listening for intracluster connections on port 29015
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Error in thread 1 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Error in thread 3 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
Version: rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Error in thread 0 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Error in thread 2 in c:\cygwin64\home\sam\rethinkdb\src\containers\binary_blob.hpp at line 39:
error: Backtrace:
error: Backtrace:
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
error: Backtrace:
error: Backtrace:
error: Tue May 27 13:25:31 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Tue May 27 13:25:31 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8B86 [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Tue May 27 13:25:31 2025
       
       1: 0x00007FF64E3DFCDF [The handle is invalid.]
       2: 0x00007FF64E3E0B0A [The handle is invalid.]
       3: 0x00007FF64E7E70B1 [The handle is invalid.]
       4: 0x00007FF64E69BE3C [The handle is invalid.]
       5: 0x00007FF64E6998F3 [The handle is invalid.]
       6: 0x00007FF64E69BAE8 [The handle is invalid.]
       7: 0x00007FF64E6996BD [The handle is invalid.]
       8: 0x00007FF64E69E145 [The handle is invalid.]
       9: 0x00007FF64E7516FF [The handle is invalid.]
       10: 0x00007FF64E7524C3 [The handle is invalid.]
       11: 0x00007FF64E3D8CDE [The handle is invalid.]
       12: 0x00007FF64E3D6CC5 [The handle is invalid.]
       13: 0x00007FF9DE8A594B [The handle is invalid.]
       14: 0x00007FF9E14182E4 [The handle is invalid.]
error: Exiting.
error: Exiting.
error: Exiting.
