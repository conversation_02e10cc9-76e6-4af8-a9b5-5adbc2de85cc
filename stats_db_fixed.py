"""
Stats Database Manager for the WOW Games application.

This module handles the storage and retrieval of game statistics
using SQLite database with security features and connection pooling.
"""

import os
import json
import time
import sqlite3
import datetime
import logging
import threading
import hashlib
from datetime import datetime, timedelta
from contextlib import contextmanager

# Import security module
try:
    from db_security import get_db_security
    DB_SECURITY_AVAILABLE = True
    print("Database security module loaded successfully")
except ImportError as e:
    DB_SECURITY_AVAILABLE = False
    print(f"Database security module not available: {e}")
    print("Using standard database connection without security features")

# Import connection pool
try:
    from db_connection_pool import get_connection_pool
    CONNECTION_POOL_AVAILABLE = True
    print("Database connection pool loaded successfully")
except ImportError as e:
    CONNECTION_POOL_AVAILABLE = False
    print(f"Database connection pool not available: {e}")
    print("Using direct connections without pooling")

# Constants
STATS_DB_PATH = os.path.join('data', 'stats.db')

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'stats_db.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class StatsDBManager:
    """Manager for statistics database operations with security features."""

    def __init__(self):
        """Initialize the stats database manager."""
        # Initialize security if available
        try:
            if DB_SECURITY_AVAILABLE:
                self.db_security = get_db_security()
                logging.info("Database security initialized successfully")
            else:
                self.db_security = None
                logging.warning("Database security module not available, running without security features")
        except Exception as e:
            self.db_security = None
            logging.error(f"Error initializing database security: {e}")
            logging.warning("Running without security features due to initialization error")

        # Ensure database exists
        self.ensure_database_exists()

    def ensure_database_exists(self):
        """Ensure the stats database exists and has the correct schema."""
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(STATS_DB_PATH), exist_ok=True)

        # Create and initialize the database
        try:
            # Use secure connection if available
            if DB_SECURITY_AVAILABLE:
                conn = self.db_security.create_secure_connection(STATS_DB_PATH)
                logging.info("Created secure database connection")
            else:
                conn = sqlite3.connect(STATS_DB_PATH)

            cursor = conn.cursor()

            # Create tables if they don't exist

            # Daily stats table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_stats (
                date TEXT PRIMARY KEY,
                games_played INTEGER DEFAULT 0,
                earnings REAL DEFAULT 0,
                winners INTEGER DEFAULT 0,
                total_players INTEGER DEFAULT 0
            )
            ''')

            # Game history table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS game_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_time TEXT,
                username TEXT,
                house TEXT,
                stake REAL,
                players INTEGER,
                total_calls INTEGER,
                commission_percent REAL,
                fee REAL,
                total_prize REAL,
                details TEXT,
                status TEXT
            )
            ''')

            # Wallet transactions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS wallet_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_time TEXT,
                amount REAL,
                transaction_type TEXT,
                description TEXT,
                balance_after REAL
            )
            ''')

            # Admin users table for access control
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE,
                password_hash TEXT,
                salt TEXT,
                last_login TEXT,
                access_level INTEGER DEFAULT 1
            )
            ''')

            # Audit log table for tracking database changes
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                user_id INTEGER,
                action TEXT,
                table_name TEXT,
                record_id INTEGER,
                details TEXT,
                FOREIGN KEY (user_id) REFERENCES admin_users(id)
            )
            ''')

            conn.commit()
            conn.close()

            logging.info("Database schema initialized successfully")
        except Exception as e:
            logging.error(f"Error initializing database schema: {e}")
            raise

    def get_connection(self):
        """
        Get a database connection with security features if available.

        Note: This method returns a direct connection that must be manually closed.
        For most operations, use get_connection_context() instead which automatically
        handles connection lifecycle.

        Returns:
            sqlite3.Connection: Database connection
        """
        # Use connection pool if available
        if CONNECTION_POOL_AVAILABLE:
            try:
                # Initialize the connection pool if needed
                pool = get_connection_pool(STATS_DB_PATH)
                # Get a connection from the pool
                # Note: This is not using the context manager, so the connection
                # won't be automatically returned to the pool
                conn = pool.pool.get(block=True, timeout=5)
                return conn
            except Exception as e:
                logging.error(f"Error getting connection from pool: {e}")
                logging.warning("Falling back to direct connection")
                # Fall through to direct connection

        # Direct connection as fallback
        try:
            if DB_SECURITY_AVAILABLE and self.db_security:
                try:
                    conn = self.db_security.create_secure_connection(STATS_DB_PATH)
                    return conn
                except Exception as e:
                    logging.error(f"Error creating secure connection: {e}")
                    logging.warning("Falling back to standard connection")
                    return sqlite3.connect(STATS_DB_PATH)
            else:
                return sqlite3.connect(STATS_DB_PATH)
        except Exception as e:
            logging.error(f"Error creating database connection: {e}")
            # Return a new connection as a last resort
            try:
                return sqlite3.connect(STATS_DB_PATH)
            except:
                # If all else fails, raise the original exception
                raise

    @contextmanager
    def get_connection_context(self):
        """
        Get a database connection as a context manager.

        This method returns a connection that will be automatically
        returned to the pool or closed when the context exits.

        Usage:
            with stats_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM table")

        Returns:
            Context manager yielding a database connection
        """
        # Use connection pool if available
        if CONNECTION_POOL_AVAILABLE:
            try:
                # Initialize the connection pool if needed
                pool = get_connection_pool(STATS_DB_PATH)
                # Use the pool's context manager
                with pool.get_connection() as conn:
                    yield conn
                return
            except Exception as e:
                logging.error(f"Error using connection pool: {e}")
                logging.warning("Falling back to direct connection")
                # Fall through to direct connection

        # Direct connection as fallback
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        finally:
            if conn:
                try:
                    conn.close()
                except Exception as e:
                    logging.error(f"Error closing connection: {e}")
                    pass

    def log_operation(self, action, table_name, record_id=None, details=None):
        """
        Log a database operation to the audit log.

        Args:
            action: Type of action (e.g., 'INSERT', 'UPDATE', 'DELETE', 'SELECT')
            table_name: Name of the table being operated on
            record_id: ID of the record being operated on (if applicable)
            details: Additional details about the operation
        """
        # Skip excessive logging for SELECT operations to improve performance
        if action == 'SELECT' and not logging.getLogger().isEnabledFor(logging.DEBUG):
            return

        # Log to security module if available
        try:
            if DB_SECURITY_AVAILABLE and self.db_security:
                self.db_security.log_db_operation(action, {
                    'table': table_name,
                    'record_id': record_id,
                    'details': details
                })
        except Exception as e:
            logging.error(f"Error logging to security module: {e}")

        # Log to audit_log table - use a separate thread to avoid blocking
        def log_to_audit_table():
            try:
                # Use connection context manager for proper cleanup
                with self.get_connection_context() as conn:
                    cursor = conn.cursor()

                    # Set a shorter timeout for audit logging
                    cursor.execute("PRAGMA busy_timeout = 1000")  # 1 second timeout

                    # Insert into audit log
                    cursor.execute('''
                    INSERT INTO audit_log (timestamp, user_id, action, table_name, record_id, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        None,  # No user ID for now
                        action,
                        table_name,
                        record_id,
                        json.dumps(details) if details else None
                    ))

                    conn.commit()
            except Exception as e:
                # Log but don't raise exception for logging failures
                logging.error(f"Error inserting into audit_log: {e}")

        # Run in a separate thread to avoid blocking the main thread
        # Only for non-SELECT operations to reduce thread overhead
        if action != 'SELECT':
            threading.Thread(target=log_to_audit_table).start()
        else:
            # For SELECT operations, log directly but only in debug mode
            if logging.getLogger().isEnabledFor(logging.DEBUG):
                log_to_audit_table()

    def get_daily_stats(self, date_str=None):
        """
        Get daily statistics for a specific date.

        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today

        Returns:
            dict: Daily statistics
        """
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')

        # Sanitize input if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            date_str = self.db_security.sanitize_input(date_str)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute(
                'SELECT date, games_played, earnings, winners, total_players FROM daily_stats WHERE date = ?',
                (date_str,)
            )

            result = cursor.fetchone()
            conn.close()

            # Log the operation
            self.log_operation('SELECT', 'daily_stats', None, {'date': date_str})

            if result:
                return {
                    'date': result[0],
                    'games_played': result[1],
                    'earnings': result[2],
                    'winners': result[3],
                    'total_players': result[4]
                }
            else:
                # Return default values if no record exists
                return {
                    'date': date_str,
                    'games_played': 0,
                    'earnings': 0,
                    'winners': 0,
                    'total_players': 0
                }
        except Exception as e:
            logging.error(f"Error getting daily stats: {e}")
            # Return default values on error
            return {
                'date': date_str,
                'games_played': 0,
                'earnings': 0,
                'winners': 0,
                'total_players': 0
            }

    def update_daily_stats(self, date_str=None, games_played=0, earnings=0, winners=0, total_players=0):
        """
        Update daily statistics for a specific date.

        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            games_played: Number of games played to add
            earnings: Amount of earnings to add
            winners: Number of winners to add
            total_players: Number of total players to add

        Returns:
            bool: True if successful, False otherwise
        """
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')

        # Sanitize input if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            date_str = self.db_security.sanitize_input(date_str)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Check if record exists
            cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date_str,))
            result = cursor.fetchone()

            if result:
                # Update existing record
                cursor.execute('''
                UPDATE daily_stats
                SET games_played = games_played + ?,
                    earnings = earnings + ?,
                    winners = winners + ?,
                    total_players = total_players + ?
                WHERE date = ?
                ''', (games_played, earnings, winners, total_players, date_str))

                # Log the operation
                self.log_operation('UPDATE', 'daily_stats', None, {
                    'date': date_str,
                    'games_played': games_played,
                    'earnings': earnings,
                    'winners': winners,
                    'total_players': total_players
                })
            else:
                # Insert new record
                cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, ?, ?, ?, ?)
                ''', (date_str, games_played, earnings, winners, total_players))

                # Log the operation
                self.log_operation('INSERT', 'daily_stats', None, {
                    'date': date_str,
                    'games_played': games_played,
                    'earnings': earnings,
                    'winners': winners,
                    'total_players': total_players
                })

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logging.error(f"Error updating daily stats: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return False

    def get_weekly_stats(self, end_date=None):
        """
        Get weekly statistics for the 7 days ending on the specified date.
        More accurate version that cross-checks with game_history table.

        Args:
            end_date: End date string in format 'YYYY-MM-DD', defaults to today

        Returns:
            list: List of daily statistics for the week
        """
        try:
            # Check if stats preloader is available and has cached data
            try:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()
                cached_stats = preloader.get_cached_data('weekly_stats')
                if cached_stats:
                    logging.debug("Using cached weekly stats")
                    return cached_stats
            except ImportError:
                pass  # Continue with database query if preloader not available

            if end_date is None:
                end_date = datetime.now()
            elif isinstance(end_date, str):
                # Sanitize input if security is available
                if DB_SECURITY_AVAILABLE and self.db_security:
                    end_date = self.db_security.sanitize_input(end_date)
                end_date = datetime.strptime(end_date, '%Y-%m-%d')

            # Calculate start date (6 days before end date to get 7 days total)
            start_date = end_date - timedelta(days=6)
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            # Create connection
            with self.get_connection_context() as conn:
                cursor = conn.cursor()

                # First, get data from daily_stats table
                cursor.execute('''
                SELECT date, games_played, earnings, winners, total_players
                FROM daily_stats
                WHERE date BETWEEN ? AND ?
                ORDER BY date
                ''', (start_date_str, end_date_str))

                results = cursor.fetchall()

                # Convert to dictionary format
                stats_by_date = {}
                for row in results:
                    stats_by_date[row[0]] = {
                        'date': row[0],
                        'games_played': row[1],
                        'earnings': row[2],
                        'winners': row[3],
                        'total_players': row[4]
                    }

                # Cross-check with game_history table to ensure data accuracy
                # Get daily games and earnings from game_history
                cursor.execute('''
                SELECT 
                    date(date_time) as game_date,
                    COUNT(*) as games_count,
                    SUM(fee) as total_fees,
                    SUM(CASE WHEN status = 'Won' OR status = 'completed' THEN 1 ELSE 0 END) as winners_count
                FROM game_history
                WHERE date(date_time) BETWEEN ? AND ?
                GROUP BY date(date_time)
                ''', (start_date_str, end_date_str))

                game_history_results = cursor.fetchall()
                
                # Update stats with game history data if needed
                for row in game_history_results:
                    game_date = row[0]
                    games_count = row[1]
                    total_fees = row[2] if row[2] is not None else 0
                    winners_count = row[3]
                    
                    if game_date in stats_by_date:
                        # Update existing entry if game history shows more activity
                        if games_count > stats_by_date[game_date]['games_played']:
                            stats_by_date[game_date]['games_played'] = games_count
                            
                            # Update the database to ensure consistency for future queries
                            try:
                                cursor.execute('''
                                UPDATE daily_stats 
                                SET games_played = ? 
                                WHERE date = ?
                                ''', (games_count, game_date))
                                conn.commit()
                            except Exception as e:
                                print(f"Error updating games_played in daily_stats: {e}")
                        
                        if total_fees > stats_by_date[game_date]['earnings']:
                            stats_by_date[game_date]['earnings'] = total_fees
                            
                            # Update the database
                            try:
                                cursor.execute('''
                                UPDATE daily_stats 
                                SET earnings = ? 
                                WHERE date = ?
                                ''', (total_fees, game_date))
                                conn.commit()
                            except Exception as e:
                                print(f"Error updating earnings in daily_stats: {e}")
                        
                        if winners_count > stats_by_date[game_date]['winners']:
                            stats_by_date[game_date]['winners'] = winners_count
                            
                            # Update the database
                            try:
                                cursor.execute('''
                                UPDATE daily_stats 
                                SET winners = ? 
                                WHERE date = ?
                                ''', (winners_count, game_date))
                                conn.commit()
                            except Exception as e:
                                print(f"Error updating winners in daily_stats: {e}")
                    else:
                        # Create new entry from game history
                        stats_by_date[game_date] = {
                            'date': game_date,
                            'games_played': games_count,
                            'earnings': total_fees,
                            'winners': winners_count,
                            'total_players': 0  # We don't have this info from game_history
                        }
                        
                        # Insert into the database
                        try:
                            cursor.execute('''
                            INSERT OR IGNORE INTO daily_stats
                            (date, games_played, earnings, winners, total_players)
                            VALUES (?, ?, ?, ?, 0)
                            ''', (game_date, games_count, total_fees, winners_count))
                            conn.commit()
                        except Exception as e:
                            print(f"Error inserting new daily_stats: {e}")

            # Generate the full week with default values for missing dates
            weekly_stats = []
            current_date = start_date

            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                if date_str in stats_by_date:
                    weekly_stats.append(stats_by_date[date_str])
                else:
                    # Default values for dates with no data
                    weekly_stats.append({
                        'date': date_str,
                        'games_played': 0,
                        'earnings': 0,
                        'winners': 0,
                        'total_players': 0
                    })
                current_date += timedelta(days=1)

            # Log the operation
            self.log_operation('SELECT', 'daily_stats', None, {
                'action': 'get_weekly_stats',
                'start_date': start_date_str,
                'end_date': end_date_str
            })

            return weekly_stats
        except Exception as e:
            logging.error(f"Error getting weekly stats: {e}")
            import traceback
            traceback.print_exc()
            # Return empty list on error
            return []

    def add_game_to_history(self, username, house, stake, players, total_calls,
                           commission_percent, fee, total_prize, details, status):
        """
        Add a game to the history.

        Args:
            username: Username of the winner
            house: House name
            stake: Stake amount
            players: Number of players
            total_calls: Total number of calls
            commission_percent: Commission percentage
            fee: Fee amount
            total_prize: Total prize amount
            details: Additional details (JSON string)
            status: Game status

        Returns:
            int: ID of the new record, or -1 if failed
        """
        date_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Sanitize inputs if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            username = self.db_security.sanitize_input(username)
            house = self.db_security.sanitize_input(house)
            status = self.db_security.sanitize_input(status)
            # Don't sanitize numeric values or JSON strings

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO game_history
            (date_time, username, house, stake, players, total_calls,
             commission_percent, fee, total_prize, details, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (date_time, username, house, stake, players, total_calls,
                 commission_percent, fee, total_prize, details, status))

            # Get the ID of the new record
            game_id = cursor.lastrowid

            conn.commit()
            conn.close()

            # Log the operation
            self.log_operation('INSERT', 'game_history', game_id, {
                'username': username,
                'house': house,
                'stake': stake,
                'players': players,
                'total_calls': total_calls,
                'commission_percent': commission_percent,
                'fee': fee,
                'total_prize': total_prize,
                'status': status
            })

            # Update daily stats
            today = datetime.now().strftime('%Y-%m-%d')
            self.update_daily_stats(
                date_str=today,
                games_played=1,
                earnings=fee,
                winners=1 if status.lower() == 'won' else 0,
                total_players=players
            )

            return game_id
        except Exception as e:
            logging.error(f"Error adding game to history: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return -1

    def get_game_history(self, limit=50, offset=0):
        """
        Get game history.

        Args:
            limit: Maximum number of records to return
            offset: Offset for pagination

        Returns:
            list: List of game history records
        """
        # Check if stats preloader is available and has cached data
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()

            # Calculate page number for cache lookup
            page_size = 10  # Standard page size
            page = offset // page_size

            # Only use cache if offset is aligned with page boundaries
            if offset % page_size == 0 and limit <= page_size:
                cached_history, _ = preloader.get_game_history_page(page, page_size)
                if cached_history:
                    logging.debug(f"Using cached game history for page {page}")
                    return cached_history
        except ImportError:
            pass  # Continue with database query if preloader not available

        # Validate and sanitize inputs
        try:
            limit = int(limit)
            offset = int(offset)

            # Apply reasonable limits
            limit = min(max(1, limit), 1000)  # Between 1 and 1000
            offset = max(0, offset)  # Non-negative

            # Use connection context manager for proper cleanup
            with self.get_connection_context() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                SELECT id, date_time, username, house, stake, players, total_calls,
                       commission_percent, fee, total_prize, details, status
                FROM game_history
                ORDER BY date_time DESC
                LIMIT ? OFFSET ?
                ''', (limit, offset))

                results = cursor.fetchall()

                # Log the operation
                self.log_operation('SELECT', 'game_history', None, {
                    'action': 'get_game_history',
                    'limit': limit,
                    'offset': offset
                })

                history = []
                for row in results:
                    # Decrypt sensitive data if needed
                    username = row[2]
                    if DB_SECURITY_AVAILABLE and self.db_security and isinstance(username, str) and username.startswith('ENCRYPT:'):
                        username = self.db_security.decrypt(username)

                    history.append({
                        'id': row[0],
                        'date_time': row[1],
                        'username': username,
                        'house': row[3],
                        'stake': row[4],
                        'players': row[5],
                        'total_calls': row[6],
                        'commission_percent': row[7],
                        'fee': row[8],
                        'total_prize': row[9],
                        'details': row[10],
                        'status': row[11]
                    })

                # Cache the result if preloader is available
                try:
                    if 'preloader' in locals() and preloader and offset % page_size == 0 and limit <= page_size:
                        cache_key = f'game_history_page_{page}'
                        preloader.cache.set(cache_key, history)
                except Exception as e:
                    logging.error(f"Error caching game history: {e}")

                return history
        except Exception as e:
            logging.error(f"Error getting game history: {e}")
            return []

    def add_wallet_transaction(self, amount, transaction_type, description):
        """
        Add a wallet transaction.

        Args:
            amount: Transaction amount
            transaction_type: Type of transaction (deposit, withdrawal, fee, etc.)
            description: Transaction description

        Returns:
            int: ID of the new record, or -1 if failed
        """
        date_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Sanitize inputs if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            transaction_type = self.db_security.sanitize_input(transaction_type)
            description = self.db_security.sanitize_input(description)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Get current balance
            cursor.execute('''
            SELECT balance_after FROM wallet_transactions
            ORDER BY id DESC LIMIT 1
            ''')

            result = cursor.fetchone()
            current_balance = result[0] if result else 0

            # Calculate new balance
            new_balance = current_balance + amount

            # Insert transaction
            cursor.execute('''
            INSERT INTO wallet_transactions
            (date_time, amount, transaction_type, description, balance_after)
            VALUES (?, ?, ?, ?, ?)
            ''', (date_time, amount, transaction_type, description, new_balance))

            # Get the ID of the new record
            transaction_id = cursor.lastrowid

            conn.commit()
            conn.close()

            # Log the operation
            self.log_operation('INSERT', 'wallet_transactions', transaction_id, {
                'amount': amount,
                'transaction_type': transaction_type,
                'description': description,
                'balance_after': new_balance
            })

            return transaction_id
        except Exception as e:
            logging.error(f"Error adding wallet transaction: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return -1

    def get_wallet_balance(self):
        """
        Get current wallet balance.

        Returns:
            float: Wallet balance
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # First try to get the balance from wallet_transactions
            cursor.execute('''
            SELECT balance_after FROM wallet_transactions
            ORDER BY id DESC LIMIT 1
            ''')

            result = cursor.fetchone()
            
            if result and result[0] is not None:
                balance = result[0]
            else:
                # If no wallet transactions, calculate from game history
                print("No wallet transactions found, calculating balance from game history")
                cursor.execute('SELECT SUM(fee) FROM game_history')
                result = cursor.fetchone()
                balance = result[0] if result and result[0] is not None else 0
                
                # Create initial wallet transaction if needed
                if balance > 0:
                    try:
                        cursor.execute('''
                        INSERT INTO wallet_transactions
                        (date_time, amount, transaction_type, description, balance_after)
                        VALUES (?, ?, ?, ?, ?)
                        ''', (
                            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            balance,
                            'initial',
                            'Initial balance calculated from game history',
                            balance
                        ))
                        conn.commit()
                    except Exception as e:
                        print(f"Error creating initial wallet transaction: {e}")
            
            conn.close()
            return balance
        except Exception as e:
            print(f"Error getting wallet balance: {e}")
            return 0

    def get_wallet_transactions(self, limit=50, offset=0):
        """
        Get wallet transactions.

        Args:
            limit: Maximum number of records to return
            offset: Offset for pagination

        Returns:
            list: List of wallet transactions
        """
        try:
            # Validate and sanitize inputs
            limit = int(limit)
            offset = int(offset)

            # Apply reasonable limits
            limit = min(max(1, limit), 1000)  # Between 1 and 1000
            offset = max(0, offset)  # Non-negative

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
            SELECT id, date_time, amount, transaction_type, description, balance_after
            FROM wallet_transactions
            ORDER BY date_time DESC
            LIMIT ? OFFSET ?
            ''', (limit, offset))

            results = cursor.fetchall()
            conn.close()

            # Log the operation
            self.log_operation('SELECT', 'wallet_transactions', None, {
                'action': 'get_wallet_transactions',
                'limit': limit,
                'offset': offset
            })

            transactions = []
            for row in results:
                transactions.append({
                    'id': row[0],
                    'date_time': row[1],
                    'amount': row[2],
                    'transaction_type': row[3],
                    'description': row[4],
                    'balance_after': row[5]
                })

            return transactions
        except Exception as e:
            logging.error(f"Error getting wallet transactions: {e}")
            return []

    def get_wallet_summary(self):
        """
        Get wallet summary statistics.

        Returns:
            dict: Wallet summary statistics
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Get total deposits
            cursor.execute('''
            SELECT SUM(amount) FROM wallet_transactions
            WHERE amount > 0
            ''')
            total_deposits = cursor.fetchone()[0] or 0

            # Get total withdrawals
            cursor.execute('''
            SELECT SUM(amount) FROM wallet_transactions
            WHERE amount < 0
            ''')
            total_withdrawals = cursor.fetchone()[0] or 0

            # Get transaction count
            cursor.execute('SELECT COUNT(*) FROM wallet_transactions')
            transaction_count = cursor.fetchone()[0] or 0

            # Get current balance
            cursor.execute('''
            SELECT balance_after FROM wallet_transactions
            ORDER BY id DESC LIMIT 1
            ''')
            result = cursor.fetchone()
            current_balance = result[0] if result else 0

            conn.close()

            # Log the operation
            self.log_operation('SELECT', 'wallet_transactions', None, {
                'action': 'get_wallet_summary'
            })

            return {
                'total_deposits': total_deposits,
                'total_withdrawals': total_withdrawals,
                'transaction_count': transaction_count,
                'current_balance': current_balance
            }
        except Exception as e:
            logging.error(f"Error getting wallet summary: {e}")
            return {
                'total_deposits': 0,
                'total_withdrawals': 0,
                'transaction_count': 0,
                'current_balance': 0
            }

    def get_total_earnings(self):
        """
        Get total earnings (fees) from all games.

        Returns:
            float: Total earnings
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # First check if daily_stats has accurate data
            cursor.execute('SELECT SUM(earnings) FROM daily_stats')
            result = cursor.fetchone()
            daily_stats_total = result[0] if result and result[0] is not None else 0

            # Then check game_history for more accurate data
            cursor.execute('SELECT SUM(fee) FROM game_history')
            result = cursor.fetchone()
            game_history_total = result[0] if result and result[0] is not None else 0
            
            # Use the larger value as it's likely more accurate
            total_earnings = max(daily_stats_total, game_history_total)
            
            # If game_history has more accurate data, update daily_stats
            if game_history_total > daily_stats_total:
                try:
                    print(f"Updating daily_stats - game_history has more accurate earnings data: {game_history_total} vs {daily_stats_total}")
                    
                    # Get earnings by date from game_history
                    cursor.execute('''
                    SELECT date(date_time) as game_date, SUM(fee) as total_fees
                    FROM game_history
                    GROUP BY date(date_time)
                    ''')
                    
                    for row in cursor.fetchall():
                        game_date = row[0]
                        total_fees = row[1] if row[1] is not None else 0
                        
                        # Update or insert into daily_stats
                        cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (game_date,))
                        if cursor.fetchone():
                            # Update existing record
                            cursor.execute('''
                            UPDATE daily_stats SET earnings = ?
                            WHERE date = ?
                            ''', (total_fees, game_date))
                        else:
                            # Insert new record
                            cursor.execute('''
                            INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                            VALUES (?, 0, ?, 0, 0)
                            ''', (game_date, total_fees))
                    
                    conn.commit()
                except Exception as e:
                    print(f"Error updating daily_stats with earnings from game_history: {e}")
            
            conn.close()
            return total_earnings
        except Exception as e:
            print(f"Error getting total earnings: {e}")
            return 0

    def get_daily_earnings(self, date_str=None):
        """
        Get daily earnings for a specific date.

        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today

        Returns:
            float: Daily earnings
        """
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')
            
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # First check daily_stats table
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (date_str,))
            result = cursor.fetchone()
            
            if result and result[0] is not None and result[0] > 0:
                # If we have a valid value in daily_stats, use it
                daily_earnings = result[0]
            else:
                # Otherwise calculate from game_history table
                # This is more accurate but slower, used as a fallback
                cursor.execute('''
                SELECT SUM(fee) FROM game_history 
                WHERE date(date_time) = ?
                ''', (date_str,))
                
                result = cursor.fetchone()
                daily_earnings = result[0] if result and result[0] is not None else 0
                
                # Update the daily_stats table with the correct value
                if daily_earnings > 0:
                    try:
                        # Check if record exists
                        cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date_str,))
                        if cursor.fetchone():
                            # Update existing record
                            cursor.execute('''
                            UPDATE daily_stats SET earnings = ?
                            WHERE date = ?
                            ''', (daily_earnings, date_str))
                        else:
                            # Insert new record
                            cursor.execute('''
                            INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                            VALUES (?, 0, ?, 0, 0)
                            ''', (date_str, daily_earnings))
                        conn.commit()
                    except Exception as e:
                        print(f"Error updating daily_stats with correct earnings: {e}")
            
            conn.close()
            return daily_earnings
        except Exception as e:
            print(f"Error getting daily earnings: {e}")
            return 0

    def get_daily_games_played(self, date_str=None):
        """
        Get daily games played for a specific date.

        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today

        Returns:
            int: Daily games played
        """
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')
            
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # First check daily_stats table
            cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (date_str,))
            result = cursor.fetchone()
            
            if result and result[0] is not None and result[0] > 0:
                # If we have a valid value in daily_stats, use it
                daily_games = result[0]
            else:
                # Otherwise calculate from game_history table
                # This is more accurate but slower, used as a fallback
                cursor.execute('''
                SELECT COUNT(*) FROM game_history 
                WHERE date(date_time) = ?
                ''', (date_str,))
                
                result = cursor.fetchone()
                daily_games = result[0] if result and result[0] is not None else 0
                
                # Update the daily_stats table with the correct value
                if daily_games > 0:
                    try:
                        # Check if record exists
                        cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date_str,))
                        if cursor.fetchone():
                            # Update existing record
                            cursor.execute('''
                            UPDATE daily_stats SET games_played = ?
                            WHERE date = ?
                            ''', (daily_games, date_str))
                        else:
                            # Insert new record
                            cursor.execute('''
                            INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                            VALUES (?, ?, 0, 0, 0)
                            ''', (date_str, daily_games))
                        conn.commit()
                    except Exception as e:
                        print(f"Error updating daily_stats with correct games_played: {e}")
            
            conn.close()
            return daily_games
        except Exception as e:
            print(f"Error getting daily games played: {e}")
            return 0

    def migrate_from_json(self):
        """
        Migrate data from the old JSON format to the new SQLite database.

        Returns:
            bool: True if successful, False otherwise
        """
        stats_path = os.path.join('data', 'stats.json')
        if not os.path.exists(stats_path):
            logging.info(f"Stats file {stats_path} not found, nothing to migrate")
            return False

        try:
            with open(stats_path, 'r') as f:
                stats_data = json.load(f)

            # Log the migration start
            logging.info(f"Starting migration from JSON: {stats_path}")

            # Update total earnings
            total_prize_pool = stats_data.get('total_prize_pool', 0)
            games_played = stats_data.get('games_played', 0)

            # Add a single game entry for each game played
            for i in range(games_played):
                self.add_game_to_history(
                    username="Unknown",
                    house="Main House",
                    stake=50,  # Default stake
                    players=stats_data.get('player_count', 0),
                    total_calls=75,  # Default total calls
                    commission_percent=20,  # Default commission
                    fee=total_prize_pool / games_played if games_played > 0 else 0,
                    total_prize=total_prize_pool / games_played if games_played > 0 else 0,
                    details="{}",
                    status="Completed"
                )

            # Add recent activity
            for activity in stats_data.get('recent_activity', []):
                event = activity.get('event', '')
                if 'Game started' in event:
                    # Extract player count
                    try:
                        player_count = int(event.split('with ')[1].split(' players')[0])
                        self.update_daily_stats(
                            total_players=player_count
                        )
                    except Exception as e:
                        logging.error(f"Error processing activity: {e}")

            # Log the migration completion
            logging.info(f"Migration from JSON completed successfully")
            return True
        except Exception as e:
            logging.error(f"Error migrating from JSON: {e}")
            return False

    def get_admin_users(self):
        """
        Get list of admin users.

        Returns:
            list: List of admin users
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
            SELECT id, username, access_level, last_login
            FROM admin_users
            ORDER BY username
            ''')

            results = cursor.fetchall()
            conn.close()

            # Log the operation
            self.log_operation('SELECT', 'admin_users', None, {
                'action': 'get_admin_users'
            })

            users = []
            for row in results:
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'access_level': row[2],
                    'last_login': row[3]
                })

            return users
        except Exception as e:
            logging.error(f"Error getting admin users: {e}")
            return []

    def add_admin_user(self, username, password, access_level=1):
        """
        Add a new admin user.

        Args:
            username: Username
            password: Password
            access_level: Access level (1=read-only, 2=read-write, 3=admin)

        Returns:
            int: ID of the new user, or -1 if failed
        """
        # Sanitize inputs if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            username = self.db_security.sanitize_input(username)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Generate salt
            salt = os.urandom(16).hex()

            # Hash password with salt
            password_hash = hashlib.sha256((password + salt).encode()).hexdigest()

            # Insert user
            cursor.execute('''
            INSERT INTO admin_users (username, password_hash, salt, access_level, last_login)
            VALUES (?, ?, ?, ?, ?)
            ''', (username, password_hash, salt, access_level, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

            # Get the ID of the new user
            user_id = cursor.lastrowid

            conn.commit()
            conn.close()

            # Log the operation
            self.log_operation('INSERT', 'admin_users', user_id, {
                'username': username,
                'access_level': access_level
            })

            return user_id
        except Exception as e:
            logging.error(f"Error adding admin user: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return -1

    def authenticate_admin_user(self, username, password):
        """
        Authenticate an admin user.

        Args:
            username: Username
            password: Password

        Returns:
            dict: User data if authenticated, None otherwise
        """
        # Sanitize inputs if security is available
        if DB_SECURITY_AVAILABLE and self.db_security:
            username = self.db_security.sanitize_input(username)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Get user data
            cursor.execute('''
            SELECT id, password_hash, salt, access_level
            FROM admin_users
            WHERE username = ?
            ''', (username,))

            result = cursor.fetchone()

            if not result:
                conn.close()
                return None

            user_id, password_hash, salt, access_level = result

            # Verify password
            hashed_password = hashlib.sha256((password + salt).encode()).hexdigest()

            if hashed_password != password_hash:
                conn.close()
                return None

            # Update last login time
            cursor.execute('''
            UPDATE admin_users
            SET last_login = ?
            WHERE id = ?
            ''', (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), user_id))

            conn.commit()
            conn.close()

            # Log the operation
            self.log_operation('SELECT', 'admin_users', user_id, {
                'action': 'authenticate_admin_user',
                'username': username
            })

            return {
                'id': user_id,
                'username': username,
                'access_level': access_level
            }
        except Exception as e:
            logging.error(f"Error authenticating admin user: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return None

    def delete_admin_user(self, user_id):
        """
        Delete an admin user.

        Args:
            user_id: User ID to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate user_id
            user_id = int(user_id)

            # Check if this is the last admin user
            admin_users = self.get_admin_users()
            admin_count = sum(1 for user in admin_users if user['access_level'] == 3)

            # Get the user to be deleted
            user_to_delete = None
            for user in admin_users:
                if user['id'] == user_id:
                    user_to_delete = user
                    break

            # Don't allow deleting the last admin user
            if user_to_delete and user_to_delete['access_level'] == 3 and admin_count <= 1:
                logging.warning(f"Cannot delete the last admin user (ID: {user_id})")
                return False

            conn = self.get_connection()
            cursor = conn.cursor()

            # Delete user
            cursor.execute('DELETE FROM admin_users WHERE id = ?', (user_id,))

            # Check if any rows were affected
            if cursor.rowcount == 0:
                conn.close()
                return False

            conn.commit()
            conn.close()

            # Log the operation
            self.log_operation('DELETE', 'admin_users', user_id, {
                'action': 'delete_admin_user'
            })

            return True
        except Exception as e:
            logging.error(f"Error deleting admin user: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return False

    def update_admin_user(self, user_id, username=None, password=None, access_level=None):
        """
        Update an admin user.

        Args:
            user_id: User ID to update
            username: New username (optional)
            password: New password (optional)
            access_level: New access level (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate user_id
            user_id = int(user_id)

            # Sanitize inputs if security is available
            if DB_SECURITY_AVAILABLE and self.db_security and username:
                username = self.db_security.sanitize_input(username)

            # Check if this is the last admin user
            if access_level is not None:
                admin_users = self.get_admin_users()
                admin_count = sum(1 for user in admin_users if user['access_level'] == 3)

                # Get the user to be updated
                user_to_update = None
                for user in admin_users:
                    if user['id'] == user_id:
                        user_to_update = user
                        break

                # Don't allow downgrading the last admin user
                if (user_to_update and user_to_update['access_level'] == 3 and
                    access_level != 3 and admin_count <= 1):
                    logging.warning(f"Cannot downgrade the last admin user (ID: {user_id})")
                    return False

            conn = self.get_connection()
            cursor = conn.cursor()

            # Build update query
            query_parts = []
            params = []

            if username is not None:
                query_parts.append("username = ?")
                params.append(username)

            if password is not None:
                # Generate salt
                salt = os.urandom(16).hex()

                # Hash password with salt
                password_hash = hashlib.sha256((password + salt).encode()).hexdigest()

                query_parts.append("password_hash = ?")
                params.append(password_hash)

                query_parts.append("salt = ?")
                params.append(salt)

            if access_level is not None:
                query_parts.append("access_level = ?")
                params.append(access_level)

            # If nothing to update, return success
            if not query_parts:
                conn.close()
                return True

            # Add user_id to params
            params.append(user_id)

            # Execute update query
            cursor.execute(f'''
            UPDATE admin_users
            SET {", ".join(query_parts)}
            WHERE id = ?
            ''', params)

            # Check if any rows were affected
            if cursor.rowcount == 0:
                conn.close()
                return False

            conn.commit()
            conn.close()

            # Log the operation
            update_details = {
                'action': 'update_admin_user'
            }
            if username is not None:
                update_details['username'] = username
            if access_level is not None:
                update_details['access_level'] = access_level

            self.log_operation('UPDATE', 'admin_users', user_id, update_details)

            return True
        except Exception as e:
            logging.error(f"Error updating admin user: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return False

    def get_admin_user(self, user_id):
        """
        Get an admin user by ID.

        Args:
            user_id: User ID

        Returns:
            dict: User data if found, None otherwise
        """
        try:
            # Validate user_id
            user_id = int(user_id)

            conn = self.get_connection()
            cursor = conn.cursor()

            # Get user data
            cursor.execute('''
            SELECT id, username, access_level, last_login
            FROM admin_users
            WHERE id = ?
            ''', (user_id,))

            result = cursor.fetchone()
            conn.close()

            if not result:
                return None

            # Log the operation
            self.log_operation('SELECT', 'admin_users', user_id, {
                'action': 'get_admin_user'
            })

            return {
                'id': result[0],
                'username': result[1],
                'access_level': result[2],
                'last_login': result[3]
            }
        except Exception as e:
            logging.error(f"Error getting admin user: {e}")
            if 'conn' in locals() and conn:
                conn.close()
            return None

# Create a singleton instance
_stats_db_manager = None

def get_stats_db_manager():
    """
    Get the singleton instance of StatsDBManager.

    Returns:
        StatsDBManager: The singleton instance
    """
    global _stats_db_manager
    if _stats_db_manager is None:
        _stats_db_manager = StatsDBManager()
    return _stats_db_manager
