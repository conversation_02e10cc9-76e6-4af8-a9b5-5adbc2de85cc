import re

def fix_apply_bet_input():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Define the pattern to match the part just before the show_message in _apply_bet_input
    pattern = r'(self\.prize_pool_manual_override = False\n                self\.calculate_prize_pool\(\))'
    
    # Define the replacement with UI state saving
    replacement = r'\1\n                \n                # Save UI state if option is enabled\n                if hasattr(self, \'remember_cartella_checkbox\') and self.remember_cartella_checkbox:\n                    try:\n                        from player_storage import save_ui_state\n                        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, False)\n                        print(f"Updated UI state after changing bet: {self.bet_amount}")\n                    except Exception as e:\n                        print(f"Error saving UI state after changing bet: {e}")'
    
    # Replace the selected part in the content
    updated_content = re.sub(pattern, replacement, content)
    
    # Check if any changes were made
    if updated_content == content:
        print("No changes made. Pattern not found or already updated.")
        return False
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print(f"Updated _apply_bet_input method in Board_selection_fixed.py")
    return True

if __name__ == "__main__":
    fix_apply_bet_input() 