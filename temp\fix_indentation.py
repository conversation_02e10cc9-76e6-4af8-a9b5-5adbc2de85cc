import re

def fix_indentation():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Find the problematic section
    pattern = r"(\s+# Reset prize pool\n\s+)self\.calculate_prize_pool\(\)"
    replacement = r"\1self.calculate_prize_pool()"
    
    # Make the replacement (fix indentation)
    new_content = re.sub(pattern, replacement, content)
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Indentation fixed successfully!")

if __name__ == "__main__":
    fix_indentation() 