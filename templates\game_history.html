{% extends "layout.html" %}
{% block content %}
    <h2>Game History</h2>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date/Time</th>
                <th>Username</th>
                <th>House</th>
                <th>Stake</th>
                <th>Players</th>
                <th>Prize</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for game in games %}
            <tr>
                <td>{{ game.id }}</td>
                <td>{{ game.date_time }}</td>
                <td>{{ game.username }}</td>
                <td>{{ game.house }}</td>
                <td>{{ game.stake }} ETB</td>
                <td>{{ game.players }}</td>
                <td>{{ game.total_prize }} ETB</td>
                <td>{{ game.status }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="pagination">
        {% if page > 1 %}
            <a href="/game-history?page={{ page - 1 }}">&laquo; Previous</a>
        {% endif %}

        <span>Page {{ page }} of {{ total_pages }}</span>

        {% if page < total_pages %}
            <a href="/game-history?page={{ page + 1 }}">Next &raquo;</a>
        {% endif %}
    </div>
{% endblock %}
