"""
Voucher Validator Module

This module validates vouchers, including those bound to specific machine UUIDs.
It works with both regular and external vouchers.
"""

import os
import sys
import time
import struct
import hashlib
import json

# Try to import compact_voucher_generator
try:
    from compact_voucher_generator import CompactVoucherGenerator, CROCKFORD_ALPHABET
except ImportError:
    # Define C<PERSON>'s Base32 alphabet if import fails
    CROCKFORD_ALPHABET = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"
    print("Warning: compact_voucher_generator.py not found. Limited validation functionality available.")

class VoucherValidator:
    """Validator for both regular and external vouchers."""
    
    def __init__(self, secret_key=None):
        """
        Initialize the voucher validator.
        
        Args:
            secret_key: Secret key for voucher validation (should match generator)
        """
        # Load secret key if available
        self.secret_key = self._load_secret_key(secret_key)
        
        # Paths to voucher databases
        self.voucher_db_path = os.path.join('vouchers', 'voucher_db.json')
        self.external_voucher_db_path = os.path.join('vouchers', 'external_voucher_db.json')
    
    def _load_secret_key(self, secret_key):
        """
        Load the secret key for voucher validation.
        
        Args:
            secret_key: Secret key to use if provided
            
        Returns:
            bytes: Secret key
        """
        if secret_key:
            return secret_key
            
        # Try to load from the standard location
        secret_path = os.path.join('keys', 'voucher_secret.bin')
        
        if os.path.exists(secret_path):
            try:
                with open(secret_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                print(f"Error loading secret key: {e}")
        
        # Return None if no secret key is available
        return None
    
    def _hmac_sha256(self, message):
        """
        Calculate HMAC-SHA256 of a message using the secret key.
        
        Args:
            message: Message to hash
            
        Returns:
            bytes: HMAC-SHA256 digest
        """
        if not self.secret_key:
            raise ValueError("No secret key available for HMAC calculation")
            
        import hmac
        return hmac.new(self.secret_key, message, hashlib.sha256).digest()
    
    def _decode_base32(self, encoded):
        """
        Decode Crockford's Base32 string to binary data.
        
        Args:
            encoded: Base32 encoded string
            
        Returns:
            bytes: Decoded binary data
        """
        value = 0
        result = bytearray()
        bits = 0
        
        for char in encoded:
            try:
                value = (value << 5) | CROCKFORD_ALPHABET.index(char)
                bits += 5
                
                if bits >= 8:
                    bits -= 8
                    result.append((value >> bits) & 0xFF)
            except ValueError:
                # Skip invalid characters
                continue
        
        return bytes(result)
    
    def _calculate_checksum(self, voucher_str):
        """
        Calculate a simple checksum character.
        
        Args:
            voucher_str: Voucher string
            
        Returns:
            str: Checksum character
        """
        checksum_value = 0
        for i, char in enumerate(voucher_str):
            checksum_value += (i + 1) * CROCKFORD_ALPHABET.index(char)
        
        return CROCKFORD_ALPHABET[checksum_value % len(CROCKFORD_ALPHABET)]
    
    def _get_voucher_data_from_db(self, voucher_code):
        """
        Get voucher data from the database.
        
        Args:
            voucher_code: Voucher code to look up
            
        Returns:
            dict: Voucher data or None if not found
        """
        # Check standard voucher database
        if os.path.exists(self.voucher_db_path):
            try:
                with open(self.voucher_db_path, 'r') as f:
                    vouchers = json.load(f)
                    if voucher_code in vouchers:
                        return vouchers[voucher_code]
            except Exception as e:
                print(f"Error loading voucher database: {e}")
        
        # Check external voucher database
        if os.path.exists(self.external_voucher_db_path):
            try:
                with open(self.external_voucher_db_path, 'r') as f:
                    vouchers = json.load(f)
                    if voucher_code in vouchers:
                        return vouchers[voucher_code]
            except Exception as e:
                print(f"Error loading external voucher database: {e}")
        
        return None
    
    def validate_voucher(self, voucher_code, machine_uuid=None):
        """
        Validate a voucher code, optionally checking UUID binding.
        
        Args:
            voucher_code: Voucher code to validate
            machine_uuid: UUID of the machine to check binding (optional)
            
        Returns:
            dict: Validation result with details
        """
        # Clean up the voucher code
        voucher_code = voucher_code.strip().upper().replace('-', '')
        
        # Check length
        if len(voucher_code) < 10 or len(voucher_code) > 15:
            return {"valid": False, "message": "Invalid voucher length"}
        
        # Check character set
        for char in voucher_code:
            if char not in CROCKFORD_ALPHABET:
                return {"valid": False, "message": "Invalid character in voucher"}
        
        # Extract and verify checksum character
        checksum_char = voucher_code[-1]
        voucher_body = voucher_code[:-1]
        
        expected_checksum = self._calculate_checksum(voucher_body)
        if checksum_char != expected_checksum:
            return {"valid": False, "message": "Invalid checksum character"}
        
        # First, try to look up the voucher in our database
        voucher_data = self._get_voucher_data_from_db(voucher_code)
        if voucher_data:
            # Voucher found in database - check if it's an external voucher
            if "machine_uuid" in voucher_data:
                # This is an external voucher - check UUID binding
                if machine_uuid:
                    if voucher_data["machine_uuid"] != machine_uuid:
                        return {
                            "valid": False, 
                            "message": "Voucher bound to different machine UUID",
                            "expected_uuid": voucher_data["machine_uuid"]
                        }
                
                # Check expiry if present
                if "expiry" in voucher_data and voucher_data["expiry"] != 0:
                    current_time = int(time.time())
                    if current_time > voucher_data["expiry"]:
                        return {"valid": False, "message": "Voucher has expired"}
                
                # Valid external voucher
                return {
                    "valid": True,
                    "amount": voucher_data["amount"],
                    "share": voucher_data["share"],
                    "expiry": voucher_data["expiry"],
                    "machine_uuid": voucher_data["machine_uuid"]
                }
            else:
                # Regular voucher
                # Check expiry if present
                if "expiry" in voucher_data and voucher_data["expiry"] != 0:
                    current_time = int(time.time())
                    if current_time > voucher_data["expiry"]:
                        return {"valid": False, "message": "Voucher has expired"}
                
                # Valid regular voucher
                return {
                    "valid": True,
                    "amount": voucher_data["amount"],
                    "share": voucher_data["share"],
                    "expiry": voucher_data["expiry"]
                }
        
        # If we don't have the voucher in our database, try cryptographic validation
        try:
            # Decode the voucher body
            decoded = self._decode_base32(voucher_body)
            
            # Check if this is an external voucher (first 4 bytes are UUID hash)
            if len(decoded) >= 12:  # UUID hash (4) + voucher ID (4) + checksum (4)
                uuid_hash = decoded[:4]
                voucher_id_bytes = decoded[4:8]
                checksum_bytes = decoded[8:12]
                
                if machine_uuid:
                    # Normalize UUID and calculate hash
                    normalized_uuid = machine_uuid.replace('-', '').lower()
                    expected_uuid_hash = hashlib.sha256(normalized_uuid.encode()).digest()[:4]
                    
                    # Compare UUID hashes
                    if uuid_hash != expected_uuid_hash:
                        return {
                            "valid": False,
                            "message": "Voucher not valid for this machine"
                        }
                
                # TODO: Complete cryptographic validation using secret key
                # This would require additional components from the voucher
                # generator implementation
                
                # For now, return limited validation
                return {
                    "valid": True,
                    "message": "External voucher cryptographically valid",
                    "warning": "Limited validation - full details not available"
                }
            
            # Standard voucher validation
            elif len(decoded) >= 8:  # voucher ID (4) + checksum (4)
                # TODO: Implement standard voucher cryptographic validation
                pass
                
        except Exception as e:
            return {"valid": False, "message": f"Validation error: {str(e)}"}
        
        # Fallback - voucher format recognized but validation failed
        return {"valid": False, "message": "Invalid voucher"}
    
    def is_voucher_valid_for_machine(self, voucher_code, machine_uuid):
        """
        Check if a voucher is valid for a specific machine.
        
        Args:
            voucher_code: Voucher code to validate
            machine_uuid: UUID of the machine to check binding
            
        Returns:
            bool: True if the voucher is valid for this machine
        """
        result = self.validate_voucher(voucher_code, machine_uuid)
        return result.get("valid", False)


def main():
    """Command-line interface for voucher validation."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate vouchers")
    parser.add_argument("voucher", help="Voucher code to validate")
    parser.add_argument("--uuid", help="Machine UUID to check binding")
    
    args = parser.parse_args()
    
    validator = VoucherValidator()
    result = validator.validate_voucher(args.voucher, args.uuid)
    
    if result["valid"]:
        print("✅ Valid voucher")
        for key, value in result.items():
            if key != "valid":
                print(f"  {key}: {value}")
    else:
        print("❌ Invalid voucher")
        print(f"  Reason: {result.get('message', 'Unknown error')}")
    
    return 0 if result["valid"] else 1


if __name__ == "__main__":
    sys.exit(main()) 