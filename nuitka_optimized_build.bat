@echo off
:: ================================================================
:: Bingo Game - Maximum Performance Nuitka Build Script
:: ================================================================
:: This script compiles the Python bingo game using Nuitka with
:: maximum performance optimizations for production deployment.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PROJECT_NAME=WOW Bingo Game"
set "PROJECT_VERSION=1.0.0"
set "MAIN_SCRIPT=main.py"
set "ICON_PATH=assets\app_logo.ico"
set "BUILD_DIR=build_optimized"
set "OUTPUT_NAME=WOWBingoGame"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Maximum Performance Compilation%RESET%
echo %CYAN%================================================================%RESET%
echo.

:: Check if main.py exists
if not exist "%MAIN_SCRIPT%" (
    echo %RED%Error: %MAIN_SCRIPT% not found!%RESET%
    pause
    exit /b 1
)

:: Check if icon exists
if not exist "%ICON_PATH%" (
    echo %YELLOW%Warning: Icon file %ICON_PATH% not found. Using default icon.%RESET%
    set "ICON_PATH="
)

:: Clean previous build
if exist "%BUILD_DIR%" (
    echo %YELLOW%Cleaning previous build directory...%RESET%
    rd /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

:: Verify Python and Nuitka
echo %CYAN%Verifying Python and Nuitka installation...%RESET%
python --version
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found!%RESET%
    pause
    exit /b 1
)

python -m nuitka --version
if %errorlevel% neq 0 (
    echo %RED%Error: Nuitka not found!%RESET%
    pause
    exit /b 1
)

:: Check for required assets
echo %CYAN%Verifying required assets...%RESET%
if not exist "assets" (
    echo %RED%Error: assets directory not found!%RESET%
    pause
    exit /b 1
)

if not exist "data" (
    echo %YELLOW%Warning: data directory not found. Creating empty data directory.%RESET%
    mkdir "data"
)

:: Clean app data for fresh build
echo %CYAN%Cleaning app data for fresh build...%RESET%
if exist "clean_app.py" (
    python clean_app.py --silent
    if %errorlevel% neq 0 (
        echo %YELLOW%Warning: Could not clean app data. Build will continue.%RESET%
    ) else (
        echo %GREEN%App data cleaned successfully.%RESET%
    )
)

:: Get CPU count for parallel compilation
set /a CPU_COUNT=%NUMBER_OF_PROCESSORS%
if %CPU_COUNT% lss 1 set CPU_COUNT=1
echo %CYAN%Using %CPU_COUNT% CPU cores for compilation...%RESET%

echo.
echo %GREEN%Starting maximum performance Nuitka compilation...%RESET%
echo %CYAN%This may take several minutes depending on your system.%RESET%
echo.

:: Build the optimized executable with maximum performance flags
python -m nuitka ^
    --standalone ^
    --onefile ^
    --output-filename=%OUTPUT_NAME%.exe ^
    --output-dir=%BUILD_DIR% ^
    --windows-icon-from-ico=%ICON_PATH% ^
    --windows-company-name="%PROJECT_NAME%" ^
    --windows-product-name="%PROJECT_NAME%" ^
    --windows-file-version=%PROJECT_VERSION% ^
    --windows-product-version=%PROJECT_VERSION% ^
    --windows-file-description="WOW Bingo Game - Professional Bingo Gaming Application" ^
    --disable-console ^
    --assume-yes-for-downloads ^
    --include-data-dir=assets=assets ^
    --include-data-dir=data=data ^
    --include-package=pygame ^
    --include-package=json ^
    --include-package=datetime ^
    --include-package=colorsys ^
    --include-package=math ^
    --include-package=random ^
    --include-package=time ^
    --include-package=pyperclip ^
    --include-package=sqlite3 ^
    --include-package=os ^
    --include-package=sys ^
    --include-package=threading ^
    --include-package=collections ^
    --include-package=itertools ^
    --include-package=functools ^
    --include-package=operator ^
    --include-package=copy ^
    --include-package=pickle ^
    --include-package=hashlib ^
    --include-package=uuid ^
    --include-package=platform ^
    --include-package=subprocess ^
    --include-package=pathlib ^
    --include-package=glob ^
    --include-package=shutil ^
    --include-package=tempfile ^
    --include-package=logging ^
    --include-package=traceback ^
    --include-package=warnings ^
    --include-package=weakref ^
    --include-package=gc ^
    --include-package=ctypes ^
    --include-package=struct ^
    --include-package=array ^
    --include-package=base64 ^
    --include-package=binascii ^
    --include-package=zlib ^
    --include-package=gzip ^
    --include-package=io ^
    --include-package=re ^
    --include-package=string ^
    --include-package=textwrap ^
    --include-package=unicodedata ^
    --include-package=locale ^
    --include-package=calendar ^
    --include-package=decimal ^
    --include-package=fractions ^
    --include-package=statistics ^
    --include-package=enum ^
    --include-package=types ^
    --include-package=inspect ^
    --include-package=importlib ^
    --include-package=pkgutil ^
    --include-package=modulefinder ^
    --include-package=runpy ^
    --include-package=ast ^
    --include-package=dis ^
    --include-package=code ^
    --include-package=codeop ^
    --include-package=py_compile ^
    --include-package=compileall ^
    --include-package=keyword ^
    --include-package=token ^
    --include-package=tokenize ^
    --include-package=symbol ^
    --include-package=parser ^
    --include-package=linecache ^
    --include-package=pprint ^
    --include-package=reprlib ^
    --include-package=contextlib ^
    --include-package=abc ^
    --include-package=atexit ^
    --include-package=signal ^
    --include-package=errno ^
    --include-package=socket ^
    --include-package=ssl ^
    --include-package=select ^
    --include-package=selectors ^
    --include-package=asyncio ^
    --include-package=concurrent ^
    --include-package=queue ^
    --include-package=multiprocessing ^
    --include-package=threading ^
    --include-package=_thread ^
    --include-package=dummy_threading ^
    --include-package=sched ^
    --include-package=heapq ^
    --include-package=bisect ^
    --include-package=collections ^
    --include-package=itertools ^
    --include-package=functools ^
    --include-package=operator ^
    --include-package=copy ^
    --include-package=pickle ^
    --include-package=copyreg ^
    --include-package=shelve ^
    --include-package=marshal ^
    --include-package=dbm ^
    --include-package=sqlite3 ^
    --include-package=zlib ^
    --include-package=gzip ^
    --include-package=bz2 ^
    --include-package=lzma ^
    --include-package=zipfile ^
    --include-package=tarfile ^
    --include-package=csv ^
    --include-package=configparser ^
    --include-package=netrc ^
    --include-package=xdrlib ^
    --include-package=plistlib ^
    --include-package=hashlib ^
    --include-package=hmac ^
    --include-package=secrets ^
    --include-package=os ^
    --include-package=io ^
    --include-package=time ^
    --include-package=argparse ^
    --include-package=optparse ^
    --include-package=getopt ^
    --include-package=logging ^
    --include-package=getpass ^
    --include-package=curses ^
    --include-package=platform ^
    --include-package=errno ^
    --include-package=ctypes ^
    --include-package=struct ^
    --include-package=codecs ^
    --include-package=encodings ^
    --include-package=stringprep ^
    --include-package=fpectl ^
    --include-package=faulthandler ^
    --include-package=pdb ^
    --include-package=profile ^
    --include-package=pstats ^
    --include-package=timeit ^
    --include-package=trace ^
    --include-package=tracemalloc ^
    --include-package=distutils ^
    --include-package=ensurepip ^
    --include-package=venv ^
    --include-package=zipapp ^
    --enable-plugin=multiprocessing ^
    --enable-plugin=numpy ^
    --enable-plugin=data-files ^
    --lto=yes ^
    --clang ^
    --jobs=%CPU_COUNT% ^
    --show-memory ^
    --show-progress ^
    --verbose ^
    --experimental=use_pgo ^
    --onefile-compression=auto ^
    --onefile-tempdir-spec=%%TEMP%%\WOWBingo_%%PID%% ^
    --windows-dpi-awareness=system ^
    --file-reference-choice=runtime ^
    --python-flag=no_site ^
    --python-flag=no_warnings ^
    --python-flag=no_docstrings ^
    --python-flag=no_asserts ^
    --remove-output ^
    %MAIN_SCRIPT%

:: Check build result
if %errorlevel% neq 0 (
    echo.
    echo %RED%================================================================%RESET%
    echo %RED%    BUILD FAILED!%RESET%
    echo %RED%================================================================%RESET%
    echo %RED%The compilation process encountered errors.%RESET%
    echo %YELLOW%Please check the output above for specific error messages.%RESET%
    echo.
    echo %CYAN%Common solutions:%RESET%
    echo - Ensure all dependencies are installed
    echo - Check that all asset files are present
    echo - Verify Python and Nuitka versions are compatible
    echo - Try running the script as administrator
    echo.
    pause
    exit /b 1
)

:: Success message
echo.
echo %GREEN%================================================================%RESET%
echo %GREEN%    BUILD COMPLETED SUCCESSFULLY!%RESET%
echo %GREEN%================================================================%RESET%
echo.

:: Find the generated executable
set "EXECUTABLE_PATH=%BUILD_DIR%\%OUTPUT_NAME%.exe"
if exist "%EXECUTABLE_PATH%" (
    echo %GREEN%Executable created: %EXECUTABLE_PATH%%RESET%

    :: Get file size
    for %%F in ("%EXECUTABLE_PATH%") do (
        set "FILE_SIZE=%%~zF"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1048576"
    )
    echo %CYAN%File size: !FILE_SIZE_MB! MB%RESET%

    :: Get creation time
    for %%F in ("%EXECUTABLE_PATH%") do (
        echo %CYAN%Created: %%~tF%RESET%
    )

    echo.
    echo %YELLOW%Performance Optimizations Applied:%RESET%
    echo - Link-time optimization (LTO)
    echo - Clang compiler for better optimization
    echo - Profile-guided optimization (PGO)
    echo - Multi-threaded compilation
    echo - Automatic compression
    echo - Runtime file references
    echo - Disabled Python site packages
    echo - Removed debug assertions
    echo - Optimized for speed over size
    echo.

    echo %GREEN%The executable is ready for distribution!%RESET%
    echo %CYAN%You can now run: %EXECUTABLE_PATH%%RESET%
    echo.

    :: Ask if user wants to test the executable
    set /p "TEST_CHOICE=Do you want to test the executable now? (y/n): "
    if /i "!TEST_CHOICE!"=="y" (
        echo %CYAN%Starting the executable...%RESET%
        start "" "%EXECUTABLE_PATH%"
    )

) else (
    echo %RED%Error: Executable not found at expected location!%RESET%
    echo %YELLOW%Build may have completed but output location is different.%RESET%
    echo %CYAN%Check the %BUILD_DIR% directory for the executable.%RESET%
)

echo.
echo %CYAN%Build process completed.%RESET%
pause
