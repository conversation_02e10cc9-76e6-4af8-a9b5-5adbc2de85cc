#!/usr/bin/env python3
"""
WOW Bingo Game - Safe MSVC Nuitka Build Script
==============================================

This script creates a standalone executable using Nuitka with MSVC compiler,
specifically avoiding Clang issues that can occur with Visual Studio Build Tools.

Features:
- Uses MSVC compiler exclusively (no Clang)
- Simplified build process for reliability
- Complete asset bundling
- Comprehensive error handling
- Windows-optimized configuration

Usage:
    python nuitka_build_msvc_safe.py [options]

Options:
    --clean                 Clean build directories first
    --test                  Test the executable after building
    --verbose               Enable verbose output
    --onefile               Create single-file executable
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

class SafeNuitkaBuilder:
    """Safe Nuitka build system that avoids Clang issues."""

    def __init__(self, verbose: bool = False):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.verbose = verbose
        self.start_time = time.time()

        # Project configuration
        self.project_name = "WOW Bingo Game"
        self.executable_name = "WOW_Bingo_Game.exe"
        self.main_script = "main.py"
        self.icon_path = self.project_root / "assets" / "app_logo.ico"

    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        elapsed = time.time() - self.start_time
        print(f"[{timestamp}] [{elapsed:6.1f}s] {level}: {message}")

    def error(self, message: str) -> None:
        """Log an error and exit."""
        self.log(message, "ERROR")
        sys.exit(1)

    def warning(self, message: str) -> None:
        """Log a warning."""
        self.log(message, "WARN")

    def check_prerequisites(self) -> None:
        """Check that all prerequisites are met."""
        self.log("Checking prerequisites...")

        # Check Python version
        if sys.version_info < (3, 7):
            self.error("Python 3.7 or higher is required")

        # Check main script
        if not (self.project_root / self.main_script).exists():
            self.error(f"Main script not found: {self.main_script}")

        # Check assets directory
        if not (self.project_root / "assets").exists():
            self.error("Assets directory not found")

        self.log("Prerequisites check passed")

    def install_nuitka(self) -> None:
        """Install Nuitka if not available."""
        try:
            subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                         capture_output=True, check=True)
            self.log("Nuitka is already installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("Installing Nuitka...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka>=2.4.0'],
                             check=True, capture_output=not self.verbose)
                self.log("Nuitka installed successfully")
            except subprocess.CalledProcessError as e:
                self.error(f"Failed to install Nuitka: {e}")

    def prepare_build_environment(self, clean: bool = False) -> None:
        """Prepare the build environment."""
        self.log("Preparing build environment...")

        if clean:
            self.log("Cleaning build directories...")
            for dir_path in [self.build_dir, self.dist_dir]:
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    self.log(f"Removed {dir_path}")

        # Create directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)

    def build_with_nuitka(self, onefile: bool = False) -> bool:
        """Build the executable using Nuitka with safe MSVC settings."""
        self.log("Building with Nuitka (MSVC safe mode)...")

        # Build command with safe settings
        cmd = [
            sys.executable, '-m', 'nuitka',
            '--standalone' if not onefile else '--onefile',
            f'--output-filename={self.executable_name}',
            f'--output-dir={self.dist_dir}',
            '--assume-yes-for-downloads',
            '--windows-console-mode=disable',
            '--show-progress',
            '--show-memory',

            # Safe compiler settings - avoid Clang issues
            '--msvc=latest',
            '--jobs=2',  # Conservative job count

            # Windows metadata
            '--windows-company-name=WOW Games',
            '--windows-product-name=WOW Bingo Game',
            '--windows-file-version=1.0.0',
            '--windows-product-version=1.0.0',
            '--windows-file-description=WOW Bingo Game - Professional Bingo Gaming Application',
        ]

        # Add icon if available
        if self.icon_path.exists():
            cmd.append(f'--windows-icon-from-ico={self.icon_path}')

        # Include data directories
        data_dirs = [
            ('assets', 'assets'),
            ('data', 'data'),
        ]

        for source_dir, target_dir in data_dirs:
            source_path = self.project_root / source_dir
            if source_path.exists():
                cmd.append(f'--include-data-dir={source_path}={target_dir}')
                self.log(f"Including data directory: {source_dir}")

        # Include essential packages
        essential_packages = [
            'pygame',
            'pygame_ce',
            'pyperclip',
            'sqlite3',
            'json',
            'datetime',
            'pathlib',
            'threading',
            'multiprocessing',
        ]

        for package in essential_packages:
            cmd.append(f'--include-package={package}')

        # Add main script
        cmd.append(str(self.main_script))

        # Execute Nuitka
        try:
            self.log("Executing Nuitka (this may take 10-15 minutes)...")
            if self.verbose:
                self.log(f"Command: {' '.join(cmd)}")

            result = subprocess.run(cmd, cwd=self.project_root,
                                  capture_output=not self.verbose, text=True)

            if result.returncode == 0:
                self.log("Nuitka build completed successfully")
                return True
            else:
                self.error(f"Nuitka build failed with return code {result.returncode}")
                if result.stderr and not self.verbose:
                    self.log(f"Error output: {result.stderr}")
                return False

        except Exception as e:
            self.error(f"Error during Nuitka build: {e}")
            return False

    def verify_executable(self) -> Path:
        """Verify that the built executable exists."""
        self.log("Verifying built executable...")

        # Look for the executable
        possible_paths = [
            self.dist_dir / self.executable_name,
            self.dist_dir / f"{self.executable_name[:-4]}.exe",
        ]

        for path in possible_paths:
            if path.exists():
                size_mb = path.stat().st_size / (1024 * 1024)
                self.log(f"Executable found: {path}")
                self.log(f"File size: {size_mb:.1f} MB")
                return path

        self.error(f"Executable not found in expected locations")

    def build(self, clean: bool = False, test: bool = False, onefile: bool = False) -> bool:
        """Main build method."""
        self.log("=" * 80)
        self.log("WOW Bingo Game - Safe MSVC Nuitka Build")
        self.log("=" * 80)

        try:
            # Check prerequisites
            self.check_prerequisites()

            # Install Nuitka if needed
            self.install_nuitka()

            # Prepare environment
            self.prepare_build_environment(clean=clean)

            # Build
            success = self.build_with_nuitka(onefile=onefile)
            if not success:
                return False

            # Verify executable
            executable_path = self.verify_executable()

            # Success message
            build_time = time.time() - self.start_time
            self.log("=" * 80)
            self.log("BUILD COMPLETED SUCCESSFULLY!")
            self.log("=" * 80)
            self.log(f"Executable: {executable_path}")
            self.log(f"Size: {executable_path.stat().st_size / (1024 * 1024):.1f} MB")
            self.log(f"Build time: {build_time:.1f} seconds")
            self.log("=" * 80)

            return True

        except Exception as e:
            self.error(f"Build failed with error: {e}")
            return False

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="WOW Bingo Game - Safe MSVC Nuitka Build Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    parser.add_argument('--clean', action='store_true', help='Clean build directories first')
    parser.add_argument('--test', action='store_true', help='Test the executable after building')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--onefile', action='store_true', help='Create single-file executable')

    args = parser.parse_args()

    # Create builder
    builder = SafeNuitkaBuilder(verbose=args.verbose)

    try:
        # Build
        success = builder.build(
            clean=args.clean,
            test=args.test,
            onefile=args.onefile
        )

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        builder.log("Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        builder.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
