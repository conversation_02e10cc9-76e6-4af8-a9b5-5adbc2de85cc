# Power Management System Guide

## Overview

The Power Management System ensures that the WOW Games application screen stays always active and prevents Windows from dimming or turning off the display during gameplay. This is essential for maintaining an uninterrupted gaming experience.

## Features

### Core Functionality
- **Screen Sleep Prevention**: Prevents Windows from turning off the display
- **Screen Dimming Prevention**: Stops Windows from dimming the screen
- **Window Focus Management**: Keeps the application window active and in foreground
- **User Activity Simulation**: Simulates minimal user activity to prevent screensaver activation
- **Background Monitoring**: Continuous monitoring to ensure power management stays active

### Settings Integration
- **Configurable Options**: All features can be enabled/disabled through settings
- **Auto-Start**: Automatically starts power management when the application launches
- **Check Interval**: Configurable monitoring interval (default: 5 minutes)

## How It Works

### Windows API Integration
The system uses Windows API calls through `ctypes` to:
- Call `SetThreadExecutionState` to prevent system sleep
- Use `ES_CONTINUOUS`, `ES_SYSTEM_REQUIRED`, `ES_DISPLAY_REQUIRED` flags
- Manage window focus with `<PERSON>ForegroundWindow`, `BringWindowToTop`
- Simulate mouse movement to prevent screensaver

### Background Thread
- Runs a daemon thread that continuously monitors power management
- Refreshes power settings every 5 minutes by default
- Automatically restarts if power management becomes inactive
- Gracefully stops when the application exits

## Configuration

### Settings File Location
Power management settings are stored in `data/settings.json` under the `power_management` section:

```json
{
  "power_management": {
    "enabled": true,
    "prevent_screen_sleep": true,
    "keep_window_active": true,
    "simulate_user_activity": true,
    "check_interval": 300,
    "auto_start": true
  }
}
```

### Setting Descriptions
- **enabled**: Master switch for the entire power management system
- **prevent_screen_sleep**: Prevents Windows from turning off the display
- **keep_window_active**: Keeps the application window in foreground
- **simulate_user_activity**: Simulates mouse movement to prevent screensaver
- **check_interval**: How often to check/refresh power management (seconds)
- **auto_start**: Automatically start power management on application startup

## Usage

### Automatic Usage
Power management starts automatically when the application launches (if enabled in settings) and stops when the application exits.

### Manual Control
```python
from power_management import start_power_management, stop_power_management, is_power_management_active

# Start power management
start_power_management()

# Check if active
if is_power_management_active():
    print("Power management is running")

# Stop power management
stop_power_management()
```

### Settings Control
```python
from settings_manager import SettingsManager

settings = SettingsManager()

# Enable/disable power management
settings.set_setting('power_management', 'enabled', True)

# Change check interval to 10 minutes
settings.set_setting('power_management', 'check_interval', 600)
```

## Technical Details

### System Requirements
- **Operating System**: Windows (uses Windows API)
- **Python Libraries**: `ctypes`, `threading`, `time`
- **Permissions**: No special permissions required

### Performance Impact
- **CPU Usage**: Minimal (background thread sleeps most of the time)
- **Memory Usage**: Very low (single background thread)
- **Network Usage**: None
- **Disk Usage**: None (except settings storage)

### Compatibility
- **Windows 10/11**: Full compatibility
- **Windows 8/8.1**: Full compatibility
- **Windows 7**: Full compatibility
- **Other OS**: Graceful degradation (no errors, but no functionality)

## Troubleshooting

### Common Issues

#### Power Management Not Starting
**Symptoms**: Screen still dims or turns off
**Solutions**:
1. Check if enabled in settings: `power_management.enabled = true`
2. Verify auto-start setting: `power_management.auto_start = true`
3. Check console output for error messages
4. Ensure running on Windows

#### Window Loses Focus
**Symptoms**: Application window goes to background
**Solutions**:
1. Enable window management: `power_management.keep_window_active = true`
2. Check if other applications are forcing focus
3. Verify pygame window is properly initialized

#### Screensaver Still Activates
**Symptoms**: Screensaver appears despite power management
**Solutions**:
1. Enable user activity simulation: `power_management.simulate_user_activity = true`
2. Reduce check interval: `power_management.check_interval = 60`
3. Check Windows screensaver settings

### Debug Information
The system provides detailed console output:
```
Power Management: Windows API initialized successfully
Power Management: Successfully prevented screen sleep
Power Management: Keep-alive thread started
Power Management: Successfully started - screen will stay always active
```

### Testing
Run the test script to verify functionality:
```bash
python test_power_management.py
```

## Integration Points

### Main Application
- **Startup**: `main.py` automatically starts power management
- **Shutdown**: `main.py` automatically stops power management
- **Monitoring**: Periodic checks in the main game loop

### Settings System
- **Configuration**: Integrated with `settings_manager.py`
- **UI Controls**: Can be controlled through settings interface
- **Persistence**: Settings saved to `data/settings.json`

### Game Loop
- **Periodic Checks**: Every 5 minutes (configurable)
- **Automatic Restart**: If power management becomes inactive
- **Error Handling**: Graceful error handling with fallbacks

## Best Practices

### For Users
1. Keep power management enabled for uninterrupted gameplay
2. Use default settings unless you have specific requirements
3. Monitor console output for any error messages

### For Developers
1. Always call `stop_power_management()` before application exit
2. Handle exceptions gracefully when power management fails
3. Provide user feedback about power management status
4. Test on different Windows versions

## Security Considerations

### What It Does
- Uses standard Windows API calls
- Only affects power management settings
- Simulates minimal mouse movement (1 pixel)
- Keeps application window active

### What It Doesn't Do
- No system-wide changes
- No registry modifications
- No file system access (except settings)
- No network communication
- No elevation of privileges required

## Future Enhancements

### Planned Features
- **Multiple Monitor Support**: Better handling of multi-monitor setups
- **Gaming Mode**: Enhanced power management for gaming sessions
- **Scheduled Activation**: Time-based power management control
- **Power Profile Integration**: Integration with Windows power profiles

### Possible Improvements
- **GPU Power Management**: Prevent GPU from entering low-power states
- **Network Keep-Alive**: Prevent network adapters from sleeping
- **USB Device Management**: Keep USB devices active
- **Advanced Scheduling**: More granular control over when power management is active
