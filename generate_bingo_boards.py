import json
import os
import random

def create_unique_board():
    """Create a single unique bingo board"""
    board = []

    # Standard Bingo columns have specific number ranges:
    # B: 1-15, I: 16-30, N: 31-45, G: 46-60, O: 61-75
    for col in range(5):
        column = []
        start = col * 15 + 1
        end = start + 14
        nums = random.sample(range(start, end + 1), 5)
        column.extend(nums)
        board.append(column)

    # Set the middle square (free space) to 0
    board[2][2] = 0  # Free space

    return board

def create_deterministic_board(cartella_number):
    """Create a deterministic board based on cartella number"""
    # Use the cartella number as seed for consistent results
    random.seed(cartella_number)

    board = []

    # For each column (B, I, N, G, O)
    for col in range(5):
        column_values = []
        # Range for this column: col*15+1 to col*15+15
        min_val = col * 15 + 1
        max_val = min_val + 14

        # Generate 5 unique random numbers for this column
        values = list(range(min_val, max_val + 1))
        random.shuffle(values)
        column_values = values[:5]

        # Set the center square (N column, 3rd row) to 0 (free space)
        if col == 2:
            column_values[2] = 0

        board.append(column_values)

    # Reset the random seed to avoid affecting other random operations
    random.seed()

    return board

def generate_unique_boards():
    """Generate 100 unique bingo boards and save them to disk"""
    boards = {}

    # Generate a unique board for each cartella number 1-100
    for cartella in range(1, 101):
        # Generate a unique board for this cartella number
        board = create_deterministic_board(cartella)
        boards[str(cartella)] = board
        print(f"Generated board for cartella {cartella}")

    # Ensure the data directory exists
    os.makedirs('data', exist_ok=True)
    boards_file = os.path.join('data', 'bingo_boards.json')

    # Save generated boards to file
    try:
        with open(boards_file, 'w') as file:
            json.dump(boards, file, indent=2)
        print(f"Successfully saved {len(boards)} boards to {boards_file}")
    except Exception as e:
        print(f"Error saving boards to file: {e}")
        
    return boards

if __name__ == "__main__":
    print("Generating bingo boards...")
    generate_unique_boards()
    print("Done!")
