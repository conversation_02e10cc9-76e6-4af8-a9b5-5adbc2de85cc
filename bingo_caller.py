import random
import time
import threading
import os
import pygame
from announcer_language_manager import AnnouncerLanguageManager

class BingoCaller:
    """
    Manages the calling of bingo numbers with various calling patterns and timing options.
    """

    def __init__(self, total_numbers=75, callback=None):
        """
        Initialize a new BingoCaller instance.

        Args:
            total_numbers: The total number of bingo numbers (default: 75)
            callback: Optional callback function called when a new number is selected
        """
        self.total_numbers = total_numbers
        self.callback = callback
        self.called_numbers = []
        self.current_number = None
        self.last_call_time = 0  # Timestamp of the last number call
        self.calling_thread = None
        self.should_stop = False
        self.paused = False
        self.call_delay = 3.0  # Seconds between calls
        self.column_letters = ['B', 'I', 'N', 'G', 'O']

        # Signal to interrupt the current delay cycle
        self.interrupt_delay = False

        # Initialize audio cache for announcement sounds
        self.announcement_sounds = {}

        # Performance optimization: Track if we're in the middle of playing an announcement
        self.announcement_playing = False
        self.announcement_channel = None

        # Try to reserve a dedicated channel for announcements
        try:
            self.announcement_channel = pygame.mixer.Channel(1)  # Use channel 1 for announcements
        except Exception as e:
            print(f"Could not reserve audio channel: {e}")

        # Preload common audio files in background
        self._preload_audio_files()

    def get_letter_for_number(self, number):
        """Get the BINGO letter for a given number"""
        if number < 1 or number > self.total_numbers:
            return ''

        # Calculate column (0-4) based on number range
        col = min(4, (number - 1) // 15)
        return self.column_letters[col]

    def _preload_audio_files(self):
        """
        Preload common audio files in the background to improve performance.
        This loads the first 10 numbers in each column (B1-B10, I16-I25, etc.)
        which are the most commonly called numbers.
        """
        try:
            # Get the selected announcer language from settings
            language = None
            try:
                from settings_manager import SettingsManager
                settings = SettingsManager()
                language = settings.get_setting('audio', 'announcer_language', 'Default')
            except Exception as e:
                print(f"Error getting announcer language from settings: {e}")
                language = 'Default'

            # Start a background thread to preload audio files
            threading.Thread(
                target=self._preload_audio_thread,
                args=(language,),
                daemon=True
            ).start()
        except Exception as e:
            print(f"Error starting preload thread: {e}")

    def _preload_audio_thread(self, language):
        """Background thread to preload audio files without blocking the main thread"""
        try:
            # Preload the first 10 numbers in each column
            for col in range(5):  # B, I, N, G, O
                start_num = col * 15 + 1
                for i in range(10):  # First 10 numbers in each column
                    num = start_num + i
                    if num <= self.total_numbers:
                        # Preload this number's announcement
                        self.get_announcement_sound(num, language, preloading=True)
                        # Small sleep to avoid overloading the system
                        time.sleep(0.01)
        except Exception as e:
            print(f"Error in preload thread: {e}")

    def get_announcement_sound(self, number, language=None, preloading=False):
        """
        Get the sound object for a number announcement.
        Caches sounds for better performance.

        Args:
            number: The bingo number to announce
            language: The language to use for the announcement (Default, Amharic, etc.)
                     If None, uses English
            preloading: Whether this is being called during preloading (affects logging)
        """
        # Create a cache key that includes both number and language
        cache_key = f"{number}_{language or 'English'}"

        if cache_key in self.announcement_sounds:
            return self.announcement_sounds[cache_key]

        letter = self.get_letter_for_number(number)

        # Use the AnnouncerLanguageManager to get the audio path
        audio_path = AnnouncerLanguageManager.get_audio_path(number, letter, language)

        try:
            if os.path.exists(audio_path):
                sound = pygame.mixer.Sound(audio_path)
                self.announcement_sounds[cache_key] = sound
                if not preloading:
                    print(f"Loaded and cached sound for {letter}{number} in {language or 'English'}")
                return sound
            else:
                if not preloading:
                    print(f"Audio file not found: {audio_path}")
        except Exception as e:
            if not preloading:
                print(f"Error loading announcement sound {audio_path}: {e}")

        return None

    def play_announcement(self, number, language=None):
        """
        Play the audio announcement for a number

        Args:
            number: The bingo number to announce
            language: The language to use for the announcement (Default, Amharic, etc.)
                     If None, uses the default language
        """
        # Get the selected announcer language from settings if not specified
        if language is None:
            try:
                # Import here to avoid circular imports
                from settings_manager import SettingsManager
                settings = SettingsManager()
                language = settings.get_setting('audio', 'announcer_language', 'Default')
            except Exception as e:
                print(f"Error getting announcer language from settings: {e}")
                language = 'Default'

        # Get the sound
        sound = self.get_announcement_sound(number, language)
        if not sound:
            return False

        # Play the sound using the dedicated channel if available
        try:
            if self.announcement_channel:
                # Stop any currently playing announcement
                self.announcement_channel.stop()
                # Play on the dedicated channel
                self.announcement_channel.play(sound)
                self.announcement_playing = True
                return True
            else:
                # Fall back to regular play if no channel is available
                sound.play()
                self.announcement_playing = True
                return True
        except Exception as e:
            print(f"Error playing announcement: {e}")
            # Try fallback method
            try:
                sound.play()
                return True
            except:
                pass

        return False

    def is_announcement_playing(self):
        """Check if an announcement is currently playing"""
        if self.announcement_channel:
            return self.announcement_channel.get_busy()
        return False

    def call_next_number(self):
        """
        Call the next random number that hasn't been called yet.
        Returns the newly called number or None if all numbers have been called.
        """
        # Performance optimization: Use a cached set of all numbers
        if not hasattr(self, '_all_numbers_set'):
            self._all_numbers_set = set(range(1, self.total_numbers + 1))

        # Check if all numbers have been called
        if len(self.called_numbers) >= self.total_numbers:
            return None

        # Performance optimization: Use set operations for faster lookup
        called_set = set(self.called_numbers)
        uncalled = list(self._all_numbers_set - called_set)

        if not uncalled:
            return None

        # Call a random number
        number = random.choice(uncalled)

        # Update state with the new number
        self.current_number = number
        self.called_numbers.append(number)

        # Update the last call timestamp for animation purposes
        self.last_call_time = time.time()

        # Log the number being called for debugging (minimal logging)
        letter = self.get_letter_for_number(number)
        print(f"BingoCaller: Calling {letter}{number}")

        # Play announcement for this number
        self.play_announcement(number)

        # Invoke callback if provided
        if self.callback:
            try:
                self.callback(self.current_number)
            except Exception as e:
                print(f"Error in bingo caller callback: {e}")

        return self.current_number

    def get_call_announcement(self, number=None):
        """Get a formatted announcement string for the called number"""
        if number is None:
            number = self.current_number

        if number is None:
            return "No number called"

        letter = self.get_letter_for_number(number)
        return f"{letter}-{number}"

    def reset(self):
        """Reset the caller for a new game"""
        self.stop_calling()  # Stop any ongoing calling
        self.called_numbers = []
        self.current_number = None
        self.last_call_time = 0

    def start_calling(self, delay=None):
        """
        Start automatically calling numbers with the specified delay.
        This runs in a separate thread to not block the main game loop.

        Args:
            delay: Time in seconds between calls (defaults to self.call_delay)
        """
        if delay is not None:
            self.call_delay = delay

        # Stop any existing thread
        self.stop_calling()

        # Start a new calling thread
        self.should_stop = False
        self.paused = False
        self.calling_thread = threading.Thread(target=self._calling_loop)
        self.calling_thread.daemon = True  # Thread won't prevent program exit
        self.calling_thread.start()

    def _calling_loop(self):
        """Internal method: Loop that calls numbers at timed intervals"""
        try:
            # Add a small initial delay before calling the first number
            # This ensures the UI is ready to display the first number
            initial_delay = 1.0  # 1 second initial delay
            print(f"BingoCaller: Adding {initial_delay}s initial delay before first number call")
            time.sleep(initial_delay)

            # Flag to track if this is the first number being called
            is_first_call = True

            # Performance optimization: Use a set for called numbers
            called_set = set()

            while not self.should_stop:
                try:
                    if not self.paused:
                        # Quick check if we've called all numbers
                        if len(called_set) >= self.total_numbers:
                            print(f"BingoCaller: All {self.total_numbers} numbers have been called. Stopping.")
                            self.paused = True
                            continue

                        # Wait for any current announcement to finish before calling next number
                        # This ensures announcements don't overlap and improves audio quality
                        if self.is_announcement_playing():
                            # Short sleep and continue the loop
                            time.sleep(0.1)
                            continue

                        # Call the next number
                        called_number = self.call_next_number()

                        # Verify the called number is valid
                        if called_number is not None:
                            # Update tracking
                            called_set.add(called_number)

                            # Log the first number call for debugging
                            if is_first_call:
                                print(f"BingoCaller: First number called: {called_number}")
                                is_first_call = False

                                # Force a small delay after the first number to ensure UI updates
                                time.sleep(0.2)  # Reduced from 0.5 to 0.2 for better performance
                        else:
                            # If no number was called, there might be an issue
                            print(f"BingoCaller: No number was called. Called numbers: {len(self.called_numbers)}/{self.total_numbers}")
                except Exception as e:
                    print(f"Error calling next number: {e}")
                    # Continue with the loop despite the error
                    time.sleep(0.5)  # Reduced from 1.0 to 0.5 for better performance
                    continue

                # Wait for the specified delay
                start_time = time.time()
                # Reset the interrupt flag at the start of each delay cycle
                self.interrupt_delay = False

                # More efficient delay loop with fewer checks
                check_interval = 0.1  # Check less frequently (100ms) for better performance
                next_check_time = start_time + check_interval

                while time.time() - start_time < self.call_delay:
                    # Only check at specific intervals rather than continuously
                    current_time = time.time()
                    if current_time >= next_check_time:
                        # Check for stop or interrupt signals
                        if self.should_stop or self.interrupt_delay:
                            if self.interrupt_delay:
                                print("BingoCaller: Delay cycle interrupted")
                            break  # Exit the delay loop immediately
                        next_check_time = current_time + check_interval

                    # Sleep for a short time to reduce CPU usage
                    time.sleep(0.05)
        except Exception as e:
            print(f"Fatal error in calling loop: {e}")
            # Try to reset the caller state
            self.should_stop = True
            self.paused = True

    def pause_calling(self):
        """Pause the automatic calling"""
        self.paused = True

    def resume_calling(self):
        """Resume the automatic calling"""
        self.paused = False

    def stop_calling(self):
        """Stop the automatic calling thread"""
        if self.calling_thread and self.calling_thread.is_alive():
            self.should_stop = True
            self.calling_thread.join(1.0)  # Wait up to 1 second for thread to end
            self.calling_thread = None

    def update_delay(self, new_delay):
        """Update the call delay and restart the calling thread if it's running

        Args:
            new_delay: New delay in seconds between number calls

        Returns:
            bool: True if the delay was updated and calling was restarted, False otherwise
        """
        # Store current state
        was_calling = self.calling_thread is not None and self.calling_thread.is_alive()

        # Update the delay
        old_delay = self.call_delay
        self.call_delay = new_delay
        print(f"BingoCaller: Updated call delay from {old_delay}s to {new_delay}s")

        # If the caller was running, interrupt the current delay cycle
        if was_calling:
            # Set the interrupt flag to break out of the current delay cycle
            self.interrupt_delay = True
            print(f"BingoCaller: Interrupting current delay cycle")

            # Give a short time for the interrupt to take effect
            time.sleep(0.1)

            # No need to restart the thread - the interrupt will cause the next cycle to use the new delay
            print(f"BingoCaller: Next number will be called with new delay: {new_delay}s")

            return True

        return False

    def get_called_count(self):
        """Get the count of called numbers"""
        return len(self.called_numbers)

    def is_number_called(self, number):
        """Check if a specific number has been called"""
        return number in self.called_numbers

    def is_recently_called(self, number, recent_timeframe=5.0):
        """
        Check if a number was called recently (within the specified timeframe).

        Args:
            number: The number to check
            recent_timeframe: Timeframe in seconds to consider "recent" (default: 5.0)

        Returns:
            bool: True if the number is the current number and was called within the timeframe
        """
        return (
            number == self.current_number and
            time.time() - self.last_call_time < recent_timeframe
        )

    def get_formatted_called_list(self):
        """Get a formatted string of all called numbers grouped by letter"""
        if not self.called_numbers:
            return "No numbers called"

        # Group by BINGO column
        columns = {letter: [] for letter in self.column_letters}

        for num in sorted(self.called_numbers):
            letter = self.get_letter_for_number(num)
            columns[letter].append(num)

        # Format the output
        result = []
        for letter in self.column_letters:
            nums = columns[letter]
            if nums:
                result.append(f"{letter}: {', '.join(str(n) for n in nums)}")

        return "\n".join(result)

    def call_specific_number(self, number):
        """
        Call a specific number (for testing or manual operation).
        Returns True if successful, False if number already called or invalid.
        """
        # Validate that the number is within the valid range (1-75)
        if not (1 <= number <= self.total_numbers):
            print(f"Error: Number {number} is outside valid range 1-{self.total_numbers}")
            return False

        # Check if all numbers have been called already
        if len(self.called_numbers) >= self.total_numbers:
            return False

        # Performance optimization: Direct check if number is in called_numbers
        if number in self.called_numbers:
            return False

        # Update state with the new number
        self.current_number = number
        self.called_numbers.append(number)

        # Update the last call timestamp for animation purposes
        self.last_call_time = time.time()

        # Play announcement for this number using the selected language
        self.play_announcement(number)

        # Invoke callback if provided
        if self.callback:
            try:
                self.callback(number)
            except Exception as e:
                print(f"Error in bingo caller callback: {e}")

        return True