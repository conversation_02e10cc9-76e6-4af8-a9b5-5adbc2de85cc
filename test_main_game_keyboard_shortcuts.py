"""
Test script for F key and Escape key functionality on the main game page

This script tests that the F key and Escape key work correctly for screen mode
toggling on the main game page, ensuring consistency with board selection page.

Test scenarios:
1. F key toggles between windowed and fullscreen modes
2. Escape key exits fullscreen mode (when in fullscreen)
3. Priority handling ensures shortcuts work even with other UI elements active
4. Consistency with board selection page behavior
"""

import pygame
import sys
import os
import time

def test_f_key_functionality():
    """Test F key screen mode toggling"""
    print("=" * 60)
    print("TESTING F KEY FUNCTIONALITY ON MAIN GAME PAGE")
    print("=" * 60)
    
    # Initialize pygame
    pygame.init()
    
    # Start in windowed mode
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("F Key Test - Main Game Page")
    
    # Initialize screen mode manager (simulating main game initialization)
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Ensure we start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print(f"Initial mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    assert not screen_mode_manager.is_fullscreen(), "Should start in windowed mode"
    
    # Test F key toggle to fullscreen
    print("\nTesting F key toggle to fullscreen...")
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    
    is_fullscreen_after_f = screen_mode_manager.is_fullscreen()
    print(f"After F key: {'fullscreen' if is_fullscreen_after_f else 'windowed'}")
    assert is_fullscreen_after_f, "F key should toggle to fullscreen"
    
    # Test F key toggle back to windowed
    print("Testing F key toggle back to windowed...")
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    
    is_windowed_after_f = not screen_mode_manager.is_fullscreen()
    print(f"After second F key: {'fullscreen' if not is_windowed_after_f else 'windowed'}")
    assert is_windowed_after_f, "F key should toggle back to windowed"
    
    print("✓ F key functionality test PASSED")
    pygame.quit()
    return True

def test_escape_key_functionality():
    """Test Escape key screen mode handling"""
    print("\n" + "=" * 60)
    print("TESTING ESCAPE KEY FUNCTIONALITY ON MAIN GAME PAGE")
    print("=" * 60)
    
    # Initialize pygame
    pygame.init()
    
    # Start in windowed mode
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Escape Key Test - Main Game Page")
    
    # Initialize screen mode manager
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    print(f"Initial mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    
    # Test Escape key in windowed mode (should do nothing)
    print("\nTesting Escape key in windowed mode...")
    screen, mode_changed = screen_mode_manager.handle_escape_key(screen)
    
    print(f"Mode changed: {mode_changed}")
    print(f"Current mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    assert not mode_changed, "Escape key should not change mode when in windowed"
    assert not screen_mode_manager.is_fullscreen(), "Should remain in windowed mode"
    
    # Switch to fullscreen first
    print("\nSwitching to fullscreen...")
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    assert screen_mode_manager.is_fullscreen(), "Should be in fullscreen mode"
    
    # Test Escape key in fullscreen mode (should exit to windowed)
    print("Testing Escape key in fullscreen mode...")
    screen, mode_changed = screen_mode_manager.handle_escape_key(screen)
    
    print(f"Mode changed: {mode_changed}")
    print(f"Current mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")
    assert mode_changed, "Escape key should change mode when in fullscreen"
    assert not screen_mode_manager.is_fullscreen(), "Should exit to windowed mode"
    
    print("✓ Escape key functionality test PASSED")
    pygame.quit()
    return True

def test_priority_handling():
    """Test that keyboard shortcuts have proper priority"""
    print("\n" + "=" * 60)
    print("TESTING PRIORITY HANDLING")
    print("=" * 60)
    
    # This test simulates the priority logic from main.py
    
    # Test scenario: F key should work even when other UI elements might be present
    print("Testing F key priority handling...")
    
    # Simulate the priority check from main.py
    # F key should only be skipped if actively typing in input fields
    
    # Case 1: No input fields active - F key should work
    input_active = False
    bet_input_active = False
    reason_input_active = False
    
    should_handle_f_key = not (input_active or bet_input_active or reason_input_active)
    print(f"No input fields active - should handle F key: {should_handle_f_key}")
    assert should_handle_f_key, "F key should work when no input fields are active"
    
    # Case 2: Input field active - F key should be skipped
    input_active = True
    should_handle_f_key = not (input_active or bet_input_active or reason_input_active)
    print(f"Input field active - should handle F key: {should_handle_f_key}")
    assert not should_handle_f_key, "F key should be skipped when input field is active"
    
    # Case 3: Bet input active - F key should be skipped
    input_active = False
    bet_input_active = True
    should_handle_f_key = not (input_active or bet_input_active or reason_input_active)
    print(f"Bet input active - should handle F key: {should_handle_f_key}")
    assert not should_handle_f_key, "F key should be skipped when bet input is active"
    
    # Case 4: Reason input active - F key should be skipped
    bet_input_active = False
    reason_input_active = True
    should_handle_f_key = not (input_active or bet_input_active or reason_input_active)
    print(f"Reason input active - should handle F key: {should_handle_f_key}")
    assert not should_handle_f_key, "F key should be skipped when reason input is active"
    
    print("✓ Priority handling test PASSED")
    return True

def test_consistency_with_board_selection():
    """Test that main game page behavior matches board selection page"""
    print("\n" + "=" * 60)
    print("TESTING CONSISTENCY WITH BOARD SELECTION PAGE")
    print("=" * 60)
    
    # Initialize pygame
    pygame.init()
    
    # Test main game page behavior
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Consistency Test")
    
    from screen_mode_manager import get_screen_mode_manager
    screen_mode_manager = get_screen_mode_manager()
    screen_mode_manager.set_screen_reference(screen)
    
    # Start in windowed mode
    screen_mode_manager.settings_manager.set_screen_mode(False)
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    
    # Test F key behavior (should match board selection)
    print("Testing F key behavior consistency...")
    initial_mode = screen_mode_manager.is_fullscreen()
    screen = screen_mode_manager.handle_f_key_toggle(screen)
    after_f_mode = screen_mode_manager.is_fullscreen()
    
    print(f"Initial: {'fullscreen' if initial_mode else 'windowed'}")
    print(f"After F: {'fullscreen' if after_f_mode else 'windowed'}")
    assert initial_mode != after_f_mode, "F key should toggle screen mode"
    
    # Test Escape key behavior (should match board selection)
    print("Testing Escape key behavior consistency...")
    if after_f_mode:  # If we're in fullscreen
        screen, mode_changed = screen_mode_manager.handle_escape_key(screen)
        after_escape_mode = screen_mode_manager.is_fullscreen()
        
        print(f"After Escape: {'fullscreen' if after_escape_mode else 'windowed'}")
        assert mode_changed, "Escape should exit fullscreen"
        assert not after_escape_mode, "Should be in windowed mode after Escape"
    
    print("✓ Consistency test PASSED")
    pygame.quit()
    return True

def main():
    """Run all keyboard shortcut tests"""
    print("MAIN GAME PAGE KEYBOARD SHORTCUTS TEST SUITE")
    print("=" * 60)
    
    all_tests_passed = True
    
    try:
        # Test 1: F key functionality
        if not test_f_key_functionality():
            all_tests_passed = False
        
        # Test 2: Escape key functionality
        if not test_escape_key_functionality():
            all_tests_passed = False
        
        # Test 3: Priority handling
        if not test_priority_handling():
            all_tests_passed = False
        
        # Test 4: Consistency with board selection
        if not test_consistency_with_board_selection():
            all_tests_passed = False
        
        # Final results
        print("\n" + "=" * 60)
        print("TEST RESULTS SUMMARY")
        print("=" * 60)
        
        if all_tests_passed:
            print("🎉 ALL TESTS PASSED! ✓")
            print("\nThe F key and Escape key functionality is working correctly:")
            print("✓ F key toggles between windowed and fullscreen modes")
            print("✓ Escape key exits fullscreen mode (when in fullscreen)")
            print("✓ Priority handling ensures shortcuts work properly")
            print("✓ Behavior is consistent with board selection page")
            print("\nUsers can now use keyboard shortcuts on the main game page!")
        else:
            print("❌ SOME TESTS FAILED! ✗")
            print("\nThe keyboard shortcuts need additional work.")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
