{% extends "layout.html" %}
{% block content %}
    <h2>Performance Monitoring</h2>

    <div class="dashboard-summary">
        <div class="summary-card">
            <h3>Total Queries</h3>
            <div class="value">{{ performance.total_queries }}</div>
        </div>
        <div class="summary-card">
            <h3>Avg Query Time</h3>
            <div class="value">{{ "%.2f"|format(performance.avg_query_time_ms) }} ms</div>
        </div>
        <div class="summary-card">
            <h3>Error Rate</h3>
            <div class="value">{{ "%.2f"|format(performance.error_rate * 100) }}%</div>
        </div>
        <div class="summary-card">
            <h3>Slow Queries</h3>
            <div class="value">{{ performance.slow_queries_count }}</div>
        </div>
    </div>

    <h3>Optimization Recommendations</h3>
    {% if recommendations %}
        {% for rec in recommendations %}
        <div class="alert alert-{{ 'danger' if rec.type in ['HIGH_ERROR_RATE', 'SLOW_QUERIES'] else 'warning' }}">
            <strong>{{ rec.type }}:</strong> {{ rec.message }}<br>
            <em>Suggestion: {{ rec.suggestion }}</em>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-success">No performance issues detected.</div>
    {% endif %}

    <h3>Top Accessed Tables</h3>
    <table>
        <thead>
            <tr>
                <th>Table</th>
                <th>Access Count</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {% for table, count in performance.top_tables %}
            <tr>
                <td>{{ table }}</td>
                <td>{{ count }}</td>
                <td>{{ "%.1f"|format((count / performance.total_queries * 100) if performance.total_queries > 0 else 0) }}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <h3>Actions</h3>
    <form method="post" action="/performance/export" style="display: inline;">
        <button type="submit">Export Metrics</button>
    </form>
    <form method="post" action="/performance/reset" style="display: inline;">
        <button type="submit" onclick="return confirm('Are you sure you want to reset all metrics?')">Reset Metrics</button>
    </form>
{% endblock %}
