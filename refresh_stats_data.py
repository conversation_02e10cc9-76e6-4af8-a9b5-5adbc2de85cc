#!/usr/bin/env python
"""
Utility script to force refresh all game statistics data.
This helps fix issues with stats page displaying incorrect data.
"""

import os
import sys
import time
import datetime
import sqlite3
import traceback

def print_separator():
    print("=" * 80)

def fix_database_paths():
    """Check if the stats_new.db file exists but stats.db doesn't, and copy it if needed."""
    data_dir = os.path.join('data')
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"Created data directory: {data_dir}")
    
    stats_db_path = os.path.join(data_dir, 'stats.db')
    stats_new_db_path = os.path.join(data_dir, 'stats_new.db')
    
    # If stats_new.db exists but stats.db doesn't, we should copy it
    if os.path.exists(stats_new_db_path) and not os.path.exists(stats_db_path):
        print(f"Found stats_new.db but stats.db doesn't exist. Copying database...")
        try:
            import shutil
            shutil.copy2(stats_new_db_path, stats_db_path)
            print(f"✓ Successfully copied stats_new.db to stats.db")
            return True
        except Exception as e:
            print(f"✗ Error copying database: {e}")
            return False
    
    # If both exist, we should check sizes
    if os.path.exists(stats_new_db_path) and os.path.exists(stats_db_path):
        new_size = os.path.getsize(stats_new_db_path)
        old_size = os.path.getsize(stats_db_path)
        
        print(f"Found both databases:")
        print(f"  - stats.db: {old_size} bytes")
        print(f"  - stats_new.db: {new_size} bytes")
        
        # If stats_new.db is newer and substantially different in size, offer to replace stats.db
        if abs(new_size - old_size) > 1024 and new_size > old_size:
            print("stats_new.db appears to be newer and larger. It may contain newer data.")
            try:
                response = input("Would you like to use stats_new.db instead? (y/n): ").strip().lower()
                if response.startswith('y'):
                    import shutil
                    backup_path = f"{stats_db_path}.bak.{int(time.time())}"
                    shutil.copy2(stats_db_path, backup_path)
                    print(f"✓ Created backup at {backup_path}")
                    shutil.copy2(stats_new_db_path, stats_db_path)
                    print(f"✓ Successfully replaced stats.db with stats_new.db")
                    return True
            except Exception as e:
                print(f"✗ Error handling database replacement: {e}")
    
    return False

def main():
    print_separator()
    print("STATS DATA REFRESH UTILITY")
    print("This utility forces a refresh of all game statistics data")
    print("and fixes issues with the stats page displaying incorrect data.")
    print_separator()
    
    # Check for database path issues and fix them
    fix_database_paths()
    
    # Check database file exists
    db_path = os.path.join('data', 'stats.db')
    if not os.path.exists(db_path):
        print(f"✗ Error: Database file not found at {db_path}")
        return 1
    else:
        print(f"✓ Found database file at {db_path}, size: {os.path.getsize(db_path)} bytes")
    
    # Connect to database and check for data
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table counts
        cursor.execute("SELECT COUNT(*) FROM game_history")
        game_count = cursor.fetchone()[0]
        print(f"✓ Found {game_count} games in database")
        
        cursor.execute("SELECT COUNT(*) FROM daily_stats")
        daily_stats_count = cursor.fetchone()[0]
        print(f"✓ Found {daily_stats_count} daily stats records in database")
        
        # Check if there's a summary record
        try:
            cursor.execute("SELECT COUNT(*) FROM overall_summary")
            summary_count = cursor.fetchone()[0]
            print(f"✓ Found {summary_count} summary records in database")
        except sqlite3.OperationalError:
            print("⚠ No overall_summary table found - will be created by stats page")
        
        # Close database connection before refresh
        conn.close()
    except Exception as e:
        print(f"✗ Error accessing database: {e}")
        traceback.print_exc()
        return 1
    
    # Try to import and use stats_integration module
    print_separator()
    print("Attempting to refresh data using stats_integration module...")
    try:
        from stats_integration import force_refresh_data
        print("✓ Successfully imported force_refresh_data function")
        
        # Run force refresh
        result = force_refresh_data()
        if result:
            print("✓ Force refresh completed successfully")
        else:
            print("⚠ Force refresh completed but reported errors")
            
    except ImportError as e:
        print(f"⚠ Could not import stats_integration module: {e}")
        print("  Trying alternative refresh method...")
        
        # Try thread_safe_db as an alternative
        try:
            import thread_safe_db
            print("✓ Successfully imported thread_safe_db module")
            
            # Close and reopen connection to ensure fresh data
            thread_safe_db.close_connection()
            conn = thread_safe_db.get_connection()
            print("✓ Refreshed database connection")
            
            # Get summary stats to trigger data recalculation
            summary = thread_safe_db.get_summary_stats()
            print(f"✓ Retrieved summary stats: {summary}")
            
            # Get weekly stats to trigger data recalculation
            weekly = thread_safe_db.get_weekly_stats()
            print(f"✓ Retrieved weekly stats: {len(weekly)} days")
            
            # Close connection
            thread_safe_db.close_connection()
            print("✓ Closed database connection")
            
        except ImportError as db_e:
            print(f"✗ Error: Could not import thread_safe_db module: {db_e}")
            print("  No refresh method available")
            return 1
        except Exception as db_e:
            print(f"✗ Error using thread_safe_db: {db_e}")
            traceback.print_exc()
            return 1
    except Exception as e:
        print(f"✗ Error during force refresh: {e}")
        traceback.print_exc()
        return 1
    
    print_separator()
    print("Stats data refresh process complete.")
    print("The stats page should now display correct data.")
    print("If you still experience issues:")
    print("1. Make sure all game data is being properly recorded")
    print("2. Check that the stats page is using the correct database path ('stats.db')")
    print("3. Try restarting the application")
    print_separator()
    
    # Pause at the end to let user read the output if run by double-clicking
    if os.name == 'nt':  # Windows
        os.system('pause')
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unhandled error: {e}")
        traceback.print_exc()
        print("\nPress Enter to exit...")
        input()
        sys.exit(1) 