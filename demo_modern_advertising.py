#!/usr/bin/env python3
"""
WOW Bingo Game - Modern Advertising Demo
=======================================

Interactive demo showcasing the beautiful, modern, and professional
advertising system with GPU acceleration and stunning visual effects.

Features demonstrated:
- GPU-accelerated rendering with smooth 60+ FPS animations
- Modern glass morphism backgrounds with gradients
- Multiple animation modes (scroll, wave, pulse, particle effects)
- Professional typography with glow effects
- Automatic quality adjustment based on hardware
- Real-time performance monitoring
"""

import asyncio
import sys
import time
from pathlib import Path

import pygame
import pygame.gfxdraw

# Add the modern application path
modern_app_path = Path(__file__).parent / "src"
sys.path.insert(0, str(modern_app_path))

try:
    from wow_bingo_game.ui.components.modern_advertising import (
        ModernAdvertisingComponent, 
        AdvertisingSettings, 
        AnimationMode, 
        VisualQuality
    )
    from wow_bingo_game.core.config import WOWBingoConfig
    from wow_bingo_game.utils.logger import setup_logging, get_logger
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    logger = get_logger(__name__)
    
    MODERN_ADVERTISING_AVAILABLE = True
except ImportError as e:
    print(f"Modern advertising not available: {e}")
    print("Please run 'python quick_start_modern.py --install-deps' first")
    MODERN_ADVERTISING_AVAILABLE = False


class ModernAdvertisingDemo:
    """Interactive demo of the modern advertising system."""
    
    def __init__(self):
        """Initialize the demo."""
        self.screen = None
        self.clock = None
        self.running = False
        self.advertising_component = None
        
        # Demo settings
        self.current_animation_mode = 0
        self.animation_modes = list(AnimationMode)
        self.current_quality = 0
        self.quality_levels = list(VisualQuality)
        
        # Demo texts
        self.demo_texts = [
            "WOW Games - Premium Bingo Experience",
            "🎮 Modern GPU-Accelerated Gaming 🎮",
            "✨ Beautiful • Smooth • Professional ✨",
            "🚀 60+ FPS Animations with Hardware Acceleration 🚀",
            "🎨 Glass Morphism • Particle Effects • Glow 🎨",
            "💎 Ultra Quality Graphics • Intelligent Optimization 💎"
        ]
        self.current_text = 0
        
        # Performance tracking
        self.fps_samples = []
        self.last_fps_update = time.time()
        
        print("🎮 Modern Advertising Demo initialized")
    
    async def initialize(self):
        """Initialize pygame and advertising component."""
        try:
            # Initialize pygame
            pygame.init()
            
            # Set up display
            self.screen = pygame.display.set_mode((1200, 800), pygame.HWSURFACE | pygame.DOUBLEBUF)
            pygame.display.set_caption("WOW Bingo - Modern Advertising Demo")
            self.clock = pygame.time.Clock()
            
            # Initialize modern advertising component
            if MODERN_ADVERTISING_AVAILABLE:
                config = WOWBingoConfig()
                self.advertising_component = ModernAdvertisingComponent(config)
                
                success = await self.advertising_component.initialize()
                if success:
                    print("✅ Modern advertising component initialized")
                    
                    # Set initial demo text
                    self.advertising_component.set_text(self.demo_texts[0])
                    
                    # Log hardware info
                    perf_info = self.advertising_component.get_performance_info()
                    print(f"🎯 GPU Available: {perf_info['gpu_available']}")
                    print(f"🎨 Visual Quality: {perf_info['visual_quality']}")
                    print(f"🎬 Animation Mode: {perf_info['animation_mode']}")
                    
                else:
                    print("❌ Failed to initialize modern advertising")
                    return False
            else:
                print("❌ Modern advertising not available")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize demo: {e}")
            return False
    
    def handle_events(self):
        """Handle pygame events."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                
                elif event.key == pygame.K_SPACE:
                    # Cycle animation modes
                    self.current_animation_mode = (self.current_animation_mode + 1) % len(self.animation_modes)
                    new_mode = self.animation_modes[self.current_animation_mode]
                    self.advertising_component.set_animation_mode(new_mode)
                    print(f"🎬 Animation mode: {new_mode.value}")
                
                elif event.key == pygame.K_q:
                    # Cycle quality levels
                    self.current_quality = (self.current_quality + 1) % len(self.quality_levels)
                    new_quality = self.quality_levels[self.current_quality]
                    self.advertising_component.set_visual_quality(new_quality)
                    print(f"🎨 Visual quality: {new_quality.value}")
                
                elif event.key == pygame.K_t:
                    # Cycle demo texts
                    self.current_text = (self.current_text + 1) % len(self.demo_texts)
                    new_text = self.demo_texts[self.current_text]
                    self.advertising_component.set_text(new_text)
                    print(f"📝 Text: {new_text[:50]}...")
                
                elif event.key == pygame.K_p:
                    # Show performance info
                    self.show_performance_info()
    
    def update(self, delta_time):
        """Update the demo."""
        if self.advertising_component:
            self.advertising_component.update(delta_time)
        
        # Update FPS tracking
        current_time = time.time()
        if current_time - self.last_fps_update >= 1.0:
            current_fps = self.clock.get_fps()
            self.fps_samples.append(current_fps)
            if len(self.fps_samples) > 60:  # Keep last 60 seconds
                self.fps_samples.pop(0)
            self.last_fps_update = current_time
    
    def render(self):
        """Render the demo."""
        # Clear screen with dark background
        self.screen.fill((20, 25, 35))
        
        # Draw background pattern
        self.draw_background_pattern()
        
        # Main advertising area (large)
        main_rect = pygame.Rect(50, 150, 1100, 200)
        self.draw_demo_section(main_rect, "Main Advertising Area", large=True)
        
        # Secondary advertising areas (smaller)
        secondary_rect1 = pygame.Rect(50, 400, 530, 120)
        self.draw_demo_section(secondary_rect1, "Secondary Area 1")
        
        secondary_rect2 = pygame.Rect(620, 400, 530, 120)
        self.draw_demo_section(secondary_rect2, "Secondary Area 2")
        
        # Compact advertising area
        compact_rect = pygame.Rect(50, 560, 1100, 80)
        self.draw_demo_section(compact_rect, "Compact Area")
        
        # Draw UI
        self.draw_ui()
        
        # Draw performance overlay
        self.draw_performance_overlay()
        
        pygame.display.flip()
    
    def draw_background_pattern(self):
        """Draw subtle background pattern."""
        try:
            # Draw subtle grid pattern
            grid_color = (30, 35, 45)
            for x in range(0, 1200, 50):
                pygame.draw.line(self.screen, grid_color, (x, 0), (x, 800))
            for y in range(0, 800, 50):
                pygame.draw.line(self.screen, grid_color, (0, y), (1200, y))
            
            # Draw corner decorations
            corner_color = (40, 50, 70)
            pygame.draw.circle(self.screen, corner_color, (100, 100), 30, 2)
            pygame.draw.circle(self.screen, corner_color, (1100, 100), 30, 2)
            pygame.draw.circle(self.screen, corner_color, (100, 700), 30, 2)
            pygame.draw.circle(self.screen, corner_color, (1100, 700), 30, 2)
            
        except Exception as e:
            pass  # Ignore background drawing errors
    
    def draw_demo_section(self, rect, title, large=False):
        """Draw a demo section with advertising."""
        try:
            # Draw section border
            border_color = (100, 120, 160)
            pygame.draw.rect(self.screen, border_color, rect, 2)
            
            # Draw title
            font_size = 16 if not large else 20
            font = pygame.font.SysFont("Arial", font_size, bold=True)
            title_surf = font.render(title, True, (200, 220, 255))
            title_x = rect.x + 10
            title_y = rect.y - 25
            self.screen.blit(title_surf, (title_x, title_y))
            
            # Draw advertising content
            if self.advertising_component:
                # Adjust rect for content area (leave some padding)
                content_rect = pygame.Rect(rect.x + 5, rect.y + 5, rect.width - 10, rect.height - 10)
                self.advertising_component.render(self.screen, content_rect)
            else:
                # Fallback rendering
                fallback_font = pygame.font.SysFont("Arial", 24, bold=True)
                fallback_text = fallback_font.render("Modern Advertising Not Available", True, (255, 100, 100))
                text_x = rect.x + (rect.width - fallback_text.get_width()) // 2
                text_y = rect.y + (rect.height - fallback_text.get_height()) // 2
                self.screen.blit(fallback_text, (text_x, text_y))
                
        except Exception as e:
            # Draw error indicator
            error_font = pygame.font.SysFont("Arial", 16)
            error_text = error_font.render(f"Render Error: {str(e)[:50]}", True, (255, 100, 100))
            self.screen.blit(error_text, (rect.x + 10, rect.y + 10))
    
    def draw_ui(self):
        """Draw user interface."""
        try:
            # Title
            title_font = pygame.font.SysFont("Arial", 32, bold=True)
            title_surf = title_font.render("WOW Bingo - Modern Advertising Demo", True, (255, 255, 255))
            title_x = (1200 - title_surf.get_width()) // 2
            self.screen.blit(title_surf, (title_x, 20))
            
            # Subtitle
            subtitle_font = pygame.font.SysFont("Arial", 18)
            subtitle_surf = subtitle_font.render("GPU-Accelerated • Professional • Beautiful", True, (200, 220, 255))
            subtitle_x = (1200 - subtitle_surf.get_width()) // 2
            self.screen.blit(subtitle_surf, (subtitle_x, 60))
            
            # Controls
            controls_font = pygame.font.SysFont("Arial", 14)
            controls = [
                "SPACE - Change Animation Mode",
                "Q - Change Visual Quality", 
                "T - Change Text",
                "P - Show Performance Info",
                "ESC - Exit"
            ]
            
            for i, control in enumerate(controls):
                control_surf = controls_font.render(control, True, (180, 200, 230))
                self.screen.blit(control_surf, (50, 680 + i * 20))
                
        except Exception as e:
            pass  # Ignore UI drawing errors
    
    def draw_performance_overlay(self):
        """Draw performance information overlay."""
        try:
            if not self.advertising_component:
                return
            
            # Get performance info
            perf_info = self.advertising_component.get_performance_info()
            current_fps = self.clock.get_fps()
            
            # Performance box
            perf_rect = pygame.Rect(950, 680, 240, 110)
            pygame.draw.rect(self.screen, (20, 30, 50, 200), perf_rect)
            pygame.draw.rect(self.screen, (100, 150, 200), perf_rect, 1)
            
            # Performance text
            perf_font = pygame.font.SysFont("Arial", 12, bold=True)
            perf_texts = [
                f"FPS: {current_fps:.1f}",
                f"GPU: {'✅' if perf_info.get('gpu_available') else '❌'}",
                f"Quality: {perf_info.get('visual_quality', 'unknown')}",
                f"Animation: {perf_info.get('animation_mode', 'unknown')}",
                f"Glow: {'✅' if perf_info.get('glow_enabled') else '❌'}",
                f"Particles: {'✅' if perf_info.get('particles_enabled') else '❌'}",
                f"Frames: {perf_info.get('frame_count', 0)}"
            ]
            
            for i, text in enumerate(perf_texts):
                color = (100, 255, 100) if '✅' in text else (255, 255, 255)
                text_surf = perf_font.render(text, True, color)
                self.screen.blit(text_surf, (perf_rect.x + 10, perf_rect.y + 10 + i * 14))
                
        except Exception as e:
            pass  # Ignore performance overlay errors
    
    def show_performance_info(self):
        """Print detailed performance information."""
        try:
            if self.advertising_component:
                perf_info = self.advertising_component.get_performance_info()
                print("\n📊 Performance Information:")
                for key, value in perf_info.items():
                    print(f"  {key}: {value}")
                print()
            else:
                print("❌ No performance information available")
                
        except Exception as e:
            print(f"❌ Failed to get performance info: {e}")
    
    async def run(self):
        """Run the demo."""
        try:
            print("🚀 Starting modern advertising demo...")
            
            if not await self.initialize():
                print("❌ Failed to initialize demo")
                return
            
            self.running = True
            last_time = time.time()
            
            print("\n🎮 Demo Controls:")
            print("  SPACE - Change Animation Mode")
            print("  Q - Change Visual Quality")
            print("  T - Change Text")
            print("  P - Show Performance Info")
            print("  ESC - Exit")
            print("\n✨ Enjoy the beautiful, modern advertising system!")
            
            while self.running:
                # Calculate delta time
                current_time = time.time()
                delta_time = current_time - last_time
                last_time = current_time
                
                # Handle events
                self.handle_events()
                
                # Update
                self.update(delta_time)
                
                # Render
                self.render()
                
                # Control frame rate
                self.clock.tick(60)  # Target 60 FPS
            
            print("🏁 Demo completed!")
            
        except Exception as e:
            print(f"❌ Demo error: {e}")
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self.advertising_component:
                self.advertising_component.cleanup()
            
            pygame.quit()
            print("🧹 Demo cleaned up")
            
        except Exception as e:
            print(f"❌ Cleanup error: {e}")


async def main():
    """Main demo function."""
    print("🎮 WOW Bingo Game - Modern Advertising Demo")
    print("=" * 50)
    
    if not MODERN_ADVERTISING_AVAILABLE:
        print("❌ Modern advertising not available")
        print("Please run: python quick_start_modern.py --install-deps")
        return
    
    # Create and run demo
    demo = ModernAdvertisingDemo()
    await demo.run()


if __name__ == "__main__":
    asyncio.run(main())
