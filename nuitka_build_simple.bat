@echo off
REM WOW Bingo Game - Simple Nuitka Build
REM ====================================
REM
REM This batch file uses the simplest possible Nuitka configuration
REM to avoid any compiler issues and ensure maximum compatibility.

echo.
echo ================================================================================
echo WOW Bingo Game - Simple Nuitka Build (Maximum Compatibility)
echo ================================================================================
echo.
echo This build uses the simplest Nuitka configuration for maximum reliability.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting simple build process...
echo.

REM Build with simple settings
echo Building executable with Nuitka (Simple Mode)...
echo This uses minimal settings for maximum compatibility.
echo Build time: approximately 10-20 minutes...
echo.

python nuitka_build_simple.py --verbose

if errorlevel 1 (
    echo.
    echo ================================================================================
    echo BUILD FAILED
    echo ================================================================================
    echo.
    echo The simple build failed. This could be due to:
    echo 1. Missing Visual Studio Build Tools
    echo 2. Missing Python dependencies
    echo 3. Insufficient system resources
    echo.
    echo Solutions:
    echo 1. Install Visual Studio Build Tools:
    echo    https://visualstudio.microsoft.com/visual-cpp-build-tools/
    echo 2. Install dependencies: pip install -r requirements.txt
    echo 3. Close other applications to free up memory
    echo 4. Try PyInstaller as alternative: python build_executable.py
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================================
echo.
echo Your executable is ready in the 'dist' directory.
echo The executable can run on any Windows PC without Python installation.
echo.
echo Next steps:
echo 1. Test the executable by running it from the dist folder
echo 2. Copy the dist folder contents to distribute your application
echo 3. The executable includes all necessary assets and dependencies
echo.
pause
