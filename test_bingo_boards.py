import json
import os

def test_bingo_boards():
    """Test that bingo boards can be loaded properly"""
    try:
        # Path to the bingo boards file
        boards_file = os.path.join('data', 'bingo_boards.json')
        
        # Check if the file exists
        if not os.path.exists(boards_file):
            print(f"ERROR: Bingo boards file not found at {boards_file}")
            return False
            
        # Load the boards from the file
        with open(boards_file, 'r') as file:
            boards = json.load(file)
            
        # Check if the boards dictionary is empty
        if not boards:
            print(f"ERROR: Bingo boards file is empty")
            return False
            
        # Check if we have at least 100 boards
        if len(boards) < 100:
            print(f"ERROR: Expected at least 100 boards, but found {len(boards)}")
            return False
            
        # Check if all boards from 1 to 100 exist
        for i in range(1, 101):
            if str(i) not in boards:
                print(f"ERROR: Board for cartella {i} not found")
                return False
                
        # Check the structure of a few random boards
        for cartella in ["1", "50", "100"]:
            board = boards[cartella]
            
            # Check if the board has 5 columns
            if len(board) != 5:
                print(f"ERROR: Board for cartella {cartella} has {len(board)} columns, expected 5")
                return False
                
            # Check if each column has 5 numbers
            for col_idx, col in enumerate(board):
                if len(col) != 5:
                    print(f"ERROR: Column {col_idx} of board for cartella {cartella} has {len(col)} numbers, expected 5")
                    return False
                    
            # Check if the middle square is a free space (0)
            if board[2][2] != 0:
                print(f"ERROR: Middle square of board for cartella {cartella} is {board[2][2]}, expected 0 (free space)")
                return False
                
        print(f"SUCCESS: All checks passed. Loaded {len(boards)} bingo boards.")
        return True
    except Exception as e:
        print(f"ERROR: Exception while testing bingo boards: {e}")
        return False

if __name__ == "__main__":
    test_bingo_boards()
