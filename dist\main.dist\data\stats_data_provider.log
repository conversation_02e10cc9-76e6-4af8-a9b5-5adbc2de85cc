2025-06-01 17:24:14.690 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.874 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.891 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.893 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-01 17:24:14.894 - Initializing StatsDataProvider
2025-06-01 17:24:14.915 - Loaded 8 items from cache
2025-06-01 17:24:14.922 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-01 17:24:14.925 - Starting background data loading thread
2025-06-01 17:24:14.929 - Background data loading started
2025-06-01 17:24:14.931 - StatsDataProvider initialization completed
2025-06-01 17:24:14.932 - Loading summary statistics
2025-06-01 17:24:14.933 - Created singleton instance of StatsDataProvider on module import
2025-06-01 17:24:14.933 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:24:14.936 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-01 17:24:14.937 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.942 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:24:14.942 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:24:14.943 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.948 - Successfully loaded weekly stats: 7 days
2025-06-01 17:24:14.950 - Saved 10 items to cache
2025-06-01 17:24:14.951 - Background data loading completed
2025-06-01 17:32:11.116 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:41:24.653 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.436 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.556 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.585 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.593 - Forcing refresh of all stats data
2025-06-01 17:48:01.605 - Attempting to force refresh via GameStatsIntegration
2025-06-01 17:48:01.686 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.693 - Force refresh via GameStatsIntegration successful
2025-06-01 17:48:01.696 - Clearing cached data
2025-06-01 17:48:01.698 - Posted refresh_stats event to trigger UI update
2025-06-01 17:48:01.698 - Starting background data reload
2025-06-01 17:48:01.727 - Starting background data loading thread
2025-06-01 17:48:01.731 - Background data loading started
2025-06-01 17:48:01.760 - Loading summary statistics
2025-06-01 17:48:01.800 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:48:01.803 - Data from GameStatsIntegration: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-01 17:48:01.806 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:48:01.810 - Successfully loaded summary stats: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-01 17:48:01.817 - Successfully loaded game history: 1 entries
2025-06-01 17:48:01.825 - Successfully loaded weekly stats: 7 days
2025-06-01 17:48:01.828 - Saved 3 items to cache
2025-06-01 17:48:01.830 - Background data loading completed
2025-06-01 17:48:30.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.229 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.335 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.371 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.382 - Forcing refresh of all stats data
2025-06-01 17:54:02.389 - Attempting to force refresh via GameStatsIntegration
2025-06-01 17:54:02.496 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.506 - Force refresh via GameStatsIntegration successful
2025-06-01 17:54:02.508 - Clearing cached data
2025-06-01 17:54:02.513 - Posted refresh_stats event to trigger UI update
2025-06-01 17:54:02.532 - Starting background data reload
2025-06-01 17:54:02.540 - Starting background data loading thread
2025-06-01 17:54:02.546 - Background data loading started
2025-06-01 17:54:02.578 - Loading summary statistics
2025-06-01 17:54:02.616 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:54:02.622 - Data from GameStatsIntegration: {'total_earnings': 80.0, 'daily_earnings': 80.0, 'daily_games': 2, 'wallet_balance': 0}
2025-06-01 17:54:02.627 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:54:02.634 - Successfully loaded summary stats: {'total_earnings': 80.0, 'daily_earnings': 80.0, 'daily_games': 2, 'wallet_balance': 0}
2025-06-01 17:54:02.639 - Successfully loaded game history: 2 entries
2025-06-01 17:54:02.640 - Successfully loaded weekly stats: 7 days
2025-06-01 17:54:02.645 - Saved 3 items to cache
2025-06-01 17:54:02.657 - Background data loading completed
2025-06-01 17:54:54.512 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.200 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.204 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.207 - Forcing refresh of all stats data
2025-06-01 18:05:39.209 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:05:39.284 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.297 - Force refresh via GameStatsIntegration successful
2025-06-01 18:05:39.306 - Clearing cached data
2025-06-01 18:05:39.312 - Posted refresh_stats event to trigger UI update
2025-06-01 18:05:39.323 - Starting background data reload
2025-06-01 18:05:39.327 - Starting background data loading thread
2025-06-01 18:05:39.332 - Background data loading started
2025-06-01 18:05:39.346 - Loading summary statistics
2025-06-01 18:05:39.409 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:05:39.418 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 3, 'wallet_balance': 0}
2025-06-01 18:05:39.421 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:05:39.426 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 3, 'wallet_balance': 0}
2025-06-01 18:05:39.433 - Successfully loaded game history: 3 entries
2025-06-01 18:05:39.439 - Successfully loaded weekly stats: 7 days
2025-06-01 18:05:39.448 - Saved 3 items to cache
2025-06-01 18:05:39.451 - Background data loading completed
2025-06-01 18:05:50.071 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.356 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.369 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.375 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.378 - Forcing refresh of all stats data
2025-06-01 18:15:42.378 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:15:42.393 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.396 - Force refresh via GameStatsIntegration successful
2025-06-01 18:15:42.396 - Clearing cached data
2025-06-01 18:15:42.397 - Posted refresh_stats event to trigger UI update
2025-06-01 18:15:42.398 - Starting background data reload
2025-06-01 18:15:42.398 - Starting background data loading thread
2025-06-01 18:15:42.401 - Background data loading started
2025-06-01 18:15:42.413 - Loading summary statistics
2025-06-01 18:15:42.455 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:15:42.462 - Data from GameStatsIntegration: {'total_earnings': 160.0, 'daily_earnings': 160.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-01 18:15:42.463 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:15:42.463 - Successfully loaded summary stats: {'total_earnings': 160.0, 'daily_earnings': 160.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-01 18:15:42.464 - Successfully loaded game history: 4 entries
2025-06-01 18:15:42.464 - Successfully loaded weekly stats: 7 days
2025-06-01 18:15:42.465 - Saved 3 items to cache
2025-06-01 18:15:42.467 - Background data loading completed
2025-06-01 18:16:57.986 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.067 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.084 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.086 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.087 - Forcing refresh of all stats data
2025-06-01 18:17:02.091 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:17:02.129 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.130 - Force refresh via GameStatsIntegration successful
2025-06-01 18:17:02.132 - Clearing cached data
2025-06-01 18:17:02.132 - Posted refresh_stats event to trigger UI update
2025-06-01 18:17:02.133 - Starting background data reload
2025-06-01 18:17:02.135 - Starting background data loading thread
2025-06-01 18:17:02.136 - Background data loading started
2025-06-01 18:17:02.143 - Loading summary statistics
2025-06-01 18:17:02.179 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:17:02.180 - Data from GameStatsIntegration: {'total_earnings': 184.0, 'daily_earnings': 184.0, 'daily_games': 5, 'wallet_balance': 0}
2025-06-01 18:17:02.180 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:17:02.180 - Successfully loaded summary stats: {'total_earnings': 184.0, 'daily_earnings': 184.0, 'daily_games': 5, 'wallet_balance': 0}
2025-06-01 18:17:02.181 - Successfully loaded game history: 5 entries
2025-06-01 18:17:02.181 - Successfully loaded weekly stats: 7 days
2025-06-01 18:17:02.183 - Saved 3 items to cache
2025-06-01 18:17:02.183 - Background data loading completed
2025-06-01 18:17:15.695 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.474 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.492 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.497 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.499 - Forcing refresh of all stats data
2025-06-01 18:23:56.500 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:23:56.513 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.514 - Force refresh via GameStatsIntegration successful
2025-06-01 18:23:56.515 - Clearing cached data
2025-06-01 18:23:56.516 - Posted refresh_stats event to trigger UI update
2025-06-01 18:23:56.517 - Starting background data reload
2025-06-01 18:23:56.518 - Starting background data loading thread
2025-06-01 18:23:56.519 - Background data loading started
2025-06-01 18:23:56.532 - Loading summary statistics
2025-06-01 18:23:56.558 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:23:56.559 - Data from GameStatsIntegration: {'total_earnings': 198.4, 'daily_earnings': 198.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-01 18:23:56.559 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:23:56.560 - Successfully loaded summary stats: {'total_earnings': 198.4, 'daily_earnings': 198.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-01 18:23:56.561 - Successfully loaded game history: 6 entries
2025-06-01 18:23:56.561 - Successfully loaded weekly stats: 7 days
2025-06-01 18:23:56.562 - Saved 3 items to cache
2025-06-01 18:23:56.564 - Background data loading completed
2025-06-01 18:24:32.253 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.334 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.460 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.485 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.494 - Forcing refresh of all stats data
2025-06-01 18:29:27.504 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:29:27.597 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.601 - Force refresh via GameStatsIntegration successful
2025-06-01 18:29:27.628 - Clearing cached data
2025-06-01 18:29:27.632 - Posted refresh_stats event to trigger UI update
2025-06-01 18:29:27.636 - Starting background data reload
2025-06-01 18:29:27.652 - Starting background data loading thread
2025-06-01 18:29:27.657 - Background data loading started
2025-06-01 18:29:27.666 - Loading summary statistics
2025-06-01 18:29:27.721 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:29:27.728 - Data from GameStatsIntegration: {'total_earnings': 212.8, 'daily_earnings': 212.8, 'daily_games': 7, 'wallet_balance': 0}
2025-06-01 18:29:27.729 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:29:27.730 - Successfully loaded summary stats: {'total_earnings': 212.8, 'daily_earnings': 212.8, 'daily_games': 7, 'wallet_balance': 0}
2025-06-01 18:29:27.731 - Successfully loaded game history: 7 entries
2025-06-01 18:29:27.735 - Successfully loaded weekly stats: 7 days
2025-06-01 18:29:27.761 - Saved 3 items to cache
2025-06-01 18:29:27.764 - Background data loading completed
2025-06-01 18:29:51.729 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.656 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.674 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.678 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.688 - Forcing refresh of all stats data
2025-06-01 18:34:26.690 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:34:26.776 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:27.068 - Force refresh via GameStatsIntegration successful
2025-06-01 18:34:27.075 - Clearing cached data
2025-06-01 18:34:27.081 - Posted refresh_stats event to trigger UI update
2025-06-01 18:34:27.084 - Starting background data reload
2025-06-01 18:34:27.086 - Starting background data loading thread
2025-06-01 18:34:27.098 - Background data loading started
2025-06-01 18:34:27.117 - Loading summary statistics
2025-06-01 18:34:27.179 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:34:27.186 - Data from GameStatsIntegration: {'total_earnings': 227.20000000000002, 'daily_earnings': 227.20000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-01 18:34:27.189 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:34:27.191 - Successfully loaded summary stats: {'total_earnings': 227.20000000000002, 'daily_earnings': 227.20000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-01 18:34:27.198 - Successfully loaded game history: 8 entries
2025-06-01 18:34:27.206 - Successfully loaded weekly stats: 7 days
2025-06-01 18:34:27.214 - Saved 3 items to cache
2025-06-01 18:34:27.217 - Background data loading completed
2025-06-01 18:35:18.769 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.616 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.635 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.645 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.665 - Forcing refresh of all stats data
2025-06-01 18:49:04.666 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:49:05.096 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:05.108 - Force refresh via GameStatsIntegration successful
2025-06-01 18:49:05.116 - Clearing cached data
2025-06-01 18:49:05.122 - Posted refresh_stats event to trigger UI update
2025-06-01 18:49:05.131 - Starting background data reload
2025-06-01 18:49:05.141 - Starting background data loading thread
2025-06-01 18:49:05.145 - Background data loading started
2025-06-01 18:49:05.158 - Loading summary statistics
2025-06-01 18:49:05.206 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:49:05.222 - Data from GameStatsIntegration: {'total_earnings': 241.60000000000002, 'daily_earnings': 241.60000000000002, 'daily_games': 9, 'wallet_balance': 0}
2025-06-01 18:49:05.226 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:49:05.229 - Successfully loaded summary stats: {'total_earnings': 241.60000000000002, 'daily_earnings': 241.60000000000002, 'daily_games': 9, 'wallet_balance': 0}
2025-06-01 18:49:05.238 - Successfully loaded game history: 9 entries
2025-06-01 18:49:05.245 - Successfully loaded weekly stats: 7 days
2025-06-01 18:49:05.252 - Saved 3 items to cache
2025-06-01 18:49:05.257 - Background data loading completed
2025-06-01 18:50:42.971 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.228 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.387 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.409 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.412 - Forcing refresh of all stats data
2025-06-01 18:53:18.416 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:53:18.534 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.548 - Force refresh via GameStatsIntegration successful
2025-06-01 18:53:18.553 - Clearing cached data
2025-06-01 18:53:18.562 - Posted refresh_stats event to trigger UI update
2025-06-01 18:53:18.568 - Starting background data reload
2025-06-01 18:53:18.577 - Starting background data loading thread
2025-06-01 18:53:18.584 - Background data loading started
2025-06-01 18:53:18.609 - Loading summary statistics
2025-06-01 18:53:18.663 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:53:18.666 - Data from GameStatsIntegration: {'total_earnings': 260.8, 'daily_earnings': 260.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-01 18:53:18.670 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:53:18.677 - Successfully loaded summary stats: {'total_earnings': 260.8, 'daily_earnings': 260.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-01 18:53:18.687 - Successfully loaded game history: 10 entries
2025-06-01 18:53:18.691 - Successfully loaded weekly stats: 7 days
2025-06-01 18:53:18.697 - Saved 3 items to cache
2025-06-01 18:53:18.706 - Background data loading completed
2025-06-01 18:54:28.938 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.833 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.849 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.852 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.853 - Forcing refresh of all stats data
2025-06-01 18:56:50.854 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:56:50.868 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.870 - Force refresh via GameStatsIntegration successful
2025-06-01 18:56:50.870 - Clearing cached data
2025-06-01 18:56:50.871 - Posted refresh_stats event to trigger UI update
2025-06-01 18:56:50.874 - Starting background data reload
2025-06-01 18:56:50.875 - Starting background data loading thread
2025-06-01 18:56:50.875 - Background data loading started
2025-06-01 18:56:50.887 - Loading summary statistics
2025-06-01 18:56:51.027 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:56:51.028 - Data from GameStatsIntegration: {'total_earnings': 280.0, 'daily_earnings': 280.0, 'daily_games': 11, 'wallet_balance': 0}
2025-06-01 18:56:51.028 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:56:51.029 - Successfully loaded summary stats: {'total_earnings': 280.0, 'daily_earnings': 280.0, 'daily_games': 11, 'wallet_balance': 0}
2025-06-01 18:56:51.030 - Successfully loaded game history: 10 entries
2025-06-01 18:56:51.038 - Successfully loaded weekly stats: 7 days
2025-06-01 18:56:51.041 - Saved 3 items to cache
2025-06-01 18:56:51.043 - Background data loading completed
2025-06-01 18:59:18.678 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.536 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.551 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.553 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.554 - Forcing refresh of all stats data
2025-06-01 19:01:19.555 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:01:19.571 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.574 - Force refresh via GameStatsIntegration successful
2025-06-01 19:01:19.575 - Clearing cached data
2025-06-01 19:01:19.576 - Posted refresh_stats event to trigger UI update
2025-06-01 19:01:19.579 - Starting background data reload
2025-06-01 19:01:19.579 - Starting background data loading thread
2025-06-01 19:01:19.580 - Background data loading started
2025-06-01 19:01:19.595 - Loading summary statistics
2025-06-01 19:01:19.620 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:01:19.620 - Data from GameStatsIntegration: {'total_earnings': 299.2, 'daily_earnings': 299.2, 'daily_games': 12, 'wallet_balance': 0}
2025-06-01 19:01:19.621 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:01:19.621 - Successfully loaded summary stats: {'total_earnings': 299.2, 'daily_earnings': 299.2, 'daily_games': 12, 'wallet_balance': 0}
2025-06-01 19:01:19.622 - Successfully loaded game history: 10 entries
2025-06-01 19:01:19.623 - Successfully loaded weekly stats: 7 days
2025-06-01 19:01:19.624 - Saved 3 items to cache
2025-06-01 19:01:19.624 - Background data loading completed
2025-06-01 19:04:57.902 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.592 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.607 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.611 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.612 - Forcing refresh of all stats data
2025-06-01 19:07:12.614 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:07:12.637 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.639 - Force refresh via GameStatsIntegration successful
2025-06-01 19:07:12.640 - Clearing cached data
2025-06-01 19:07:12.650 - Posted refresh_stats event to trigger UI update
2025-06-01 19:07:12.653 - Starting background data reload
2025-06-01 19:07:12.660 - Starting background data loading thread
2025-06-01 19:07:12.669 - Background data loading started
2025-06-01 19:07:12.689 - Loading summary statistics
2025-06-01 19:07:12.721 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:07:12.724 - Data from GameStatsIntegration: {'total_earnings': 323.2, 'daily_earnings': 323.2, 'daily_games': 13, 'wallet_balance': 0}
2025-06-01 19:07:12.739 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:07:12.750 - Successfully loaded summary stats: {'total_earnings': 323.2, 'daily_earnings': 323.2, 'daily_games': 13, 'wallet_balance': 0}
2025-06-01 19:07:12.754 - Successfully loaded game history: 10 entries
2025-06-01 19:07:12.763 - Successfully loaded weekly stats: 7 days
2025-06-01 19:07:12.771 - Saved 3 items to cache
2025-06-01 19:07:12.776 - Background data loading completed
2025-06-01 19:10:29.613 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.102 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.164 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.200 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.205 - Forcing refresh of all stats data
2025-06-01 19:15:07.213 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:15:07.328 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.344 - Force refresh via GameStatsIntegration successful
2025-06-01 19:15:07.348 - Clearing cached data
2025-06-01 19:15:07.350 - Posted refresh_stats event to trigger UI update
2025-06-01 19:15:07.357 - Starting background data reload
2025-06-01 19:15:07.370 - Starting background data loading thread
2025-06-01 19:15:07.384 - Background data loading started
2025-06-01 19:15:07.401 - Loading summary statistics
2025-06-01 19:15:07.453 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:15:07.456 - Data from GameStatsIntegration: {'total_earnings': 347.2, 'daily_earnings': 347.2, 'daily_games': 14, 'wallet_balance': 0}
2025-06-01 19:15:07.459 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:15:07.462 - Successfully loaded summary stats: {'total_earnings': 347.2, 'daily_earnings': 347.2, 'daily_games': 14, 'wallet_balance': 0}
2025-06-01 19:15:07.472 - Successfully loaded game history: 10 entries
2025-06-01 19:15:07.476 - Successfully loaded weekly stats: 7 days
2025-06-01 19:15:07.480 - Saved 3 items to cache
2025-06-01 19:15:07.480 - Background data loading completed
2025-06-01 19:15:58.141 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.843 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.864 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.868 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.872 - Forcing refresh of all stats data
2025-06-01 19:21:25.873 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:21:25.887 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.889 - Force refresh via GameStatsIntegration successful
2025-06-01 19:21:25.890 - Clearing cached data
2025-06-01 19:21:25.890 - Posted refresh_stats event to trigger UI update
2025-06-01 19:21:25.891 - Starting background data reload
2025-06-01 19:21:25.892 - Starting background data loading thread
2025-06-01 19:21:25.894 - Background data loading started
2025-06-01 19:21:25.895 - Loading summary statistics
2025-06-01 19:21:25.996 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:21:26.181 - Data from GameStatsIntegration: {'total_earnings': 376.0, 'daily_earnings': 376.0, 'daily_games': 15, 'wallet_balance': 0}
2025-06-01 19:21:26.183 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:21:26.186 - Successfully loaded summary stats: {'total_earnings': 376.0, 'daily_earnings': 376.0, 'daily_games': 15, 'wallet_balance': 0}
2025-06-01 19:21:26.186 - Successfully loaded game history: 10 entries
2025-06-01 19:21:26.192 - Successfully loaded weekly stats: 7 days
2025-06-01 19:21:26.218 - Saved 3 items to cache
2025-06-01 19:21:26.220 - Background data loading completed
2025-06-01 19:22:27.527 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.747 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.883 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.909 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.916 - Forcing refresh of all stats data
2025-06-01 19:25:48.922 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:25:49.292 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:49.300 - Force refresh via GameStatsIntegration successful
2025-06-01 19:25:49.304 - Clearing cached data
2025-06-01 19:25:49.308 - Posted refresh_stats event to trigger UI update
2025-06-01 19:25:49.321 - Starting background data reload
2025-06-01 19:25:49.325 - Starting background data loading thread
2025-06-01 19:25:49.331 - Background data loading started
2025-06-01 19:25:49.333 - Loading summary statistics
2025-06-01 19:25:49.368 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:25:49.400 - Data from GameStatsIntegration: {'total_earnings': 409.6, 'daily_earnings': 409.6, 'daily_games': 16, 'wallet_balance': 0}
2025-06-01 19:25:49.412 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:25:49.415 - Successfully loaded summary stats: {'total_earnings': 409.6, 'daily_earnings': 409.6, 'daily_games': 16, 'wallet_balance': 0}
2025-06-01 19:25:49.426 - Successfully loaded game history: 10 entries
2025-06-01 19:25:49.430 - Successfully loaded weekly stats: 7 days
2025-06-01 19:25:49.463 - Saved 3 items to cache
2025-06-01 19:25:49.465 - Background data loading completed
2025-06-01 19:27:08.940 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.621 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.636 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.642 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.643 - Forcing refresh of all stats data
2025-06-01 19:29:44.646 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:29:44.714 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.718 - Force refresh via GameStatsIntegration successful
2025-06-01 19:29:44.719 - Clearing cached data
2025-06-01 19:29:44.720 - Posted refresh_stats event to trigger UI update
2025-06-01 19:29:44.748 - Starting background data reload
2025-06-01 19:29:44.754 - Starting background data loading thread
2025-06-01 19:29:44.763 - Background data loading started
2025-06-01 19:29:44.784 - Loading summary statistics
2025-06-01 19:29:44.830 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:29:44.834 - Data from GameStatsIntegration: {'total_earnings': 452.8, 'daily_earnings': 452.8, 'daily_games': 17, 'wallet_balance': 0}
2025-06-01 19:29:44.839 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:29:44.930 - Successfully loaded summary stats: {'total_earnings': 452.8, 'daily_earnings': 452.8, 'daily_games': 17, 'wallet_balance': 0}
2025-06-01 19:29:45.104 - Successfully loaded game history: 10 entries
2025-06-01 19:29:45.107 - Successfully loaded weekly stats: 7 days
2025-06-01 19:29:45.119 - Saved 3 items to cache
2025-06-01 19:29:45.125 - Background data loading completed
2025-06-01 19:33:12.807 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.675 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.691 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.694 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.695 - Forcing refresh of all stats data
2025-06-01 19:37:16.699 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:37:16.872 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.878 - Force refresh via GameStatsIntegration successful
2025-06-01 19:37:16.882 - Clearing cached data
2025-06-01 19:37:16.883 - Posted refresh_stats event to trigger UI update
2025-06-01 19:37:16.886 - Starting background data reload
2025-06-01 19:37:16.909 - Starting background data loading thread
2025-06-01 19:37:16.919 - Background data loading started
2025-06-01 19:37:16.954 - Loading summary statistics
2025-06-01 19:37:16.989 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:37:16.991 - Data from GameStatsIntegration: {'total_earnings': 496.0, 'daily_earnings': 496.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-01 19:37:16.995 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:37:17.006 - Successfully loaded summary stats: {'total_earnings': 496.0, 'daily_earnings': 496.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-01 19:37:17.010 - Successfully loaded game history: 10 entries
2025-06-01 19:37:17.014 - Successfully loaded weekly stats: 7 days
2025-06-01 19:37:17.023 - Saved 3 items to cache
2025-06-01 19:37:17.023 - Background data loading completed
2025-06-01 19:42:54.719 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.151 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.169 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.174 - Forcing refresh of all stats data
2025-06-01 19:48:36.176 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:48:36.195 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.195 - Force refresh via GameStatsIntegration successful
2025-06-01 19:48:36.196 - Clearing cached data
2025-06-01 19:48:36.204 - Posted refresh_stats event to trigger UI update
2025-06-01 19:48:36.205 - Starting background data reload
2025-06-01 19:48:36.207 - Starting background data loading thread
2025-06-01 19:48:36.210 - Background data loading started
2025-06-01 19:48:36.211 - Loading summary statistics
2025-06-01 19:48:36.255 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:48:36.285 - Data from GameStatsIntegration: {'total_earnings': 544.0, 'daily_earnings': 544.0, 'daily_games': 19, 'wallet_balance': 0}
2025-06-01 19:48:36.285 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:48:36.286 - Successfully loaded summary stats: {'total_earnings': 544.0, 'daily_earnings': 544.0, 'daily_games': 19, 'wallet_balance': 0}
2025-06-01 19:48:36.292 - Successfully loaded game history: 10 entries
2025-06-01 19:48:36.299 - Successfully loaded weekly stats: 7 days
2025-06-01 19:48:36.307 - Saved 3 items to cache
2025-06-01 19:48:36.309 - Background data loading completed
2025-06-01 19:50:00.035 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.525 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.630 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.655 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.658 - Forcing refresh of all stats data
2025-06-01 19:55:10.663 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:55:10.763 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.764 - Force refresh via GameStatsIntegration successful
2025-06-01 19:55:10.776 - Clearing cached data
2025-06-01 19:55:10.794 - Posted refresh_stats event to trigger UI update
2025-06-01 19:55:10.800 - Starting background data reload
2025-06-01 19:55:10.813 - Starting background data loading thread
2025-06-01 19:55:10.822 - Background data loading started
2025-06-01 19:55:10.841 - Loading summary statistics
2025-06-01 19:55:10.889 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:55:10.892 - Data from GameStatsIntegration: {'total_earnings': 601.6, 'daily_earnings': 601.6, 'daily_games': 20, 'wallet_balance': 0}
2025-06-01 19:55:10.893 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:55:10.895 - Successfully loaded summary stats: {'total_earnings': 601.6, 'daily_earnings': 601.6, 'daily_games': 20, 'wallet_balance': 0}
2025-06-01 19:55:10.898 - Successfully loaded game history: 10 entries
2025-06-01 19:55:10.901 - Successfully loaded weekly stats: 7 days
2025-06-01 19:55:10.904 - Saved 3 items to cache
2025-06-01 19:55:10.909 - Background data loading completed
2025-06-01 19:56:42.203 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.572 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.677 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.696 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.699 - Forcing refresh of all stats data
2025-06-01 19:59:44.706 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:59:44.797 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.800 - Force refresh via GameStatsIntegration successful
2025-06-01 19:59:44.806 - Clearing cached data
2025-06-01 19:59:44.831 - Posted refresh_stats event to trigger UI update
2025-06-01 19:59:44.835 - Starting background data reload
2025-06-01 19:59:44.847 - Starting background data loading thread
2025-06-01 19:59:44.855 - Background data loading started
2025-06-01 19:59:44.879 - Loading summary statistics
2025-06-01 19:59:44.924 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:59:44.927 - Data from GameStatsIntegration: {'total_earnings': 668.8000000000001, 'daily_earnings': 668.8000000000001, 'daily_games': 21, 'wallet_balance': 0}
2025-06-01 19:59:44.929 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:59:44.931 - Successfully loaded summary stats: {'total_earnings': 668.8000000000001, 'daily_earnings': 668.8000000000001, 'daily_games': 21, 'wallet_balance': 0}
2025-06-01 19:59:44.933 - Successfully loaded game history: 10 entries
2025-06-01 19:59:44.936 - Successfully loaded weekly stats: 7 days
2025-06-01 19:59:44.941 - Saved 3 items to cache
2025-06-01 19:59:44.942 - Background data loading completed
2025-06-01 20:00:11.670 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.460 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.566 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.599 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.607 - Forcing refresh of all stats data
2025-06-01 20:03:44.616 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:03:44.708 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.723 - Force refresh via GameStatsIntegration successful
2025-06-01 20:03:44.728 - Clearing cached data
2025-06-01 20:03:44.730 - Posted refresh_stats event to trigger UI update
2025-06-01 20:03:44.737 - Starting background data reload
2025-06-01 20:03:44.749 - Starting background data loading thread
2025-06-01 20:03:44.755 - Background data loading started
2025-06-01 20:03:44.787 - Loading summary statistics
2025-06-01 20:03:44.814 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:03:44.815 - Data from GameStatsIntegration: {'total_earnings': 736.0000000000001, 'daily_earnings': 736.0000000000001, 'daily_games': 22, 'wallet_balance': 0}
2025-06-01 20:03:44.821 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:03:44.844 - Successfully loaded summary stats: {'total_earnings': 736.0000000000001, 'daily_earnings': 736.0000000000001, 'daily_games': 22, 'wallet_balance': 0}
2025-06-01 20:03:44.849 - Successfully loaded game history: 10 entries
2025-06-01 20:03:44.855 - Successfully loaded weekly stats: 7 days
2025-06-01 20:03:44.867 - Saved 3 items to cache
2025-06-01 20:03:44.873 - Background data loading completed
2025-06-01 20:06:13.066 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.530 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.728 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.762 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.931 - Forcing refresh of all stats data
2025-06-01 20:10:49.935 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:10:49.998 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:50.031 - Force refresh via GameStatsIntegration successful
2025-06-01 20:10:50.035 - Clearing cached data
2025-06-01 20:10:50.044 - Posted refresh_stats event to trigger UI update
2025-06-01 20:10:50.048 - Starting background data reload
2025-06-01 20:10:50.061 - Starting background data loading thread
2025-06-01 20:10:50.066 - Background data loading started
2025-06-01 20:10:50.081 - Loading summary statistics
2025-06-01 20:10:50.089 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:10:50.108 - Data from GameStatsIntegration: {'total_earnings': 788.8000000000001, 'daily_earnings': 788.8000000000001, 'daily_games': 23, 'wallet_balance': 0}
2025-06-01 20:10:50.142 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:10:50.144 - Successfully loaded summary stats: {'total_earnings': 788.8000000000001, 'daily_earnings': 788.8000000000001, 'daily_games': 23, 'wallet_balance': 0}
2025-06-01 20:10:50.148 - Successfully loaded game history: 10 entries
2025-06-01 20:10:50.149 - Successfully loaded weekly stats: 7 days
2025-06-01 20:10:50.152 - Saved 3 items to cache
2025-06-01 20:10:50.161 - Background data loading completed
2025-06-01 20:14:05.932 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:35.970 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.072 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.095 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.106 - Forcing refresh of all stats data
2025-06-01 20:18:36.115 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:18:36.217 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.226 - Force refresh via GameStatsIntegration successful
2025-06-01 20:18:36.232 - Clearing cached data
2025-06-01 20:18:36.239 - Posted refresh_stats event to trigger UI update
2025-06-01 20:18:36.241 - Starting background data reload
2025-06-01 20:18:36.244 - Starting background data loading thread
2025-06-01 20:18:36.261 - Background data loading started
2025-06-01 20:18:36.282 - Loading summary statistics
2025-06-01 20:18:36.338 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:18:36.345 - Data from GameStatsIntegration: {'total_earnings': 846.4000000000001, 'daily_earnings': 846.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-01 20:18:36.348 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:18:36.351 - Successfully loaded summary stats: {'total_earnings': 846.4000000000001, 'daily_earnings': 846.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-01 20:18:36.362 - Successfully loaded game history: 10 entries
2025-06-01 20:18:36.369 - Successfully loaded weekly stats: 7 days
2025-06-01 20:18:36.374 - Saved 3 items to cache
2025-06-01 20:18:36.377 - Background data loading completed
2025-06-01 20:20:19.077 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.316 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.438 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.462 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.471 - Forcing refresh of all stats data
2025-06-01 20:26:34.480 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:26:34.657 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.665 - Force refresh via GameStatsIntegration successful
2025-06-01 20:26:34.671 - Clearing cached data
2025-06-01 20:26:34.679 - Posted refresh_stats event to trigger UI update
2025-06-01 20:26:34.685 - Starting background data reload
2025-06-01 20:26:34.689 - Starting background data loading thread
2025-06-01 20:26:34.693 - Background data loading started
2025-06-01 20:26:34.702 - Loading summary statistics
2025-06-01 20:26:34.734 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:26:34.767 - Data from GameStatsIntegration: {'total_earnings': 908.8000000000001, 'daily_earnings': 908.8000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-01 20:26:34.779 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:26:34.782 - Successfully loaded summary stats: {'total_earnings': 908.8000000000001, 'daily_earnings': 908.8000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-01 20:26:34.787 - Successfully loaded game history: 10 entries
2025-06-01 20:26:34.795 - Successfully loaded weekly stats: 7 days
2025-06-01 20:26:34.804 - Saved 3 items to cache
2025-06-01 20:26:34.808 - Background data loading completed
2025-06-01 20:27:31.017 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.026 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.696 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.712 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.717 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.718 - Forcing refresh of all stats data
2025-06-01 20:30:15.719 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:30:15.734 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.735 - Force refresh via GameStatsIntegration successful
2025-06-01 20:30:15.736 - Clearing cached data
2025-06-01 20:30:15.737 - Posted refresh_stats event to trigger UI update
2025-06-01 20:30:15.737 - Starting background data reload
2025-06-01 20:30:15.737 - Starting background data loading thread
2025-06-01 20:30:15.741 - Background data loading started
2025-06-01 20:30:15.757 - Loading summary statistics
2025-06-01 20:30:15.779 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:30:15.782 - Data from GameStatsIntegration: {'total_earnings': 956.8000000000001, 'daily_earnings': 956.8000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-01 20:30:15.790 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:30:15.791 - Successfully loaded summary stats: {'total_earnings': 956.8000000000001, 'daily_earnings': 956.8000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-01 20:30:15.792 - Successfully loaded game history: 10 entries
2025-06-01 20:30:15.793 - Successfully loaded weekly stats: 7 days
2025-06-01 20:30:15.796 - Saved 3 items to cache
2025-06-01 20:30:15.797 - Background data loading completed
2025-06-01 20:34:28.976 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.024 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.028 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.033 - Forcing refresh of all stats data
2025-06-01 20:34:29.036 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:34:29.115 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.126 - Force refresh via GameStatsIntegration successful
2025-06-01 20:34:29.133 - Clearing cached data
2025-06-01 20:34:29.138 - Posted refresh_stats event to trigger UI update
2025-06-01 20:34:29.146 - Starting background data reload
2025-06-01 20:34:29.152 - Starting background data loading thread
2025-06-01 20:34:29.160 - Background data loading started
2025-06-01 20:34:29.178 - Loading summary statistics
2025-06-01 20:34:29.222 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:34:29.223 - Data from GameStatsIntegration: {'total_earnings': 1004.8000000000001, 'daily_earnings': 1004.8000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-01 20:34:29.228 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:34:29.235 - Successfully loaded summary stats: {'total_earnings': 1004.8000000000001, 'daily_earnings': 1004.8000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-01 20:34:29.254 - Successfully loaded game history: 10 entries
2025-06-01 20:34:29.258 - Successfully loaded weekly stats: 7 days
2025-06-01 20:34:29.273 - Saved 3 items to cache
2025-06-01 20:34:29.278 - Background data loading completed
2025-06-01 20:35:22.286 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.769 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.786 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.791 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.793 - Forcing refresh of all stats data
2025-06-01 20:37:59.795 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:37:59.810 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.811 - Force refresh via GameStatsIntegration successful
2025-06-01 20:37:59.812 - Clearing cached data
2025-06-01 20:37:59.812 - Posted refresh_stats event to trigger UI update
2025-06-01 20:37:59.813 - Starting background data reload
2025-06-01 20:37:59.815 - Starting background data loading thread
2025-06-01 20:37:59.818 - Background data loading started
2025-06-01 20:37:59.828 - Loading summary statistics
2025-06-01 20:37:59.856 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:37:59.856 - Data from GameStatsIntegration: {'total_earnings': 1048.0, 'daily_earnings': 1048.0, 'daily_games': 28, 'wallet_balance': 0}
2025-06-01 20:37:59.857 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:37:59.857 - Successfully loaded summary stats: {'total_earnings': 1048.0, 'daily_earnings': 1048.0, 'daily_games': 28, 'wallet_balance': 0}
2025-06-01 20:37:59.857 - Successfully loaded game history: 10 entries
2025-06-01 20:37:59.858 - Successfully loaded weekly stats: 7 days
2025-06-01 20:37:59.859 - Saved 3 items to cache
2025-06-01 20:37:59.860 - Background data loading completed
2025-06-01 20:40:10.639 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.139 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.247 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.260 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.267 - Forcing refresh of all stats data
2025-06-01 20:45:24.292 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:45:24.468 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.476 - Force refresh via GameStatsIntegration successful
2025-06-01 20:45:24.484 - Clearing cached data
2025-06-01 20:45:24.619 - Posted refresh_stats event to trigger UI update
2025-06-01 20:45:24.629 - Starting background data reload
2025-06-01 20:45:24.632 - Starting background data loading thread
2025-06-01 20:45:24.643 - Background data loading started
2025-06-01 20:45:24.660 - Loading summary statistics
2025-06-01 20:45:24.710 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:45:24.719 - Data from GameStatsIntegration: {'total_earnings': 1091.2, 'daily_earnings': 1091.2, 'daily_games': 29, 'wallet_balance': 0}
2025-06-01 20:45:24.722 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:45:24.725 - Successfully loaded summary stats: {'total_earnings': 1091.2, 'daily_earnings': 1091.2, 'daily_games': 29, 'wallet_balance': 0}
2025-06-01 20:45:24.728 - Successfully loaded game history: 10 entries
2025-06-01 20:45:24.730 - Successfully loaded weekly stats: 7 days
2025-06-01 20:45:24.734 - Saved 3 items to cache
2025-06-01 20:45:24.753 - Background data loading completed
2025-06-01 20:46:43.540 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.752 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.771 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.774 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.775 - Forcing refresh of all stats data
2025-06-01 20:49:19.776 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:49:19.792 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.794 - Force refresh via GameStatsIntegration successful
2025-06-01 20:49:19.795 - Clearing cached data
2025-06-01 20:49:19.795 - Posted refresh_stats event to trigger UI update
2025-06-01 20:49:19.796 - Starting background data reload
2025-06-01 20:49:19.798 - Starting background data loading thread
2025-06-01 20:49:19.800 - Background data loading started
2025-06-01 20:49:19.811 - Loading summary statistics
2025-06-01 20:49:19.839 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:49:19.840 - Data from GameStatsIntegration: {'total_earnings': 1139.2, 'daily_earnings': 1139.2, 'daily_games': 30, 'wallet_balance': 0}
2025-06-01 20:49:19.840 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:49:19.841 - Successfully loaded summary stats: {'total_earnings': 1139.2, 'daily_earnings': 1139.2, 'daily_games': 30, 'wallet_balance': 0}
2025-06-01 20:49:19.841 - Successfully loaded game history: 10 entries
2025-06-01 20:49:19.842 - Successfully loaded weekly stats: 7 days
2025-06-01 20:49:19.844 - Saved 3 items to cache
2025-06-01 20:49:19.844 - Background data loading completed
2025-06-01 20:52:18.922 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.758 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.887 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.912 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.914 - Forcing refresh of all stats data
2025-06-01 20:55:23.921 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:55:24.018 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:24.028 - Force refresh via GameStatsIntegration successful
2025-06-01 20:55:24.039 - Clearing cached data
2025-06-01 20:55:24.042 - Posted refresh_stats event to trigger UI update
2025-06-01 20:55:24.046 - Starting background data reload
2025-06-01 20:55:24.050 - Starting background data loading thread
2025-06-01 20:55:24.052 - Background data loading started
2025-06-01 20:55:24.065 - Loading summary statistics
2025-06-01 20:55:24.432 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:55:24.446 - Data from GameStatsIntegration: {'total_earnings': 1187.2, 'daily_earnings': 1187.2, 'daily_games': 31, 'wallet_balance': 0}
2025-06-01 20:55:24.448 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:55:24.453 - Successfully loaded summary stats: {'total_earnings': 1187.2, 'daily_earnings': 1187.2, 'daily_games': 31, 'wallet_balance': 0}
2025-06-01 20:55:24.460 - Successfully loaded game history: 10 entries
2025-06-01 20:55:24.468 - Successfully loaded weekly stats: 7 days
2025-06-01 20:55:24.477 - Saved 3 items to cache
2025-06-01 20:55:24.481 - Background data loading completed
2025-06-01 20:56:25.788 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.753 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.768 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.770 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.772 - Forcing refresh of all stats data
2025-06-01 20:59:51.776 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:59:51.791 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.792 - Force refresh via GameStatsIntegration successful
2025-06-01 20:59:51.793 - Clearing cached data
2025-06-01 20:59:51.793 - Posted refresh_stats event to trigger UI update
2025-06-01 20:59:51.794 - Starting background data reload
2025-06-01 20:59:51.796 - Starting background data loading thread
2025-06-01 20:59:51.797 - Background data loading started
2025-06-01 20:59:51.808 - Loading summary statistics
2025-06-01 20:59:51.837 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:59:51.838 - Data from GameStatsIntegration: {'total_earnings': 1235.2, 'daily_earnings': 1235.2, 'daily_games': 32, 'wallet_balance': 0}
2025-06-01 20:59:51.838 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:59:51.838 - Successfully loaded summary stats: {'total_earnings': 1235.2, 'daily_earnings': 1235.2, 'daily_games': 32, 'wallet_balance': 0}
2025-06-01 20:59:51.839 - Successfully loaded game history: 10 entries
2025-06-01 20:59:51.840 - Successfully loaded weekly stats: 7 days
2025-06-01 20:59:51.842 - Saved 3 items to cache
2025-06-01 20:59:51.843 - Background data loading completed
2025-06-01 21:03:31.709 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.203 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.219 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.224 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.225 - Forcing refresh of all stats data
2025-06-01 21:06:51.228 - Attempting to force refresh via GameStatsIntegration
2025-06-01 21:06:51.244 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.245 - Force refresh via GameStatsIntegration successful
2025-06-01 21:06:51.246 - Clearing cached data
2025-06-01 21:06:51.247 - Posted refresh_stats event to trigger UI update
2025-06-01 21:06:51.248 - Starting background data reload
2025-06-01 21:06:51.250 - Starting background data loading thread
2025-06-01 21:06:51.250 - Background data loading started
2025-06-01 21:06:51.261 - Loading summary statistics
2025-06-01 21:06:51.293 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 21:06:51.293 - Data from GameStatsIntegration: {'total_earnings': 1275.2, 'daily_earnings': 1275.2, 'daily_games': 33, 'wallet_balance': 0}
2025-06-01 21:06:51.293 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 21:06:51.294 - Successfully loaded summary stats: {'total_earnings': 1275.2, 'daily_earnings': 1275.2, 'daily_games': 33, 'wallet_balance': 0}
2025-06-01 21:06:51.294 - Successfully loaded game history: 10 entries
2025-06-01 21:06:51.295 - Successfully loaded weekly stats: 7 days
2025-06-01 21:06:51.296 - Saved 3 items to cache
2025-06-01 21:06:51.297 - Background data loading completed
2025-06-02 09:24:09.345 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.386 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.414 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.415 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-02 09:24:09.415 - Initializing StatsDataProvider
2025-06-02 09:24:09.416 - Loaded 8 items from cache
2025-06-02 09:24:09.417 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-02 09:24:09.417 - Starting background data loading thread
2025-06-02 09:24:09.418 - StatsDataProvider initialization completed
2025-06-02 09:24:09.418 - Background data loading started
2025-06-02 09:24:09.418 - Created singleton instance of StatsDataProvider on module import
2025-06-02 09:24:09.419 - Loading summary statistics
2025-06-02 09:24:09.419 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-02 09:24:09.419 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 09:24:09.420 - get_stats_provider called, returning provider with initialized=True
2025-06-02 09:24:09.420 - Data from GameStatsIntegration: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.421 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 09:24:09.421 - Successfully loaded summary stats: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.422 - Successfully loaded game history: 10 entries
2025-06-02 09:24:09.422 - Successfully loaded weekly stats: 7 days
2025-06-02 09:24:09.425 - Saved 10 items to cache
2025-06-02 09:24:09.425 - Background data loading completed
