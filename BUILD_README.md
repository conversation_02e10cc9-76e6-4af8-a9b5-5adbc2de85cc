# WOW Bingo Game - Build Guide

This guide explains how to build the WOW Bingo Game into a standalone executable that can run on any Windows PC without requiring Python installation.

## 🚀 Quick Start

### Option 1: Easy Build (Recommended)
1. **Double-click** `build_game.bat`
2. **Select option 1** (Quick Build)
3. **Wait** for the build to complete
4. **Find your executable** in the `dist` folder

### Option 2: Command Line
```bash
# Install dependencies and build
python build_executable.py --install-deps

# Or just build (if dependencies are already installed)
python build_executable.py
```

## 📋 Prerequisites

### Required Software
- **Python 3.7+** - [Download from python.org](https://www.python.org/downloads/)
- **Windows 10/11** (recommended)
- **At least 2GB free disk space** for build process
- **Internet connection** for downloading dependencies

### Verify Python Installation
```bash
python --version
pip --version
```

## 🛠️ Build Options

### 1. Quick Build (Default)
```bash
python build_executable.py
```
- Uses PyInstaller
- Creates single executable file
- Includes all assets and dependencies
- **Recommended for most users**

### 2. Install Dependencies First
```bash
python build_executable.py --install-deps
```
- Automatically installs all required packages
- Then builds the executable
- **Use this if you get dependency errors**

### 3. Clean Build
```bash
python build_executable.py --clean
```
- Removes previous build files
- Starts fresh build process
- **Use this if previous builds failed**

### 4. Optimized Build
```bash
python build_executable.py --optimize
```
- Enables maximum optimizations
- Smaller executable size
- **Takes longer to build**

### 5. Test Build
```bash
python build_executable.py --test --verbose
```
- Builds and tests the executable
- Shows detailed output
- **Good for troubleshooting**

### 6. Nuitka Build (Alternative)
```bash
python build_executable.py --tool nuitka --verbose
```
- Uses Nuitka compiler instead of PyInstaller
- May produce smaller executables
- **Experimental - use if PyInstaller fails**

## 📁 Output Files

After a successful build, you'll find these files in the `dist` directory:

- **`WOW_Bingo_Game.exe`** - The main executable (this is what you distribute)
- **`build_report.json`** - Build details and statistics

## 🎯 Distribution

### What to Distribute
- **Only the `.exe` file** - Everything is bundled inside
- The executable is **completely standalone**
- **No additional files needed** on target PCs

### System Requirements for End Users
- **Windows 7/8/10/11** (32-bit or 64-bit)
- **No Python installation required**
- **No additional software required**
- **Minimum 100MB free disk space**

## 🔧 Troubleshooting

### Common Issues and Solutions

#### "Python not found"
```bash
# Install Python from python.org
# Make sure to check "Add Python to PATH" during installation
```

#### "Module not found" errors
```bash
# Install dependencies first
python build_executable.py --install-deps
```

#### "Build failed" with PyInstaller
```bash
# Try Nuitka instead
python build_executable.py --tool nuitka --verbose
```

#### Executable is too large (>200MB)
```bash
# Use optimized build
python build_executable.py --optimize --clean
```

#### Executable won't run on other PCs
- Make sure target PC has **Visual C++ Redistributable** installed
- Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe

### Build Logs
Check these files for detailed error information:
- Console output during build
- `dist/build_report.json` for build statistics

## 🎮 Testing Your Build

### Local Testing
1. **Navigate to** `dist` folder
2. **Double-click** `WOW_Bingo_Game.exe`
3. **Verify** the game starts correctly
4. **Test** basic functionality (start game, play sounds, etc.)

### Testing on Other PCs
1. **Copy** `WOW_Bingo_Game.exe` to another Windows PC
2. **Run** the executable
3. **Verify** all features work correctly

## 📊 Build Statistics

Typical build results:
- **Build time**: 2-5 minutes
- **Executable size**: 80-150 MB
- **Startup time**: 3-8 seconds
- **Memory usage**: 50-100 MB

## 🔄 Advanced Configuration

### Custom PyInstaller Spec
Edit `WOW_Bingo_Game.spec` to customize:
- Hidden imports
- Data files inclusion
- Executable metadata
- Icon and version info

### Environment Variables
Set these before building for custom behavior:
```bash
set PYTHONOPTIMIZE=1  # Enable Python optimizations
set UPX_DIR=C:\upx    # Custom UPX location for compression
```

## 📝 Build Script Features

The build system includes:
- ✅ **Automatic dependency detection**
- ✅ **Asset bundling and verification**
- ✅ **Multiple build tool support**
- ✅ **Comprehensive error handling**
- ✅ **Build verification and testing**
- ✅ **Detailed progress reporting**
- ✅ **Clean build environment management**

## 🆘 Getting Help

If you encounter issues:

1. **Check this README** for common solutions
2. **Run with verbose output**: `python build_executable.py --verbose`
3. **Try clean build**: `python build_executable.py --clean`
4. **Install dependencies**: `python build_executable.py --install-deps`
5. **Check Python version**: Must be 3.7 or higher

## 📄 Files Overview

- **`build_executable.py`** - Main build script
- **`build_game.bat`** - Easy Windows batch file
- **`WOW_Bingo_Game.spec`** - PyInstaller configuration
- **`build_requirements.txt`** - Build dependencies
- **`BUILD_README.md`** - This guide

## 🎉 Success!

Once built successfully, your `WOW_Bingo_Game.exe` can be:
- ✅ **Copied to any Windows PC**
- ✅ **Run without Python installation**
- ✅ **Distributed to end users**
- ✅ **Used commercially** (check license)

The executable includes everything needed to run the game, including all assets, audio files, and splash screen images.
