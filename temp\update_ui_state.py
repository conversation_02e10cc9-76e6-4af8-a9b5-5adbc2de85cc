#!/usr/bin/env python
"""
Script to update Board_selection_fixed.py to use save_ui_state and load_ui_state 
instead of save_remembered_cartellas and load_remembered_cartellas
"""

import os
import re
import shutil
import datetime

# Define file paths
file_path = 'Board_selection_fixed.py'
backup_path = f'Board_selection_fixed.py.{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.bak'

# First, create a backup
print(f"Creating backup at {backup_path}")
shutil.copy2(file_path, backup_path)

# Load the file
print(f"Reading {file_path}")
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# Function to replace save_remembered_cartellas with save_ui_state
def replace_save_remembered_cartellas(match):
    # Extract the arguments
    args = match.group(1).strip()
    # Split by commas but respecting parentheses and brackets
    import re
    args_list = []
    start = 0
    paren_level = 0
    for i, char in enumerate(args):
        if char == ',' and paren_level == 0:
            args_list.append(args[start:i].strip())
            start = i + 1
        elif char in '([{':
            paren_level += 1
        elif char in ')]}':
            paren_level -= 1
    args_list.append(args[start:].strip())
    
    # Change the function name
    return f'save_ui_state({args})'

# Function to replace load_remembered_cartellas with load_ui_state
def replace_load_remembered_cartellas(match):
    # Change the function name
    return 'load_ui_state()'

# Replace the function call pattern for saving
save_pattern = r'save_remembered_cartellas\((.*?)\)'
content = re.sub(save_pattern, replace_save_remembered_cartellas, content)

# Replace the function call pattern for loading
load_pattern = r'load_remembered_cartellas\(\)'
content = re.sub(load_pattern, replace_load_remembered_cartellas, content)

# Replace import statements
import_pattern = r'from player_storage import (save_remembered_cartellas|load_remembered_cartellas)'
content = re.sub(import_pattern, r'from player_storage import save_ui_state, load_ui_state', content)

# Also handle cases where both are imported in one line
both_import_pattern = r'from player_storage import load_remembered_cartellas, save_remembered_cartellas'
content = re.sub(both_import_pattern, r'from player_storage import load_ui_state, save_ui_state', content)

# Update references to "remembered cartella numbers" in log statements
content = content.replace("remembered cartella numbers", "UI state")

# Write the updated content back to the file
print(f"Writing updated content to {file_path}")
with open(file_path, 'w', encoding='utf-8') as f:
    f.write(content)

print(f"Update complete! Backup saved at {backup_path}") 