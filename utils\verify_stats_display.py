#!/usr/bin/env python3
"""
Script to verify that the inserted stats data will be displayed correctly on the stats page.
This simulates how the stats page retrieves and formats the data.
"""

import sqlite3
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def verify_database_data(db_path):
    """Verify the data in a specific database."""
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n📊 Verifying data in: {db_path}")
        
        # Get all data from daily_stats
        cursor.execute('''
        SELECT date, earnings, games_played 
        FROM daily_stats 
        WHERE date BETWEEN '2025-05-26' AND '2025-06-01'
        ORDER BY date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   📋 Historical Data:")
            print("   Date       | Day | Earnings | Games")
            print("   -----------|-----|----------|------")
            
            total_earnings = 0
            for date_str, earnings, games in results:
                try:
                    dt = datetime.strptime(date_str, '%Y-%m-%d')
                    day_name = dt.strftime('%a')
                    total_earnings += earnings
                    print(f"   {date_str} | {day_name} | {earnings:>8.1f} | {games:>5}")
                except:
                    print(f"   {date_str}     | ??? | {earnings:>8.1f} | {games:>5}")
            
            print("   -----------|-----|----------|------")
            print(f"   TOTAL      |     | {total_earnings:>8.1f} |")
            
        else:
            print("   ❌ No data found in the specified date range.")
            conn.close()
            return False
        
        # Test weekly stats retrieval (simulating stats page logic)
        print("\n   📈 Weekly Stats Simulation:")
        today = datetime.now()
        weekly_data = []
        
        for i in range(7):
            day = today - timedelta(days=6-i)
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%a %m/%d')
            
            # Get earnings for this day
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (day_str,))
            result = cursor.fetchone()
            earnings = result[0] if result else 0
            
            weekly_data.append({
                'date': day_str,
                'name': day_name,
                'earnings': earnings
            })
        
        print("   Last 7 Days (as displayed on stats page):")
        print("   Day        | Earnings")
        print("   -----------|---------")
        
        weekly_total = 0
        for day_data in weekly_data:
            weekly_total += day_data['earnings']
            print(f"   {day_data['name']:<10} | {day_data['earnings']:>8.1f}")
        
        print("   -----------|---------")
        print(f"   TOTAL      | {weekly_total:>8.1f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying {db_path}: {e}")
        return False

def simulate_stats_page_data():
    """Simulate how the stats page would retrieve and display the data."""
    print("\n🎯 Stats Page Display Simulation")
    print("=" * 50)
    
    # This simulates the stats page logic for getting weekly stats
    db_path = 'data/stats.db'  # Primary database used by stats page
    
    if not os.path.exists(db_path):
        print(f"❌ Primary database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get today's date
        today = datetime.now()
        
        # Get last 7 days of data (as the stats page does)
        print("📊 Weekly Earnings Cards (as shown on stats page):")
        print("   Card | Day Name | Date       | Earnings")
        print("   -----|----------|------------|----------")
        
        total_week_earnings = 0
        for i in range(7):
            day = today - timedelta(days=6-i)
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%a')  # Short day name
            display_date = day.strftime('%m/%d')  # Month/day format
            
            # Get earnings for this day
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (day_str,))
            result = cursor.fetchone()
            earnings = result[0] if result else 0
            total_week_earnings += earnings
            
            print(f"   {i+1:>4} | {day_name:<8} | {display_date:<10} | {earnings:>8.1f} ETB")
        
        print("   -----|----------|------------|----------")
        print(f"   TOTAL|          |            | {total_week_earnings:>8.1f} ETB")
        
        # Get total earnings (all time)
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        total_all_time = cursor.fetchone()[0] or 0
        
        # Get today's earnings
        today_str = today.strftime('%Y-%m-%d')
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today_str,))
        today_result = cursor.fetchone()
        today_earnings = today_result[0] if today_result else 0
        
        # Get today's games
        cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today_str,))
        today_games_result = cursor.fetchone()
        today_games = today_games_result[0] if today_games_result else 0
        
        print(f"\n📈 Summary Cards (as shown on stats page):")
        print(f"   TOTAL EARNING:    {total_all_time:>8.1f} ETB")
        print(f"   Daily GAMES PLAYED: {today_games:>6}")
        print(f"   Daily Earning:    {today_earnings:>8.1f} ETB")
        print(f"   WALLET BALANCE:   [From wallet system]")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error simulating stats page: {e}")
        return False

def main():
    """Main function."""
    print("🔍 Stats Display Verification")
    print("=" * 60)
    
    # Verify data in both databases
    databases = ['data/stats.db', 'data/stats_new.db']
    
    for db_path in databases:
        verify_database_data(db_path)
    
    # Simulate stats page display
    simulate_stats_page_data()
    
    print("\n✅ Verification completed!")
    print("💡 The historical data should now be visible on the stats page.")
    print("📝 Note: The data will appear in the weekly earnings cards and summary.")

if __name__ == "__main__":
    main()
