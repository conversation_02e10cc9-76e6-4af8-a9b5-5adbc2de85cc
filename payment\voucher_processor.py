"""
Voucher Processor Module

Handles processing of voucher codes in the game, including external vouchers bound to specific machine UUIDs.
"""

import os
import sys
import time
import platform
import uuid
import json
import subprocess
from datetime import datetime

# Try importing the validator directly
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from voucher_validator import VoucherValidator
    VALIDATOR_IMPORTED = True
except ImportError:
    VALIDATOR_IMPORTED = False
    print("Warning: VoucherValidator not imported. Using limited validation.")

class VoucherProcessor:
    """Processes voucher codes for in-game credit recharges."""

    def __init__(self):
        """Initialize the voucher processor."""
        self.validator = VoucherValidator() if VALIDATOR_IMPORTED else None
        self.redeemed_vouchers_path = os.path.join('data', 'redeemed_vouchers.json')
        self.machine_uuid = self._get_machine_uuid()

        # Create data directory if it doesn't exist
        os.makedirs('data', exist_ok=True)

    def _get_machine_uuid(self):
        """
        Get the machine UUID from the system.

        Returns:
            str: Machine UUID
        """
        # First, try to load cached UUID
        cached_uuid_path = os.path.join('data', 'machine_uuid.txt')
        if os.path.exists(cached_uuid_path):
            try:
                with open(cached_uuid_path, 'r') as f:
                    return f.read().strip()
            except Exception as e:
                print(f"Warning: Could not load cached UUID: {e}")

        # Try multiple methods to get a reliable UUID
        machine_id = None

        try:
            system = platform.system().lower()

            if system == 'windows':
                # Method 1: Try WMI (if available)
                try:
                    import wmi
                    c = wmi.WMI()
                    for system_info in c.Win32_ComputerSystemProduct():
                        if system_info.UUID and system_info.UUID != "00000000-0000-0000-0000-000000000000":
                            machine_id = system_info.UUID.upper()
                            print(f"Got UUID from WMI: {machine_id}")
                            break
                except ImportError:
                    print("WMI not available, trying wmic...")
                except Exception as e:
                    print(f"WMI error: {e}")

                # Method 2: Use wmic command
                if not machine_id:
                    try:
                        result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'],
                                            capture_output=True, text=True, check=True, timeout=10)

                        lines = result.stdout.strip().split('\n')
                        if len(lines) >= 2:
                            candidate = lines[1].strip().upper()
                            if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                machine_id = candidate
                                print(f"Got UUID from wmic: {machine_id}")
                    except Exception as e:
                        print(f"wmic error: {e}")

                # Method 3: Try Windows registry
                if not machine_id:
                    try:
                        import winreg
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                           r"SOFTWARE\Microsoft\Cryptography") as key:
                            machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                            if machine_guid:
                                machine_id = machine_guid.upper()
                                print(f"Got UUID from registry: {machine_id}")
                    except Exception as e:
                        print(f"Registry error: {e}")

            elif system == 'linux':
                # Method 1: Try machine-id
                try:
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            candidate = f.read().strip()
                            if candidate:
                                machine_id = candidate.upper()
                                print(f"Got UUID from machine-id: {machine_id}")
                except Exception as e:
                    print(f"machine-id error: {e}")

                # Method 2: Try DMI UUID
                if not machine_id:
                    try:
                        if os.path.exists('/sys/class/dmi/id/product_uuid'):
                            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                                candidate = f.read().strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    machine_id = candidate.upper()
                                    print(f"Got UUID from DMI: {machine_id}")
                    except Exception as e:
                        print(f"DMI error: {e}")

            elif system == 'darwin':  # macOS
                try:
                    result = subprocess.run(['ioreg', '-rd1', '-c', 'IOPlatformExpertDevice'],
                                         capture_output=True, text=True, check=True, timeout=10)

                    for line in result.stdout.split('\n'):
                        if 'IOPlatformUUID' in line:
                            parts = line.split('"')
                            if len(parts) >= 4:
                                candidate = parts[3].strip()
                                if candidate and candidate != "00000000-0000-0000-0000-000000000000":
                                    machine_id = candidate.upper()
                                    print(f"Got UUID from ioreg: {machine_id}")
                                    break
                except Exception as e:
                    print(f"ioreg error: {e}")

        except Exception as e:
            print(f"Error during UUID detection: {e}")

        # If we got a valid UUID, cache and return it
        if machine_id:
            self._cache_machine_uuid(machine_id)
            return machine_id

        # Fallback: Generate a consistent machine fingerprint
        try:
            import hashlib

            # Collect machine-specific information
            fingerprint_data = []
            fingerprint_data.append(platform.node() or "unknown")
            fingerprint_data.append(platform.machine() or "unknown")
            fingerprint_data.append(platform.processor() or "unknown")
            fingerprint_data.append(platform.system() or "unknown")

            # Add MAC address if available
            try:
                mac = uuid.getnode()
                fingerprint_data.append(str(mac))
            except:
                fingerprint_data.append("no-mac")

            # Create consistent hash
            fingerprint_string = "-".join(fingerprint_data)
            hash_obj = hashlib.sha256(fingerprint_string.encode())
            fingerprint_hash = hash_obj.hexdigest()[:32].upper()

            # Format as UUID-like string
            fallback_id = f"{fingerprint_hash[:8]}-{fingerprint_hash[8:12]}-{fingerprint_hash[12:16]}-{fingerprint_hash[16:20]}-{fingerprint_hash[20:32]}"

            print(f"Generated consistent machine fingerprint: {fallback_id}")
            self._cache_machine_uuid(fallback_id)
            return fallback_id

        except Exception as e:
            print(f"Error generating machine fingerprint: {e}")

            # Ultimate fallback: random UUID (but cache it for consistency)
            fallback_id = str(uuid.uuid4()).upper()
            print(f"Using random UUID as last resort: {fallback_id}")
            self._cache_machine_uuid(fallback_id)
            return fallback_id

    def _cache_machine_uuid(self, uuid_str):
        """
        Cache the machine UUID to a file.

        Args:
            uuid_str: UUID to cache
        """
        try:
            cached_uuid_path = os.path.join('data', 'machine_uuid.txt')
            with open(cached_uuid_path, 'w') as f:
                f.write(uuid_str)
        except Exception as e:
            print(f"Warning: Could not cache UUID: {e}")

    def process_voucher(self, voucher_code):
        """
        Process a voucher code.

        Args:
            voucher_code: Voucher code to process

        Returns:
            dict: Processing result
        """
        # Clean up the voucher code
        voucher_code = voucher_code.strip().upper().replace('-', '')

        # First, check if the voucher has already been redeemed
        if self._is_voucher_redeemed(voucher_code):
            return {
                "success": False,
                "message": "Voucher has already been redeemed",
                "code": "already_redeemed"
            }

        # Validate the voucher
        if self.validator:
            # Use the validator with machine UUID check
            validation = self.validator.validate_voucher(voucher_code, self.machine_uuid)

            if not validation.get("valid", False):
                return {
                    "success": False,
                    "message": validation.get("message", "Invalid voucher"),
                    "code": "invalid_voucher"
                }

            # Extract voucher details
            amount = validation.get("amount", 0)
            share = validation.get("share", 80)
            expiry = validation.get("expiry", 0)

            # Mark the voucher as redeemed
            self._mark_voucher_redeemed(voucher_code, amount, share)

            return {
                "success": True,
                "message": "Voucher redeemed successfully",
                "amount": amount,
                "share": share,
                "code": "success"
            }

        else:
            # Limited validation without the validator module
            # Just check format and record redemption
            # This is a minimal validation that should be enhanced
            if not (10 <= len(voucher_code) <= 15):
                return {
                    "success": False,
                    "message": "Invalid voucher format",
                    "code": "invalid_format"
                }

            # Mark the voucher as redeemed (with default values)
            self._mark_voucher_redeemed(voucher_code, 100, 80)

            return {
                "success": True,
                "message": "Voucher processed (limited validation)",
                "amount": 100,  # Default amount
                "share": 80,    # Default share
                "code": "limited_validation"
            }

    def _is_voucher_redeemed(self, voucher_code):
        """
        Check if a voucher has already been redeemed.

        Args:
            voucher_code: Voucher code to check

        Returns:
            bool: True if already redeemed
        """
        # Load redeemed vouchers
        redeemed_vouchers = self._load_redeemed_vouchers()

        # Check if the voucher is already in the list
        return voucher_code in redeemed_vouchers

    def _mark_voucher_redeemed(self, voucher_code, amount, share):
        """
        Mark a voucher as redeemed.

        Args:
            voucher_code: Voucher code to mark
            amount: Credit amount
            share: Commission share percentage
        """
        # Load redeemed vouchers
        redeemed_vouchers = self._load_redeemed_vouchers()

        # Add the new voucher
        redeemed_vouchers[voucher_code] = {
            "redeemed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "amount": amount,
            "share": share,
            "machine_uuid": self.machine_uuid
        }

        # Save the updated list
        try:
            with open(self.redeemed_vouchers_path, 'w') as f:
                json.dump(redeemed_vouchers, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save redeemed vouchers: {e}")

    def _load_redeemed_vouchers(self):
        """
        Load the list of redeemed vouchers.

        Returns:
            dict: Redeemed vouchers
        """
        if os.path.exists(self.redeemed_vouchers_path):
            try:
                with open(self.redeemed_vouchers_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Warning: Could not load redeemed vouchers: {e}")

        return {}

    def get_machine_info(self):
        """
        Get information about the current machine.

        Returns:
            dict: Machine information
        """
        return {
            "uuid": self.machine_uuid,
            "system": platform.system(),
            "version": platform.version(),
            "processor": platform.processor(),
            "node": platform.node()
        }


def main():
    """Command-line interface for voucher processing."""
    if len(sys.argv) < 2:
        print("Usage: python voucher_processor.py <voucher_code>")
        return 1

    processor = VoucherProcessor()
    voucher_code = sys.argv[1]

    # Show machine info
    machine_info = processor.get_machine_info()
    print("Machine Information:")
    for key, value in machine_info.items():
        print(f"  {key}: {value}")
    print()

    # Process the voucher
    result = processor.process_voucher(voucher_code)

    if result["success"]:
        print("✅ Voucher processed successfully")
        print(f"  Amount: {result.get('amount', 0)} credits")
        print(f"  Message: {result.get('message', '')}")
    else:
        print("❌ Voucher processing failed")
        print(f"  Reason: {result.get('message', 'Unknown error')}")

    return 0 if result["success"] else 1


if __name__ == "__main__":
    sys.exit(main())