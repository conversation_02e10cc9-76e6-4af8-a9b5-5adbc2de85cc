# RethinkDB Integration with Offline Sync

This document explains how to set up and use the RethinkDB integration with offline sync capabilities for the WOW Games application.

## Overview

The WOW Games application now supports a hybrid database system that combines:

1. **Local SQLite database** - For offline operation and local data storage
2. **Remote RethinkDB database** - For real-time updates and synchronization across multiple clients
3. **Offline-to-online sync** - Ensures data is synchronized when connectivity is restored

This system allows the application to work seamlessly in both online and offline modes, with automatic synchronization when connectivity is available.

## Features

- **Offline operation** - The application continues to work when there's no internet connection
- **Automatic synchronization** - Changes made offline are automatically synced when connectivity is restored
- **Real-time updates** - Changes are propagated in real-time to all connected clients
- **Fault tolerance** - Data integrity is maintained even during network outages
- **Backward compatibility** - Existing code continues to work with the new database system

## Requirements

- RethinkDB server (v2.4.0 or higher)
- Python 3.7 or higher
- Required Python packages:
  - rethinkdb
  - sqlite3 (included with Python)

## Installation

### 1. Install RethinkDB

Follow the instructions for your operating system at [https://rethinkdb.com/docs/install/](https://rethinkdb.com/docs/install/)

Alternatively, use our setup utility:

```bash
python setup_rethinkdb.py --download
```

### 2. Install Required Python Packages

```bash
pip install rethinkdb
```

### 3. Set Up RethinkDB

You can use our setup utility to initialize and configure RethinkDB:

```bash
# Check if RethinkDB is installed and running
python setup_rethinkdb.py --check

# Start RethinkDB server (if it's not already running)
python setup_rethinkdb.py --start

# Initialize the database
python setup_rethinkdb.py --init

# Migrate data from SQLite to RethinkDB
python setup_rethinkdb.py --migrate
```

### 4. Configure RethinkDB Connection

Update the RethinkDB connection settings in `rethink_config.py` or use the setup utility:

```bash
python setup_rethinkdb.py --config
```

## Usage

### Using the Hybrid Database Manager

To use the hybrid database system in your code, import and use the `HybridDBManager`:

```python
from db_hybrid import get_hybrid_db_manager

# Get the hybrid database manager
db = get_hybrid_db_manager()

# Use the database (it will automatically use local or remote as appropriate)
stats = db.get_daily_stats()
db.update_daily_stats(games_played=1, earnings=50)
```

### Using the Integration Layer (Recommended)

For better backward compatibility, use the integration layer:

```python
from hybrid_db_integration import get_hybrid_db_integration

# Get the hybrid database integration
db_integration = get_hybrid_db_integration()

# Use methods that match the existing API
stats_summary = db_integration.get_stats_summary()
game_history = db_integration.get_game_history()
```

### Checking Connection Status

You can check if the application is currently in online or offline mode:

```python
from hybrid_db_integration import get_hybrid_db_integration

db_integration = get_hybrid_db_integration()

if db_integration.is_online():
    print("Application is in online mode")
else:
    print("Application is in offline mode")
```

### Forcing Online/Offline Mode

You can force the application to use online or offline mode:

```python
from hybrid_db_integration import get_hybrid_db_integration

db_integration = get_hybrid_db_integration()

# Force online mode
db_integration.force_online_mode()

# Force offline mode
db_integration.force_offline_mode()
```

## Real-time Updates

When in online mode, you can subscribe to real-time updates:

```python
from hybrid_db_integration import get_hybrid_db_integration

db_integration = get_hybrid_db_integration()

# Define a handler function
def on_game_added(change):
    print(f"New game added: {change['new_val']}")

# Register the handler
handler_id = db_integration.register_event_handler('game_added', on_game_added)

# Later, unregister the handler when no longer needed
db_integration.unregister_event_handler('game_added', handler_id)
```

## Architecture

The hybrid database system consists of the following components:

1. **RethinkDB Manager (`rethink_db.py`)** - Manages the connection to RethinkDB and provides CRUD operations
2. **Sync Manager (`sync_manager.py`)** - Handles synchronization between SQLite and RethinkDB
3. **Hybrid DB Manager (`db_hybrid.py`)** - Provides a unified interface for database operations
4. **Integration Layer (`hybrid_db_integration.py`)** - Ensures backward compatibility with existing code

### Data Flow

1. **Write Operations**:
   - Data is written to the local SQLite database
   - If online, data is also sent to RethinkDB
   - If offline, changes are queued for later synchronization

2. **Read Operations**:
   - Data is always read from the local SQLite database for consistency

3. **Synchronization**:
   - When online, the sync manager periodically synchronizes data between SQLite and RethinkDB
   - Changes made offline are uploaded to RethinkDB
   - Changes made in RethinkDB by other clients are downloaded to SQLite

## Configuration Options

The following configuration options can be set in `rethink_config.py`:

- `RETHINKDB_HOST` - RethinkDB server hostname
- `RETHINKDB_PORT` - RethinkDB server port
- `RETHINKDB_DB` - RethinkDB database name
- `RETHINKDB_USER` - RethinkDB username
- `RETHINKDB_PASSWORD` - RethinkDB password
- `RETHINKDB_SSL` - Whether to use SSL for the connection
- `SYNC_INTERVAL` - Time interval (in seconds) between synchronization attempts
- `MAX_SYNC_RETRIES` - Maximum number of sync retries before giving up
- `SYNC_BATCH_SIZE` - Number of records to sync in each batch
- `SYNC_TABLES` - List of tables to synchronize

## Troubleshooting

### Connection Issues

If you're having trouble connecting to RethinkDB:

1. Check if RethinkDB is running:
   ```bash
   python setup_rethinkdb.py --check
   ```

2. Test the connection:
   ```bash
   python setup_rethinkdb.py --test
   ```

3. Update the configuration:
   ```bash
   python setup_rethinkdb.py --config
   ```

### Synchronization Issues

If data is not synchronizing properly:

1. Force a manual sync:
   ```python
   from hybrid_db_integration import get_hybrid_db_integration
   
   db_integration = get_hybrid_db_integration()
   db_integration.force_refresh_data()
   ```

2. Check the log files in the `data` directory for error messages:
   - `rethink_db.log`
   - `sync_manager.log`
   - `db_hybrid.log`
   - `hybrid_db_integration.log`

## Best Practices

1. **Always use the integration layer** - Use `hybrid_db_integration.py` instead of directly accessing `db_hybrid.py` for better backward compatibility.

2. **Handle both online and offline modes** - Design your UI to work in both modes and provide appropriate feedback to users.

3. **Periodically force synchronization** - In key places of your application (e.g., when a game ends), force a synchronization to ensure data is up-to-date.

4. **Monitor log files** - Regularly check log files for synchronization issues or errors.

5. **Implement conflict resolution** - If the same data is modified offline by multiple clients, implement conflict resolution strategies.

## Advanced Usage

### Custom Synchronization

You can implement custom synchronization logic by extending the `SyncManager` class:

```python
from sync_manager import SyncManager

class CustomSyncManager(SyncManager):
    def _sync_table(self, table):
        # Custom synchronization logic
        pass
```

### Custom RethinkDB Queries

For more complex queries, you can access the RethinkDB connection directly:

```python
from rethink_db import get_rethink_db_manager

rethink_db = get_rethink_db_manager()
conn = rethink_db.get_connection()

# Use RethinkDB's query language (ReQL)
import rethinkdb as r
result = r.table('game_history').filter(
    r.row['earnings'].gt(100)
).run(conn)
```

## License

This RethinkDB integration is part of the WOW Games application and is subject to the same license terms.