#!/usr/bin/env python3
"""
Test script to debug keyboard event handling in the recharge popup.
This will help identify why clipboard operations and other keyboard shortcuts are not working.
"""

import pygame
import sys

def test_keyboard_events():
    """Test keyboard event handling to debug the issues."""
    
    print("=" * 60)
    print("TESTING KEYBOARD EVENT HANDLING")
    print("=" * 60)
    
    # Initialize pygame
    pygame.init()
    
    # Create a test screen
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Keyboard Event Test")
    
    try:
        # Import the recharge UI
        from payment.simple_recharge_ui import SimpleRechargeUI
        from payment import get_voucher_manager
        
        print("✓ Successfully imported SimpleRechargeUI")
        
        # Create voucher manager and recharge UI
        voucher_manager = get_voucher_manager()
        recharge_ui = SimpleRechargeUI(screen, voucher_manager)
        
        print("✓ Successfully created SimpleRechargeUI instance")
        
        # Show the recharge UI
        recharge_ui.show()
        print(f"✓ Recharge UI shown, visible: {recharge_ui.visible}")
        print(f"✓ Input field active: {recharge_ui.input_active}")
        
        # Test different types of keyboard events
        print("\n--- Testing Keyboard Event Types ---")
        
        # Test 1: TEXTINPUT event
        print("\n1. Testing TEXTINPUT event:")
        test_event = pygame.event.Event(pygame.TEXTINPUT, text='A')
        handled = recharge_ui.handle_event(test_event)
        print(f"   TEXTINPUT 'A' handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Test 2: KEYDOWN event with regular key
        print("\n2. Testing KEYDOWN event (regular key):")
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_b, unicode='b')
        handled = recharge_ui.handle_event(test_event)
        print(f"   KEYDOWN 'b' handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Test 3: KEYDOWN event with backspace
        print("\n3. Testing KEYDOWN event (backspace):")
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_BACKSPACE)
        handled = recharge_ui.handle_event(test_event)
        print(f"   KEYDOWN backspace handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Test 4: KEYDOWN event with Enter
        print("\n4. Testing KEYDOWN event (enter):")
        # Set some input first
        recharge_ui.voucher_input = "TEST123"
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        handled = recharge_ui.handle_event(test_event)
        print(f"   KEYDOWN enter handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Test 5: Modifier key detection
        print("\n5. Testing modifier key detection:")
        
        # Test current pygame modifier state
        mods = pygame.key.get_mods()
        print(f"   Current modifiers: {mods}")
        print(f"   CTRL pressed: {bool(mods & pygame.KMOD_CTRL)}")
        print(f"   SHIFT pressed: {bool(mods & pygame.KMOD_SHIFT)}")
        print(f"   ALT pressed: {bool(mods & pygame.KMOD_ALT)}")
        
        # Test 6: Ctrl+V event (clipboard paste)
        print("\n6. Testing Ctrl+V event:")
        
        # Clear input first
        recharge_ui.voucher_input = ""
        
        # Try to set up clipboard content
        try:
            import pyperclip
            pyperclip.copy("CLIPBOARD-TEST-123")
            print("   ✓ Set clipboard content: 'CLIPBOARD-TEST-123'")
        except Exception as e:
            print(f"   ⚠ Could not set clipboard: {e}")
        
        # Create Ctrl+V event
        test_event = pygame.event.Event(pygame.KEYDOWN, 
                                      key=pygame.K_v, 
                                      mod=pygame.KMOD_CTRL)
        
        print(f"   Event created - key: {test_event.key}, mod: {getattr(test_event, 'mod', 'None')}")
        
        # Test the event
        handled = recharge_ui.handle_event(test_event)
        print(f"   Ctrl+V handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Test 7: Alternative Ctrl+V event creation
        print("\n7. Testing alternative Ctrl+V event:")
        
        # Clear input
        recharge_ui.voucher_input = ""
        
        # Simulate pressing Ctrl first, then V
        pygame.key.set_mods(pygame.KMOD_CTRL)
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_v)
        
        print(f"   Modifiers set to: {pygame.key.get_mods()}")
        print(f"   Event key: {test_event.key}")
        
        handled = recharge_ui.handle_event(test_event)
        print(f"   Alternative Ctrl+V handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Reset modifiers
        pygame.key.set_mods(0)
        
        # Test 8: Ctrl+C event (clipboard copy)
        print("\n8. Testing Ctrl+C event:")
        
        # Set some input to copy
        recharge_ui.voucher_input = "COPY-TEST-456"
        
        # Create Ctrl+C event
        pygame.key.set_mods(pygame.KMOD_CTRL)
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_c)
        
        handled = recharge_ui.handle_event(test_event)
        print(f"   Ctrl+C handled: {handled}")
        
        # Check clipboard content
        try:
            import pyperclip
            clipboard_content = pyperclip.paste()
            print(f"   Clipboard content: '{clipboard_content}'")
        except Exception as e:
            print(f"   Could not read clipboard: {e}")
        
        # Reset modifiers
        pygame.key.set_mods(0)
        
        # Test 9: Ctrl+A event (select all)
        print("\n9. Testing Ctrl+A event:")
        
        # Set some input
        recharge_ui.voucher_input = "SELECT-ALL-TEST"
        
        # Create Ctrl+A event
        pygame.key.set_mods(pygame.KMOD_CTRL)
        test_event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_a)
        
        handled = recharge_ui.handle_event(test_event)
        print(f"   Ctrl+A handled: {handled}")
        print(f"   Current input: '{recharge_ui.voucher_input}'")
        
        # Reset modifiers
        pygame.key.set_mods(0)
        
        print("\n" + "=" * 60)
        print("KEYBOARD EVENT TEST COMPLETED")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

def main():
    """Main test function."""
    
    print("Starting Keyboard Event Debug Tests...")
    print("This will test keyboard event handling to identify clipboard and shortcut issues.")
    
    success = test_keyboard_events()
    
    if success:
        print("\n🎉 Keyboard event test completed successfully!")
        print("Check the output above to identify any issues with event handling.")
        return 0
    else:
        print("\n❌ Keyboard event test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
