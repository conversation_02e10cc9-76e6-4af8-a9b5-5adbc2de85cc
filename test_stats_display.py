#!/usr/bin/env python3
"""
Test script to verify stats page data retrieval and display
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stats_data_retrieval():
    """Test stats data retrieval functions"""
    
    print("=" * 80)
    print("TESTING STATS DATA RETRIEVAL")
    print("=" * 80)
    
    # Test 1: Direct database access
    print("\n1. Testing direct database access...")
    try:
        import thread_safe_db
        
        # Get game history
        history, total_pages = thread_safe_db.get_game_history(page=0, page_size=10)
        print(f"✅ Retrieved {len(history)} game history records, {total_pages} total pages")
        
        if history:
            print("\nSample records:")
            for i, record in enumerate(history[:3]):  # Show first 3 records
                print(f"  Record {i+1}: ID={record.get('id')}, Winner={record.get('username')}, "
                      f"Date={record.get('date_time')}, Prize={record.get('total_prize')}")
        
    except Exception as e:
        print(f"❌ Error with direct database access: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Stats integration
    print("\n2. Testing stats integration...")
    try:
        from stats_integration import get_game_history
        
        history, total_pages = get_game_history(page=0, page_size=10)
        print(f"✅ Stats integration retrieved {len(history)} records, {total_pages} total pages")
        
    except Exception as e:
        print(f"❌ Error with stats integration: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Game stats integration
    print("\n3. Testing game stats integration...")
    try:
        from game_stats_integration import GameStatsIntegration
        
        history, total_pages = GameStatsIntegration.get_game_history(page=0, page_size=10)
        print(f"✅ Game stats integration retrieved {len(history)} records, {total_pages} total pages")
        
    except Exception as e:
        print(f"❌ Error with game stats integration: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 4: Summary stats
    print("\n4. Testing summary stats...")
    try:
        summary = thread_safe_db.get_summary_stats()
        print(f"✅ Summary stats: {summary}")
        
    except Exception as e:
        print(f"❌ Error with summary stats: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 5: Date grouping logic
    print("\n5. Testing date grouping logic...")
    try:
        # Simulate the stats page date grouping
        if 'history' in locals() and history:
            grouped_data = {}
            for record in history:
                date_time = record.get('date_time', '')
                if date_time:
                    try:
                        dt = datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S')
                        date_key = dt.strftime('%Y-%m-%d')
                        day_name = dt.strftime('%A')
                        
                        if date_key not in grouped_data:
                            grouped_data[date_key] = {
                                'day_name': day_name,
                                'games': []
                            }
                        grouped_data[date_key]['games'].append(record)
                    except ValueError:
                        print(f"Warning: Invalid date format: {date_time}")
            
            print(f"✅ Grouped data into {len(grouped_data)} date groups:")
            for date_key, group in grouped_data.items():
                print(f"  {date_key} ({group['day_name']}): {len(group['games'])} games")
        
    except Exception as e:
        print(f"❌ Error with date grouping: {e}")
        import traceback
        traceback.print_exc()

def test_database_schema():
    """Test database schema and content"""
    
    print("\n" + "=" * 80)
    print("TESTING DATABASE SCHEMA AND CONTENT")
    print("=" * 80)
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        # Check table schema
        cursor.execute("PRAGMA table_info(game_history)")
        columns = cursor.fetchall()
        
        print("Game history table schema:")
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check data types and sample values
        cursor.execute("SELECT * FROM game_history ORDER BY date_time DESC LIMIT 3")
        records = cursor.fetchall()
        
        print(f"\nSample records ({len(records)} found):")
        if records:
            # Get column names
            col_names = [description[0] for description in cursor.description]
            
            for i, record in enumerate(records):
                print(f"\nRecord {i+1}:")
                for j, value in enumerate(record):
                    print(f"  {col_names[j]}: {value} ({type(value).__name__})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stats_data_retrieval()
    test_database_schema()
