@echo off
echo ========================================================
echo     WOW Games RethinkDB Complete Setup
echo ========================================================
echo.

echo Step 1: Installing required packages...
python -m pip install flask rethinkdb==2.4.9
if %ERRORLEVEL% NEQ 0 (
    echo Error installing packages. Please try again.
    pause
    exit /b 1
)
echo Packages installed successfully.
echo.

echo Step 2: Creating RethinkDB wrapper...
echo import sys > rethinkdb_wrapper.py
echo import rethinkdb >> rethinkdb_wrapper.py
echo # Add connect method to rethinkdb module >> rethinkdb_wrapper.py
echo def connect(*args, **kwargs): >> rethinkdb_wrapper.py
echo     return rethinkdb.r.connect(*args, **kwargs) >> rethinkdb_wrapper.py
echo rethinkdb.connect = connect >> rethinkdb_wrapper.py
echo # Make r accessible >> rethinkdb_wrapper.py
echo r = rethinkdb.r >> rethinkdb_wrapper.py
echo print("RethinkDB wrapper initialized successfully") >> rethinkdb_wrapper.py
python rethinkdb_wrapper.py
echo RethinkDB wrapper created.
echo.

echo Step 3: Fixing import issues...
python fix_rethink_imports.py
echo Import issues fixed.
echo.

echo Step 4: Updating modules...
echo import rethinkdb_wrapper > temp_fix.py
python temp_fix.py
echo Modules updated.
echo.

echo Step 5: Creating data directories...
mkdir data 2>nul
mkdir data\sync_cache 2>nul
echo Directories created.
echo.

echo Step 6: Integrating RethinkDB with the application...
python integrate_rethinkdb.py --all
echo Integration completed.
echo.

echo Step 7: Starting SQLite in offline mode...
echo.
echo ========================================================
echo     Your system is now set up for RethinkDB integration
echo.
echo     To run the game in offline mode:
echo       start_rethinkdb_all_fixed.bat
echo.
echo     To view data through the web dashboard:
echo       start_dashboard_fixed.bat
echo.
echo     To set up a remote server, follow the instructions in:
echo       ONLINE_RETHINKDB_GUIDE.md
echo ========================================================
echo.

pause