# CRITICAL: Import SDL display fixes BEFORE pygame to set environment variables
import sdl_display_fix

import random
import pygame
import math
import time
import json
import os
import sys
import colorsys
from pygame import gfxdraw
from view_players import ViewPlayers, Player
from player_storage import save_players_to_json, load_players_from_json
from settings_manager import SettingsManager
import datetime
from game_state_handler import GameS<PERSON>
from game_ui_handler import GameUIHandler
from settings_window import SettingsWindow
from amharic_support import Amharic<PERSON>upport  # Import our Amharic support class
from bingo_favor_mode import BingoFavorMode  # Import the developer cheat mode
from screen_mode_manager import get_screen_mode_manager
# pygame_stats_view import removed - stats view disabled

# Import payment system
try:
    from payment import get_voucher_manager
    from payment.game_integration import integrate_with_game
    PAYMENT_SYSTEM_AVAILABLE = True
except ImportError:
    PAYMENT_SYSTEM_AVAILABLE = False
    print("Payment system not available. Credit tracking will be disabled.")

# Constants for window dimensions and scaling
SCREEN_WIDTH, SCREEN_HEIGHT = 1024, 768
BASE_WIDTH, BASE_HEIGHT = 1024, 768

# Initialize pygame
pygame.init()

# Initialize stats database
try:
    from game_stats_integration import GameStatsIntegration
    # Ensure database exists and has required tables
    GameStatsIntegration.ensure_database_exists()
    print("Stats database initialized successfully")

    # Try to import stats event hooks for real-time updates
    try:
        from stats_event_hooks import get_stats_event_hooks
        stats_hooks = get_stats_event_hooks()
        STATS_HOOKS_AVAILABLE = True
        print("Stats event hooks system initialized successfully")
    except ImportError:
        STATS_HOOKS_AVAILABLE = False
        print("Stats event hooks not available. Using direct stats integration.")
except ImportError:
    print("Game stats integration module not available. Stats database initialization skipped.")
    STATS_HOOKS_AVAILABLE = False

# Initialize RethinkDB sync manager for real-time synchronization
try:
    from sync_manager import get_sync_manager
    sync_manager = get_sync_manager()
    SYNC_MANAGER_AVAILABLE = True
    print("RethinkDB sync manager initialized successfully")
    print(f"Sync manager status: Connected={sync_manager.remote_db.is_connected() if sync_manager.remote_db else False}")
except ImportError:
    SYNC_MANAGER_AVAILABLE = False
    print("RethinkDB sync manager not available. Real-time sync disabled.")
except Exception as e:
    SYNC_MANAGER_AVAILABLE = False
    print(f"Error initializing RethinkDB sync manager: {e}")

# Initialize hybrid database integration for unified data access
try:
    from db_hybrid import get_hybrid_db_manager
    hybrid_db = get_hybrid_db_manager()
    HYBRID_DB_AVAILABLE = True
    print("Hybrid database manager initialized successfully")
    print(f"Hybrid DB mode: {'Online' if hybrid_db.is_online() else 'Offline'}")
except ImportError:
    HYBRID_DB_AVAILABLE = False
    print("Hybrid database manager not available.")
except Exception as e:
    HYBRID_DB_AVAILABLE = False
    print(f"Error initializing hybrid database manager: {e}")

# Set process priority to below normal to reduce CPU usage
try:
    import psutil
    # Get current process
    process = psutil.Process(os.getpid())
    # Set process priority to below normal
    process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if hasattr(psutil, 'BELOW_NORMAL_PRIORITY_CLASS') else 10)
except ImportError:
    pass  # psutil not available, skip priority setting

# Import external display manager and HDMI fixer
from external_display_manager import get_external_display_manager
from hdmi_display_fixer import get_hdmi_display_fixer

# Import cartella preview overlay
from cartella_preview_overlay import CartellaPreviewOverlay

# Import power management to keep screen always active
from power_management import start_power_management, stop_power_management, is_power_management_active

# Import modern advertising system
try:
    from modern_advertising_integration import ModernAdvertisingAdapter
    MODERN_ADVERTISING_AVAILABLE = True
    print("Modern GPU-accelerated advertising system available")
except ImportError as e:
    MODERN_ADVERTISING_AVAILABLE = False
    print(f"Modern advertising not available: {e}")
    print("Using legacy advertising system")

# Initialize display with enhanced external monitor support
def initialize_display_with_external_support():
    """
    Initialize display with enhanced external monitor support using the external display manager
    """
    try:
        print("Initializing display with external monitor support...")

        # Get the external display manager
        display_manager = get_external_display_manager()

        # Detect available displays and their capabilities
        display_info = display_manager.detect_displays()

        # Print detailed display information
        print(f"Display detection results:")
        print(f"  Driver: {display_info.get('driver', 'Unknown')}")
        print(f"  External display detected: {display_info.get('external_detected', False)}")
        print(f"  Available modes: {len(display_info.get('available_modes', []))}")

        # Get recommended resolution
        recommended_width, recommended_height = display_manager.get_recommended_resolution()
        print(f"  Recommended resolution: {recommended_width}x{recommended_height}")

        # Create compatible display
        screen, actual_width, actual_height = display_manager.create_compatible_display(
            preferred_width=recommended_width,
            preferred_height=recommended_height,
            fullscreen=False
        )

        print(f"Display initialized successfully: {actual_width}x{actual_height}")
        return screen, actual_width, actual_height

    except Exception as e:
        print(f"Critical display initialization error: {e}")
        print("Falling back to basic pygame display initialization...")

        # Emergency fallback to basic pygame initialization
        try:
            screen = pygame.display.set_mode((1024, 768), pygame.RESIZABLE)
            return screen, 1024, 768
        except Exception as e2:
            print(f"Emergency fallback also failed: {e2}")
            # Absolute last resort
            screen = pygame.display.set_mode((800, 600))
            return screen, 800, 600

# Initialize display with external monitor support
screen, SCREEN_WIDTH, SCREEN_HEIGHT = initialize_display_with_external_support()
pygame.display.set_caption("WOW Games")

# Define base resolution that the game was designed for
BASE_WIDTH, BASE_HEIGHT = 1024, 768

# Set the application icon
try:
    app_icon = pygame.image.load("assets/app_logo.ico")
    pygame.display.set_icon(app_icon)
    print("Successfully set application icon")
except Exception as e:
    print(f"Error setting application icon: {e}")

# Calculate scaling factors for responsive layout
scale_x = SCREEN_WIDTH / BASE_WIDTH
scale_y = SCREEN_HEIGHT / BASE_HEIGHT

# Colors - updated to exactly match the image
DARK_BLUE = (10, 30, 45)  # Background color
LIGHT_BLUE = (20, 50, 70)  # Slightly lighter background
YELLOW = (255, 200, 50)   # For 'O' in BINGO
RED = (255, 50, 50)       # For 'B' and 'G' in BINGO
GREEN = (50, 200, 100)    # For highlighted numbers
BLUE = (50, 100, 220)     # For 'N' in BINGO
ORANGE = (255, 120, 30)   # For the "Total Callout" text
WHITE = (255, 255, 255)   # For text
GRAY = (60, 60, 70)       # For non-highlighted number circles
BLACK = (20, 20, 25)      # For number row backgrounds
GOLD = (255, 215, 0)      # For special elements
DARK_GREEN = (0, 100, 50) # Darker green for contrast
LIGHT_GREEN = (50, 200, 100) # For highlighted numbers
PURPLE = (128, 0, 128)    # Unused but available
NAV_BAR_BG = (15, 35, 55) # Dark blue for navigation bar
NAV_BAR_ACTIVE = (30, 100, 130) # Teal highlight for active nav item

# Fonts - scale font sizes based on screen dimensions
def scaled_font_size(base_size):
    return int(base_size * min(scale_x, scale_y))

title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

# Create directory for assets if it doesn't exist
os.makedirs("assets", exist_ok=True)

# Load background image or create a default
try:
    background_img = pygame.image.load("assets/bingo_background.jpg")
    background_img = pygame.transform.scale(background_img, (SCREEN_WIDTH, SCREEN_HEIGHT))
except:
    background_img = None

class BingoGame:
    def __init__(self):
        self.called_numbers = []
        self.current_number = None
        self.total_numbers = 75
        self.total_callout = 75  # Match the image
        self.prize_pool = 500    # Default prize pool value
        self.prize_pool_manual_override = False  # Flag to indicate if prize pool is manually set
        self.bet_amount = 50
        self.cartella_number = 12
        self.game_started = False
        self.is_demo_mode = False  # Flag to indicate if we're in demo mode
        self.force_redraw = False  # Flag to force a redraw of the screen

        # Add debounce mechanism for pause/start button
        self.last_pause_click_time = 0
        self.pause_debounce_ms = 500  # Minimum time between pause button clicks (500ms)

        # PERFORMANCE OPTIMIZATION: Enhanced caching systems
        self._text_cache = {}
        self._text_cache_order = []
        self._text_cache_max_size = 500  # Increased cache size for better performance
        self._last_cache_cleanup = time.time()

        # PERFORMANCE OPTIMIZATION: Surface caching for complex elements
        self._surface_cache = {}
        self._surface_cache_order = []
        self._surface_cache_max_size = 100

        # PERFORMANCE OPTIMIZATION: Pre-rendered static elements
        self._static_elements_cache = {}
        self._last_static_render = 0
        self._static_render_interval = 1.0  # Re-render static elements every 1 second

        # PERFORMANCE OPTIMIZATION: Dirty region tracking
        self._dirty_regions = []
        self._full_redraw_needed = True

        # PERFORMANCE OPTIMIZATION: Animation frame skipping for older hardware
        self._frame_skip_counter = 0
        self._animation_quality = "high"  # Will be auto-detected based on performance

        # PERFORMANCE OPTIMIZATION: Batch rendering flags
        self._batch_text_renders = []
        self._batch_circle_renders = []

        # PERFORMANCE OPTIMIZATION: Memory pool for frequently used objects
        self._rect_pool = []
        self._surface_pool = {}

        # PERFORMANCE OPTIMIZATION: Performance monitoring
        self._performance_monitor = {
            'frame_times': [],
            'avg_fps': 60.0,
            'last_fps_check': time.time(),
            'render_times': {},
            'memory_usage': 0,
            'low_performance_mode': False
        }

        # Advertising section state
        self.ad_zoomed = False  # Track whether advertising is in zoomed mode
        self.ad_section_rect = None  # Initialize the ad section rect

        # Session tracking
        self.session_players = []
        self.session_start_time = pygame.time.get_ticks()

        # Input field state
        self.input_active = False
        self.input_text = ""
        self.input_cursor_visible = True
        self.input_cursor_timer = 0

        # Bet input field state
        self.bet_input_active = False
        self.bet_input_text = ""
        self.bet_input_cursor_visible = True
        self.bet_input_cursor_timer = 0

        # Prize pool input state
        self.prize_pool_input_active = False

        # Error message for feedback
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"  # Can be "info", "error", "success"

        # Button animation states
        self.button_states = {}
        self.init_button_animations()

        # Players list and ViewPlayers instance
        self.players = []
        self.view_players = None
        self.show_view_players = False

        # Load saved players from JSON
        self.load_players()

        # Remove automatic sample player creation
        # No longer adding sample players on startup

        # Active navigation section
        self.active_nav = "play"  # Default active navigation item

        # Initialize with empty green numbers list (no highlights on startup)
        self.green_numbers = []

        # Initialize bingo board system
        self.bingo_board = []
        # Use os.path.join to create a proper path to bingo_boards.json in the data directory
        self.bingo_boards_db = os.path.join('data', 'bingo_boards.json')

        # Ensure boards database exists
        self.ensure_boards_exist()
        # Load board for current cartella
        self.load_board_for_cartella()

        # Initialize pygame mixer with more channels
        pygame.mixer.init()
        pygame.mixer.set_num_channels(16)  # Increase number of channels

        # Reserve a dedicated channel for game state announcements
        self.announcement_channel = pygame.mixer.Channel(1)  # Use channel 1 for announcements

        # Load sound effects
        try:
            self.number_call_sound = pygame.mixer.Sound("assets/audio-effects/number_call.mp3")
            # Load button click sound from MP3 file
            self.button_click_sound = pygame.mixer.Sound("assets/audio-effects/button-click.mp3")
            # Load shuffle sound effect (using MP3 format for better compatibility)
            self.shuffle_sound = pygame.mixer.Sound("assets/audio-effects/Number_shufle.mp3")
            # Load game start and pause audio announcements
            self.game_started_sound = pygame.mixer.Sound("assets/audio-effects/game_started.mp3")
            self.game_paused_sound = pygame.mixer.Sound("assets/audio-effects/Game_Paused_.mp3")
            # Load penalization sound
            self.penalize_sound = pygame.mixer.Sound("assets/audio-effects/Non-Won_Cartel_Removal.mp3")
            # Load warning sound for unregistered boards
            self.warning_sound = pygame.mixer.Sound("assets/audio-effects/warning_effect.mp3")
            # Load clear announce sound for game reset
            self.clear_announce_sound = pygame.mixer.Sound("assets/audio-effects/clear-announce.mp3")
            # Load winner announcement sound
            self.winner_sound = pygame.mixer.Sound("assets/audio-effects/game_winner_announcer.mp3")
            # Load non-winner late claim sound
            self.nonwinner_late_claim_sound = pygame.mixer.Sound("assets/audio-effects/nonWon_late_claim.mp3")

            # Get the actual duration of the announcement sounds
            try:
                # Get actual duration in milliseconds with a small buffer to ensure completion
                buffer_ms = 500  # 500ms buffer to ensure sound completes
                self.game_started_sound_duration = int(self.game_started_sound.get_length() * 1000) + buffer_ms
                self.game_paused_sound_duration = int(self.game_paused_sound.get_length() * 1000) + buffer_ms
                self.winner_sound_duration = int(self.winner_sound.get_length() * 1000) + buffer_ms
                print(f"Actual sound durations: start={self.game_started_sound_duration}ms, pause={self.game_paused_sound_duration}ms, winner={self.winner_sound_duration}ms")
            except Exception as e:
                # Fallback to default durations if we can't get actual durations
                print(f"Error getting sound durations: {e}. Using default values.")
                self.game_started_sound_duration = 1500  # Default 1.5 seconds
                self.game_paused_sound_duration = 1500   # Default 1.5 seconds
                self.winner_sound_duration = 3000  # Default 3 seconds

            print("Successfully loaded all sound effects")
        except Exception as e:
            print(f"Error loading sound effects: {e}")
            self.number_call_sound = None
            self.button_click_sound = None
            self.shuffle_sound = None
            self.game_started_sound = None
            self.game_paused_sound = None
            self.penalize_sound = None
            self.warning_sound = None
            self.clear_announce_sound = None
            self.announcement_channel = None

        # Store hit areas for buttons to fix responsiveness
        self.hit_areas = {}

        # Store current scaling factors as instance variables
        self.scale_x = scale_x
        self.scale_y = scale_y

        # Configuration options - will be loaded from settings
        # Don't set default values here, they will be loaded from settings
        # This ensures that saved settings are always used
        # Default values are defined in the load_game_settings method

        # Load settings from settings manager
        self.load_game_settings()

        # Calculate initial prize pool after settings are loaded
        self.calculate_prize_pool()

        # Game state and UI handlers
        self.game_state = GameState(self)
        self.ui_handler = GameUIHandler(self)

        # Delayed action variables
        self.pending_action = None
        self.action_delay_start = 0
        self.action_delay_duration = 1000  # Default delay duration in milliseconds (1 second)

        # Settings window
        self.settings_window = SettingsWindow(screen, self)

        # Initialize screen mode manager
        self.screen_mode_manager = get_screen_mode_manager()
        self.screen_mode_manager.set_screen_reference(screen)

        # Stats view disabled - no longer initialized

        # Shuffle animation variables
        self.shuffle_active = False
        self.shuffle_start_time = 0
        self.shuffle_offsets = {}  # Store random offsets for each number during shuffle
        self.current_shuffle_effects = None  # Store current visual effects for shuffle
        self.shuffle_sound_channel = None  # Store the channel for shuffle sound to control it

        # Initialize the developer cheat mode (Bingo Favor Mode)
        self.favor_mode = BingoFavorMode(self)

        # Initialize cartella preview overlay
        self.cartella_preview = CartellaPreviewOverlay(screen, self)

        # PERFORMANCE OPTIMIZATION: Initialize performance monitoring
        self._init_performance_monitoring()

        # Initialize modern advertising system
        self.modern_advertising = None
        if MODERN_ADVERTISING_AVAILABLE:
            try:
                # Use the settings manager from the settings window
                self.modern_advertising = ModernAdvertisingAdapter(self.settings_window.settings_manager)
                print("🚀 Modern GPU-accelerated advertising initialized successfully")
            except Exception as e:
                print(f"Failed to initialize modern advertising: {e}")
                self.modern_advertising = None
        else:
            print("⚠️ Using legacy advertising system")

    def _init_performance_monitoring(self):
        """Initialize performance monitoring and auto-optimization"""
        self._performance_start_time = time.time()
        self._frame_count = 0
        self._last_performance_check = time.time()

    def _monitor_performance(self, frame_time):
        """Monitor performance and adjust quality settings automatically"""
        current_time = time.time()
        self._frame_count += 1

        # Update frame times for averaging
        self._performance_monitor['frame_times'].append(frame_time)
        if len(self._performance_monitor['frame_times']) > 60:  # Keep last 60 frames
            self._performance_monitor['frame_times'].pop(0)

        # Check performance every 2 seconds
        if current_time - self._last_performance_check > 2.0:
            avg_frame_time = sum(self._performance_monitor['frame_times']) / len(self._performance_monitor['frame_times'])
            current_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 60.0
            self._performance_monitor['avg_fps'] = current_fps

            # Auto-adjust quality based on performance
            if current_fps < 20 and not self._performance_monitor['low_performance_mode']:
                print("Performance: Enabling low-performance optimizations (FPS < 20)")
                self._enable_low_performance_mode()
            elif current_fps > 40 and self._performance_monitor['low_performance_mode']:
                print("Performance: Disabling low-performance optimizations (FPS > 40)")
                self._disable_low_performance_mode()

            self._last_performance_check = current_time

    def _enable_low_performance_mode(self):
        """Enable aggressive optimizations for older hardware"""
        self._performance_monitor['low_performance_mode'] = True
        self._animation_quality = "low"
        # Reduce cache sizes to save memory
        self._text_cache_max_size = 200
        self._surface_cache_max_size = 50
        print("Performance: Low-performance mode enabled")

    def _disable_low_performance_mode(self):
        """Disable aggressive optimizations when performance improves"""
        self._performance_monitor['low_performance_mode'] = False
        self._animation_quality = "high"
        # Restore normal cache sizes
        self._text_cache_max_size = 500
        self._surface_cache_max_size = 100
        print("Performance: Low-performance mode disabled")

    def _get_rect_from_pool(self, x, y, width, height):
        """Get a rectangle from the memory pool to reduce allocations"""
        if self._rect_pool:
            rect = self._rect_pool.pop()
            rect.x, rect.y, rect.width, rect.height = x, y, width, height
            return rect
        return pygame.Rect(x, y, width, height)

    def _return_rect_to_pool(self, rect):
        """Return a rectangle to the memory pool"""
        if len(self._rect_pool) < 50:  # Limit pool size
            self._rect_pool.append(rect)

    def _get_surface_from_pool(self, size, flags=0):
        """Get a surface from the memory pool to reduce allocations"""
        cache_key = (size, flags)
        if cache_key in self._surface_pool and self._surface_pool[cache_key]:
            return self._surface_pool[cache_key].pop()
        return pygame.Surface(size, flags)

    def _return_surface_to_pool(self, surface, flags=0):
        """Return a surface to the memory pool"""
        cache_key = (surface.get_size(), flags)
        if cache_key not in self._surface_pool:
            self._surface_pool[cache_key] = []
        if len(self._surface_pool[cache_key]) < 10:  # Limit pool size per type
            self._surface_pool[cache_key].append(surface)

    def init_button_animations(self):
        """Initialize animation states for all animated buttons"""
        # This method allows us to easily add more animated buttons in the future
        buttons = ["add_player", "view_players"]  # Can add more buttons here

        for button in buttons:
            self.button_states[button] = {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0  # For smooth hover transition (0-255)
            }

    def update(self):
        """Update game state"""
        # Update animation timers
        current_time = pygame.time.get_ticks()

        # Update cursor blink timer for input field
        if self.input_active:
            if current_time - self.input_cursor_timer > 500:  # Blink every 500ms
                self.input_cursor_visible = not self.input_cursor_visible
                self.input_cursor_timer = current_time

        # Update cursor blink timer for bet input field
        if self.bet_input_active:
            if current_time - self.bet_input_cursor_timer > 500:  # Blink every 500ms
                self.bet_input_cursor_visible = not self.bet_input_cursor_visible
                self.bet_input_cursor_timer = current_time

        # Update message timer
        if self.message_timer > 0:
            self.message_timer -= 1
            if self.message_timer <= 0:
                self.message = ""

        # CRITICAL FIX: Check and clear the reset_recently_canceled flag in UI handler
        if hasattr(self, 'ui_handler'):
            # CRITICAL FIX: Force unlock buttons if they've been locked for too long
            # This prevents buttons from getting permanently locked
            current_time = pygame.time.get_ticks()
            if hasattr(self.ui_handler, 'reset_button_locked') and self.ui_handler.reset_button_locked:
                if current_time - self.ui_handler.button_lock_time > 2000:  # 2 seconds max lock time
                    self.ui_handler.reset_button_locked = False
                    print("CRITICAL FIX: Force unlocked reset button after extended lock")

            if hasattr(self.ui_handler, 'resume_button_locked') and self.ui_handler.resume_button_locked:
                if current_time - self.ui_handler.button_lock_time > 2000:  # 2 seconds max lock time
                    self.ui_handler.resume_button_locked = False
                    print("CRITICAL FIX: Force unlocked resume button after extended lock")

            # CRITICAL FIX: Force clear post-reset-cancel state if it's been active too long
            if hasattr(self.ui_handler, 'post_reset_cancel_state') and self.ui_handler.post_reset_cancel_state:
                if current_time - self.ui_handler.post_reset_cancel_time > 2000:  # 2 seconds max duration
                    self.ui_handler.post_reset_cancel_state = False
                    print("CRITICAL FIX: Force cleared post-reset-cancel state after extended duration")

            # CRITICAL FIX: Force redraw if reset confirmation dialog is showing
            # This ensures the dialog and its buttons are properly displayed
            if hasattr(self.ui_handler, 'show_reset_confirmation') and self.ui_handler.show_reset_confirmation:
                self.force_redraw = True
                # REMOVED: pygame.display.flip() to prevent flickering - let main loop handle display updates

            # Regular checks
            if hasattr(self.ui_handler, 'check_reset_recently_canceled'):
                self.ui_handler.check_reset_recently_canceled()

            # CRITICAL FIX: Check and clear button locks
            if hasattr(self.ui_handler, 'check_button_locks'):
                self.ui_handler.check_button_locks()

            # CRITICAL FIX: Check and clear post-reset-cancel state
            if hasattr(self.ui_handler, 'check_post_reset_cancel_state'):
                self.ui_handler.check_post_reset_cancel_state()

        # Process any pending delayed actions
        self.process_delayed_actions(current_time)

        # Update cartella preview overlay
        if hasattr(self, 'cartella_preview'):
            self.cartella_preview.update()

    def process_delayed_actions(self, current_time):
        """Process any pending delayed actions"""
        if self.pending_action is None:
            return

        # Check if the delay time has elapsed
        if current_time - self.action_delay_start >= self.action_delay_duration:
            action = self.pending_action
            self.pending_action = None  # Clear the pending action

            # Execute the delayed action
            if action == "start_game":
                # Actually start the game now
                self._execute_start_game()
            elif action == "pause_game":
                # Actually pause the game now
                self._execute_pause_game()
            elif action == "resume_game":
                # Actually resume the game now
                self._execute_resume_game()

    def update_button_hover_states(self, mouse_pos):
        """Update hover states for all buttons based on mouse position"""
        # This is called from the main game loop on MOUSEMOTION events
        for key, area in self.hit_areas.items():
            # Skip if this button doesn't have animation state
            if key not in self.button_states:
                continue

            # Check if mouse is over this button
            hover = area.collidepoint(mouse_pos)
            btn_state = self.button_states[key]

            # Update hover state
            if hover and not btn_state["hover"]:
                btn_state["hover"] = True
            elif not hover and btn_state["hover"]:
                btn_state["hover"] = False

        # Update hover transition animations
        for key, state in self.button_states.items():
            # Smooth transition for hover effect (0-255)
            if state["hover"] and state["hover_alpha"] < 255:
                state["hover_alpha"] = min(255, state["hover_alpha"] + 15)  # Smooth fade in
            elif not state["hover"] and state["hover_alpha"] > 0:
                state["hover_alpha"] = max(0, state["hover_alpha"] - 15)  # Smooth fade out

    def add_sample_players(self):
        """Add sample players for demonstration"""
        # Sample player data to match the mockup
        sample_data = [
            {"cartela_no": 50, "bet_amount": 50, "deposited": 200, "remaining": 50},
            {"cartela_no": 32, "bet_amount": 50, "deposited": 50, "remaining": 50},
            {"cartela_no": 45, "bet_amount": 50, "deposited": 100, "remaining": 0},
            {"cartela_no": 2, "bet_amount": 50, "deposited": 500, "remaining": 370},
            # More players to demonstrate pagination
            {"cartela_no": 18, "bet_amount": 50, "deposited": 150, "remaining": 100},
            {"cartela_no": 23, "bet_amount": 50, "deposited": 200, "remaining": 150},
            {"cartela_no": 27, "bet_amount": 50, "deposited": 100, "remaining": 50},
            {"cartela_no": 39, "bet_amount": 50, "deposited": 300, "remaining": 250},
            {"cartela_no": 41, "bet_amount": 50, "deposited": 50, "remaining": 0},
            {"cartela_no": 55, "bet_amount": 50, "deposited": 150, "remaining": 100},
            {"cartela_no": 63, "bet_amount": 50, "deposited": 200, "remaining": 150},
            {"cartela_no": 70, "bet_amount": 50, "deposited": 100, "remaining": 50},
            {"cartela_no": 87, "bet_amount": 50, "deposited": 300, "remaining": 250},
            {"cartela_no": 92, "bet_amount": 50, "deposited": 50, "remaining": 0},
            {"cartela_no": 15, "bet_amount": 50, "deposited": 150, "remaining": 100},
            {"cartela_no": 28, "bet_amount": 50, "deposited": 200, "remaining": 150},
            {"cartela_no": 37, "bet_amount": 50, "deposited": 100, "remaining": 50},
            {"cartela_no": 43, "bet_amount": 50, "deposited": 300, "remaining": 250},
            {"cartela_no": 58, "bet_amount": 50, "deposited": 50, "remaining": 0},
            {"cartela_no": 76, "bet_amount": 50, "deposited": 150, "remaining": 100},
        ]

        # Create Player objects
        for data in sample_data:
            player = Player(
                cartela_no=data["cartela_no"],
                bet_amount=data["bet_amount"],
                deposited=data["deposited"]
            )
            # Override remaining value to match the mockup exactly
            if "remaining" in data:
                player.remaining = data["remaining"]
            self.players.append(player)

    def update_scaling(self):
        """Update scaling factors when screen size changes"""
        global scale_x, scale_y

        # Get current screen dimensions
        screen_width, screen_height = screen.get_size()

        # Recalculate scaling factors
        scale_x = screen_width / BASE_WIDTH
        scale_y = screen_height / BASE_HEIGHT

        # Update instance variables
        self.scale_x = scale_x
        self.scale_y = scale_y

        # Update view_players scaling if it exists
        if self.view_players:
            # Update the scaling factors
            self.view_players.scale_x = scale_x
            self.view_players.scale_y = scale_y

            # Update fonts with new scaling
            self.view_players.title_font = pygame.font.SysFont("Arial", self.view_players.scaled_font_size(36), bold=True)
            self.view_players.header_font = pygame.font.SysFont("Arial", self.view_players.scaled_font_size(24), bold=True)
            self.view_players.text_font = pygame.font.SysFont("Arial", self.view_players.scaled_font_size(20))
            self.view_players.button_font = pygame.font.SysFont("Arial", self.view_players.scaled_font_size(18), bold=True)
            self.view_players.number_font = pygame.font.SysFont("Arial", self.view_players.scaled_font_size(28), bold=True)

    def ensure_boards_exist(self):
        """Ensure that bingo boards are generated and stored"""
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

        if os.path.exists(self.bingo_boards_db):
            # Check if the file contains valid data
            try:
                with open(self.bingo_boards_db, 'r') as file:
                    boards = json.load(file)
                # Make sure we have at least the basic 100 cartella boards
                if len(boards) >= 100 and all(str(i) in boards for i in range(1, 101)):
                    print(f"Bingo boards file exists with {len(boards)} boards")
                    return  # File exists and has valid data
                else:
                    print(f"Bingo boards file exists but is incomplete, regenerating...")
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error reading bingo boards file: {e}, regenerating...")
        else:
            print(f"Bingo boards file not found at {self.bingo_boards_db}, generating...")

        # Generate 100 unique boards (basic set)
        self.generate_unique_boards()
        print(f"Generated 100 unique bingo boards and saved to {self.bingo_boards_db}")

    def generate_unique_boards(self):
        """Generate 100 unique bingo boards and save them to disk"""
        boards = {}

        # Generate a unique board for each cartella number 1-100
        for cartella in range(1, 101):
            # Generate a unique board for this cartella number
            board = self.create_unique_board()
            boards[str(cartella)] = board
            print(f"Generated board for cartella {cartella}")

        # Ensure the data directory exists
        os.makedirs(os.path.dirname(self.bingo_boards_db), exist_ok=True)

        # Save generated boards to file
        try:
            with open(self.bingo_boards_db, 'w') as file:
                json.dump(boards, file)
            print(f"Successfully saved {len(boards)} boards to {self.bingo_boards_db}")
        except Exception as e:
            print(f"Error saving boards to file: {e}")

        return boards

    def create_unique_board(self):
        """Create a single unique bingo board"""
        board = []

        # Standard Bingo columns have specific number ranges:
        # B: 1-15, I: 16-30, N: 31-45, G: 46-60, O: 61-75
        for col in range(5):
            column = []
            start = col * 15 + 1
            end = start + 14
            nums = random.sample(range(start, end + 1), 5)
            column.extend(nums)
            board.append(column)

        # Set the middle square (free space) to 0
        board[2][2] = 0  # Free space

        return board

    def load_board_for_cartella(self):
        """Load a specific board for the current cartella number"""
        # Fallback to create_random_board if loading fails
        try:
            # Ensure the data directory exists
            os.makedirs('data', exist_ok=True)

            # Check if the bingo_boards.json file exists
            if not os.path.exists(self.bingo_boards_db):
                print(f"Bingo boards file not found at {self.bingo_boards_db}, generating boards...")
                self.generate_unique_boards()

            # Load the boards from the JSON file
            with open(self.bingo_boards_db, 'r') as file:
                boards = json.load(file)

            # Get board for current cartella number
            cartella_key = str(self.cartella_number)
            if cartella_key in boards:
                print(f"Loading board for cartella {self.cartella_number} from JSON file")
                self.bingo_board = boards[cartella_key]
            else:
                # If cartella number is in the extended range (101-1200) but not found in the file,
                # try to generate it deterministically
                if 1 <= self.cartella_number <= 1200:
                    print(f"Generating board for cartella {self.cartella_number} deterministically")
                    self.bingo_board = self.create_deterministic_board(self.cartella_number)

                    # Save the generated board to the JSON file for future use
                    boards[cartella_key] = self.bingo_board
                    with open(self.bingo_boards_db, 'w') as file:
                        json.dump(boards, file)
                    print(f"Saved board for cartella {self.cartella_number} to JSON file")
                else:
                    # Fallback to random board if cartella is out of range
                    print(f"Cartella number {self.cartella_number} is out of range, creating random board")
                    self.create_random_board()
        except (FileNotFoundError, json.JSONDecodeError) as e:
            # Fallback to random board if file doesn't exist or is invalid
            print(f"Error loading bingo boards: {e}, creating random board")
            self.create_random_board()

    def create_deterministic_board(self, cartella_number):
        """Create a deterministic board based on cartella number"""
        # Use the cartella number as seed for consistent results
        random.seed(cartella_number)

        board = []

        # For each column (B, I, N, G, O)
        for col in range(5):
            column_values = []
            # Range for this column: col*15+1 to col*15+15
            min_val = col * 15 + 1
            max_val = min_val + 14

            # Generate 5 unique random numbers for this column
            values = list(range(min_val, max_val + 1))
            random.shuffle(values)
            column_values = values[:5]

            # Set the center square (N column, 3rd row) to 0 (free space)
            if col == 2:
                column_values[2] = 0

            board.append(column_values)

        # Reset the random seed to avoid affecting other random operations
        random.seed()

        return board

    def create_random_board(self):
        """Legacy method for backward compatibility and fallback"""
        # B (1-15), I (16-30), N (31-45), G (46-60), O (61-75)
        self.bingo_board = []
        for col in range(5):
            column = []
            start = col * 15 + 1
            end = start + 14
            nums = random.sample(range(start, end + 1), 5)
            column.extend(nums)
            self.bingo_board.append(column)

        # Set the middle square (free space) to 0
        self.bingo_board[2][2] = 0  # Free space

    def draw(self):
        """Main draw method with comprehensive error handling for display surface operations"""
        try:
            # Validate screen surface before any operations
            try:
                screen_size = screen.get_size()
                screen_flags = screen.get_flags()
            except pygame.error as screen_error:
                print(f"Error in original draw method: {screen_error}")
                return  # Exit draw method if screen surface is invalid

            # Check if we need to force a redraw for highlighting the first called number
            if hasattr(self, 'force_redraw') and self.force_redraw:
                print("Main draw: Forced redraw triggered")
                # Reset the flag
                self.force_redraw = False

                # Log the current number for debugging
                if hasattr(self, 'current_number') and self.current_number is not None:
                    print(f"Current number during forced redraw: {self.current_number}")
                    if hasattr(self, 'called_numbers'):
                        print(f"Called numbers: {self.called_numbers}")

            # Fill background with gradient - optimized for lower CPU usage
            try:
                if background_img:
                    # Only scale background image when needed (not every frame)
                    if not hasattr(self, '_scaled_bg') or self._scaled_bg.get_size() != screen_size:
                        # Use a more efficient scaling method
                        try:
                            # Use regular scale for better performance (sacrificing some quality)
                            self._scaled_bg = pygame.transform.scale(background_img, screen_size)

                            # Convert to a format that blits faster
                            if hasattr(self._scaled_bg, 'convert'):
                                self._scaled_bg = self._scaled_bg.convert()
                        except Exception as scale_error:
                            print(f"Error scaling background image: {scale_error}")
                            # Fallback to regular scale if first method fails
                            try:
                                self._scaled_bg = pygame.transform.scale(background_img, screen_size)
                            except Exception as fallback_error:
                                print(f"Fallback scaling also failed: {fallback_error}")
                                # Skip background image if scaling fails
                                self._scaled_bg = None

                        # Apply a subtle darkening effect to make text more readable
                        if self._scaled_bg:
                            try:
                                darkening = pygame.Surface(screen_size)
                                darkening.fill((0, 0, 0))
                                darkening.set_alpha(30)  # Semi-transparent black
                                self._scaled_bg.blit(darkening, (0, 0))
                            except Exception as darkening_error:
                                print(f"Error applying darkening effect: {darkening_error}")

                    # Blit the background image
                    if hasattr(self, '_scaled_bg') and self._scaled_bg:
                        try:
                            screen.blit(self._scaled_bg, (0, 0))
                        except pygame.error as blit_error:
                            print(f"Error blitting background image: {blit_error}")
                            # Fallback to solid color
                            try:
                                screen.fill((10, 30, 45))
                            except pygame.error as fill_error:
                                print(f"Error filling background color: {fill_error}")
                                return  # Exit if we can't even fill the screen
                else:
                    # Create a gradient background if image not available
                    # Cache the gradient background to avoid recreating it every frame
                    if not hasattr(self, '_bg_surface') or self._bg_surface.get_size() != screen_size:
                        try:
                            self._bg_surface = pygame.Surface(screen_size)
                            width, height = screen_size

                            # Use a larger step size to reduce the number of lines drawn
                            step_size = max(1, height // 100)  # Draw at most 100 lines (reduced from 200)

                            for y in range(0, height, step_size):
                                ratio = y / height
                                r = 10 * (1 - ratio) + 5 * ratio
                                g = 30 * (1 - ratio) + 15 * ratio
                                b = 45 * (1 - ratio) + 35 * ratio
                                # Draw thicker lines to fill the gaps
                                pygame.draw.rect(self._bg_surface, (r, g, b), (0, y, width, step_size))

                            # Convert to a format that blits faster with error handling
                            if hasattr(self._bg_surface, 'convert'):
                                try:
                                    self._bg_surface = self._bg_surface.convert()
                                except Exception as e:
                                    print(f"Warning: Failed to convert background surface: {e}")
                                    # Continue without conversion
                        except Exception as gradient_error:
                            print(f"Error creating gradient background: {gradient_error}")
                            # Create a simple solid color surface as fallback
                            try:
                                self._bg_surface = pygame.Surface(screen_size)
                                self._bg_surface.fill((10, 30, 45))
                            except Exception as solid_error:
                                print(f"Error creating solid background: {solid_error}")
                                self._bg_surface = None

                    # Add error handling for blitting operation
                    if hasattr(self, '_bg_surface') and self._bg_surface:
                        try:
                            screen.blit(self._bg_surface, (0, 0))
                        except pygame.error as blit_error:
                            print(f"Error blitting background surface: {blit_error}")
                            # Fallback: Fill with a solid color if blitting fails
                            try:
                                screen.fill((10, 30, 45))  # Dark blue background as fallback
                            except pygame.error as fill_error:
                                print(f"Critical error: Even fallback fill failed: {fill_error}")
                                return  # Exit if we can't even fill the screen
                    else:
                        # No background surface available, use solid fill
                        try:
                            screen.fill((10, 30, 45))
                        except pygame.error as fill_error:
                            print(f"Error filling screen with solid color: {fill_error}")
                            return  # Exit if we can't fill the screen
            except Exception as background_error:
                print(f"General background error: {background_error}")
                # Last resort: try to fill with solid color
                try:
                    screen.fill((10, 30, 45))
                except pygame.error as final_fill_error:
                    print(f"Final fallback fill failed: {final_fill_error}")
                    return  # Exit draw method

            # Get current screen dimensions for calculations
            try:
                screen_width, screen_height = screen.get_size()
            except pygame.error as size_error:
                print(f"Error getting screen size: {size_error}")
                return  # Exit if we can't get screen dimensions

            # Check if advertising is in zoomed mode - if so, draw it and return early
            if self.ad_zoomed:
                try:
                    self.draw_zoomed_advertising()
                    # Draw toast message on top of zoomed advertising
                    self.draw_toast_message()
                except Exception as zoom_ad_error:
                    print(f"Error drawing zoomed advertising: {zoom_ad_error}")
                return

            # Draw navigation bar at the top
            try:
                nav_height = int(40 * scale_y)
                self.draw_navigation_bar(screen_width, screen_height)
            except Exception as nav_error:
                print(f"Error drawing navigation bar: {nav_error}")
                # Continue without navigation bar

            # Check which view to display based on active_nav
            if self.active_nav == "stats":
                # Stats view is no longer available - just draw background and message
                try:
                    self.draw_toast_message()
                except Exception as toast_error:
                    print(f"Error drawing toast message for stats: {toast_error}")
                return
            elif self.active_nav == "settings":
                # Just draw the navigation bar and background for settings
                # The settings window will be drawn separately
                try:
                    self.draw_toast_message()
                except Exception as toast_error:
                    print(f"Error drawing toast message for settings: {toast_error}")
                return

            # Calculate dimensions for responsive layout
            # Use percentage of screen height for better scaling in fullscreen
            # Left panel width for prize pool
            left_panel_width = int(screen_width * 0.28)

            # Top section (Bingo title) - Reduced height to give more space to number grid
            bingo_title_height = int(screen_height * 0.14)  # Reduced height allocation for logo
            bingo_title_y = nav_height + int(10 * scale_y)

            # Prize pool section - Define height and width first
            prize_pool_height = int(screen_height * 0.15)  # Match the height of bottom controls
            prize_pool_width = left_panel_width      # Use left panel width

            # Bottom section - Add space from the bottom of the screen for bottom controls
            bottom_controls_height = int(screen_height * 0.15)  # Reduced from 0.18 to 0.15 to fit better
            bottom_margin = int(screen_height * 0.02)  # Margin at the bottom of the screen

            # Calculate the top position of the lucky numbers section (below the logo)
            bingo_title_bottom = bingo_title_y + bingo_title_height
            lucky_numbers_top_margin = bingo_title_bottom - int(25 * scale_y)  # Larger negative gap to overlap more with logo area

            # Calculate the position of the footer based on the last row of the number grid
            # This ensures the footer is positioned below the number grid with proper spacing
            row_height = int(84 * scale_y)  # Height of each BINGO row
            row_spacing = int(10 * scale_y)  # Spacing between rows
            header_height = int(35 * scale_y)  # Height of the LUCKY NUMBERS header

            # Position the footer at a fixed position from the bottom of the screen
            # This ensures it's always in the same place regardless of the grid size
            bottom_controls_y = screen_height - bottom_controls_height - int(30 * scale_y)  # Fixed position from bottom

            # Calculate the maximum height for the lucky numbers section to prevent overlap
            # Use a much larger gap (100px) to ensure clear separation between grid and footer
            max_lucky_numbers_height = bottom_controls_y - lucky_numbers_top_margin - int(100 * scale_y)  # 100px gap

            # Position prize pool at the same level as the bottom controls
            prize_pool_y = bottom_controls_y

            # Calculate the ideal height for the lucky numbers section based on the 5 BINGO rows
            # Each row has a fixed height plus some spacing
            row_height = int(84 * scale_y)  # Height of each BINGO row
            row_spacing = int(10 * scale_y)  # Spacing between rows
            total_rows_height = (row_height * 5) + (row_spacing * 4)  # 5 rows with spacing between them

            # Add header height and some padding
            header_height = int(35 * scale_y)
            padding_top = int(10 * scale_y)
            padding_bottom = int(20 * scale_y)

            # Calculate the ideal height based on content
            ideal_height = header_height + padding_top + total_rows_height + padding_bottom

            # Use the smaller of the ideal height and max height to prevent overlap with footer
            lucky_numbers_height = min(ideal_height, max_lucky_numbers_height)

            # Make sure the height is not too small
            lucky_numbers_height = max(lucky_numbers_height, int(200 * scale_y))

            # Make lucky numbers section start from the left edge and span the full width
            lucky_numbers_x = int(10 * scale_x)  # Smaller margin from left edge to maximize width
            lucky_numbers_width = screen_width - int(20 * scale_x)  # Full width with minimal margins

            # Ensure prize pool doesn't extend beyond bottom of screen
            if prize_pool_y + prize_pool_height > screen_height - bottom_margin:
                prize_pool_height = screen_height - bottom_margin - prize_pool_y

            # Draw all the components in the order they appear in the mockup
            try:
                # Left side elements - first row
                try:
                    self.draw_bingo_title(bingo_title_y)
                except Exception as title_error:
                    print(f"Error drawing bingo title: {title_error}")

                # Draw advertising section above the lucky numbers
                try:
                    ad_height = int(90 * scale_y)  # Increased height to 2x the previous size
                    self.draw_advertising_text(lucky_numbers_x, lucky_numbers_top_margin - ad_height, lucky_numbers_width, ad_height)
                except Exception as ad_error:
                    print(f"Error drawing advertising: {ad_error}")

                # Lucky numbers section - full width below the logo
                try:
                    self.draw_lucky_numbers(lucky_numbers_x, lucky_numbers_width, lucky_numbers_height, lucky_numbers_top_margin)
                except Exception as lucky_error:
                    print(f"Error drawing lucky numbers: {lucky_error}")

                # Bottom row elements - positioned at the bottom of the screen
                try:
                    self.draw_bottom_sections(screen_width, screen_height, bottom_controls_y)
                except Exception as bottom_error:
                    print(f"Error drawing bottom sections: {bottom_error}")

                # Draw view players popup if active
                try:
                    if self.show_view_players and self.view_players:
                        self.view_players.draw()
                except Exception as view_players_error:
                    print(f"Error drawing view players: {view_players_error}")

                # Draw toast message on top of everything else
                try:
                    self.draw_toast_message()
                except Exception as toast_error:
                    print(f"Error drawing toast message: {toast_error}")

                # Draw modal overlays if active
                try:
                    self.ui_handler.draw_pause_reason_prompt(screen)
                    self.ui_handler.draw_admin_control(screen)
                    self.ui_handler.draw_winner_validation(screen)

                    # CRITICAL DEBUG: Check for display flag conflicts
                    if hasattr(self.game_state, 'show_winner_display') and hasattr(self.game_state, 'show_missed_winner_display'):
                        if self.game_state.show_winner_display and self.game_state.show_missed_winner_display:
                            print("CRITICAL ERROR: Both show_winner_display AND show_missed_winner_display are True!")
                            print("This will cause the missed winner display to override the winner display")
                            print("Fixing by setting show_missed_winner_display = False")
                            self.game_state.show_missed_winner_display = False

                    self.ui_handler.draw_winner_display(screen)
                    self.ui_handler.draw_invalid_claim_display(screen)
                    self.ui_handler.draw_missed_winner_display(screen)
                    self.ui_handler.draw_reset_confirmation(screen)
                    self.ui_handler.draw_exit_confirmation(screen)
                except Exception as modal_error:
                    print(f"Error drawing modal overlays: {modal_error}")

                # Draw settings window if visible
                try:
                    if hasattr(self, 'settings_window'):
                        self.settings_window.draw()
                except Exception as settings_error:
                    print(f"Error drawing settings window: {settings_error}")

                # Draw cartella preview overlay if active
                try:
                    if hasattr(self, 'cartella_preview') and self.cartella_preview.active:
                        self.cartella_preview.draw()
                except Exception as preview_error:
                    print(f"Error drawing cartella preview: {preview_error}")

                # Draw the favor mode indicator if active
                try:
                    if hasattr(self, 'favor_mode') and self.favor_mode.active:
                        self.favor_mode.draw_indicator(screen)
                except Exception as favor_error:
                    print(f"Error drawing favor mode indicator: {favor_error}")

            except Exception as components_error:
                print(f"Error drawing game components: {components_error}")

        except Exception as draw_error:
            print(f"Critical error in draw method: {draw_error}")
            # Try to at least fill the screen with a solid color as a last resort
            try:
                screen.fill((10, 30, 45))
            except Exception as final_error:
                print(f"Final fallback in draw method failed: {final_error}")
                # If even this fails, the screen surface is completely invalid

    def draw_navigation_bar(self, screen_width, _):
        """Draw a modern compact navigation menu at the top right of the screen"""
        try:
            # Validate screen_width
            if screen_width <= 0:
                print(f"Error: Invalid screen width: {screen_width}")
                return

            # Configuration for the modern navbar
            nav_height = int(40 * scale_y)
            nav_width = int(screen_width * 0.4)  # 40% of screen width, positioned at right
            nav_x = screen_width - nav_width

            # Slightly transparent background for the nav bar
            nav_rect = pygame.Rect(nav_x, 0, nav_width, nav_height)

            # Validate surface dimensions before creating
            if nav_rect.width <= 0 or nav_rect.height <= 0:
                print(f"Error: Invalid nav surface dimensions: {nav_rect.width}x{nav_rect.height}")
                return

            nav_surface = pygame.Surface((nav_rect.width, nav_rect.height), pygame.SRCALPHA)
            nav_surface.fill((NAV_BAR_BG[0], NAV_BAR_BG[1], NAV_BAR_BG[2], 220))  # Semi-transparent
            screen.blit(nav_surface, nav_rect)

            # Add a subtle bottom border
            pygame.draw.line(screen, (60, 80, 100),
                           (nav_x, nav_height-1),
                           (screen_width, nav_height-1), 1)
            # Navigation items with modern icons
            nav_items = [
                {"id": "play", "text": "Game", "icon": "🎮"},
                {"id": "preview", "text": "Preview", "icon": "👁️"},
                {"id": "stats", "text": "Stats", "icon": "📊"},
                {"id": "settings", "text": "Settings", "icon": "⚙️"},
                {"id": "help", "text": "Help", "icon": "❓"}
            ]

            # Calculate positioning - more compact layout
            item_width = nav_width / len(nav_items)
            item_x_start = nav_x
            nav_font = pygame.font.SysFont("Arial", scaled_font_size(14), bold=True)
        except Exception as e:
            print(f"Error in draw_navigation_bar: {e}")
            return

        try:
            # Draw each navigation item
            for i, item in enumerate(nav_items):
                try:
                    # Calculate item position
                    item_x = item_x_start + i * item_width
                    item_rect = pygame.Rect(item_x, 0, item_width, nav_height)

                    # Highlight active item with a more subtle effect
                    if item["id"] == self.active_nav:
                        # Modern highlight with rounded bottom corners
                        highlight_rect = pygame.Rect(item_x + item_width * 0.15, 0,
                                                  item_width * 0.7, nav_height)

                        # Validate surface dimensions
                        if highlight_rect.width <= 0 or highlight_rect.height <= 0:
                            print(f"Warning: Invalid highlight surface dimensions: {highlight_rect.width}x{highlight_rect.height}")
                            continue

                        # Draw a rounded rectangle for active item with semi-transparency
                        highlight_surface = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
                        pygame.draw.rect(highlight_surface, (*NAV_BAR_ACTIVE, 180),
                                       (0, 0, highlight_rect.width, highlight_rect.height),
                                       border_radius=int(5 * min(scale_x, scale_y)))
                        screen.blit(highlight_surface, highlight_rect)

                        # Add subtle bottom indicator
                        indicator_width = item_width * 0.4
                        indicator_x = item_x + (item_width - indicator_width) / 2
                        indicator_height = int(3 * scale_y)
                        pygame.draw.rect(screen, GOLD,
                                       pygame.Rect(indicator_x, nav_height-indicator_height,
                                                  indicator_width, indicator_height),
                                       border_radius=int(2 * min(scale_x, scale_y)))

                        text_color = WHITE
                    else:
                        # Add hover detection for navigation items
                        mouse_pos = pygame.mouse.get_pos()
                        if item_rect.collidepoint(mouse_pos):
                            # Hover state - subtle highlight
                            hover_width = item_rect.width * 0.7
                            hover_height = item_rect.height

                            # Validate surface dimensions
                            if hover_width <= 0 or hover_height <= 0:
                                print(f"Warning: Invalid hover surface dimensions: {hover_width}x{hover_height}")
                            else:
                                hover_surface = pygame.Surface((hover_width, hover_height), pygame.SRCALPHA)
                                pygame.draw.rect(hover_surface, (255, 255, 255, 15),
                                               (0, 0, hover_surface.get_width(), hover_surface.get_height()),
                                               border_radius=int(5 * min(scale_x, scale_y)))
                                screen.blit(hover_surface, (item_x + item_rect.width * 0.15, 0))

                        text_color = (180, 180, 180)  # Slightly dimmed text for inactive items

                    # Draw icons directly without relying on emoji rendering
                    icon_size = int(16 * min(scale_x, scale_y))
                    icon_x = item_x + item_width/2
                    icon_y = nav_height/2 - int(7 * scale_y)

                    # Draw custom icons based on navigation item
                    if item["id"] == "play":
                        # Game controller icon - draw directly without creating a rect
                        # Main controller body
                        pygame.draw.rect(screen, text_color,
                                       pygame.Rect(icon_x - icon_size/3, icon_y - icon_size/4,
                                                  icon_size/1.5, icon_size/2),
                                       border_radius=int(2 * min(scale_x, scale_y)))
                        # Controller buttons
                        pygame.draw.circle(screen, text_color,
                                         (icon_x - icon_size/4, icon_y + icon_size/6), icon_size/8)
                        pygame.draw.circle(screen, text_color,
                                         (icon_x + icon_size/4, icon_y + icon_size/6), icon_size/8)

                    elif item["id"] == "stats":
                        # Stats bar chart icon
                        bar_width = icon_size/5
                        # Draw three bars of increasing height
                        pygame.draw.rect(screen, text_color,
                                       pygame.Rect(icon_x - icon_size/2, icon_y,
                                                  bar_width, icon_size/2))
                        pygame.draw.rect(screen, text_color,
                                       pygame.Rect(icon_x - icon_size/6, icon_y - icon_size/4,
                                                  bar_width, icon_size*3/4))
                        pygame.draw.rect(screen, text_color,
                                       pygame.Rect(icon_x + icon_size/6, icon_y - icon_size/2,
                                                  bar_width, icon_size))

                    elif item["id"] == "settings":
                        # Settings gear icon
                        gear_radius = icon_size/3
                        # Draw gear circle
                        pygame.draw.circle(screen, text_color, (icon_x, icon_y), gear_radius, width=int(1.5 * min(scale_x, scale_y)))
                        # Draw gear teeth
                        for angle in range(0, 360, 45):
                            rad = math.radians(angle)
                            pygame.draw.line(screen, text_color,
                                           (icon_x + gear_radius * math.cos(rad), icon_y + gear_radius * math.sin(rad)),
                                           (icon_x + (gear_radius+icon_size/4) * math.cos(rad), icon_y + (gear_radius+icon_size/4) * math.sin(rad)),
                                           width=int(2 * min(scale_x, scale_y)))

                    elif item["id"] == "preview":
                        # Preview (eye) icon
                        eye_radius = icon_size/4
                        # Draw outer eye shape (ellipse)
                        pygame.draw.ellipse(screen, text_color,
                                          pygame.Rect(icon_x - icon_size/3, icon_y - eye_radius,
                                                     icon_size*2/3, eye_radius*2), width=int(1.5 * min(scale_x, scale_y)))
                        # Draw inner circle (pupil)
                        pygame.draw.circle(screen, text_color, (icon_x, icon_y), eye_radius/2)
                        # Draw highlight dot
                        pygame.draw.circle(screen, (255, 255, 255),
                                         (icon_x - eye_radius/4, icon_y - eye_radius/4), eye_radius/4)

                    elif item["id"] == "help":
                        # Help (?) icon
                        # Draw circle
                        pygame.draw.circle(screen, text_color, (icon_x, icon_y), icon_size/3, width=int(1.5 * min(scale_x, scale_y)))
                        # Draw question mark
                        question_font = pygame.font.SysFont("Arial", int(icon_size/1.5), bold=True)
                        question_text = question_font.render("?", True, text_color)
                        question_rect = question_text.get_rect(center=(icon_x, icon_y))
                        screen.blit(question_text, question_rect)

                    # Text below icon with hotkey hint for preview
                    if item["id"] == "preview":
                        # Show hotkey hint for preview button
                        main_text = item["text"]
                        hotkey_text = "(Ctrl+P)"

                        # Draw main text
                        text_surf = nav_font.render(main_text, True, text_color)
                        text_rect = text_surf.get_rect(center=(item_x + item_width/2, nav_height/2 + int(4 * scale_y)))
                        screen.blit(text_surf, text_rect)

                        # Draw hotkey hint in smaller font
                        hotkey_font = pygame.font.SysFont("Arial", int(10 * min(scale_x, scale_y)), italic=True)
                        hotkey_surf = hotkey_font.render(hotkey_text, True, (180, 180, 180))
                        hotkey_rect = hotkey_surf.get_rect(center=(item_x + item_width/2, nav_height/2 + int(15 * scale_y)))
                        screen.blit(hotkey_surf, hotkey_rect)
                    else:
                        # Normal text for other navigation items
                        text_surf = nav_font.render(item["text"], True, text_color)
                        text_rect = text_surf.get_rect(center=(item_x + item_width/2, nav_height/2 + int(7 * scale_y)))
                        screen.blit(text_surf, text_rect)

                    # Store hit area for navigation item
                    self.hit_areas[f"nav_{item['id']}"] = item_rect
                except Exception as e:
                    print(f"Error drawing navigation item {item['id']}: {e}")
                    continue
        except Exception as e:
            print(f"Error drawing navigation items: {e}")

    def draw_bingo_title(self, y_position):
        # Scale positions based on screen size - left aligned with smaller size
        title_x = int(90 * scale_x)  # Moved slightly left
        title_y = y_position + int(35 * scale_y)  # Reduced vertical offset
        circle_radius = int(70 * min(scale_x, scale_y))  # Smaller circle

        # Draw the yellow circle background with enhanced glow effect
        # Use exact colors from mockup (bright yellow)
        yellow_color = (255, 215, 0)  # More vibrant gold yellow to match mockup
        for i in range(5):
            alpha = 100-i*20
            if alpha < 0:
                alpha = 0
            glow_surface = pygame.Surface((circle_radius*2+i*6*min(scale_x, scale_y), circle_radius*2+i*6*min(scale_x, scale_y)), pygame.SRCALPHA)
            pygame.draw.circle(glow_surface, (*yellow_color, alpha),
                              (glow_surface.get_width()//2, glow_surface.get_height()//2),
                              circle_radius+i*3*min(scale_x, scale_y))
            screen.blit(glow_surface, (title_x-glow_surface.get_width()//2, title_y-glow_surface.get_height()//2))

        # Draw main yellow circle
        pygame.draw.circle(screen, yellow_color, (title_x, title_y), circle_radius)

        # Add some subtle shading for a 3D effect
        for i in range(1, 20, 2):
            shade_radius = circle_radius - i
            if shade_radius > 0:
                shade_color = (
                    max(0, min(255, int(yellow_color[0] - i*2))),
                    max(0, min(255, int(yellow_color[1] - i*3))),
                    max(0, min(255, int(yellow_color[2] - i*1)))
                )
                pygame.draw.circle(screen, shade_color, (title_x, title_y), shade_radius, 1)

        # Draw "WOW Games" text with enhanced 3D effect
        # Updated colors to match splash screen
        colors = [
            (255, 50, 50),    # Bright red for F
            (255, 90, 50),    # Reddish orange for u
            (255, 130, 50),   # Orange for k
            (255, 170, 50),   # Golden orange for k
            (255, 200, 50),   # Yellow for r
            (200, 200, 200),  # Silver for space
            (50, 220, 100),   # Bright green for G
            (50, 180, 160),   # Teal for a
            (50, 140, 200),   # Sky blue for m
            (50, 100, 220),   # Blue for e
            (100, 80, 220)    # Purplish blue for s
        ]
        letters = "WOW Games"
        x_pos = int(5 * scale_x)  # Start position even further left
        letter_spacing = int(22 * scale_x)  # Further reduced spacing for more compact text

        # Track rendering position for non-space characters
        letter_count = 0
        for i, letter in enumerate(letters):
            # Skip spaces but account for them in positioning
            if letter == " ":
                x_pos += letter_spacing // 2  # Half space for the space character
                continue

            # Use color directly from the colors array with bounds checking
            # to avoid index errors

            # Multiple shadow layers for stronger 3D effect
            for offset in range(1, 4):
                shadow_surf = title_font.render(letter, True,
                                               (max(0, colors[i][0] - 150),
                                                max(0, colors[i][1] - 150),
                                                max(0, colors[i][2] - 150)))
                screen.blit(shadow_surf, (x_pos + offset*scale_x, y_position + int(3*scale_y) + offset*scale_y))

            # Main letter - using exact colors from splash screen
            letter_surf = title_font.render(letter, True, colors[i])
            screen.blit(letter_surf, (x_pos, y_position))

            # Highlight/reflection on top of letter for glossy effect
            highlight_surf = title_font.render(letter, True,
                                              (min(255, colors[i][0] + 50),
                                               min(255, colors[i][1] + 50),
                                               min(255, colors[i][2] + 50)))
            # Only show top portion of highlight using a mask
            highlight_height = int(letter_surf.get_height() * 0.3)
            highlight_mask = pygame.Surface((letter_surf.get_width(), highlight_height), pygame.SRCALPHA)
            highlight_mask.blit(highlight_surf, (0, 0))
            screen.blit(highlight_mask, (x_pos, y_position))

            x_pos += letter_spacing
            letter_count += 1

    # Playing board preview has been removed as this functionality is already implemented
    # in the Board_selection_fixed.py page with Ctrl+click on the number

    def draw_prize_pool(self, x_position, y_position, section_width, section_height):
        # --- Unified Prize Pool UI (matches Board Selection Page) ---
        GOLD = (255, 220, 50)
        ORANGE = (255, 160, 0)
        # Background gradient
        section_bg_color_start = (40, 35, 10)
        section_bg_color_end = (60, 45, 15)
        prize_rect = pygame.Rect(x_position, y_position, section_width, section_height)
        self.draw_gradient_rect(prize_rect, section_bg_color_start, section_bg_color_end, 12)

        # Draw 'PRIZE POOL' label
        label_font_size = scaled_font_size(18)
        label_font = pygame.font.SysFont("Arial", label_font_size, bold=True)
        label_text = label_font.render("PRIZE POOL", True, (230, 230, 230))
        label_x = prize_rect.x + (prize_rect.width - label_text.get_width()) // 2
        label_y = prize_rect.y + 6
        screen.blit(label_text, (label_x, label_y))

        # Prize amount (with commas)
        prize_font_size = scaled_font_size(58)
        prize_font = pygame.font.SysFont("Arial", prize_font_size, bold=True)
        formatted_prize = f"{self.prize_pool:,}"
        prize_text = prize_font.render(formatted_prize, True, GOLD)
        # Center prize text
        prize_x = prize_rect.x + (prize_rect.width - prize_text.get_width()) // 2
        prize_y = label_y + label_text.get_height() + 8

        # Glowing effect (multiple layers)
        glow_size = min(20, int(20 * min(scale_x, scale_y)))
        for i in range(3):
            layer_size = glow_size * (3 - i) // 3
            glow_surf = pygame.Surface((prize_text.get_width() + layer_size * 2, prize_text.get_height() + layer_size * 2), pygame.SRCALPHA)
            alpha = 40 - (i * 10)
            pygame.draw.ellipse(
                glow_surf,
                (255, 220, 50, alpha),
                (0, 0, prize_text.get_width() + layer_size * 2, prize_text.get_height() + layer_size * 2),
            )
            offset = layer_size
            screen.blit(glow_surf, (prize_x - offset, prize_y - offset))

        # Draw the prize amount
        screen.blit(prize_text, (prize_x, prize_y))

        # Draw 'ETB' label in orange below the number
        etb_font_size = scaled_font_size(22)
        etb_font = pygame.font.SysFont("Arial", etb_font_size, bold=True)
        etb_text = etb_font.render("ETB", True, ORANGE)
        etb_x = prize_rect.x + (prize_rect.width - etb_text.get_width()) // 2
        etb_y = prize_y + prize_text.get_height() + 4
        screen.blit(etb_text, (etb_x, etb_y))

        # Add edit indicator if manual override is active
        if hasattr(self, 'prize_pool_manual_override') and self.prize_pool_manual_override:
            # Draw "MANUAL" indicator text in bottom right
            manual_font = pygame.font.SysFont("Arial", scaled_font_size(12), bold=True)
            manual_text = manual_font.render("MANUAL", True, (255, 180, 0))
            manual_x = prize_rect.right - manual_text.get_width() - 8
            manual_y = prize_rect.bottom - manual_text.get_height() - 4
            screen.blit(manual_text, (manual_x, manual_y))

        # Add a clickable area to set prize pool manually
        self.hit_areas["prize_pool"] = prize_rect

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0):
        """Draw a rectangle with a vertical gradient and improved edge quality - optimized for performance"""
        # Create a cache key based on the parameters
        cache_key = (rect.width, rect.height, color1, color2, border_radius)

        # Check if this gradient rect is already in cache
        if not hasattr(self, '_gradient_cache'):
            self._gradient_cache = {}

        # Use cached surface if available
        if cache_key in self._gradient_cache:
            cached_surface = self._gradient_cache[cache_key]
            screen.blit(cached_surface, (rect.x, rect.y))
            return

        # Create new surface if not in cache
        surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Draw main gradient - optimized with larger step size
        height_float = float(rect.height)

        # Use a step size to reduce the number of lines drawn
        step_size = max(1, rect.height // 20)  # Draw at most 20 gradient steps

        for y in range(0, rect.height, step_size):
            # Calculate gradient color with simplified calculations
            ratio = y / height_float
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)

            # Draw a rectangle instead of individual lines
            end_y = min(y + step_size, rect.height)
            pygame.draw.rect(surface, (r, g, b), (0, y, rect.width, end_y - y))

        if border_radius > 0:
            # Scale border radius
            scaled_radius = max(1, int(border_radius * min(scale_x, scale_y)))

            # Create a mask with rounded corners
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255),
                            (0, 0, rect.width, rect.height),
                            border_radius=scaled_radius)

            # Apply mask
            surface.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

            # Add a subtle highlight at the top edge for 3D effect - simplified
            if rect.height > 10:  # Only add highlight if rectangle is tall enough
                pygame.draw.rect(surface, (255, 255, 255, 40),
                               (1, 1, rect.width-2, int(rect.height * 0.1)),
                               border_radius=scaled_radius)

            # Add a subtle shadow at the bottom edge
            pygame.draw.rect(surface, (0, 0, 0, 40),
                           (1, rect.height - int(rect.height * 0.1), rect.width-2, int(rect.height * 0.1)),
                           border_radius=scaled_radius)

        # Add a subtle inner border
        if rect.width > 4 and rect.height > 4:
            if border_radius > 0:
                pygame.draw.rect(surface, (255, 255, 255, 30),
                               (1, 1, rect.width-2, rect.height-2),
                               border_radius=max(1, scaled_radius-1), width=1)
            else:
                pygame.draw.rect(surface, (255, 255, 255, 30),
                               (1, 1, rect.width-2, rect.height-2), width=1)

        # Store in cache (limit cache size to prevent memory issues)
        if len(self._gradient_cache) > 100:  # Limit cache size
            # Remove a random item to keep memory usage in check
            self._gradient_cache.pop(next(iter(self._gradient_cache)))
        self._gradient_cache[cache_key] = surface.copy()

        # Draw to screen
        screen.blit(surface, (rect.x, rect.y))

    def get_number_audio_path(self, number):
        """Get the path to the audio announcement file for a number"""
        if not hasattr(self, 'bingo_caller') or not self.bingo_caller:
            # Calculate column (0-4) based on number range
            col = min(4, (number - 1) // 15)
            letter = ['B', 'I', 'N', 'G', 'O'][col]
        else:
            letter = self.bingo_caller.get_letter_for_number(number)

        return f"assets/Audios/English/{letter}{number}.mp3"

    def play_number_announcement(self, number):
        """Play the audio announcement for a called number"""
        if number is None:
            return False

        # Performance optimization: Use a cache for announcement sounds
        if not hasattr(self, 'announcement_cache'):
            self.announcement_cache = {}

        # Check if we already have this sound cached
        cache_key = f"announce_{number}"
        if cache_key in self.announcement_cache:
            # Use the cached sound
            try:
                self.announcement_cache[cache_key].play()
                return True
            except:
                # If playing fails, remove from cache and try loading again
                del self.announcement_cache[cache_key]

        # Get the audio path
        audio_path = self.get_number_audio_path(number)

        try:
            # Try to load and play the specific number announcement
            announcement = pygame.mixer.Sound(audio_path)

            # Cache the sound for future use
            self.announcement_cache[cache_key] = announcement

            # Play the sound
            announcement.play()
            return True
        except Exception as e:
            # Fall back to the generic call sound if specific file not found
            if self.number_call_sound:
                self.number_call_sound.play()
            return False

    def play_penalize_sound(self):
        """Play the penalization sound when a player is penalized"""
        try:
            # Use the dedicated announcement channel for better reliability
            if hasattr(self, 'announcement_channel') and self.announcement_channel:
                # Stop any currently playing announcements
                self.announcement_channel.stop()

                # If we have the penalize sound loaded, play it on the announcement channel
                if hasattr(self, 'penalize_sound') and self.penalize_sound:
                    self.announcement_channel.play(self.penalize_sound)
                    print("Playing penalization sound on announcement channel")
                    return True

            # Fallback to direct play if announcement channel is not available
            if hasattr(self, 'penalize_sound') and self.penalize_sound:
                self.penalize_sound.play()
                print("Playing penalization sound directly")
                return True

            return False
        except Exception as e:
            print(f"Error playing penalization sound: {e}")
            return False

    def play_warning_sound(self):
        """Play the warning sound for unregistered boards"""
        try:
            # CRITICAL AUDIO FIX: Enhanced warning sound playback with comprehensive debugging
            print("=" * 60)
            print("AUDIO DEBUG: play_warning_sound() called")
            print("=" * 60)

            # First check if we have the warning sound loaded
            if not hasattr(self, 'warning_sound') or self.warning_sound is None:
                # Try to load the warning sound if it's not already loaded
                try:
                    # Try both path formats to handle inconsistencies
                    try:
                        self.warning_sound = pygame.mixer.Sound("assets/audio-effects/warning_effect.mp3")
                        print("AUDIO DEBUG: Successfully loaded warning sound from assets/audio-effects/warning_effect.mp3")
                    except:
                        self.warning_sound = pygame.mixer.Sound("assets\\audio-effects\\warning_effect.mp3")
                        print("AUDIO DEBUG: Successfully loaded warning sound from assets\\audio-effects\\warning_effect.mp3")
                except Exception as load_e:
                    print(f"AUDIO DEBUG: Error loading warning sound: {load_e}")
                    return False

            # CRITICAL AUDIO FIX: Add comprehensive audio deduplication and conflict detection
            current_time = pygame.time.get_ticks()

            # Check if winner sound was recently played
            if hasattr(self, '_last_winner_sound_time'):
                time_since_winner = current_time - self._last_winner_sound_time
                if time_since_winner < 2000:  # If winner sound was played within 2 seconds
                    print(f"🔇 AUDIO CONFLICT PREVENTION: Skipping warning sound because winner sound was played {time_since_winner}ms ago")
                    print("🔇 AUDIO CONFLICT PREVENTION: This prevents audio conflicts in valid winner scenarios")
                    print("🔇 This is CORRECT behavior - warning sound should NOT play after winner sound")
                    return False

            # Check if warning sound was recently played
            if hasattr(self, '_last_warning_sound_time'):
                time_since_last = current_time - self._last_warning_sound_time
                if time_since_last < 1000:  # Prevent multiple warning sounds within 1 second
                    print(f"AUDIO DEDUPLICATION: Skipping warning sound (last played {time_since_last}ms ago)")
                    return True

            # Store the time we played this sound
            self._last_warning_sound_time = current_time
            print(f"AUDIO DEBUG: Playing warning sound at time {current_time}")

            # CRITICAL AUDIO FIX: Use a dedicated channel for warning sounds to avoid conflicts
            # Use channel 2 for warning sounds (different from announcement channel 1)
            warning_channel = None
            try:
                warning_channel = pygame.mixer.Channel(2)
                if warning_channel.get_busy():
                    print("AUDIO DEBUG: Warning channel 2 is busy, stopping it")
                    warning_channel.stop()
                print("AUDIO DEBUG: Using dedicated warning channel 2")
            except Exception as channel_e:
                print(f"AUDIO DEBUG: Error accessing channel 2: {channel_e}, finding alternative")
                warning_channel = pygame.mixer.find_channel(True)

            if warning_channel:
                # Play the warning sound on the dedicated channel
                warning_channel.play(self.warning_sound)
                print(f"AUDIO DEBUG: Successfully playing warning sound on channel {warning_channel}")
                print("AUDIO DEBUG: Warning sound playback initiated")
                return True
            else:
                # Fallback to direct play if no channel available
                self.warning_sound.play()
                print("AUDIO DEBUG: Playing warning sound directly (no channel available)")
                return True

        except Exception as e:
            print(f"AUDIO DEBUG: Error playing warning sound: {e}")
            return False

    def play_nonwinner_late_claim_sound(self):
        """Play the sound for non-winner late claims"""
        try:
            # Load the sound if not already loaded
            if not hasattr(self, 'nonwinner_late_claim_sound') or self.nonwinner_late_claim_sound is None:
                try:
                    self.nonwinner_late_claim_sound = pygame.mixer.Sound("assets/audio-effects/nonWon_late_claim.mp3")
                    print("Loaded non-winner late claim sound")
                except Exception as e:
                    print(f"Error loading non-winner late claim sound: {e}")
                    return False

            # Use the dedicated announcement channel for better reliability
            if hasattr(self, 'announcement_channel') and self.announcement_channel:
                # Stop any currently playing announcements
                self.announcement_channel.stop()

                # Play the sound on the announcement channel
                self.announcement_channel.play(self.nonwinner_late_claim_sound)
                print("Playing non-winner late claim sound on announcement channel")
                return True

            # Fallback to direct play if announcement channel is not available
            self.nonwinner_late_claim_sound.play()
            print("Playing non-winner late claim sound directly")
            return True
        except Exception as e:
            print(f"Error playing non-winner late claim sound: {e}")
            return False

    def play_clear_announce_sound(self):
        """Play the clear announce sound when resetting the game"""
        try:
            # Use the dedicated announcement channel for better reliability
            if hasattr(self, 'announcement_channel') and self.announcement_channel:
                # Stop any currently playing announcements
                self.announcement_channel.stop()

                # If we have the clear announce sound loaded, play it on the announcement channel
                if hasattr(self, 'clear_announce_sound') and self.clear_announce_sound:
                    self.announcement_channel.play(self.clear_announce_sound)
                    print("Playing clear announce sound on announcement channel")
                    return True

            # Fallback to direct play if announcement channel is not available
            if hasattr(self, 'clear_announce_sound') and self.clear_announce_sound:
                self.clear_announce_sound.play()
                print("Playing clear announce sound directly")
                return True

            return False
        except Exception as e:
            print(f"Error playing clear announce sound: {e}")
            return False

    def play_winner_sound(self):
        """Play the winner announcement sound"""
        try:
            # CRITICAL AUDIO FIX: Enhanced winner sound playback with comprehensive debugging
            print("=" * 60)
            print("🏆 AUDIO DEBUG: play_winner_sound() called")
            print("🏆 This should be the ONLY audio for valid winners")
            print("🏆 NO warning sound should play after this")
            print("=" * 60)

            # CRITICAL AUDIO FIX: Add audio deduplication and timing tracking
            current_time = pygame.time.get_ticks()

            # Check if winner sound was recently played
            if hasattr(self, '_last_winner_sound_time'):
                time_since_last = current_time - self._last_winner_sound_time
                if time_since_last < 1000:  # Prevent multiple winner sounds within 1 second
                    print(f"AUDIO DEDUPLICATION: Skipping winner sound (last played {time_since_last}ms ago)")
                    return True

            # Store the time we played this sound
            self._last_winner_sound_time = current_time
            print(f"AUDIO DEBUG: Playing winner sound at time {current_time}")

            # Use the dedicated announcement channel for better reliability
            if hasattr(self, 'announcement_channel') and self.announcement_channel:
                try:
                    # Stop any currently playing announcements
                    self.announcement_channel.stop()
                    print("AUDIO DEBUG: Stopped any existing announcements on channel 1")

                    # If we have the winner sound loaded, play it on the announcement channel
                    if hasattr(self, 'winner_sound') and self.winner_sound:
                        self.announcement_channel.play(self.winner_sound)
                        print("AUDIO DEBUG: Successfully playing winner sound on announcement channel 1")
                        print("AUDIO DEBUG: Winner sound playback initiated")
                        return True
                    else:
                        print("AUDIO DEBUG: Winner sound not loaded")
                        return False
                except Exception as channel_e:
                    print(f"AUDIO DEBUG: Error playing on announcement channel: {channel_e}, trying direct play")
                    # Fall through to direct play if channel play fails

            # Fallback to direct play if announcement channel is not available
            if hasattr(self, 'winner_sound') and self.winner_sound:
                self.winner_sound.play()
                print("AUDIO DEBUG: Playing winner sound directly (no announcement channel)")
                return True
            else:
                print("AUDIO DEBUG: Winner sound not available for direct play")

            return False
        except Exception as e:
            print(f"AUDIO DEBUG: Error playing winner announcement sound: {e}")
            return False

    def call_number(self):
        """
        Call a random bingo number that hasn't been called yet.
        Uses the BingoCaller instance if available, with fallback to direct implementation.

        Returns:
            int or None: The called number, or None if no number could be called
        """
        # Check if game is in a valid state for calling
        if hasattr(self, 'game_state') and hasattr(self.game_state, 'is_paused') and self.game_state.is_paused:
            # Game is paused, can't call numbers
            print("Game is paused, can't call numbers")
            return None

        # Check if all numbers have been called
        if len(self.called_numbers) >= self.total_numbers:
            print(f"All {self.total_numbers} numbers have been called already")
            return None

        # Create a set of called numbers for faster lookup and duplicate checking
        called_set = set(self.called_numbers)

        # Use BingoCaller if available (preferred path)
        if hasattr(self, 'bingo_caller') and self.bingo_caller:
            # Check if the caller is paused and temporarily resume it for one call
            was_paused = self.bingo_caller.paused
            if was_paused:
                # We'll call one number but not change the overall pause state
                self.bingo_caller.paused = False

            # Call the next number through the official BingoCaller
            result = self.bingo_caller.call_next_number()

            # Restore pause state if needed
            if was_paused:
                self.bingo_caller.paused = True

            # Double check that the number was not already called
            # This is defensive programming to ensure we never have duplicates
            if result is not None:
                if result in called_set and result != self.current_number:
                    print(f"Warning: Number {result} was already called. Skipping to prevent duplicate.")
                    return None

                # Verify the number is in the valid range
                if not (1 <= result <= self.total_numbers):
                    print(f"Warning: Invalid number {result} returned by bingo caller. Skipping.")
                    return None

                # Play specific number announcement if successful
                self.play_number_announcement(result)

                # Ensure the number is added to our called_numbers list
                if result not in called_set:
                    self.called_numbers.append(result)
                    self.current_number = result
                    print(f"Added number {result} to main game called_numbers list")

            return result

        # Fallback to direct implementation if no BingoCaller is available
        # This ensures backward compatibility
        all_numbers = set(range(1, self.total_numbers + 1))
        uncalled = list(all_numbers - called_set)

        if uncalled:
            # Call a random number
            number = random.choice(uncalled)

            # Double-check it's not already called (should never happen, but just in case)
            if number in called_set:
                print(f"Warning: Selected number {number} is already in called_numbers. Skipping.")
                return None

            # Update game state
            self.current_number = number
            self.called_numbers.append(number)

            # Log the call
            print(f"Called number {number} using fallback method")

            # Play specific number announcement
            self.play_number_announcement(number)

            return number
        else:
            print("No uncalled numbers available")
            return None

    def start_game(self):
        """Start or pause the game depending on current state"""
        # Implement debounce mechanism to prevent rapid clicks
        current_time = pygame.time.get_ticks()
        if current_time - self.last_pause_click_time < self.pause_debounce_ms:
            print(f"Ignoring rapid pause/start button click (debounce: {self.pause_debounce_ms}ms)")
            return

        # Update last click time
        self.last_pause_click_time = current_time

        # Check if there's already a pending action
        if hasattr(self, 'pending_action') and self.pending_action is not None:
            print(f"Ignoring pause/start button click - already processing {self.pending_action}")
            return

        if not self.game_started:
            # Validation before starting the game
            validation_passed = True

            # Check if we're in demo mode (2 or fewer players)
            self.is_demo_mode = len(self.players) <= 2

            # Check if bet amount is set
            is_valid_bet, error_msg = self.validate_bet_amount(str(self.bet_amount))
            if not is_valid_bet:
                self.message = error_msg
                self.message_type = "error"
                self.message_timer = 180
                validation_passed = False

            # Check if enough players are added (at least 2 for normal mode, 1 for demo mode)
            if len(self.players) < 1:
                self.message = "At least 1 player is required to start the game"
                self.message_type = "error"
                self.message_timer = 180
                validation_passed = False
            elif len(self.players) == 1:
                # Single player is allowed in demo mode
                self.is_demo_mode = True
                self.message = "Starting in DEMO MODE with 1 player - no credits will be used"
                self.message_type = "info"
                self.message_timer = 180
            elif len(self.players) == 2:
                # Two players is allowed in demo mode
                self.is_demo_mode = True
                self.message = "Starting in DEMO MODE with 2 players - no credits will be used"
                self.message_type = "info"
                self.message_timer = 180

            # Only start the game if validation passes
            if validation_passed:
                # Reload game settings to ensure we have the latest values
                self.load_game_settings()

                # Log demo mode status
                if self.is_demo_mode:
                    print(f"Starting game in DEMO MODE with {len(self.players)} players")
                else:
                    print(f"Starting game in NORMAL MODE with {len(self.players)} players")

                # Play game started announcement on the dedicated channel
                if hasattr(self, 'game_started_sound') and self.game_started_sound and hasattr(self, 'announcement_channel'):
                    # Stop any currently playing announcements
                    self.announcement_channel.stop()

                    # Play the game started announcement on the dedicated channel
                    self.announcement_channel.play(self.game_started_sound)
                    print("Playing game started announcement")

                    # Make sure we have an accurate duration for the sound
                    try:
                        # Recalculate the duration to ensure it's accurate
                        actual_duration = int(self.game_started_sound.get_length() * 1000)
                        buffer_ms = 500  # 500ms buffer to ensure sound completes
                        self.game_started_sound_duration = actual_duration + buffer_ms
                        print(f"Updated game started sound duration: {self.game_started_sound_duration}ms (actual: {actual_duration}ms + {buffer_ms}ms buffer)")
                    except Exception as e:
                        # Keep existing duration if we can't get the actual duration
                        print(f"Using existing game started sound duration: {self.game_started_sound_duration}ms")

                    # Schedule the actual game start after a delay
                    self.pending_action = "start_game"
                    self.action_delay_start = pygame.time.get_ticks()
                    self.action_delay_duration = self.game_started_sound_duration  # Use the sound duration
                    print(f"Game will start in {self.action_delay_duration/1000} seconds")
                else:
                    # Fallback to button click sound if game started sound is not available
                    if self.button_click_sound:
                        self.button_click_sound.play()
                    # Start the game immediately if no announcement sound
                    self._execute_start_game()
        else:
            # Check if the game is already paused
            if hasattr(self, 'game_state') and hasattr(self.game_state, 'is_paused') and self.game_state.is_paused:
                print("Game is already paused, ignoring pause button click")
                return

            # Game is already running, so pause it IMMEDIATELY for fair play
            # First pause the game immediately
            self._execute_pause_game()
            print("Game paused immediately for fair play")

            # Then play the pause announcement (after the game is already paused)
            if hasattr(self, 'game_paused_sound') and self.game_paused_sound and hasattr(self, 'announcement_channel'):
                # Stop any currently playing announcements
                self.announcement_channel.stop()

                # Play the game paused announcement on the dedicated channel
                self.announcement_channel.play(self.game_paused_sound)
                print("Playing game paused announcement")
            else:
                # Fallback to button click sound if game paused sound is not available
                if self.button_click_sound:
                    self.button_click_sound.play()

    def _execute_start_game(self):
        """Actually start the game after the delay"""
        print("=" * 80)
        print("Executing delayed game start")
        print("=" * 80)
        # Reset the sound playing flag
        self._start_sound_playing = False

        # Verify that the announcement has completed playing
        if hasattr(self, 'announcement_channel') and self.announcement_channel.get_busy():
            print("Warning: Game started announcement is still playing. Waiting for it to complete...")
            # Wait a short time for the announcement to complete
            pygame.time.wait(100)  # Wait 100ms

            # If it's still playing, force it to stop to avoid overlap with number calling
            if self.announcement_channel.get_busy():
                print("Stopping announcement to proceed with game start")
                self.announcement_channel.stop()

        # Try to record the game start event using stats hooks first (preferred method)
        try:
            print("-" * 50)
            print("RECORDING GAME START EVENT TO STATS")
            print(f"Demo mode: {self.is_demo_mode}, Player count: {len(self.players)}, Bet amount: {self.bet_amount}")
            print("-" * 50)

            try:
                # Check if stats_event_hooks module is available
                import importlib
                stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
                if stats_hooks_spec is not None:
                    print("stats_event_hooks module is available")
                    STATS_HOOKS_AVAILABLE = True
                else:
                    print("stats_event_hooks module could not be imported")
                    STATS_HOOKS_AVAILABLE = False
            except Exception as import_e:
                print(f"Error checking stats_event_hooks availability: {import_e}")
                STATS_HOOKS_AVAILABLE = False

            if 'STATS_HOOKS_AVAILABLE' in globals() and STATS_HOOKS_AVAILABLE:
                # Use the event hooks system for real-time updates
                print("Using event hooks system for real-time updates")
                from stats_event_hooks import get_stats_event_hooks
                stats_hooks = get_stats_event_hooks()

                # Debug log the stats_hooks object
                print(f"stats_hooks object: {stats_hooks}, type: {type(stats_hooks)}")

                # Debug log method presence
                print(f"Has on_game_started method: {hasattr(stats_hooks, 'on_game_started')}")

                result = stats_hooks.on_game_started(
                    player_count=len(self.players),
                    bet_amount=self.bet_amount,
                    is_demo_mode=self.is_demo_mode
                )
                print(f"Added game start event to stats via event hooks (demo mode: {self.is_demo_mode}): {result}")

                # Check if any events are in the queue
                if hasattr(stats_hooks, 'queue_lock') and hasattr(stats_hooks, 'event_queue'):
                    with stats_hooks.queue_lock:
                        print(f"Events in queue: {len(stats_hooks.event_queue)}")
                        print(f"Queue contents: {stats_hooks.event_queue}")

                # Check if worker thread is running
                if hasattr(stats_hooks, 'worker_thread'):
                    print(f"Worker thread is alive: {stats_hooks.worker_thread.is_alive()}")
            else:
                # Fallback to direct stats page integration if available
                print("Fallback to direct StatsPage integration")
                try:
                    # First check if StatsPage module can be imported
                    import importlib
                    stats_page_spec = importlib.util.find_spec('stats_page')
                    if stats_page_spec is not None:
                        print("stats_page module is available")
                    else:
                        print("WARNING: stats_page module could not be imported")

                    from stats_page import StatsPage

                    # Debug log method presence
                    print(f"Has add_game_start_event method: {hasattr(StatsPage, 'add_game_start_event')}")

                    StatsPage.add_game_start_event(len(self.players), self.bet_amount, self.is_demo_mode)
                    print(f"Added game start event to stats via StatsPage (demo mode: {self.is_demo_mode})")
                except Exception as stats_page_e:
                    print(f"Error importing stats_page or calling add_game_start_event: {stats_page_e}")
                    import traceback
                    traceback.print_exc()
        except Exception as e:
            print(f"Error adding game start event to stats: {e}")
            import traceback
            traceback.print_exc()

        # Try to directly access thread_safe_db to verify database connectivity
        try:
            print("-" * 50)
            print("DIRECTLY TESTING DATABASE CONNECTION")
            print("-" * 50)
            import thread_safe_db

            # Test connection to database
            conn = thread_safe_db.get_connection()
            cursor = conn.cursor()

            # Get current time for the check
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Insert a test record directly
            try:
                cursor.execute('SELECT COUNT(*) FROM game_history')
                count_before = cursor.fetchone()[0]
                print(f"Current game_history record count: {count_before}")

                # Try to create a direct test record if not in demo mode
                if not self.is_demo_mode:
                    print("Attempting to directly insert a test record")
                    cursor.execute('''
                    INSERT INTO game_history
                    (date_time, username, cartella_number, claim_type, prize_amount, commission, game_duration, player_count, called_numbers)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (current_time, "DIRECT_TEST", 999, "TEST", 100, 20, 60, len(self.players), "1,2,3,4,5"))

                    conn.commit()

                    cursor.execute('SELECT COUNT(*) FROM game_history')
                    count_after = cursor.fetchone()[0]
                    print(f"Game_history record count after test: {count_after}")
                    print(f"Test record added successfully: {count_after > count_before}")
                else:
                    print("Skipping direct test record insert due to demo mode")
            except Exception as insert_e:
                print(f"Error inserting test record: {insert_e}")

            # Check daily_stats table
            try:
                today = datetime.datetime.now().strftime("%Y-%m-%d")
                cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (today,))
                result = cursor.fetchone()
                if result:
                    print(f"Found daily stats for today: {result}")
                else:
                    print("No daily stats found for today")
            except Exception as daily_e:
                print(f"Error checking daily stats: {daily_e}")

        except Exception as db_e:
            print(f"Error directly testing database: {db_e}")
            import traceback
            traceback.print_exc()

        # Start a new game
        self.game_state.start_game()

    def _execute_pause_game(self):
        """Actually pause the game after the delay"""
        print("Executing delayed game pause")
        # Reset the sound playing flag
        self._pause_sound_playing = False
        # Pause the game
        self.game_state.pause_game()

    def _execute_resume_game(self):
        """Actually resume the game after the delay"""
        print("Executing delayed game resume")

        # Verify that the announcement has completed playing
        if hasattr(self, 'announcement_channel') and self.announcement_channel.get_busy():
            print("Warning: Game started announcement is still playing. Waiting for it to complete...")
            # Wait a short time for the announcement to complete
            pygame.time.wait(100)  # Wait 100ms

            # If it's still playing, force it to stop to avoid overlap with number calling
            if self.announcement_channel.get_busy():
                print("Stopping announcement to proceed with game resume")
                self.announcement_channel.stop()

        # Resume the game
        self.game_state.resume_game()

    def resume_game_with_audio(self):
        """Resume the game with audio announcement"""
        # Play game started announcement on the dedicated channel
        if hasattr(self, 'game_started_sound') and self.game_started_sound and hasattr(self, 'announcement_channel'):
            # Stop any currently playing announcements
            self.announcement_channel.stop()

            # Play the game started announcement on the dedicated channel
            self.announcement_channel.play(self.game_started_sound)
            print("Playing game started announcement for resume")

            # Make sure we have an accurate duration for the sound
            try:
                # Recalculate the duration to ensure it's accurate
                actual_duration = int(self.game_started_sound.get_length() * 1000)
                buffer_ms = 500  # 500ms buffer to ensure sound completes
                self.game_started_sound_duration = actual_duration + buffer_ms
                print(f"Updated game started sound duration for resume: {self.game_started_sound_duration}ms (actual: {actual_duration}ms + {buffer_ms}ms buffer)")
            except Exception as e:
                # Keep existing duration if we can't get the actual duration
                print(f"Using existing game started sound duration for resume: {self.game_started_sound_duration}ms")

            # Schedule the actual game resume after a delay
            self.pending_action = "resume_game"
            self.action_delay_start = pygame.time.get_ticks()
            self.action_delay_duration = self.game_started_sound_duration  # Use the sound duration
            print(f"Game will resume in {self.action_delay_duration/1000} seconds")
        else:
            # Resume immediately if no sound available
            self._execute_resume_game()

    def check_button_click(self, pos):
        """Check if a button was clicked at the given position and perform the associated action"""
        # First check if any modal overlay buttons were clicked
        if hasattr(self, 'ui_handler'):
            if self.ui_handler.check_modal_button_click(pos):
                # If a modal button was clicked, always redraw the screen
                pygame.display.flip()
                return True

        # Initialize variable to track if we need to redraw
        need_redraw = False

        # Check for clicks in hit areas
        for key, rect in self.hit_areas.items():
            # Navigation bar items
            if key.startswith("nav_") and rect.collidepoint(pos):
                nav_item = key.replace("nav_", "")

                # Play click sound
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle navigation item clicks
                if nav_item == "stats":
                    # Navigate to stats page with screen mode consistency
                    try:
                        from stats_page import show_stats_page
                        # Ensure screen mode consistency before navigation
                        current_screen = self.screen_mode_manager.ensure_consistent_mode(screen)
                        show_stats_page(current_screen, previous_page="main_game")
                        need_redraw = True
                        return need_redraw
                    except Exception as e:
                        print(f"Error showing stats page: {e}")
                        self.message = f"Error loading stats page: {str(e)}"
                        self.message_type = "error"
                        self.message_timer = 180
                        need_redraw = True
                        return need_redraw
                elif nav_item == "play":
                    # Show a simple message that we're already on the main game page
                    self.message = "Already on the main game page"
                    self.message_type = "info"
                    self.message_timer = 180  # Display for 3 seconds
                    need_redraw = True
                    return need_redraw
                elif nav_item == "preview":
                    # Show cartella preview overlay
                    if hasattr(self, 'cartella_preview'):
                        self.cartella_preview.show()
                        need_redraw = True
                        return need_redraw
                    else:
                        self.message = "Preview feature not available"
                        self.message_type = "error"
                        self.message_timer = 180
                        need_redraw = True
                        return need_redraw
                elif nav_item == "settings":
                    # Navigate to settings page with screen mode consistency
                    try:
                        from settings_page import show_settings_page
                        # Ensure screen mode consistency before navigation
                        current_screen = self.screen_mode_manager.ensure_consistent_mode(screen)
                        show_settings_page(current_screen)
                        need_redraw = True
                        return need_redraw
                    except Exception as e:
                        print(f"Error showing settings page: {e}")
                        self.message = f"Error loading settings page: {str(e)}"
                        self.message_type = "error"
                        self.message_timer = 180
                        need_redraw = True
                        return need_redraw
                elif nav_item == "help":
                    # Show help message
                    self.message = "Help feature coming soon"
                    self.message_type = "info"
                    self.message_timer = 180
                    need_redraw = True
                    return need_redraw

            # Cartella selection
            elif key.startswith("cartella_") and rect.collidepoint(pos):
                cartella_number = int(key.replace("cartella_", ""))
                self.cartella_number = cartella_number
                self.load_board_for_cartella()
                need_redraw = True
                return need_redraw

            elif key == "add_player" and rect.collidepoint(pos):
                # Handle add player button click
                # Enable UI for entering player info
                self.input_active = True
                self.input_text = ""
                # Update button animation state
                if "add_player" in self.button_states:
                    self.button_states["add_player"]["click"] = True
                    self.button_states["add_player"]["click_time"] = pygame.time.get_ticks()
                # Play click sound
                if self.button_click_sound:
                    self.button_click_sound.play()
                need_redraw = True

            elif key == "view_players" and rect.collidepoint(pos):
                # Handle view players button click
                if not self.show_view_players:
                    # Create view players popup if it doesn't exist
                    if not self.view_players:
                        self.view_players = ViewPlayers(self.players)
                    # Make it visible
                    self.view_players.show()
                    self.show_view_players = True
                else:
                    # Hide it if it's already visible
                    if self.view_players:
                        self.view_players.hide()
                    self.show_view_players = False
                # Update button animation state
                if "view_players" in self.button_states:
                    self.button_states["view_players"]["click"] = True
                    self.button_states["view_players"]["click_time"] = pygame.time.get_ticks()
                # Play click sound
                if self.button_click_sound:
                    self.button_click_sound.play()
                need_redraw = True

            elif key == "start_game" and rect.collidepoint(pos):
                # Handle start button click
                if not self.game_started:
                    # Start the game
                    self.start_game()
                    # Play click sound
                    if self.button_click_sound:
                        self.button_click_sound.play()
                    need_redraw = True
                else:
                    # If the game is already started, check for debounce
                    current_time = pygame.time.get_ticks()
                    if current_time - self.last_pause_click_time > self.pause_debounce_ms:
                        # Toggle between pause and resume
                        if not self.game_state.is_paused:
                            self.game_state.pause_game()
                        else:
                            # Resume with audio announcement
                            self.resume_game_with_audio()
                        # Update last click time
                        self.last_pause_click_time = current_time
                        # Play click sound
                        if self.button_click_sound:
                            self.button_click_sound.play()
                        need_redraw = True

            elif key == "shuffle_button" and rect.collidepoint(pos):
                # Handle shuffle button click
                if not self.shuffle_active and not self.game_started:
                    # Start shuffle animation
                    self.start_shuffle_animation()
                    # Play click sound
                    if self.button_click_sound:
                        self.button_click_sound.play()
                    need_redraw = True

            elif key == "bet_input" and rect.collidepoint(pos):
                # Handle bet input field click
                self.bet_input_active = True
                self.bet_input_text = ""
                if hasattr(self, 'prize_pool_input_active'):
                    self.prize_pool_input_active = False
                self.input_active = False
                need_redraw = True

            elif key == "prize_pool" and rect.collidepoint(pos):
                # Handle prize pool click - activate prize pool input mode
                self.bet_input_active = True
                self.bet_input_text = ""
                self.prize_pool_input_active = True  # New attribute to track prize pool input mode
                self.input_active = False
                # Show hint message
                self.message = "Enter new prize pool amount"
                self.message_type = "info"
                self.message_timer = 180
                # Play click sound
                if self.button_click_sound:
                    self.button_click_sound.play()
                need_redraw = True

            elif key == "call_speed_decrease" and rect.collidepoint(pos):
                # Handle decrease call speed button click (increase speed by decreasing delay)
                # Decrease number_call_delay with minimum bound
                old_value = self.number_call_delay
                self.number_call_delay = max(0.5, self.number_call_delay - 0.5)

                # Only update if value actually changed
                if old_value != self.number_call_delay:
                    print(f"\n==== CALL SPEED DECREASE BUTTON CLICKED =====")
                    print(f"Previous value: {old_value}s")
                    print(f"New value: {self.number_call_delay}s")

                    # Always update the bingo caller delay if it exists, regardless of game state
                    # This ensures the setting is applied immediately
                    self.update_bingo_caller_delay()

                    # Show feedback message
                    self.message = f"Call speed increased to {self.number_call_delay:.1f}s"
                    self.message_type = "info"
                    self.message_timer = 120

                    # Play click sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # If settings window is open, update it to reflect the new value
                    if hasattr(self, 'settings_window') and self.settings_window.visible:
                        try:
                            print(f"Updating settings window to reflect new call speed: {self.number_call_delay}s")
                            from settings_manager import SettingsManager
                            settings_manager = SettingsManager()
                            settings_manager.set_setting('game', 'number_call_delay', self.number_call_delay)
                            settings_manager.save_settings()
                        except Exception as e:
                            print(f"Error updating settings window: {e}")

                    print(f"==== CALL SPEED DECREASE BUTTON COMPLETED ====\n")

                need_redraw = True

            elif key == "call_speed_increase" and rect.collidepoint(pos):
                # Handle increase call speed button click (decrease speed by increasing delay)
                # Increase number_call_delay with maximum bound
                old_value = self.number_call_delay
                self.number_call_delay = min(10.0, self.number_call_delay + 0.5)

                # Only update if value actually changed
                if old_value != self.number_call_delay:
                    print(f"\n==== CALL SPEED INCREASE BUTTON CLICKED =====")
                    print(f"Previous value: {old_value}s")
                    print(f"New value: {self.number_call_delay}s")

                    # Always update the bingo caller delay if it exists, regardless of game state
                    # This ensures the setting is applied immediately
                    self.update_bingo_caller_delay()

                    # Show feedback message
                    self.message = f"Call speed decreased to {self.number_call_delay:.1f}s"
                    self.message_type = "info"
                    self.message_timer = 120

                    # Play click sound
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # If settings window is open, update it to reflect the new value
                    if hasattr(self, 'settings_window') and self.settings_window.visible:
                        try:
                            print(f"Updating settings window to reflect new call speed: {self.number_call_delay}s")
                            from settings_manager import SettingsManager
                            settings_manager = SettingsManager()
                            settings_manager.set_setting('game', 'number_call_delay', self.number_call_delay)
                            settings_manager.save_settings()
                        except Exception as e:
                            print(f"Error updating settings window: {e}")

                    print(f"==== CALL SPEED INCREASE BUTTON COMPLETED ====\n")

                need_redraw = True

        return need_redraw

    def _apply_input(self):
        """Helper method to apply input text to cartella number"""
        if not self.input_text:
            return

        is_valid, error_msg = self.validate_cartella_number(self.input_text)
        if is_valid:
            old_number = self.cartella_number
            self.cartella_number = int(self.input_text)
            # Show success message only if number actually changed
            if old_number != self.cartella_number:
                # Load the corresponding board for the new cartella number
                self.load_board_for_cartella()
                self.message = f"Cartella number set to {self.cartella_number}"
                self.message_type = "success"
                self.message_timer = 180
        else:
            # Show error message
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180
            # Play error sound
            if self.button_click_sound:
                self.button_click_sound.play()

    def draw_bottom_sections(self, screen_width, screen_height, y_position):
        """Draw the bottom sections of the UI with a unified background"""
        # Apply the Add Players section background to the entire footer area
        section_bg_color_start = (20, 45, 60)
        section_bg_color_end = (25, 50, 65)

        # Create a background that spans the entire footer area
        footer_bg_rect = pygame.Rect(
            int(20 * scale_x),  # Left margin
            y_position,         # Top of the footer area
            screen_width - int(40 * scale_x),  # Full width minus margins
            int(screen_height * 0.15)  # Height of the footer area - reduced to match bottom_controls_height
        )
        self.draw_gradient_rect(footer_bg_rect, section_bg_color_start, section_bg_color_end, 10)

        # Use fixed positions for all elements to prevent overlap
        footer_height = footer_bg_rect.height

        # 1. Draw the prize pool on the far left (fixed width)
        prize_pool_width = int(120 * scale_x)  # Fixed width
        prize_pool_height = int(footer_height * 0.8)
        prize_pool_x = footer_bg_rect.x + int(20 * scale_x)  # Fixed position
        prize_pool_y = y_position + (footer_height - prize_pool_height) // 2

        # Draw prize pool with fixed position and size
        self.draw_prize_pool(
            x_position=prize_pool_x,  # Pass x position explicitly
            y_position=prize_pool_y,
            section_width=prize_pool_width,
            section_height=prize_pool_height
        )

        # 2. Draw the recent calls section in the center-left
        recent_calls_width = int(300 * scale_x)  # Fixed width
        recent_calls_height = int(footer_height * 0.8)
        # Position after prize pool with fixed gap
        recent_calls_x = prize_pool_x + prize_pool_width + int(50 * scale_x)
        recent_calls_y = y_position + (footer_height - recent_calls_height) // 2

        # Draw recent calls with fixed position and size
        self.draw_recent_calls(
            x=recent_calls_x,
            y=recent_calls_y,
            width=recent_calls_width,
            height=recent_calls_height
        )

        # 3. Draw the shuffle button between recent calls and current number
        shuffle_button_width = int(50 * scale_x)  # Fixed width
        shuffle_button_height = int(50 * scale_y)  # Fixed height
        # Position between recent calls and current number
        shuffle_button_x = recent_calls_x + recent_calls_width + int(30 * scale_x)
        shuffle_button_y = y_position + (footer_height - shuffle_button_height) // 2  # Center vertically

        # Draw shuffle button
        self.draw_shuffle_button(
            x=shuffle_button_x,
            y=shuffle_button_y,
            width=shuffle_button_width,
            height=shuffle_button_height
        )

        # 4. Draw the current called number on the right side
        # Fixed position on the right side of the screen
        called_circle_center_x = int(screen_width * 0.7)  # Fixed position at 70% of screen width
        called_circle_center_y = y_position + int(footer_height * 0.5)  # Center vertically
        # Increase circle radius by approximately 1.3x (from 55 to 72)
        circle_radius = int(72 * min(scale_x, scale_y))  # Enlarged by ~1.3x

        # Draw current called number with fixed position
        self.draw_current_called_circle(
            center_x=called_circle_center_x,
            center_y=called_circle_center_y,
            radius=circle_radius
        )

        # 5. Draw Start Game button on the far right
        button_width = int(160 * scale_x)  # Fixed width
        button_height = int(45 * scale_y)  # Fixed height
        # Fixed position on the far right
        button_x = screen_width - button_width - int(40 * scale_x)
        button_y = y_position + (footer_height - button_height) // 2  # Center vertically

        # Draw start game button with fixed position and get its rectangle
        start_button_rect = self.draw_start_game_button(
            x=button_x,
            y=button_y,
            width=button_width,
            height=button_height
        )

        # 6. Draw Call Speed control below the Start Game button
        call_speed_width = button_width  # Same width as start button
        call_speed_height = int(button_height * 0.7)  # More compact height (reduced from 0.85)
        call_speed_x = button_x  # Same x position as start button

        # Increase spacing between Start Game button and Call Speed control (from 10px to 20px)
        # and ensure it's positioned in the darker area below
        call_speed_y = start_button_rect.bottom + int(20 * scale_y)

        # Draw call speed control
        self.draw_call_delay_control(
            x=call_speed_x,
            y=call_speed_y,
            width=call_speed_width,
            height=call_speed_height
        )

        # Note: Add Players section has been removed as this functionality is already implemented in board_selection_fixed.py

    def draw_start_game_button(self, x, y, width, height):
        """Draw the Start Game button as shown in the second image with play icon"""
        start_button_rect = pygame.Rect(x, y, width, height)

        # Check if we're in demo mode (2 or fewer players)
        # Only update the flag if the game hasn't started yet
        if not self.game_started:
            is_demo_mode = len(self.players) <= 2
            self.is_demo_mode = is_demo_mode  # Update the flag only before game starts
        else:
            # Use the existing flag value when game is running
            is_demo_mode = self.is_demo_mode

        # Choose colors based on game state and demo mode
        if self.game_started:
            if self.is_demo_mode:
                # Demo mode + started: Gold/amber colors
                button_start = (120, 80, 0)  # Dark gold
                button_end = (150, 100, 0)  # Lighter gold
            else:
                # Normal mode + started: Purple
                button_start = (70, 0, 80)  # Purple
                button_end = (100, 10, 100)  # Lighter purple
        else:
            if self.is_demo_mode:
                # Demo mode + not started: Gold/amber colors
                button_start = (100, 70, 0)  # Dark gold
                button_end = (130, 90, 0)  # Lighter gold
            else:
                # Normal mode + not started: Teal
                button_start = (0, 70, 80)  # Darker teal
                button_end = (10, 90, 100)  # Slightly lighter teal

        # Draw the button with gradient
        self.draw_gradient_rect(start_button_rect, button_start, button_end, 12)

        # Add a subtle glow effect for demo mode
        if self.is_demo_mode:
            glow_size = 6
            glow_color = (255, 215, 0, 70)  # Gold with transparency
            glow_surface = pygame.Surface((start_button_rect.width + glow_size*2, start_button_rect.height + glow_size*2), pygame.SRCALPHA)
            pygame.draw.rect(glow_surface, glow_color, (0, 0, start_button_rect.width + glow_size*2, start_button_rect.height + glow_size*2), border_radius=15)
            screen.blit(glow_surface, (start_button_rect.x - glow_size, start_button_rect.y - glow_size))

        # Calculate positions for the play icon and text
        play_icon_size = int(height * 0.5)
        text_spacing = int(10 * scale_x)

        # Calculate total width needed for icon + spacing + text
        start_text_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

        # Change text based on game state and demo mode
        if self.game_started:
            button_text = "Pause Game"
        else:
            button_text = "Start Demo" if self.is_demo_mode else "Start Game"

        text_surf = start_text_font.render(button_text, True, WHITE)

        # Center the icon + text combination in the button
        total_width = play_icon_size + text_spacing + text_surf.get_width()
        start_x = start_button_rect.centerx - total_width/2

        # Draw the icon - white triangle pointing right for start, pause bars for pause
        icon_y = start_button_rect.centery
        icon_x = start_x + play_icon_size/2

        if not self.game_started:
            # Create the triangle points (play icon)
            triangle_points = [
                (icon_x - play_icon_size/2, icon_y - play_icon_size/2),  # Top left
                (icon_x + play_icon_size/2, icon_y),                    # Right point
                (icon_x - play_icon_size/2, icon_y + play_icon_size/2)   # Bottom left
            ]

            # Draw white triangle
            pygame.draw.polygon(screen, WHITE, triangle_points)
        else:
            # Draw pause icon (two vertical bars)
            bar_width = play_icon_size/4
            bar_height = play_icon_size/1.2
            bar_spacing = play_icon_size/6

            # Left bar
            pygame.draw.rect(screen, WHITE,
                             (icon_x - bar_spacing - bar_width, icon_y - bar_height/2,
                              bar_width, bar_height))

            # Right bar
            pygame.draw.rect(screen, WHITE,
                             (icon_x + bar_spacing, icon_y - bar_height/2,
                              bar_width, bar_height))

        # Draw the button text to the right of the icon
        text_rect = text_surf.get_rect(midleft=(start_x + play_icon_size + text_spacing, icon_y))
        screen.blit(text_surf, text_rect)

        # Add a "DEMO" label above the button if in demo mode
        if self.is_demo_mode:
            demo_font = pygame.font.SysFont("Arial", scaled_font_size(14), bold=True)
            demo_text = demo_font.render("DEMO MODE", True, (255, 215, 0))  # Gold color
            demo_rect = demo_text.get_rect(centerx=start_button_rect.centerx, bottom=start_button_rect.top - 2)

            # Draw a small background for the demo text
            demo_bg_rect = demo_rect.inflate(10, 4)
            demo_bg = pygame.Surface((demo_bg_rect.width, demo_bg_rect.height), pygame.SRCALPHA)
            demo_bg.fill((0, 0, 0, 150))  # Semi-transparent black
            screen.blit(demo_bg, demo_bg_rect)

            # Draw the demo text
            screen.blit(demo_text, demo_rect)

        # Store for hit detection
        self.hit_areas["start_game"] = start_button_rect

        # Return the button rect for positioning the call delay control
        return start_button_rect

    def draw_call_delay_control(self, x, y, width, height):
        """Draw the call delay adjustment controls below the Start Game button"""
        # Create control panel rect
        control_rect = pygame.Rect(x, y, width, height)

        # Define background colors based on game state - using darker colors for better contrast
        if self.game_started:
            # More vibrant when game is running, but darker for better positioning in the dark area
            bg_start = (25, 50, 70)  # Darker blue
            bg_end = (35, 70, 90)   # Lighter blue
        else:
            # More subdued when game is not running, even darker for contrast
            bg_start = (20, 30, 40)  # Darker gray-blue
            bg_end = (30, 40, 50)    # Lighter gray-blue

        # Draw the control panel background with smaller border radius for more minimal look
        self.draw_gradient_rect(control_rect, bg_start, bg_end, 6)  # Reduced from 8 to 6

        # Define text and button dimensions - more compact layout
        label_height = int(height * 0.4)  # Increased from 0.35 for better proportions
        value_height = int(height * 0.6)  # Reduced from 0.65 for more compact display
        button_width = int(height * 0.7)  # Slightly smaller buttons (from 0.8)
        button_size = int(height * 0.38)  # Slightly smaller + and - buttons (from 0.4)

        # Draw "Call Speed" label at the top with smaller font
        label_font = pygame.font.SysFont("Arial", scaled_font_size(12), bold=True)  # Reduced from 14
        label_text = label_font.render("Call Speed", True, WHITE)
        label_rect = label_text.get_rect(center=(x + width//2, y + label_height//2))
        screen.blit(label_text, label_rect)

        # Draw the current value with units - same size for better readability
        value_font = pygame.font.SysFont("Arial", scaled_font_size(16), bold=True)
        value_text = value_font.render(f"{self.number_call_delay:.1f}s", True, WHITE)
        value_rect = value_text.get_rect(center=(x + width//2, y + label_height + value_height//2 - int(4 * scale_y)))  # Moved up slightly
        screen.blit(value_text, value_rect)

        # Draw speed indicator text more subtly and compact with smaller font
        speed_text = ""
        if self.number_call_delay <= 1.5:
            speed_text = "FAST"
            speed_color = (255, 120, 50)  # Orange for fast
        elif self.number_call_delay <= 4.0:
            speed_text = "MEDIUM"
            speed_color = (255, 220, 50)  # Yellow for medium
        else:
            speed_text = "SLOW"
            speed_color = (50, 220, 100)  # Green for slow

        if speed_text:
            speed_font = pygame.font.SysFont("Arial", scaled_font_size(10), bold=True)  # Reduced from 12
            speed_label = speed_font.render(speed_text, True, speed_color)
            speed_rect = speed_label.get_rect(midtop=(value_rect.centerx, value_rect.bottom + 1))  # Less spacing (from 2)
            screen.blit(speed_label, speed_rect)

        # Calculate positions for decrease (-) and increase (+) buttons - move buttons closer to edges
        button_y = y + (height - button_size) // 2

        decrease_rect = pygame.Rect(
            x + int(width * 0.12) - button_size//2,  # Closer to left edge (from 0.15)
            button_y,
            button_size,
            button_size
        )

        increase_rect = pygame.Rect(
            x + int(width * 0.88) - button_size//2,  # Closer to right edge (from 0.85)
            button_y,
            button_size,
            button_size
        )

        # REMOVED: Check if game is running - buttons are now always enabled
        # buttons_enabled = self.game_started

        # Draw decrease button (-) with smaller border radius for minimal look
        # Always use active colors to indicate buttons are clickable at all times
        decrease_start = (60, 80, 120)  # Blue
        decrease_end = (80, 100, 150)   # Lighter blue

        self.draw_gradient_rect(decrease_rect, decrease_start, decrease_end, 3)  # Reduced radius from 4 to 3

        # Draw minus symbol - slightly smaller
        minus_width = int(button_size * 0.55)  # Reduced from 0.6
        minus_height = int(button_size * 0.15)
        pygame.draw.rect(screen, WHITE, (
            decrease_rect.centerx - minus_width//2,
            decrease_rect.centery - minus_height//2,
            minus_width,
            minus_height
        ))

        # Draw increase button (+) with smaller border radius
        # Always use active colors
        increase_start = (60, 80, 120)  # Blue
        increase_end = (80, 100, 150)   # Lighter blue

        self.draw_gradient_rect(increase_rect, increase_start, increase_end, 3)  # Reduced radius from 4 to 3

        # Draw plus symbol - slightly smaller
        plus_width = int(button_size * 0.55)  # Reduced from 0.6
        plus_height = int(button_size * 0.15)
        # Horizontal line
        pygame.draw.rect(screen, WHITE, (
            increase_rect.centerx - plus_width//2,
            increase_rect.centery - plus_height//2,
            plus_width,
            plus_height
        ))
        # Vertical line
        pygame.draw.rect(screen, WHITE, (
            increase_rect.centerx - plus_height//2,
            increase_rect.centery - plus_width//2,
            plus_height,
            plus_width
        ))

        # Store hit areas for buttons - ALWAYS register hit areas regardless of game state
        self.hit_areas["call_speed_decrease"] = decrease_rect
        self.hit_areas["call_speed_increase"] = increase_rect

        return control_rect

    def add_player(self, bet_amount=None):
        """Add a new player with the current cartella number"""
        if bet_amount is None:
            bet_amount = self.bet_amount

        # Validate the cartella number
        is_valid, error_msg = self.validate_cartella_number(self.cartella_number)
        if not is_valid:
            # Play error sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            # Set error message
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180
            return False

        # Create new player
        new_player = Player(
            cartela_no=self.cartella_number,
            bet_amount=bet_amount,
            deposited=0  # New players start with 0 deposit
        )
        self.players.append(new_player)

        # Add to session players list to track current session
        self.session_players.append(new_player)

        # Recalculate prize pool after adding player
        self.calculate_prize_pool()

        # Save players to JSON
        self.save_players()

        # Set success message
        self.message = f"Added player with cartella #{new_player.cartela_no}!"
        self.message_type = "success"
        self.message_timer = 180  # Display for 3 seconds

        return True

    def save_players(self):
        """Save players to JSON file"""
        success = save_players_to_json(self.players)
        if not success:
            self.message = "Error saving players data"
            self.message_type = "error"
            self.message_timer = 180
        return success

    def load_players(self):
        """Load players from JSON file"""
        loaded_players = load_players_from_json()
        if loaded_players:
            self.players = loaded_players
            return True
        return False

    def draw_current_called_circle(self, center_x, center_y, radius):
        """Draw the current called number circle with optimized smooth animations"""
        # ANIMATION FIX: Use consistent timing for all animations
        current_time = time.time()

        # PERFORMANCE FIX: Cache glow surface to prevent recreation every frame
        glow_cache_key = f"current_glow_{radius}_{int(current_time * 2) % 10}"  # Update every 0.5s

        if not hasattr(self, '_current_glow_cache'):
            self._current_glow_cache = {}

        # ANIMATION FIX: Smooth pulsating effect with consistent timing
        pulse = (math.sin(current_time * 2) + 1) / 2  # Consistent 2Hz pulse

        # PERFORMANCE FIX: Only recreate glow surface when needed
        if glow_cache_key not in self._current_glow_cache:
            glow_radius = radius * 1.15
            glow_surface = pygame.Surface((glow_radius*2, glow_radius*2), pygame.SRCALPHA)

            # ANIMATION FIX: Simplified glow layers for smoother performance
            for i in range(2):  # Reduced from 3 to 2 layers
                glow_size = max(1, glow_radius - (i * 8))  # Prevent zero/negative size
                alpha = max(10, 60 - (i * 25))  # Ensure minimum alpha
                pygame.draw.circle(glow_surface, (255, 255, 100, alpha),
                                 (glow_radius, glow_radius), int(glow_size))

            # Cache the glow surface
            self._current_glow_cache[glow_cache_key] = glow_surface

            # MEMORY FIX: Limit cache size
            if len(self._current_glow_cache) > 10:
                oldest_key = next(iter(self._current_glow_cache))
                del self._current_glow_cache[oldest_key]

        # ANIMATION FIX: Apply pulse effect to positioning for smooth animation
        glow_surface = self._current_glow_cache[glow_cache_key]
        glow_radius = radius * 1.15
        pulse_offset = int(2 * pulse)  # Subtle movement

        # Position and draw the glow with smooth pulse animation
        screen.blit(glow_surface, (center_x - glow_radius + pulse_offset,
                                  center_y - glow_radius + pulse_offset))

        # Draw outer metallic ring with gradient effect
        border_thickness = int(4 * min(scale_x, scale_y))  # Slightly thicker border

        # Create a metallic gold gradient for the border
        for i in range(border_thickness):
            # Calculate gradient color - from darker to brighter gold
            ratio = i / border_thickness
            r = int(180 + (75 * ratio))  # 180-255 range
            g = int(140 + (75 * ratio))  # 140-215 range
            b = int(20 + (30 * ratio))   # 20-50 range
            border_color = (r, g, b)

            # Draw concentric circles with decreasing radius for gradient effect
            border_radius = radius - i
            pygame.draw.circle(screen, border_color, (center_x, center_y), border_radius, 1)

        # Inner yellow circle with radial gradient for 3D effect
        inner_radius = radius - border_thickness

        # Create a surface for the gradient fill
        circle_surface = pygame.Surface((inner_radius*2, inner_radius*2), pygame.SRCALPHA)

        # Draw radial gradient from bright center to slightly darker edges
        for i in range(inner_radius, 0, -1):
            # Calculate gradient color
            ratio = i / inner_radius
            r = int(255 - (40 * (1-ratio)))  # Brighter in center
            g = int(215 - (30 * (1-ratio)))
            b = int(0 + (20 * ratio))  # Add a bit of blue near the center for shine
            gradient_color = (r, g, b)

            # Draw concentric circles from outside in
            pygame.draw.circle(circle_surface, gradient_color, (inner_radius, inner_radius), i, 1)

        # Add a bright highlight at the top-left for 3D effect
        highlight_pos = (inner_radius - inner_radius//3, inner_radius - inner_radius//3)
        highlight_radius = inner_radius // 4
        highlight_color = (255, 255, 200, 150)  # Semi-transparent white
        pygame.draw.circle(circle_surface, highlight_color, highlight_pos, highlight_radius)

        # Position and draw the gradient-filled circle
        screen.blit(circle_surface, (center_x - inner_radius, center_y - inner_radius))

        # ANIMATION FIX: Display current number with optimized smooth rendering
        if self.current_number:
            # PERFORMANCE FIX: Cache font and text surfaces to prevent recreation
            font_cache_key = f"current_font_{scaled_font_size(110)}"
            text_cache_key = f"current_text_{self.current_number}_{int(current_time * 1.5) % 20}"  # Update every ~0.67s

            if not hasattr(self, '_current_text_cache'):
                self._current_text_cache = {}

            # ANIMATION FIX: Consistent timing for color animation
            color_pulse = (math.sin(current_time * 1.5) + 1) / 2  # Smooth 1.5Hz color pulse

            # PERFORMANCE FIX: Cache font creation
            if font_cache_key not in self._current_text_cache:
                current_num_font = pygame.font.SysFont("Arial", scaled_font_size(110), bold=True)
                self._current_text_cache[font_cache_key] = current_num_font
            else:
                current_num_font = self._current_text_cache[font_cache_key]

            # ANIMATION FIX: Smooth color transition without stacking
            if text_cache_key not in self._current_text_cache:
                # Create smooth metallic color transition
                r = int(200 + (55 * color_pulse))  # 200-255 range
                g = int(20 + (180 * color_pulse))  # 20-200 range
                b = int(0 + (50 * color_pulse))    # 0-50 range
                metallic_color = (r, g, b)

                # PERFORMANCE FIX: Pre-render text layers as single surface
                text_surface = pygame.Surface((scaled_font_size(130), scaled_font_size(130)), pygame.SRCALPHA)
                text_center = (scaled_font_size(65), scaled_font_size(65))

                # Layer 1: Shadow (simplified)
                shadow_offset = int(3 * min(scale_x, scale_y))
                shadow_text = current_num_font.render(str(self.current_number), True, (80, 20, 20))
                shadow_rect = shadow_text.get_rect(center=(text_center[0] + shadow_offset, text_center[1] + shadow_offset))
                text_surface.blit(shadow_text, shadow_rect)

                # Layer 2: Main text
                main_text = current_num_font.render(str(self.current_number), True, metallic_color)
                main_rect = main_text.get_rect(center=text_center)
                text_surface.blit(main_text, main_rect)

                # Layer 3: Highlight (simplified)
                highlight_text = current_num_font.render(str(self.current_number), True, (255, 255, 255, 120))
                highlight_rect = highlight_text.get_rect(center=(text_center[0] - shadow_offset//2, text_center[1] - shadow_offset//2))
                text_surface.blit(highlight_text, highlight_rect)

                # Cache the complete text surface
                self._current_text_cache[text_cache_key] = text_surface

                # MEMORY FIX: Limit cache size
                if len(self._current_text_cache) > 25:
                    oldest_key = next(iter(self._current_text_cache))
                    del self._current_text_cache[oldest_key]

            # ANIMATION FIX: Draw cached text surface with smooth positioning
            text_surface = self._current_text_cache[text_cache_key]
            text_rect = text_surface.get_rect(center=(center_x, center_y))

            # ANIMATION FIX: Subtle breathing effect without stacking
            breath_offset = int(1 * math.sin(current_time * 3))  # Gentle breathing
            screen.blit(text_surface, (text_rect.x + breath_offset, text_rect.y + breath_offset))

            # ANIMATION FIX: Controlled sparkle effects (no random stacking)
            sparkle_time = int(current_time * 2) % 10  # Sparkle every 5 seconds
            if sparkle_time == 0:  # Only sparkle at specific intervals
                sparkle_size = int(4 * min(scale_x, scale_y))
                # Fixed sparkle positions to prevent random stacking
                sparkle_positions = [
                    (center_x - radius//3, center_y - radius//3),
                    (center_x + radius//3, center_y + radius//3),
                ]
                for sparkle_x, sparkle_y in sparkle_positions:
                    pygame.draw.circle(screen, (255, 255, 255), (sparkle_x, sparkle_y), sparkle_size)
                    pygame.draw.circle(screen, (255, 255, 200), (sparkle_x, sparkle_y), sparkle_size//2)

        # "current called" text with enhanced styling
        current_font = pygame.font.SysFont("Arial", scaled_font_size(22), italic=True, bold=True)

        # Create a subtle glow effect for the text
        glow_text = current_font.render("current called", True, (200, 200, 255, 150))
        glow_rect = glow_text.get_rect(center=(center_x, center_y + radius + int(25 * scale_y)))
        screen.blit(glow_text, (glow_rect.x + 1, glow_rect.y + 1))

        # Main text
        current_text = current_font.render("current called", True, WHITE)
        text_rect = current_text.get_rect(center=(center_x, center_y + radius + int(25 * scale_y)))
        screen.blit(current_text, text_rect)

    def draw_shuffle_button(self, x, y, width, height):
        """Draw a visually appealing shuffle button"""
        # Create the button rectangle
        shuffle_button_rect = pygame.Rect(x, y, width, height)

        # Check if button should be disabled (game is active)
        is_disabled = self.game_started

        # Define colors for the button with rainbow effect during shuffle
        if hasattr(self, 'shuffle_active') and self.shuffle_active:
            # Active state with rainbow color cycling
            hue = (time.time() * 2) % 1.0  # Cycle through hues
            r, g, b = colorsys.hsv_to_rgb(hue, 0.8, 0.8)

            # Convert to 0-255 range
            button_start_color = (int(r * 180), int(g * 100), int(b * 255))  # Darker shade
            button_end_color = (int(r * 255), int(g * 180), int(b * 255))   # Brighter shade
        elif is_disabled:
            # Disabled state colors (grayed out)
            button_start_color = (50, 50, 60)  # Dark gray
            button_end_color = (70, 70, 80)    # Slightly lighter gray
        else:
            # Normal state colors
            button_start_color = (0, 80, 100)  # Deep teal
            button_end_color = (0, 120, 140)  # Brighter teal

        # Draw the button with gradient
        self.draw_gradient_rect(shuffle_button_rect, button_start_color, button_end_color, 10)

        # Draw shuffle icon (two curved arrows in a circle)
        icon_size = int(min(width, height) * 0.6)
        icon_center_x = x + width // 2
        icon_center_y = y + height // 2

        # Check if button is disabled
        is_disabled = self.game_started

        # Add rotation animation during shuffle (only if not disabled)
        if hasattr(self, 'shuffle_active') and self.shuffle_active and not is_disabled:
            # Calculate rotation based on time
            rotation = (time.time() * 360) % 360  # Full rotation every second

            # Create a surface for the rotating icon
            icon_surface = pygame.Surface((icon_size, icon_size), pygame.SRCALPHA)

            # Draw the circular arrows on the surface
            # First arrow (clockwise)
            arrow_points1 = [
                (icon_size//2 - icon_size//4, icon_size//2 - icon_size//3),  # Tip
                (icon_size//2 - icon_size//2, icon_size//2 - icon_size//6),  # Left point
                (icon_size//2 - icon_size//4, icon_size//2)                  # Bottom point
            ]

            # Second arrow (counter-clockwise)
            arrow_points2 = [
                (icon_size//2 + icon_size//4, icon_size//2 + icon_size//3),  # Tip
                (icon_size//2 + icon_size//2, icon_size//2 + icon_size//6),  # Right point
                (icon_size//2 + icon_size//4, icon_size//2)                  # Top point
            ]

            # Draw circular path for the arrows
            pygame.draw.arc(icon_surface, WHITE,
                           (icon_size//4, icon_size//4,
                            icon_size//2, icon_size//2),
                           math.pi/4, math.pi*7/4, 2)

            pygame.draw.arc(icon_surface, WHITE,
                           (icon_size//4, icon_size//4,
                            icon_size//2, icon_size//2),
                           math.pi*7/4, math.pi*15/4, 2)

            # Draw the arrow heads
            pygame.draw.polygon(icon_surface, WHITE, arrow_points1)
            pygame.draw.polygon(icon_surface, WHITE, arrow_points2)

            # Rotate the surface
            rotated_surface = pygame.transform.rotate(icon_surface, rotation)

            # Position the rotated surface centered on the button
            rotated_rect = rotated_surface.get_rect(center=(icon_center_x, icon_center_y))
            screen.blit(rotated_surface, rotated_rect.topleft)
        else:
            # Draw static arrows when not shuffling
            # First arrow (clockwise)
            arrow_points1 = [
                (icon_center_x - icon_size//4, icon_center_y - icon_size//3),  # Tip
                (icon_center_x - icon_size//2, icon_center_y - icon_size//6),  # Left point
                (icon_center_x - icon_size//4, icon_center_y)                  # Bottom point
            ]

            # Second arrow (counter-clockwise)
            arrow_points2 = [
                (icon_center_x + icon_size//4, icon_center_y + icon_size//3),  # Tip
                (icon_center_x + icon_size//2, icon_center_y + icon_size//6),  # Right point
                (icon_center_x + icon_size//4, icon_center_y)                  # Top point
            ]

            # Choose color based on disabled state
            if is_disabled:
                # Grayed out color for disabled state
                icon_color = (150, 150, 150)  # Light gray
            else:
                # Normal color
                icon_color = WHITE

            # Draw circular path for the arrows
            pygame.draw.arc(screen, icon_color,
                           (icon_center_x - icon_size//2, icon_center_y - icon_size//2,
                            icon_size, icon_size),
                           math.pi/4, math.pi*7/4, 2)

            pygame.draw.arc(screen, icon_color,
                           (icon_center_x - icon_size//2, icon_center_y - icon_size//2,
                            icon_size, icon_size),
                           math.pi*7/4, math.pi*15/4, 2)

            # Draw the arrow heads
            pygame.draw.polygon(screen, icon_color, arrow_points1)
            pygame.draw.polygon(screen, icon_color, arrow_points2)

            # Add a "disabled" indicator if game is active
            if is_disabled:
                # Draw a small "X" or slash over the button to indicate it's disabled
                slash_width = 2
                pygame.draw.line(screen, (200, 50, 50),  # Red color
                               (x + int(width * 0.25), y + int(height * 0.25)),
                               (x + int(width * 0.75), y + int(height * 0.75)),
                               slash_width)
                pygame.draw.line(screen, (200, 50, 50),  # Red color
                               (x + int(width * 0.75), y + int(height * 0.25)),
                               (x + int(width * 0.25), y + int(height * 0.75)),
                               slash_width)

        # Add a pulsating effect if shuffle is active and not disabled
        if hasattr(self, 'shuffle_active') and self.shuffle_active and not is_disabled:
            # Create a pulsating glow effect
            pulse = (math.sin(time.time() * 8) + 1) / 2  # Faster pulsing (8x)
            glow_size = int(width * (1.2 + 0.2 * pulse))  # Larger glow
            glow_surface = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)

            # Draw the glow with semi-transparency and rainbow colors
            hue = (time.time() * 1.5) % 1.0  # Slightly different cycle speed
            r, g, b = colorsys.hsv_to_rgb(hue, 0.8, 0.9)
            glow_color = (int(r * 255), int(g * 255), int(b * 255), 100)  # Rainbow glow with transparency

            pygame.draw.rect(glow_surface, glow_color,
                           (0, 0, glow_size, glow_size),
                           border_radius=10)

            # Position the glow centered on the button
            glow_x = x + (width - glow_size) // 2
            glow_y = y + (height - glow_size) // 2
            screen.blit(glow_surface, (glow_x, glow_y))

            # Add particles for extra visual appeal
            self.draw_shuffle_particles(x, y, width, height)

        # Store the button rect for hit detection
        self.hit_areas["shuffle_button"] = shuffle_button_rect

    def draw_shuffle_particles(self, x, y, width, height):
        """Draw particles around the shuffle button for extra visual appeal"""
        # Create particles based on time
        num_particles = 8
        center_x = x + width // 2
        center_y = y + height // 2
        radius = width // 2 + 10  # Slightly larger than button

        for i in range(num_particles):
            # Calculate position on a circle around the button
            angle = (time.time() * 3 + i * (2 * math.pi / num_particles)) % (2 * math.pi)
            particle_x = center_x + int(radius * math.cos(angle))
            particle_y = center_y + int(radius * math.sin(angle))

            # Calculate particle size with pulsating effect
            pulse = (math.sin(time.time() * 5 + i) + 1) / 2
            particle_size = int(5 * pulse) + 2

            # Calculate particle color (rainbow effect)
            hue = (time.time() + i * 0.1) % 1.0
            r, g, b = colorsys.hsv_to_rgb(hue, 0.8, 0.9)
            particle_color = (int(r * 255), int(g * 255), int(b * 255))

            # Draw the particle
            pygame.draw.circle(screen, particle_color, (particle_x, particle_y), particle_size)

    def draw_recent_calls(self, x, y, width, height):
        """Draw the 5 most recent called numbers with their BINGO letters"""
        # Create a container with a slightly darker background than the footer
        container_rect = pygame.Rect(x, y, width, height)
        container_bg_start = (15, 35, 50)  # Slightly darker than footer
        container_bg_end = (20, 40, 55)
        self.draw_gradient_rect(container_rect, container_bg_start, container_bg_end, 10)

        # Draw the header
        header_height = int(25 * scale_y)  # Reduced height to give more space to circles
        header_rect = pygame.Rect(x, y, width, header_height)
        header_bg_start = (30, 50, 70)  # Slightly lighter than container
        header_bg_end = (40, 60, 80)
        self.draw_gradient_rect(header_rect, header_bg_start, header_bg_end, 10)

        # Draw header text with a more appealing style
        header_font = pygame.font.SysFont("Arial", scaled_font_size(16), bold=True)  # Smaller font
        header_text = header_font.render("RECENT CALLS", True, (255, 220, 100))  # Gold color for header
        header_text_rect = header_text.get_rect(center=(x + width//2, y + header_height//2))
        screen.blit(header_text, header_text_rect)

        # Get the 5 most recent called numbers
        recent_numbers = []
        if hasattr(self, 'called_numbers') and self.called_numbers:
            # Get the last 5 numbers in reverse order (most recent first)
            recent_numbers = list(self.called_numbers[-5:])[::-1]

        # If we have fewer than 5 numbers, pad the list
        while len(recent_numbers) < 5:
            recent_numbers.append(None)

        # Calculate the size and spacing for each number circle - horizontal layout
        # Make circles a fixed size to ensure consistency
        circle_size = int(min(45 * scale_x, (height - header_height) * 0.7))
        # Distribute circles evenly across the width
        circle_spacing = (width - (circle_size * 5)) / 6

        # Draw each recent number in a horizontal row
        for i, number in enumerate(recent_numbers):
            if number is None:
                # Draw empty placeholder circle with low opacity
                placeholder_x = x + circle_spacing * (i + 1) + circle_size * i + circle_size // 2
                placeholder_y = y + header_height + (height - header_height) // 2

                # Draw placeholder circle
                pygame.draw.circle(screen, (100, 100, 100, 50), (placeholder_x, placeholder_y), circle_size // 2, 1)
                continue

            # Calculate position - horizontal layout
            circle_x = x + circle_spacing * (i + 1) + circle_size * i + circle_size // 2
            circle_y = y + header_height + (height - header_height) // 2

            # Get the BINGO letter for this number
            letter = self.get_letter_for_number(number)

            # Draw the circle with number and letter
            self.draw_recent_call_circle(circle_x, circle_y, circle_size // 2, number, letter, i)

    def render_text(self, text, font, color, antialias=True):
        """Render text with enhanced caching and performance optimizations"""
        # PERFORMANCE OPTIMIZATION: Create a more efficient cache key
        # Use font size and family instead of font object for better caching
        font_key = (font.get_height(), font.get_bold(), font.get_italic())
        cache_key = (text, font_key, color, antialias)

        # PERFORMANCE OPTIMIZATION: Batch cache cleanup for better performance
        current_time = time.time()
        if current_time - self._last_cache_cleanup > 30:  # More frequent cleanup
            self._cleanup_text_cache()
            self._last_cache_cleanup = current_time

        # Return cached text surface if available
        if cache_key in self._text_cache:
            # PERFORMANCE OPTIMIZATION: Simplified LRU update
            if cache_key in self._text_cache_order:
                self._text_cache_order.remove(cache_key)
            self._text_cache_order.append(cache_key)
            return self._text_cache[cache_key]

        # PERFORMANCE OPTIMIZATION: Batch text rendering for similar requests
        if self._performance_monitor['low_performance_mode']:
            # In low performance mode, use simpler rendering
            try:
                text_surface = font.render(str(text), False, color)  # Disable antialiasing
            except Exception:
                # Create a minimal fallback surface
                text_surface = pygame.Surface((len(str(text)) * 8, font.get_height()), pygame.SRCALPHA)
        else:
            # Normal quality rendering
            try:
                text_surface = font.render(str(text), antialias, color)
            except Exception:
                # Create a fallback surface for error cases
                height = font.get_height()
                width = max(10, len(str(text)) * (height // 2))
                text_surface = pygame.Surface((width, height), pygame.SRCALPHA)

        # Cache the result with size limit check
        if len(self._text_cache) < self._text_cache_max_size:
            self._text_cache[cache_key] = text_surface
            self._text_cache_order.append(cache_key)

        return text_surface

    def _cleanup_text_cache(self):
        """Efficiently clean up text cache"""
        # Remove oldest entries if cache is too large
        while len(self._text_cache) > self._text_cache_max_size:
            if self._text_cache_order:
                oldest_key = self._text_cache_order.pop(0)
                if oldest_key in self._text_cache:
                    del self._text_cache[oldest_key]
            else:
                break

    def get_letter_for_number(self, number):
        """Get the BINGO letter for a number"""
        if hasattr(self, 'bingo_caller') and self.bingo_caller:
            return self.bingo_caller.get_letter_for_number(number)
        else:
            # Calculate column (0-4) based on number range
            col = min(4, (number - 1) // 15)
            return ['B', 'I', 'N', 'G', 'O'][col]

    def draw_recent_call_circle(self, center_x, center_y, radius, number, letter, index):
        """Draw a circle with a number and its BINGO letter"""
        # Define colors based on the BINGO letter
        letter_colors = {
            'B': (255, 60, 60),    # Red for B
            'I': (50, 220, 100),   # Green for I
            'N': (80, 120, 255),   # Blue for N
            'G': (255, 60, 60),    # Red for G (matching B)
            'O': (255, 220, 50)    # Yellow for O
        }

        # Get color based on letter, default to white if letter not found
        color = letter_colors.get(letter, WHITE)

        # Calculate brightness based on recency (most recent is brightest)
        brightness = 1.0 - (index * 0.12)  # 100%, 88%, 76%, 64%, 52% - less dimming

        # Adjust color brightness
        adjusted_color = (
            int(color[0] * brightness),
            int(color[1] * brightness),
            int(color[2] * brightness)
        )

        # ANIMATION FIX: Smooth glow effect for recent calls
        if index < 2:  # Only for the 2 most recent calls
            # ANIMATION FIX: Consistent timing for all glow effects
            current_time = time.time()
            pulse = (math.sin(current_time * 3) + 1) / 2  # Consistent 3Hz pulse

            # PERFORMANCE FIX: Cache glow surfaces for recent calls
            glow_cache_key = f"recent_call_glow_{number}_{index}_{int(current_time * 3) % 6}"

            if not hasattr(self, '_recent_call_glow_cache'):
                self._recent_call_glow_cache = {}

            if glow_cache_key not in self._recent_call_glow_cache:
                # Calculate glow properties
                base_glow_size = int(radius * 1.2)
                pulse_variation = int(radius * 0.2 * pulse)
                glow_size = base_glow_size + pulse_variation

                # Create glow surface
                glow_surface = pygame.Surface((glow_size*2, glow_size*2), pygame.SRCALPHA)

                # ANIMATION FIX: Smooth alpha transition
                glow_alpha = max(50, 150 - (index * 50))  # Ensure minimum alpha
                pygame.draw.circle(glow_surface, (*adjusted_color, glow_alpha),
                                 (glow_size, glow_size), glow_size)

                # Cache the glow surface
                self._recent_call_glow_cache[glow_cache_key] = {
                    'surface': glow_surface,
                    'size': glow_size
                }

                # MEMORY FIX: Limit cache size
                if len(self._recent_call_glow_cache) > 12:
                    oldest_key = next(iter(self._recent_call_glow_cache))
                    del self._recent_call_glow_cache[oldest_key]

            # Draw cached glow
            glow_data = self._recent_call_glow_cache[glow_cache_key]
            glow_surface = glow_data['surface']
            glow_size = glow_data['size']
            screen.blit(glow_surface, (center_x - glow_size, center_y - glow_size))

        # Draw outer circle with letter color
        pygame.draw.circle(screen, adjusted_color, (center_x, center_y), radius)

        # Draw inner circle with gradient effect
        inner_radius = int(radius * 0.85)

        # Create a gradient fill for the inner circle
        for i in range(inner_radius, 0, -1):
            # Calculate gradient color
            gradient_factor = i / inner_radius  # 1.0 to 0.0
            inner_color = (
                max(0, int(adjusted_color[0] * (0.7 + 0.3 * gradient_factor))),
                max(0, int(adjusted_color[1] * (0.7 + 0.3 * gradient_factor))),
                max(0, int(adjusted_color[2] * (0.7 + 0.3 * gradient_factor)))
            )
            pygame.draw.circle(screen, inner_color, (center_x, center_y), i, 1)

        # Add a highlight at the top for 3D effect
        highlight_pos = (center_x, center_y - int(radius * 0.5))
        highlight_radius = int(radius * 0.2)
        highlight_color = (
            min(255, adjusted_color[0] + 70),
            min(255, adjusted_color[1] + 70),
            min(255, adjusted_color[2] + 70)
        )
        pygame.draw.circle(screen, highlight_color, highlight_pos, highlight_radius)

        # Draw the number with a slight shadow for better visibility
        number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)  # Increased size

        # Draw shadow
        shadow_color = (0, 0, 0, 180)  # Semi-transparent black
        shadow_offset = int(2 * scale_y)
        shadow_text = number_font.render(str(number), True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(center_x + shadow_offset, center_y - int(5 * scale_y) + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Draw main number text
        number_text = number_font.render(str(number), True, WHITE)
        number_rect = number_text.get_rect(center=(center_x, center_y - int(5 * scale_y)))
        screen.blit(number_text, number_rect)

        # Draw the letter with a stylized background
        letter_bg_radius = int(radius * 0.35)
        letter_bg_pos = (center_x, center_y + int(15 * scale_y))
        pygame.draw.circle(screen, (0, 0, 0, 128), letter_bg_pos, letter_bg_radius)  # Semi-transparent black

        # Draw letter text - use cached text rendering
        letter_font = pygame.font.SysFont("Arial", scaled_font_size(18), bold=True)  # Increased size
        letter_text = self.render_text(letter, letter_font, WHITE)
        letter_rect = letter_text.get_rect(center=letter_bg_pos)
        screen.blit(letter_text, letter_rect)

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0, glossy=False):
        """Draw a rectangle with a vertical gradient - heavily optimized for performance"""
        # PERFORMANCE OPTIMIZATION: Enhanced cache key with performance mode consideration
        cache_key = (rect.width, rect.height, color1, color2, border_radius, glossy, self._animation_quality)

        # Check if this gradient rect is already in cache
        if not hasattr(self, '_gradient_cache'):
            self._gradient_cache = {}
            self._gradient_cache_order = []

        # Use cached surface if available
        if cache_key in self._gradient_cache:
            # PERFORMANCE OPTIMIZATION: Simplified LRU update
            if cache_key in self._gradient_cache_order:
                self._gradient_cache_order.remove(cache_key)
            self._gradient_cache_order.append(cache_key)

            cached_surface = self._gradient_cache[cache_key]
            screen.blit(cached_surface, (rect.x, rect.y))
            return

        # PERFORMANCE OPTIMIZATION: Use surface pool to reduce allocations
        surf = self._get_surface_from_pool((rect.width, rect.height), pygame.SRCALPHA)
        surf.fill((0, 0, 0, 0))  # Clear the surface

        # PERFORMANCE OPTIMIZATION: Adaptive quality based on performance mode
        if self._performance_monitor['low_performance_mode']:
            # Low quality mode: Use larger steps for better performance
            step_size = max(4, rect.height // 8)  # Fewer steps for older hardware
        else:
            # Normal quality mode
            step_size = max(1, min(rect.height // 20, 6))

        # PERFORMANCE OPTIMIZATION: Pre-calculate all colors at once
        num_steps = (rect.height + step_size - 1) // step_size
        colors = []
        for i in range(num_steps):
            y_pos = i * step_size
            ratio = min(1.0, y_pos / rect.height)
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            colors.append((r, g, b))

        # PERFORMANCE OPTIMIZATION: Batch draw rectangles
        for i, y in enumerate(range(0, rect.height, step_size)):
            if i < len(colors):
                height = min(step_size, rect.height - y)
                pygame.draw.rect(surf, colors[i], (0, y, rect.width, height))

        # PERFORMANCE OPTIMIZATION: Simplified glossy effect for low performance mode
        if glossy and not self._performance_monitor['low_performance_mode']:
            highlight_height = int(rect.height * 0.25)  # Reduced from 0.3
            highlight_step = max(2, highlight_height // 6)  # Fewer steps

            for y in range(0, highlight_height, highlight_step):
                alpha = max(20, 80 - int(60 * y / highlight_height))  # Reduced alpha range
                height = min(highlight_step, highlight_height - y)
                pygame.draw.rect(surf, (255, 255, 255, alpha), (0, y, rect.width, height))

        # PERFORMANCE OPTIMIZATION: Simplified rounded corners
        if border_radius > 0 and not self._performance_monitor['low_performance_mode']:
            # Only apply rounded corners in normal performance mode
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)
            surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Cache the surface with size limit check
        if len(self._gradient_cache) < self._surface_cache_max_size:
            self._gradient_cache[cache_key] = surf.copy()
            self._gradient_cache_order.append(cache_key)

        # PERFORMANCE OPTIMIZATION: LRU cache cleanup
        while len(self._gradient_cache) > self._surface_cache_max_size:
            if self._gradient_cache_order:
                oldest_key = self._gradient_cache_order.pop(0)
                if oldest_key in self._gradient_cache:
                    del self._gradient_cache[oldest_key]
            else:
                break

        # Blit the surface to the screen
        screen.blit(surf, rect)

        # Return surface to pool for reuse
        self._return_surface_to_pool(surf, pygame.SRCALPHA)

    def draw_led_background(self, rect, pixel_size=4, border_radius=12):
        """Draw an LED-style background with a grid of small dots - optimized for performance and visual quality"""
        # Cache the LED background based on dimensions and pixel size
        cache_key = f"led_{rect.width}_{rect.height}_{pixel_size}_{border_radius}"

        # Check if we already have this background cached
        if hasattr(self, '_led_bg_cache') and cache_key in self._led_bg_cache:
            # Use the cached surface
            surf = self._led_bg_cache[cache_key]
            screen.blit(surf, rect)
            return

        # Create a surface for the LED background
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Fill with dark background color (deep blue-black for better contrast)
        surf.fill((5, 8, 15))

        # BALANCED OPTIMIZATION: Use a reasonable pixel size for good visuals with good performance
        actual_pixel_size = int(pixel_size * 1.5)  # Increase pixel size for better performance

        # Use a pattern-based approach for efficient rendering
        pattern_size = 40  # Pattern size that works well visually

        # Create a pattern tile that we'll reuse
        pattern = pygame.Surface((pattern_size, pattern_size), pygame.SRCALPHA)
        pattern.fill((5, 8, 15))  # Same dark background

        # Draw a grid of dots in the pattern
        dot_spacing = actual_pixel_size * 1.5  # Reasonable spacing between dots

        # Draw dots in the pattern with slight brightness variation
        for x in range(0, pattern_size, int(dot_spacing)):
            for y in range(0, pattern_size, int(dot_spacing)):
                # Add slight brightness variation for visual interest
                brightness_base = 25
                brightness_var = 5
                brightness = brightness_base + ((x + y) % brightness_var)

                # Draw dots with fixed size
                dot_size = actual_pixel_size
                pygame.draw.rect(pattern, (brightness, brightness, brightness+5),
                               (x, y, dot_size, dot_size))

        # Efficient tiling approach
        tiles_x = max(1, (rect.width + pattern_size - 1) // pattern_size)
        tiles_y = max(1, (rect.height + pattern_size - 1) // pattern_size)

        # Create a single row of tiles first (horizontal strip)
        row_surf = pygame.Surface((pattern_size * tiles_x, pattern_size), pygame.SRCALPHA)
        row_surf.fill((5, 8, 15))  # Fill with background color

        # Blit the pattern across the row
        for x in range(tiles_x):
            row_surf.blit(pattern, (x * pattern_size, 0))

        # Now blit the row multiple times vertically
        for y in range(tiles_y):
            surf.blit(row_surf, (0, y * pattern_size))

        # Add a proper border with slight gradient for better visual appeal
        frame_color = (50, 50, 60)  # Medium gray with slight blue tint
        highlight_color = (70, 70, 80)  # Slightly lighter for top edge
        shadow_color = (30, 30, 40)  # Slightly darker for bottom edge

        # Main border
        pygame.draw.rect(surf, frame_color, (0, 0, rect.width, rect.height),
                        2, border_radius=border_radius)

        # Add subtle highlight at the top (just inside the main border)
        if rect.height > 10 and rect.width > 10:  # Only for larger rectangles
            pygame.draw.line(surf, highlight_color, (3, 3), (rect.width-3, 3), 1)

            # Add subtle shadow at the bottom
            pygame.draw.line(surf, shadow_color,
                           (3, rect.height-3), (rect.width-3, rect.height-3), 1)

        # Cache the LED background
        if not hasattr(self, '_led_bg_cache'):
            self._led_bg_cache = {}
        self._led_bg_cache[cache_key] = surf

        # Limit cache size to prevent memory issues
        if len(self._led_bg_cache) > 10:  # Keep only 10 most recent backgrounds
            # Remove oldest cache entry
            oldest_key = next(iter(self._led_bg_cache))
            del self._led_bg_cache[oldest_key]

        # Blit the surface to the screen
        screen.blit(surf, rect)

    def draw_advertising_text(self, x, y, width, height):
        """Draw advertising text with modern GPU acceleration and beautiful effects"""
        try:
            # Use modern GPU-accelerated advertising if available
            if self.modern_advertising:
                self.modern_advertising.draw_advertising_text(x, y, width, height, screen)
                return
            else:
                # Fallback to legacy advertising system
                self._draw_legacy_advertising_text(x, y, width, height)

        except Exception as e:
            print(f"Modern advertising error: {e}")
            # Emergency fallback to simple text
            try:
                self._draw_emergency_fallback_advertising(x, y, width, height)
            except:
                pass  # Ignore fallback errors

    def _draw_legacy_advertising_text(self, x, y, width, height):
        """Legacy advertising text drawing method (fallback)"""
        # Get advertising settings from settings manager - but only check once per second
        current_time = time.time()
        if not hasattr(self, '_ad_settings_check_time') or current_time - self._ad_settings_check_time > 1.0:
            settings_manager = SettingsManager()
            self._ad_enabled = settings_manager.get_setting('advertising', 'enabled', True)
            self._ad_hidden = settings_manager.get_setting('advertising', 'hidden', False)
            self._ad_settings_check_time = current_time

        # Create the rect for click detection even when hidden
        self.ad_section_rect = pygame.Rect(x, y, width, height)

        # If disabled in settings or hidden by click, don't draw
        if not self._ad_enabled or self._ad_hidden:
            return

        # If in zoomed mode, draw the full-screen version instead
        if self.ad_zoomed:
            self.draw_zoomed_advertising()
            return

        # Calculate the position for the ad box (narrower width but taller height)
        ad_width = int(width * 0.7)  # Make the ad box 70% of the original width
        ad_x = x + int(width * 0.25)  # Start at 25% from the left edge
        bg_rect = pygame.Rect(ad_x, y, ad_width, height)
        self.ad_section_rect = bg_rect

        # ANIMATION FIX: Optimized text rendering with smooth animation timing
        # Update settings and render text with consistent intervals for smooth animation
        if not hasattr(self, '_ad_full_update_time') or current_time - self._ad_full_update_time > 1.0:  # Reduced from 2.0s to 1.0s
            # Get advertising text and styling - cache these values
            settings_manager = SettingsManager()
            self._ad_settings_cache = {
                'ad_text': settings_manager.get_setting('advertising', 'text', "PLACE THE ADVERT HERE!"),
                'font_name': settings_manager.get_setting('advertising', 'font', "Arial"),
                'font_size': settings_manager.get_setting('advertising', 'font_size', 24),
                'text_color_hex': settings_manager.get_setting('advertising', 'text_color', "#FFFFFF"),
                'scroll_speed': settings_manager.get_setting('advertising', 'scroll_speed', 2.0),
                'bold': settings_manager.get_setting('advertising', 'bold', True),
                'italic': settings_manager.get_setting('advertising', 'italic', False),
                'rainbow_text': settings_manager.get_setting('advertising', 'rainbow_text', True),
                'text_glow': settings_manager.get_setting('advertising', 'text_glow', True),
                'led_style': settings_manager.get_setting('advertising', 'led_style', True)
            }

            # Convert hex colors to RGB
            self._ad_settings_cache['text_color'] = settings_manager.hex_to_rgb(self._ad_settings_cache['text_color_hex'])

            # Get cached settings
            ad_text = self._ad_settings_cache['ad_text']
            font_name = self._ad_settings_cache['font_name']
            text_color = self._ad_settings_cache['text_color']
            rainbow_text = self._ad_settings_cache['rainbow_text']
            text_glow = self._ad_settings_cache['text_glow']
            italic = self._ad_settings_cache['italic']

            # Remove BOM (Byte Order Mark) if present
            if ad_text.startswith('\ufeff'):
                ad_text = ad_text.replace('\ufeff', '')

            # ANIMATION FIX: Consistent font sizing for smooth scaling
            optimal_font_size = int(height * 0.65)  # Target 65% of height
            scaled_size = int(optimal_font_size * min(scale_x, scale_y))

            # PERFORMANCE FIX: Cache font creation
            font_cache_key = f"ad_font_{font_name}_{scaled_size}_{italic}"
            if not hasattr(self, '_ad_font_cache'):
                self._ad_font_cache = {}

            if font_cache_key not in self._ad_font_cache:
                font = pygame.font.SysFont(font_name, scaled_size, bold=True, italic=italic)
                self._ad_font_cache[font_cache_key] = font
                # Limit font cache size
                if len(self._ad_font_cache) > 10:
                    oldest_key = next(iter(self._ad_font_cache))
                    del self._ad_font_cache[oldest_key]
            else:
                font = self._ad_font_cache[font_cache_key]

            # ANIMATION FIX: Smooth color transitions for rainbow text
            if rainbow_text:
                # Create smooth color cycling instead of fixed color
                color_cycle = (math.sin(current_time * 0.5) + 1) / 2  # Slow color cycle
                golden_colors = [
                    (200, 140, 20),   # Medium amber
                    (240, 180, 60),   # Bright gold
                    (255, 220, 100)   # Light gold
                ]
                # Interpolate between colors for smooth transition
                color_index = color_cycle * (len(golden_colors) - 1)
                base_index = int(color_index)
                blend_factor = color_index - base_index

                if base_index >= len(golden_colors) - 1:
                    text_color = golden_colors[-1]
                else:
                    color1 = golden_colors[base_index]
                    color2 = golden_colors[base_index + 1]
                    text_color = (
                        int(color1[0] * (1 - blend_factor) + color2[0] * blend_factor),
                        int(color1[1] * (1 - blend_factor) + color2[1] * blend_factor),
                        int(color1[2] * (1 - blend_factor) + color2[2] * blend_factor)
                    )

            # PERFORMANCE FIX: Render text surface once and cache it
            text_surf = font.render(ad_text, True, text_color)
            total_width = text_surf.get_width()

            # Cache the rendered text surface
            self._ad_text_cache = {
                'surface': text_surf,
                'width': total_width,
                'height': text_surf.get_height()
            }

            # ANIMATION FIX: Optimized glow effect with better caching
            if text_glow:
                glow_cache_key = f"ad_glow_{ad_text}_{scaled_size}_{text_color}"
                if not hasattr(self, '_ad_glow_cache_dict'):
                    self._ad_glow_cache_dict = {}

                if glow_cache_key not in self._ad_glow_cache_dict:
                    # Create optimized glow surface
                    padding = 8  # Reduced padding for better performance
                    glow_surf = pygame.Surface((total_width + padding*2, text_surf.get_height() + padding*2), pygame.SRCALPHA)

                    # ANIMATION FIX: Single glow layer for smooth performance
                    glow_color = (255, 220, 100, 30)  # Reduced opacity for subtlety
                    glow_text = font.render(ad_text, True, glow_color)

                    # Simplified glow with fewer offset copies
                    offsets = [(1, 1), (1, -1), (-1, 1), (-1, -1)]  # Reduced offset distance
                    for offset_x, offset_y in offsets:
                        glow_surf.blit(glow_text, (padding + offset_x, padding + offset_y))

                    # Cache the glow surface
                    self._ad_glow_cache_dict[glow_cache_key] = {
                        'surface': glow_surf,
                        'padding': padding
                    }

                    # Limit glow cache size
                    if len(self._ad_glow_cache_dict) > 5:
                        oldest_key = next(iter(self._ad_glow_cache_dict))
                        del self._ad_glow_cache_dict[oldest_key]

                # Use cached glow
                self._ad_glow_cache = self._ad_glow_cache_dict[glow_cache_key]

            # Update the full update timestamp
            self._ad_full_update_time = current_time

        # Draw the LED-style background on every frame
        # This ensures it doesn't blink out between updates
        self.draw_led_background(bg_rect)

        # ANIMATION FIX: Smooth scroll animation with consistent timing
        text_surf = self._ad_text_cache['surface']
        total_width = self._ad_text_cache['width']
        text_height = self._ad_text_cache['height']

        # Get scroll speed from cache
        scroll_speed = self._ad_settings_cache['scroll_speed']
        text_glow = self._ad_settings_cache['text_glow']

        # ANIMATION FIX: Smooth scroll calculation with high precision
        # Use continuous time for smooth animation without stepping
        scroll_distance = current_time * scroll_speed * 50  # Pixels per second
        scroll_cycle_length = ad_width + total_width + 50  # Add gap between repetitions
        scroll_offset = scroll_distance % scroll_cycle_length

        # ANIMATION FIX: Calculate smooth text position
        text_x = ad_x + ad_width - scroll_offset
        text_y = y + (height - text_height) // 2  # Center vertically

        # PERFORMANCE FIX: Create clipping rect once and reuse
        if not hasattr(self, '_ad_clip_rect') or self._ad_clip_rect != bg_rect:
            self._ad_clip_rect = bg_rect

        # Apply clipping to prevent text overflow
        original_clip = screen.get_clip()
        screen.set_clip(self._ad_clip_rect)

        # ANIMATION FIX: Clear the background area first to prevent stacking
        # This ensures smooth animation without visual artifacts
        clear_rect = pygame.Rect(ad_x, y, ad_width, height)
        pygame.draw.rect(screen, (0, 0, 0, 0), clear_rect)  # Clear with transparency

        # ANIMATION FIX: Draw glow with proper positioning
        if text_glow and hasattr(self, '_ad_glow_cache'):
            glow_surf = self._ad_glow_cache['surface']
            padding = self._ad_glow_cache['padding']

            # Primary glow position
            glow_x = text_x - padding
            glow_y = text_y - padding

            # Only draw if glow is visible in the clipping area
            if glow_x + glow_surf.get_width() > ad_x and glow_x < ad_x + ad_width:
                screen.blit(glow_surf, (glow_x, glow_y))

        # ANIMATION FIX: Draw main text with smooth positioning
        # Only draw if text is visible in the clipping area
        if text_x + total_width > ad_x and text_x < ad_x + ad_width:
            screen.blit(text_surf, (text_x, text_y))

        # ANIMATION FIX: Handle seamless text repetition for continuous scroll
        # Calculate when to show the repeated text for seamless looping
        repeat_threshold = ad_x + ad_width - total_width
        if text_x <= repeat_threshold:
            # Calculate repeat position for seamless scrolling
            repeat_x = text_x + scroll_cycle_length

            # Draw repeated glow if enabled and visible
            if text_glow and hasattr(self, '_ad_glow_cache'):
                repeat_glow_x = repeat_x - padding
                if repeat_glow_x + glow_surf.get_width() > ad_x and repeat_glow_x < ad_x + ad_width:
                    screen.blit(glow_surf, (repeat_glow_x, glow_y))

            # Draw repeated text if visible
            if repeat_x + total_width > ad_x and repeat_x < ad_x + ad_width:
                screen.blit(text_surf, (repeat_x, text_y))

        # Reset the clipping region
        screen.set_clip(original_clip)

    def _draw_emergency_fallback_advertising(self, x, y, width, height):
        """Emergency fallback advertising when all else fails"""
        try:
            # Simple background
            bg_rect = pygame.Rect(x, y, width, height)
            pygame.draw.rect(screen, (30, 50, 100, 180), bg_rect)
            pygame.draw.rect(screen, (100, 150, 255), bg_rect, 2)

            # Simple text
            font_size = min(height // 2, 24)
            font = pygame.font.SysFont("Arial", font_size, bold=True)
            text = "WOW Games - Premium Bingo Experience"
            text_surf = font.render(text, True, (255, 215, 0))

            # Center text
            text_x = x + (width - text_surf.get_width()) // 2
            text_y = y + (height - text_surf.get_height()) // 2

            screen.blit(text_surf, (text_x, text_y))

        except Exception as e:
            print(f"Emergency fallback advertising failed: {e}")

    def draw_zoomed_advertising(self):
        """Draw the advertising section in zoomed mode (full screen) - optimized with animation"""
        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Create a semi-transparent overlay for the background
        # Cache this overlay to avoid recreating it every frame
        overlay_key = f"overlay_{screen_width}_{screen_height}"
        if not hasattr(self, '_overlay_cache') or overlay_key not in self._overlay_cache:
            overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 200))  # Dark semi-transparent background
            if not hasattr(self, '_overlay_cache'):
                self._overlay_cache = {}
            self._overlay_cache[overlay_key] = overlay
        else:
            overlay = self._overlay_cache[overlay_key]

        # Draw the overlay
        screen.blit(overlay, (0, 0))

        # Calculate dimensions for the zoomed ad section (full width)
        ad_width = screen_width  # 100% of screen width
        ad_height = int(screen_height * 0.7)  # 70% of screen height
        ad_x = 0  # No horizontal margin
        ad_y = (screen_height - ad_height) // 2  # Center vertically

        # Create background rectangle with rounded corners
        bg_rect = pygame.Rect(ad_x, ad_y, ad_width, ad_height)

        # Store the ad section rect for click detection (full screen in zoomed mode)
        self.ad_section_rect = pygame.Rect(0, 0, screen_width, screen_height)

        # OPTIMIZATION: Cache the LED background
        # Only redraw it when screen size changes
        led_key = f"led_bg_{ad_width}_{ad_height}"
        if not hasattr(self, '_zoomed_led_cache') or led_key not in self._zoomed_led_cache:
            # Draw the LED-style background with larger pixel size for better visibility
            self.draw_led_background(bg_rect, pixel_size=8, border_radius=5)

            # Create a new cache if needed
            if not hasattr(self, '_zoomed_led_cache'):
                self._zoomed_led_cache = {}

            # We don't need to cache the actual surface since draw_led_background
            # already has its own caching. Just mark that we've drawn it.
            self._zoomed_led_cache[led_key] = True
        else:
            # Use the cached LED background by calling draw_led_background
            # which will use its internal cache
            self.draw_led_background(bg_rect, pixel_size=8, border_radius=5)

        # OPTIMIZATION: Cache settings and font
        # Only update these every 2 seconds
        current_time = time.time()
        if not hasattr(self, '_zoomed_settings_time') or current_time - self._zoomed_settings_time > 2.0:
            # Get advertising text and settings
            settings_manager = SettingsManager()
            self._zoomed_ad_text = settings_manager.get_setting('advertising', 'text', "PLACE THE ADVERT HERE!")
            self._zoomed_font_name = settings_manager.get_setting('advertising', 'font', "Arial")
            self._zoomed_scroll_speed = settings_manager.get_setting('advertising', 'scroll_speed', 2.0) * 0.7  # Slightly slower in zoomed mode
            self._zoomed_text_glow = settings_manager.get_setting('advertising', 'text_glow', True)

            # Remove BOM (Byte Order Mark) if present
            if self._zoomed_ad_text.startswith('\ufeff'):
                self._zoomed_ad_text = self._zoomed_ad_text.replace('\ufeff', '')

            # Use a fixed font size based on ad height
            optimal_font_size = int(ad_height * 0.4)  # Target 40% of height
            self._zoomed_font = pygame.font.SysFont(self._zoomed_font_name, optimal_font_size, bold=True)

            # Use a fixed gold color for text
            self._zoomed_text_color = (255, 190, 60)  # Bright gold

            # Render the text
            self._zoomed_text_surf = self._zoomed_font.render(self._zoomed_ad_text, True, self._zoomed_text_color)

            # Create glow effect if enabled
            if self._zoomed_text_glow:
                # Create a simple glow surface
                padding = 10
                glow_surf = pygame.Surface((self._zoomed_text_surf.get_width() + padding*2,
                                          self._zoomed_text_surf.get_height() + padding*2), pygame.SRCALPHA)

                # Use a single glow color
                glow_color = (255, 220, 100, 40)  # Light gold with low opacity

                # Render the text with glow color
                glow_text = self._zoomed_font.render(self._zoomed_ad_text, True, glow_color)

                # Create a simple glow effect with just 4 offset copies
                offsets = [(2, 2), (2, -2), (-2, 2), (-2, -2)]
                for offset_x, offset_y in offsets:
                    glow_surf.blit(glow_text, (padding + offset_x, padding + offset_y))

                self._zoomed_glow_surf = glow_surf
                self._zoomed_glow_padding = padding

            # Update the settings timestamp
            self._zoomed_settings_time = current_time

        # RESTORE ANIMATION: Calculate scroll position based on time
        # Use a slower update rate (0.1 seconds) to reduce CPU usage while maintaining animation
        scroll_time = int(current_time * 10) / 10
        scroll_offset = (scroll_time * self._zoomed_scroll_speed * 50) % (ad_width + self._zoomed_text_surf.get_width())
        text_x = ad_x + ad_width - scroll_offset
        text_y = (ad_height - self._zoomed_text_surf.get_height()) // 2 + ad_y

        # Create a clipping rect to keep text inside the ad box
        original_clip = screen.get_clip()
        screen.set_clip(bg_rect)

        # Draw glow if enabled
        if self._zoomed_text_glow:
            screen.blit(self._zoomed_glow_surf, (text_x - self._zoomed_glow_padding, text_y - self._zoomed_glow_padding))

        # Draw the main text
        screen.blit(self._zoomed_text_surf, (text_x, text_y))

        # If text is scrolling off the left edge, draw it again on the right
        if text_x < ad_x:
            gap = ad_width + self._zoomed_text_surf.get_width()

            # Draw glow for the repeated text if enabled
            if self._zoomed_text_glow:
                screen.blit(self._zoomed_glow_surf, (text_x + gap - self._zoomed_glow_padding,
                                                  text_y - self._zoomed_glow_padding))

            # Draw the repeated text
            screen.blit(self._zoomed_text_surf, (text_x + gap, text_y))

        # Reset the clipping region
        screen.set_clip(original_clip)

        # OPTIMIZATION: Cache the instruction text
        if not hasattr(self, '_instruction_surf'):
            instruction_font = pygame.font.SysFont("Arial", int(24 * min(scale_x, scale_y)), bold=True)
            instruction_text = "Press Ctrl+Click to return to normal view"
            self._instruction_surf = instruction_font.render(instruction_text, True, (220, 220, 220))

        # Position instruction text at the bottom
        instruction_x = (screen_width - self._instruction_surf.get_width()) // 2
        instruction_y = ad_y + ad_height + 20

        # Draw instruction text
        screen.blit(self._instruction_surf, (instruction_x, instruction_y))

    def draw_lucky_numbers(self, x_position, section_width, section_height, top_margin):
        # Use the full width of the right panel for the lucky numbers section

        # Cache key for header based on dimensions and callout count
        header_cache_key = f"lucky_header_{x_position}_{section_width}_{top_margin}_{len(self.called_numbers)}"

        # Initialize header cache if it doesn't exist
        if not hasattr(self, '_lucky_header_cache'):
            self._lucky_header_cache = {}

        # Check if we need to redraw the header
        if header_cache_key not in self._lucky_header_cache:
            # Position header correctly using passed coordinates and height
            # Make the header a fixed height at the top of the section - more compact
            header_height = int(35 * scale_y)
            header_rect = pygame.Rect(
                x_position,
                top_margin,  # Position below navigation bar with proper spacing
                section_width,             # Full width of the section
                header_height              # Fixed height for header
            )

            # Create a surface for the header
            header_surf = pygame.Surface((section_width, header_height), pygame.SRCALPHA)

            # Draw gradient background on the header surface
            temp_rect = pygame.Rect(0, 0, section_width, header_height)

            # Create a surface for the gradient
            grad_surf = pygame.Surface((section_width, header_height), pygame.SRCALPHA)

            # Draw the gradient with optimized step size
            step_size = max(1, header_height // 20)  # Use at most 20 lines
            for y in range(0, header_height, step_size):
                # Calculate the color for this line
                ratio = y / header_height
                r = DARK_BLUE[0] * (1 - ratio) + LIGHT_BLUE[0] * ratio
                g = DARK_BLUE[1] * (1 - ratio) + LIGHT_BLUE[1] * ratio
                b = DARK_BLUE[2] * (1 - ratio) + LIGHT_BLUE[2] * ratio
                color = (int(r), int(g), int(b))

                # Draw a rectangle instead of a line to fill the step
                height = min(step_size, header_height - y)
                pygame.draw.rect(grad_surf, color, (0, y, section_width, height))

            # Add rounded corners
            mask = pygame.Surface((section_width, header_height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), temp_rect, border_radius=10)
            grad_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

            # Blit the gradient to the header surface
            header_surf.blit(grad_surf, (0, 0))

            # Draw "LUCKY NUMBERS" text positioned properly in the header
            lucky_surf = header_font.render("LUCKY NUMBERS", True, WHITE)
            lucky_x = int(20 * scale_x)
            # Center text vertically in the header
            lucky_y = (header_height - lucky_surf.get_height()) // 2
            header_surf.blit(lucky_surf, (lucky_x, lucky_y))

            # Draw total callout with orange color properly aligned
            total_surf = header_font.render(f"#Total Callout:{len(self.called_numbers)}", True, ORANGE)
            total_x = section_width - total_surf.get_width() - int(20 * scale_x)
            # Center text vertically in the header
            total_y = (header_height - total_surf.get_height()) // 2
            header_surf.blit(total_surf, (total_x, total_y))

            # Cache the header surface
            self._lucky_header_cache[header_cache_key] = {
                'surface': header_surf,
                'rect': header_rect
            }

            # Limit cache size
            if len(self._lucky_header_cache) > 10:  # Keep only 10 most recent headers
                oldest_key = next(iter(self._lucky_header_cache))
                del self._lucky_header_cache[oldest_key]

        # Draw the cached header
        header_data = self._lucky_header_cache[header_cache_key]
        screen.blit(header_data['surface'], header_data['rect'])
        header_rect = header_data['rect']

        # Advertising section is now drawn directly in the main draw method
        # to ensure it's always visible

        # Letters for BINGO rows with improved colors for better visibility
        bingo_letters = "BINGO"
        row_colors = {
            'B': (255, 60, 60),    # Slightly brighter red for B
            'I': (50, 220, 100),   # Brighter green for I
            'N': (80, 120, 255),   # Brighter blue for N
            'G': (255, 60, 60),    # Same red for G (matching B)
            'O': (255, 220, 50)    # Brighter gold/yellow for O
        }

        # Calculate the available content area (total height minus header and margins)
        # Reduce top gap to maximize vertical space
        content_start_y = header_rect.bottom + int(5 * scale_y)  # Reduced gap from 10 to 5
        # Reduce bottom margin to maximize vertical space
        content_height = section_height - (header_rect.bottom - top_margin) - int(10 * scale_y)  # Reduced margin from 20 to 10

        # Increase row height to accommodate larger circles
        min_row_height = int(84 * scale_y)  # Increased from 60 to 84 (1.4x) for larger circles

        # Adjust row height based on available space
        row_height = max(min_row_height, content_height / 5)  # Ensure minimum row height
        row_start_y = content_start_y  # Start rows right below the header

        # Increase BINGO letter font size by 1.4x to match the number grid updates
        bingo_letter_font = pygame.font.SysFont("Arial Black", scaled_font_size(78), bold=True)  # Increased from 56 to 78 (1.4x)
        # Increased number font size by 1.4x (from 28 to 39)
        number_font = pygame.font.SysFont("Arial", scaled_font_size(39), bold=True)

        # Calculate pulsating effect for animations
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1
        fast_pulse = (math.sin(time.time() * 6) + 1) / 2  # Faster pulsing for current number

        # Check if we have a BingoCaller for recent call detection
        has_bingo_caller = hasattr(self, 'bingo_caller') and self.bingo_caller is not None

        # Cache key for BINGO letters based on dimensions and position
        bingo_letters_cache_key = f"bingo_letters_{header_rect.x}_{row_start_y}_{row_height}_{scale_x}_{scale_y}"

        # Initialize BINGO letters cache if it doesn't exist
        if not hasattr(self, '_bingo_letters_cache'):
            self._bingo_letters_cache = {}

        # Check if we need to redraw the BINGO letters
        if bingo_letters_cache_key not in self._bingo_letters_cache:
            # Create a surface for all BINGO letters
            letters_height = int(row_height * 5)  # Height for all 5 rows
            letters_width = int(150 * scale_x)  # Width for the letters and decorative lines
            letters_surf = pygame.Surface((letters_width, letters_height), pygame.SRCALPHA)

            # Draw each row of the bingo board
            for row_idx, letter in enumerate(bingo_letters):
                row_color = row_colors[letter]
                # Position BINGO letters with proper margin from left edge - adjusted for better spacing
                letter_x = int(50 * scale_x)  # Reduced from 70 to 50 to move letters closer to left edge
                # Calculate vertical position with proper spacing between rows
                letter_y = row_idx * row_height - int(5 * scale_y)  # Slight vertical adjustment for better alignment

                # Draw cleaner horizontal lines (reduced count for cleaner look)
                line_count = 4
                line_length = int(25 * scale_x)  # Increased from 18 to 25 (1.4x)
                line_thickness = max(1, int(4 * min(scale_x, scale_y)))  # Increased from 3 to 4 (1.3x)
                line_spacing = int(11 * scale_y)  # Increased from 8 to 11 (1.4x)
                line_start_y = letter_y + int(49 * scale_y)  # Increased from 35 to 49 (1.4x)

                for i in range(line_count):
                    line_y_pos = line_start_y + i * line_spacing
                    pygame.draw.line(letters_surf, row_color,
                                   (letter_x - line_length - int(5 * scale_x), line_y_pos),  # Reduced from 7 to 5
                                   (letter_x - int(5 * scale_x), line_y_pos),  # Reduced from 7 to 5
                                   line_thickness)

                # Draw the BINGO letter with reduced 3D effect for better readability
                letter_surf = bingo_letter_font.render(letter, True, row_color)

                # Add subtle shadow for minimal 3D effect with increased offset for larger font
                shadow_offset = int(3 * scale_x)  # Increased from 2 to 3 (1.5x)
                shadow_surf = bingo_letter_font.render(letter, True, (row_color[0]//2, row_color[1]//2, row_color[2]//2))
                shadow_pos = (letter_x + shadow_offset, letter_y + shadow_offset)
                letters_surf.blit(shadow_surf, shadow_pos)

                # Draw main letter
                letters_surf.blit(letter_surf, (letter_x, letter_y))

            # Cache the BINGO letters surface
            self._bingo_letters_cache[bingo_letters_cache_key] = {
                'surface': letters_surf,
                'x': header_rect.x,
                'y': row_start_y
            }

            # Limit cache size
            if len(self._bingo_letters_cache) > 5:  # Keep only 5 most recent BINGO letter sets
                oldest_key = next(iter(self._bingo_letters_cache))
                del self._bingo_letters_cache[oldest_key]

        # Draw the cached BINGO letters
        letters_data = self._bingo_letters_cache[bingo_letters_cache_key]
        screen.blit(letters_data['surface'], (letters_data['x'], letters_data['y']))

        # Get the letter_x value for row calculations
        letter_x = header_rect.x + int(50 * scale_x)  # Same as in the cached drawing code

        # Calculate proper dimensions for the number row background
        # Start after the BINGO letter with proper spacing - adjusted for better visibility
        row_bg_x = letter_x + int(90 * scale_x)  # Reduced from 112 to 90 to prevent cutoff
        # Ensure the width fits within the section boundaries with minimal margin to maximize width
        row_bg_width = section_width - (row_bg_x - x_position) - int(10 * scale_x)  # Reduced margin from 20 to 10
        # Make sure the row background extends to the right edge of the screen
        row_bg_width = max(row_bg_width, section_width - (row_bg_x - x_position) - int(10 * scale_x))  # Reduced margin from 20 to 10
        # Height based on available row height with padding - increased for larger circles
        row_bg_height = min(int(77 * scale_y), int(row_height * 0.85))  # Increased from 55 to 77 (1.4x)

        # Ensure minimum width for proper number display with larger circles
        min_width_per_number = int(52 * scale_x)  # Adjusted from 56 to 52 for better fit
        min_total_width = min_width_per_number * 15  # 15 numbers per row
        row_bg_width = max(row_bg_width, min_total_width)

        # PERFORMANCE OPTIMIZATION: Batch render number grid with optimizations
        self._render_number_grid_optimized(bingo_letters, row_colors, row_start_y, row_height,
                                          row_bg_x, row_bg_width, row_bg_height)

    def _render_number_grid_optimized(self, bingo_letters, row_colors, row_start_y, row_height,
                                    row_bg_x, row_bg_width, row_bg_height):
        """Optimized number grid rendering with batching and caching"""
        # PERFORMANCE OPTIMIZATION: Pre-calculate common values
        current_time = time.time()
        pulse = (math.sin(current_time * 3) + 1) / 2
        fast_pulse = (math.sin(current_time * 6) + 1) / 2

        # PERFORMANCE OPTIMIZATION: Batch process rows
        for row_idx, letter in enumerate(bingo_letters):
            row_color = row_colors[letter]
            letter_y = row_start_y + row_idx * row_height - int(5 * scale_y)

            # PERFORMANCE OPTIMIZATION: Use cached row background if available
            row_cache_key = f"row_bg_{row_idx}_{row_bg_width}_{row_bg_height}_{self._animation_quality}"

            if not hasattr(self, '_row_bg_cache'):
                self._row_bg_cache = {}

            row_bg_rect = self._get_rect_from_pool(
                row_bg_x,
                letter_y + int(20 * scale_y),
                row_bg_width,
                row_bg_height
            )

            # PERFORMANCE OPTIMIZATION: Simplified background drawing in low performance mode
            if self._performance_monitor['low_performance_mode']:
                pygame.draw.rect(screen, (20, 20, 25), row_bg_rect)
            else:
                border_radius = int(min(42 * scale_y, row_bg_height // 2))
                pygame.draw.rect(screen, (20, 20, 25), row_bg_rect, border_radius=border_radius)

            # PERFORMANCE OPTIMIZATION: Batch render numbers in this row
            self._render_row_numbers_optimized(row_idx, row_bg_rect, row_color, pulse, fast_pulse)

            # Return rect to pool
            self._return_rect_to_pool(row_bg_rect)

    def _render_row_numbers_optimized(self, row_idx, row_bg_rect, row_color, pulse, fast_pulse):
        """Optimized rendering of numbers in a single row"""
        start_num = row_idx * 15 + 1
        num_spacing = row_bg_rect.width / 15.2

        # PERFORMANCE OPTIMIZATION: Pre-calculate positions for all numbers in row
        positions = []
        for col in range(15):
            base_num_x = row_bg_rect.x + int(num_spacing/2.5) + col * num_spacing
            base_num_y = row_bg_rect.centery
            positions.append((base_num_x, base_num_y))

        # PERFORMANCE OPTIMIZATION: Batch process numbers with reduced calculations
        for col in range(15):
            num = start_num + col
            base_num_x, base_num_y = positions[col]

            # PERFORMANCE OPTIMIZATION: Handle shuffle animation efficiently
            num_x, num_y = self._apply_shuffle_animation(num, base_num_x, base_num_y, row_idx, col)

            # PERFORMANCE OPTIMIZATION: Calculate circle properties once
            circle_radius = self._calculate_circle_radius(num_spacing)

            # PERFORMANCE OPTIMIZATION: Batch check number states
            is_called = num in self.called_numbers
            is_current = num == self.current_number
            is_recently_called = self._is_recently_called(num, is_current)

            # PERFORMANCE OPTIMIZATION: Render circle and text efficiently
            self._render_number_circle_optimized(num_x, num_y, circle_radius, num, row_color,
                                               is_called, is_current, is_recently_called,
                                               pulse, fast_pulse)

    def _apply_shuffle_animation(self, num, base_num_x, base_num_y, row_idx, col):
        """Apply shuffle animation with optimizations"""
        if not self.shuffle_active:
            return base_num_x, base_num_y

        # PERFORMANCE OPTIMIZATION: Simplified shuffle animation for low performance mode
        if self._performance_monitor['low_performance_mode']:
            # Reduced animation complexity for older hardware
            offset_range = 6  # Reduced from 12
        else:
            offset_range = 12

        num_key = f"{row_idx}_{col}"
        if num_key not in self.shuffle_offsets:
            offset_x = random.randint(-offset_range, offset_range) * scale_x
            offset_y = random.randint(-offset_range, offset_range) * scale_y
            color_shift = random.uniform(0.8, 1.2)  # Reduced range
            size_shift = random.uniform(0.9, 1.1)   # Reduced range
            self.shuffle_offsets[num_key] = (offset_x, offset_y, color_shift, size_shift)

        offset_x, offset_y, color_shift, size_shift = self.shuffle_offsets[num_key]

        # PERFORMANCE OPTIMIZATION: Simplified animation calculations
        elapsed = time.time() - self.shuffle_start_time
        progress = min(1.0, elapsed / self.shuffle_duration)
        vibration_strength = 1.0 - progress

        # Apply animation with reduced complexity
        num_x = base_num_x + offset_x * vibration_strength
        num_y = base_num_y + offset_y * vibration_strength

        # Store effects for rendering
        self.current_shuffle_effects = {
            'color_shift': color_shift * vibration_strength + (1.0 - vibration_strength),
            'size_shift': size_shift * vibration_strength + (1.0 - vibration_strength),
            'progress': progress
        }

        # End animation check
        if progress >= 1.0 and num == 75:  # Check only on last number
            self.shuffle_active = False
            self.shuffle_offsets.clear()
            self.current_shuffle_effects = None
            print(f"Shuffle animation completed after {self.shuffle_duration} seconds")

            # Stop the shuffle sound when animation ends
            if self.shuffle_sound_channel is not None:
                try:
                    print("Stopping shuffle sound as animation has completed")
                    self.shuffle_sound_channel.stop()
                except Exception as e:
                    print(f"Error stopping shuffle sound: {e}")
                    pass

        return num_x, num_y

    def _calculate_circle_radius(self, num_spacing):
        """Calculate optimal circle radius with performance considerations"""
        if self._performance_monitor['low_performance_mode']:
            # Slightly smaller circles for better performance
            optimal_radius = num_spacing * 0.4
        else:
            optimal_radius = num_spacing * 0.45

        min_radius = int(17 * min(scale_x, scale_y))
        max_radius = int(34 * min(scale_x, scale_y))

        base_radius = max(min_radius, min(int(optimal_radius), max_radius))

        # Apply shuffle size variation if active
        if self.shuffle_active and hasattr(self, 'current_shuffle_effects') and self.current_shuffle_effects:
            size_shift = self.current_shuffle_effects['size_shift']
            return int(base_radius * size_shift)

        return base_radius

    def _is_recently_called(self, num, is_current):
        """Check if number was recently called with performance optimization"""
        if hasattr(self, 'bingo_caller') and self.bingo_caller:
            return self.bingo_caller.is_recently_called(num, 5.0)
        return is_current

    def _render_number_circle_optimized(self, num_x, num_y, circle_radius, num, row_color,
                                      is_called, is_current, is_recently_called, pulse, fast_pulse):
        """Optimized rendering of individual number circles"""
        # PERFORMANCE OPTIMIZATION: Simplified rendering based on performance mode
        if is_recently_called:
            self._render_recently_called_circle(num_x, num_y, circle_radius, num, row_color, fast_pulse)
        elif is_called:
            self._render_called_circle(num_x, num_y, circle_radius, num, row_color, pulse)
        else:
            self._render_uncalled_circle(num_x, num_y, circle_radius, num, row_color)

        # Store hit area for click detection
        hit_rect = self._get_rect_from_pool(
            num_x - circle_radius, num_y - circle_radius,
            circle_radius * 2, circle_radius * 2
        )
        self.hit_areas[f"lucky_number_{num}"] = hit_rect

    def _render_recently_called_circle(self, num_x, num_y, circle_radius, num, row_color, fast_pulse):
        """Render recently called number with optimized smooth effects"""
        # ANIMATION FIX: Use consistent timing for all pulse effects
        current_time = time.time()
        smooth_pulse = (math.sin(current_time * 6) + 1) / 2  # Consistent 6Hz pulse

        if self._performance_monitor['low_performance_mode']:
            # PERFORMANCE FIX: Simplified version for older hardware
            highlight_color = (min(255, row_color[0] + 100),
                             min(255, row_color[1] + 100),
                             min(255, row_color[2] + 100))
            pygame.draw.circle(screen, highlight_color, (num_x, num_y), circle_radius)
            pygame.draw.circle(screen, WHITE, (num_x, num_y), int(circle_radius * 0.7))
        else:
            # ANIMATION FIX: Smooth glow effect with consistent timing
            base_glow_radius = circle_radius * 1.2
            pulse_variation = 0.2 * smooth_pulse  # Reduced variation for smoother effect
            glow_radius = base_glow_radius + (circle_radius * pulse_variation)

            # PERFORMANCE FIX: Cache glow surfaces for recently called numbers
            glow_cache_key = f"recent_glow_{num}_{int(current_time * 6) % 12}"  # Update every 0.17s

            if not hasattr(self, '_recent_glow_cache'):
                self._recent_glow_cache = {}

            if glow_cache_key not in self._recent_glow_cache:
                # Create glow surface
                glow_size = int(glow_radius * 2)
                glow_surface = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)

                # ANIMATION FIX: Simplified glow layers for smooth performance
                for i in range(2):  # Reduced layers
                    layer_radius = max(1, int(glow_radius - i * 4))
                    alpha = max(20, 150 - i * 60)  # Ensure minimum alpha
                    glow_color = (*row_color, alpha)
                    pygame.draw.circle(glow_surface, glow_color,
                                     (glow_size//2, glow_size//2), layer_radius, 2)

                # Cache the glow surface
                self._recent_glow_cache[glow_cache_key] = glow_surface

                # MEMORY FIX: Limit cache size
                if len(self._recent_glow_cache) > 15:
                    oldest_key = next(iter(self._recent_glow_cache))
                    del self._recent_glow_cache[oldest_key]

            # Draw cached glow
            glow_surface = self._recent_glow_cache[glow_cache_key]
            glow_rect = glow_surface.get_rect(center=(num_x, num_y))
            screen.blit(glow_surface, glow_rect)

            # ANIMATION FIX: Smooth brightness animation
            brightness_base = 200
            brightness_variation = int(30 * smooth_pulse)
            brightness = brightness_base + brightness_variation

            highlight_color = (min(255, row_color[0] + brightness//3),
                             min(255, row_color[1] + brightness//3),
                             min(255, row_color[2] + brightness//3))

            # Draw main circle with smooth animation
            pygame.draw.circle(screen, highlight_color, (num_x, num_y), circle_radius)

            # Inner white circle with slight pulse
            inner_radius = int(circle_radius * (0.65 + 0.05 * smooth_pulse))
            pygame.draw.circle(screen, WHITE, (num_x, num_y), inner_radius)

        # Render number text with consistent styling
        self._render_number_text(num_x, num_y, num, (0, 0, 0), True)  # Black text on white

    def _render_called_circle(self, num_x, num_y, circle_radius, num, row_color, pulse):
        """Render called number with optimized effects"""
        if self._performance_monitor['low_performance_mode']:
            # Simplified version
            pygame.draw.circle(screen, row_color, (num_x, num_y), circle_radius)
        else:
            # Normal quality version with subtle pulse
            highlight_intensity = int(150 + 30 * pulse)  # Reduced range
            highlight_color = (min(255, row_color[0] * highlight_intensity // 255),
                             min(255, row_color[1] * highlight_intensity // 255),
                             min(255, row_color[2] * highlight_intensity // 255))

            pygame.draw.circle(screen, highlight_color, (num_x, num_y), circle_radius)

            # Inner circle for contrast
            inner_color = (min(255, highlight_color[0] + 20),
                          min(255, highlight_color[1] + 20),
                          min(255, highlight_color[2] + 20))
            pygame.draw.circle(screen, inner_color, (num_x, num_y), int(circle_radius * 0.75))

        # Render number text
        self._render_number_text(num_x, num_y, num, WHITE, False)

    def _render_uncalled_circle(self, num_x, num_y, circle_radius, num, row_color):
        """Render uncalled number with optimized effects"""
        if self.shuffle_active and hasattr(self, 'current_shuffle_effects') and self.current_shuffle_effects:
            # Simplified shuffle effects for performance
            if self._performance_monitor['low_performance_mode']:
                # Very simple shuffle effect
                pygame.draw.circle(screen, (80, 80, 90), (num_x, num_y), circle_radius)
            else:
                # Normal shuffle effects but simplified
                color_shift = self.current_shuffle_effects['color_shift']
                base_color = (int(100 * color_shift), int(100 * color_shift), int(110 * color_shift))
                pygame.draw.circle(screen, base_color, (num_x, num_y), circle_radius)
        else:
            # Normal uncalled state
            pygame.draw.circle(screen, (60, 60, 70), (num_x, num_y), circle_radius)

        # Render number text
        self._render_number_text(num_x, num_y, num, (200, 200, 200), False)

    def _render_number_text(self, num_x, num_y, num, color, is_recent):
        """Optimized number text rendering"""
        if is_recent and not self._performance_monitor['low_performance_mode']:
            # Larger font for recently called numbers
            font_size = scaled_font_size(48)
            font = pygame.font.SysFont("Arial", font_size, bold=True)
        else:
            # Normal font
            font_size = scaled_font_size(39)
            font = pygame.font.SysFont("Arial", font_size, bold=True)

        # Use cached text rendering
        num_surf = self.render_text(str(num), font, color)
        num_rect = num_surf.get_rect(center=(num_x, num_y))
        screen.blit(num_surf, num_rect)

    def handle_input(self, event):
        """Handle keyboard input events"""
        # First check if any modal input is active
        if hasattr(self, 'ui_handler'):
            if self.ui_handler.handle_input(event):
                return True

        # If we get here, the modal inputs didn't handle the event
        # Handle the game's own input fields
        if not self.input_active and not self.bet_input_active:
            return False

        if event.type != pygame.KEYDOWN:
            return False

        # Flag to track if we should play a sound
        play_sound = False

        if event.key == pygame.K_ESCAPE:
            # Cancel input
            if self.input_active:
                self.input_active = False
                self.input_text = str(self.cartella_number)
                play_sound = True
            elif self.bet_input_active:
                self.bet_input_active = False
                self.bet_input_text = str(self.bet_amount)
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
            # Confirm input
            if self.input_active:
                self.input_active = False
                is_valid, error_msg = self.validate_cartella_number(self.input_text)
                if is_valid:
                    self.cartella_number = int(self.input_text)
                    play_sound = True
                else:
                    # Show error message
                    self.message = error_msg
                    self.message_type = "error"
                    self.message_timer = 180
                    play_sound = True
            elif self.bet_input_active:
                self._apply_bet_input()
                self.bet_input_active = False
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_BACKSPACE:
            # Remove last character
            if self.input_active and self.input_text:
                self.input_text = self.input_text[:-1]
                play_sound = True
            elif self.bet_input_active and self.bet_input_text:
                self.bet_input_text = self.bet_input_text[:-1]
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        # Handle different input based on which field is active
        if self.input_active:
            # Only allow digits, and limit to 3 characters (for 100)
            if event.unicode.isdigit() and len(self.input_text) < 3:
                new_text = self.input_text + event.unicode

                # Validate as we go - don't allow numbers > 100
                try:
                    num = int(new_text)
                    if num <= 100:  # Only accept if within range
                        self.input_text = new_text
                        if self.button_click_sound:
                            self.button_click_sound.play()
                except ValueError:
                    pass
                return True
        elif self.bet_input_active:
            # Only allow digits for bet amount
            if event.unicode.isdigit() and len(self.bet_input_text) < 6:  # Limit to 6 digits (allow for large bets)
                new_text = self.bet_input_text + event.unicode
                self.bet_input_text = new_text
                if self.button_click_sound:
                    self.button_click_sound.play()
                return True

        return False

    def _clear_input(self):
        """Reset input field after a successful operation"""
        self.input_active = False
        self.input_text = ""
        self.input_cursor_visible = True
        self.input_cursor_timer = 0
        self.bet_input_active = False
        self.bet_input_text = ""
        self.bet_input_cursor_visible = True
        self.bet_input_cursor_timer = 0

    def start_shuffle_animation(self):
        """Start the shuffle animation for all numbers"""
        # Set shuffle animation variables
        self.shuffle_active = True
        self.shuffle_start_time = time.time()
        self.shuffle_offsets = {}  # Clear any existing offsets

        # Stop any existing shuffle sound if it's still playing
        if self.shuffle_sound_channel is not None:
            try:
                self.shuffle_sound_channel.stop()
            except:
                pass

        # Play the shuffle sound effect
        try:
            print("Starting shuffle animation...")
            if hasattr(self, 'shuffle_sound') and self.shuffle_sound:
                # Play the dedicated shuffle sound effect on a specific channel
                print(f"Playing shuffle sound effect with duration matching animation ({self.shuffle_duration}s)...")

                # Find an available channel for the shuffle sound
                channel_id = pygame.mixer.find_channel(True)  # Force=True to always find a channel
                if channel_id:
                    # Store the channel for later control
                    self.shuffle_sound_channel = channel_id

                    # Play the sound on this channel
                    self.shuffle_sound_channel.play(self.shuffle_sound)
                    print("Shuffle sound effect played successfully on channel", self.shuffle_sound_channel)
                else:
                    # Fallback to regular play if no channel available
                    print("No channel available, using regular play")
                    self.shuffle_sound.play()
            elif self.button_click_sound:  # Fallback to button click sound if shuffle sound not available
                # Play it multiple times for a shuffling effect
                print("Shuffle sound not available, using button click sound as fallback")
                self.button_click_sound.play()
                pygame.time.delay(100)
                self.button_click_sound.play()
                pygame.time.delay(100)
                self.button_click_sound.play()
            else:
                print("No sound effects available for shuffle animation")
        except Exception as e:
            print(f"Error playing shuffle sound: {e}")  # Log the error for debugging
            # Try to play the sound using an alternative method
            try:
                if hasattr(self, 'shuffle_sound') and self.shuffle_sound:
                    # Set volume explicitly and try again
                    self.shuffle_sound.set_volume(1.0)
                    channel_id = pygame.mixer.find_channel(True)
                    if channel_id:
                        self.shuffle_sound_channel = channel_id
                        self.shuffle_sound_channel.play(self.shuffle_sound)
                        print("Played shuffle sound using alternative method on channel", self.shuffle_sound_channel)
                    else:
                        self.shuffle_sound.play()
                        print("Played shuffle sound using alternative method")
            except Exception as e2:
                print(f"Alternative method also failed: {e2}")
                # Continue even if sound fails

    def _apply_bet_input(self):
        """Apply bet input value to set bet amount or prize pool based on active mode"""
        # Ignore if text is empty
        if not self.bet_input_text.strip():
            self.bet_input_active = False
            return

        # Check which input field is active
        if hasattr(self, 'prize_pool_input_active') and self.prize_pool_input_active:
            # Validate and apply prize pool input
            try:
                value = int(self.bet_input_text)
                if value < 0:
                    self.message = "Prize pool cannot be negative"
                    self.message_type = "error"
                    self.message_timer = 180
                else:
                    # Use the set_prize_pool_manually function to set and persist the prize pool
                    self.set_prize_pool_manually(value)
            except ValueError:
                self.message = "Invalid prize pool value"
                self.message_type = "error"
                self.message_timer = 180

            # Reset input state
            self.bet_input_text = ""
            self.bet_input_active = False
            self.prize_pool_input_active = False
            return

        # Normal bet amount validation
        is_valid, error_msg = self.validate_bet_amount(self.bet_input_text)
        if is_valid:
            # Apply the new bet amount
            try:
                old_bet = self.bet_amount
                self.bet_amount = int(self.bet_input_text)

                # Show confirmation message
                self.message = f"Bet amount updated to {self.bet_amount} ETB"
                self.message_type = "success"
                self.message_timer = 180

                # Recalculate prize pool based on new bet amount (if not manually set)
                if not hasattr(self, 'prize_pool_manual_override') or not self.prize_pool_manual_override:
                    self.calculate_prize_pool()

                # Save settings to maintain bet amount
                try:
                    from player_storage import save_game_settings
                    settings = {
                        'commission_percentage': self.commission_percentage,
                        'bet_amount': self.bet_amount,
                        'prize_pool': self.prize_pool,
                        'prize_pool_manual_override': getattr(self, 'prize_pool_manual_override', False)
                    }
                    save_game_settings(settings)
                except Exception as e:
                    print(f"Error saving game settings: {e}")
            except ValueError:
                pass
        else:
            # Show error message
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180

        # Clear input and deactivate
        self.bet_input_text = ""
        self.bet_input_active = False

    def validate_cartella_number(self, number):
        """Validate a cartella number and return (is_valid, error_message)"""
        # Check if it's a valid number
        try:
            num = int(number)
            # Check range
            if not (1 <= num <= 1200):  # Increased upper limit from 100 to 1200
                return False, f"Number must be between 1-1200"

            # Check if it already exists
            for player in self.players:
                if player.cartela_no == num:
                    return False, f"Player with cartella #{num} already exists!"

            # If we get here, it's valid
            return True, ""
        except ValueError:
            return False, "Invalid number"

    def validate_bet_amount(self, amount):
        """Validate bet amount and return (is_valid, error_message)"""
        try:
            num = int(amount)
            if num <= 0:
                return False, "Bet amount must be positive"
            return True, ""
        except ValueError:
            return False, "Invalid bet amount"

    def ensure_boards_exist(self):
        """
        Ensure the bingo_boards.json file exists in the data directory.
        If it doesn't exist, create the data directory and an empty file.
        """
        try:
            # Create data directory if it doesn't exist
            os.makedirs('data', exist_ok=True)

            # Check if the boards file exists
            if not os.path.exists(self.bingo_boards_db):
                print(f"Bingo boards file not found at {self.bingo_boards_db}. Creating empty file.")
                # Create an empty boards file
                with open(self.bingo_boards_db, 'w') as file:
                    json.dump({}, file)
                print(f"Created empty bingo boards file at {self.bingo_boards_db}")
            else:
                # Verify the file is valid JSON
                try:
                    with open(self.bingo_boards_db, 'r') as file:
                        boards_data = json.load(file)
                    print(f"Successfully loaded bingo boards file with {len(boards_data)} boards")
                except json.JSONDecodeError:
                    print(f"Error: Bingo boards file at {self.bingo_boards_db} is not valid JSON. Creating new file.")
                    # Backup the invalid file
                    backup_path = f"{self.bingo_boards_db}.backup"
                    try:
                        import shutil
                        shutil.copy2(self.bingo_boards_db, backup_path)
                        print(f"Backed up invalid boards file to {backup_path}")
                    except Exception as e:
                        print(f"Error backing up invalid boards file: {e}")

                    # Create a new empty file
                    with open(self.bingo_boards_db, 'w') as file:
                        json.dump({}, file)
                    print(f"Created new empty bingo boards file at {self.bingo_boards_db}")
        except Exception as e:
            print(f"Error ensuring boards exist: {e}")

    def load_board_for_cartella(self):
        """
        Load the bingo board for the current cartella number.
        This sets self.bingo_board to the board data from the JSON file.
        """
        try:
            # Check if the boards file exists
            if os.path.exists(self.bingo_boards_db):
                with open(self.bingo_boards_db, 'r') as file:
                    boards_data = json.load(file)

                # Check if the cartella number exists in the data
                cartella_key = str(self.cartella_number)
                if cartella_key in boards_data:
                    self.bingo_board = boards_data[cartella_key]
                    print(f"Loaded board for cartella {self.cartella_number}")
                else:
                    # If the specific cartella_no doesn't exist, use modulo to select a predefined board
                    available_keys = list(boards_data.keys())
                    if available_keys:
                        # Use modulo to select a board based on cartella_no
                        selected_key = available_keys[self.cartella_number % len(available_keys)]
                        self.bingo_board = boards_data[selected_key]
                        print(f"Cartella {self.cartella_number} not found in boards data. Using board {selected_key} instead.")
                    else:
                        # If no boards are available, create an empty board
                        self.bingo_board = []
                        print(f"No boards available in {self.bingo_boards_db}. Using empty board.")
            else:
                # If the file doesn't exist, create an empty board
                self.bingo_board = []
                print(f"Bingo boards file not found at {self.bingo_boards_db}. Using empty board.")
        except Exception as e:
            print(f"Error loading board for cartella {self.cartella_number}: {e}")
            # If there's an error, create an empty board
            self.bingo_board = []

    def load_game_settings(self):
        """Load game settings from saved file and SettingsManager"""
        try:
            print(f"\n==== LOAD_GAME_SETTINGS =====")

            # Store the current call delay value to preserve any pre-game adjustments
            current_call_delay = getattr(self, 'number_call_delay', None)
            print(f"Current call delay before loading: {current_call_delay}s")

            # Load settings from player_storage first
            from player_storage import load_game_settings
            settings = load_game_settings()

            # Apply loaded settings to this instance
            self.commission_percentage = settings.get('commission_percentage', 20.0)
            self.bet_amount = settings.get('bet_amount', 50)
            self.prize_pool = settings.get('prize_pool', 500)
            self.prize_pool_manual_override = settings.get('prize_pool_manual_override', False)

            # Check if number_call_delay is in player_storage settings
            player_storage_delay = settings.get('number_call_delay')
            if player_storage_delay is not None:
                # Always use the player_storage value when the app starts
                # This ensures the saved value is always loaded on startup
                self.number_call_delay = player_storage_delay
                print(f"Loaded number_call_delay={player_storage_delay}s from player_storage")

                # If we have a current value that's different, log it but still use player_storage value
                if current_call_delay is not None and current_call_delay != player_storage_delay:
                    print(f"Note: Current value {current_call_delay}s differs from player_storage value {player_storage_delay}s, using player_storage value")

            print(f"Loaded from player_storage: commission={self.commission_percentage}%, bet={self.bet_amount}, prize_pool={self.prize_pool}, call_delay={player_storage_delay}s")

            # Then load settings from the SettingsManager for game settings
            try:
                from settings_manager import SettingsManager
                settings_manager = SettingsManager()

                # Load all game settings from SettingsManager
                self.strict_claim_timing = settings_manager.get_setting('game', 'strict_claim_timing', True)
                self.shuffle_duration = settings_manager.get_setting('game', 'shuffle_duration', 3.0)
                self.show_total_selected = settings_manager.get_setting('game', 'show_total_selected', True)

                # For number_call_delay, we always want to use the value from player_storage if available
                # and sync it to SettingsManager to ensure consistency
                if hasattr(self, 'number_call_delay') and self.number_call_delay is not None:
                    # We already have a value (from player_storage or default), sync it to SettingsManager
                    settings_manager.set_setting('game', 'number_call_delay', self.number_call_delay)
                    print(f"Synced number_call_delay {self.number_call_delay}s to SettingsManager")
                else:
                    # This should rarely happen, but if we don't have a value yet, get from settings
                    self.number_call_delay = settings_manager.get_setting('game', 'number_call_delay', 3.0)
                    print(f"Loaded call delay from SettingsManager: {self.number_call_delay}s")

                # Update bingo caller if it exists
                if hasattr(self, 'bingo_caller') and self.bingo_caller:
                    self.update_bingo_caller_delay()
                    print(f"Updated bingo caller with current delay: {self.number_call_delay}s")
            except Exception as e:
                print(f"Error loading settings from SettingsManager: {e}")
                # If there was an error but we have a current delay value, keep it
                if current_call_delay is not None:
                    self.number_call_delay = current_call_delay
                    print(f"Kept current call delay after SettingsManager error: {current_call_delay}s")

            print(f"Game settings loaded successfully")
            print(f"Final number_call_delay: {self.number_call_delay}s")
            print(f"==== LOAD_GAME_SETTINGS COMPLETED ====\n")
        except Exception as e:
            print(f"Error loading game settings: {e}")
            # Ensure we have default values if loading fails
            if not hasattr(self, 'commission_percentage'):
                self.commission_percentage = 20.0
            if not hasattr(self, 'bet_amount'):
                self.bet_amount = 50
            if not hasattr(self, 'prize_pool'):
                self.prize_pool = 500
            if not hasattr(self, 'prize_pool_manual_override'):
                self.prize_pool_manual_override = False
            if not hasattr(self, 'number_call_delay'):
                self.number_call_delay = 3.0
            if not hasattr(self, 'strict_claim_timing'):
                self.strict_claim_timing = True
            if not hasattr(self, 'shuffle_duration'):
                self.shuffle_duration = 3.0
            if not hasattr(self, 'show_total_selected'):
                self.show_total_selected = True

            print(f"Using default settings after error: number_call_delay={self.number_call_delay}s")

    def calculate_prize_pool(self):
        """Calculate the prize pool based on players' bets and commission percentage"""
        # Only perform calculation if not manually overridden
        if not getattr(self, 'prize_pool_manual_override', False):
            total_bets = 0
            for player in self.players:
                total_bets += player.bet_amount

            # If there are bets, calculate prize pool
            if total_bets > 0:
                commission_rate = getattr(self, 'commission_percentage', 20.0) / 100.0
                prize_pool = total_bets * (1 - commission_rate)

                # Round to nearest whole number
                self.prize_pool = round(prize_pool)
                print(f"Calculated prize pool: {self.prize_pool} ETB from {total_bets} ETB in bets")
            else:
                # Default if no players have bet
                if not hasattr(self, 'prize_pool') or self.prize_pool < 0:
                    self.prize_pool = 500

    def set_prize_pool_manually(self, value):
        """Manually set the prize pool value and enable the manual override flag"""
        try:
            value = int(value)
            if value >= 0:
                self.prize_pool = value
                self.prize_pool_manual_override = True
                return True
        except (ValueError, TypeError):
            pass
        return False

    def draw_toast_message(self):
        """Draw toast message notification on screen"""
        if not self.message or self.message_timer <= 0:
            return

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Define toast dimensions and position (bottom center)
        toast_width = min(screen_width * 0.8, 600 * self.scale_x)
        toast_height = 60 * self.scale_y
        toast_x = (screen_width - toast_width) / 2
        toast_y = screen_height - toast_height - (20 * self.scale_y)  # 20px margin from bottom

        # Create toast rect
        toast_rect = pygame.Rect(toast_x, toast_y, toast_width, toast_height)

        # Set color based on message type
        if self.message_type == "error":
            color1 = (180, 60, 60)  # Darker red
            color2 = (220, 80, 80)  # Lighter red
        elif self.message_type == "success":
            color1 = (60, 180, 60)  # Darker green
            color2 = (80, 220, 80)  # Lighter green
        else:  # info or default
            color1 = (60, 60, 180)  # Darker blue
            color2 = (80, 80, 220)  # Lighter blue

        # Draw gradient background with rounded corners
        self.draw_gradient_rect(toast_rect, color1, color2, border_radius=10)

        # Draw message text
        font_size = scaled_font_size(18)
        font = pygame.font.SysFont("Arial", font_size, bold=True)
        text_surf = font.render(self.message, True, (255, 255, 255))
        text_rect = text_surf.get_rect(center=toast_rect.center)
        self.screen.blit(text_surf, text_rect)

    def update_bingo_caller_delay(self):
        """Update the bingo caller's delay to match the current game setting

        This method is called when the number_call_delay setting is changed
        in the settings interface, ensuring that changes take effect immediately.
        """
        try:
            # Log the current state for debugging
            print(f"\n==== UPDATE_BINGO_CALLER_DELAY =====")
            print(f"Current number_call_delay: {self.number_call_delay}s")
            print(f"Game started: {getattr(self, 'game_started', False)}")
            print(f"Has bingo_caller: {hasattr(self, 'bingo_caller')}")

            if hasattr(self, 'bingo_caller') and self.bingo_caller:
                # Get the current delay from the game instance
                current_delay = self.number_call_delay

                # Update the bingo caller's delay
                self.bingo_caller.update_delay(current_delay)
                print(f"Game: Successfully updated bingo caller delay to {current_delay}s")

                # Save the setting to ensure it persists in both settings systems
                self.save_game_setting("number_call_delay", current_delay)

                # Also save to player_storage for additional persistence
                try:
                    from player_storage import save_game_settings, load_game_settings
                    # Get current settings first
                    settings = load_game_settings()
                    # Update with new number_call_delay
                    settings['number_call_delay'] = current_delay
                    # Save back to player_storage
                    save_game_settings(settings)
                    print(f"Game: Saved number_call_delay={current_delay}s to player_storage")
                except Exception as ps_error:
                    print(f"Error saving to player_storage: {ps_error}")
            else:
                print("Game: Cannot update bingo caller delay - bingo caller not initialized")
                # Still save the setting even if bingo caller is not initialized
                self.save_game_setting("number_call_delay", self.number_call_delay)

                # Also save to player_storage for additional persistence
                try:
                    from player_storage import save_game_settings, load_game_settings
                    # Get current settings first
                    settings = load_game_settings()
                    # Update with new number_call_delay
                    settings['number_call_delay'] = self.number_call_delay
                    # Save back to player_storage
                    save_game_settings(settings)
                    print(f"Game: Saved number_call_delay={self.number_call_delay}s to player_storage anyway")
                except Exception as ps_error:
                    print(f"Error saving to player_storage: {ps_error}")

            print(f"==== UPDATE_BINGO_CALLER_DELAY COMPLETED ====\n")
        except Exception as e:
            print(f"Error updating bingo caller delay: {e}")
            # Try to save the setting even if there was an error
            try:
                self.save_game_setting("number_call_delay", self.number_call_delay)
            except Exception as save_error:
                print(f"Error saving number_call_delay setting: {save_error}")

    def save_game_setting(self, key, value):
        """Save a game setting to the settings file and update SettingsManager"""
        try:
            # First, update the setting in the SettingsManager
            try:
                from settings_manager import SettingsManager
                settings_manager = SettingsManager()
                settings_manager.set_setting('game', key, value)
                print(f"Updated {key}={value} in SettingsManager")
            except Exception as e:
                print(f"Error updating SettingsManager: {e}")
                # Continue with file-based saving even if SettingsManager update fails

            # Get the current settings from the file
            settings_file = os.path.join('data', 'settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as file:
                    settings = json.load(file)

                # Make sure the 'game' section exists
                if 'game' not in settings:
                    settings['game'] = {}

                # Update the setting
                settings['game'][key] = value

                # Save the updated settings
                with open(settings_file, 'w', encoding='utf-8') as file:
                    json.dump(settings, file, indent=4, ensure_ascii=False)

                print(f"Saved {key}={value} to game settings file")
                return True
            else:
                print(f"Error: Settings file {settings_file} not found")
                # Try to create the file with default settings
                try:
                    os.makedirs(os.path.dirname(settings_file), exist_ok=True)
                    settings = {'game': {key: value}}
                    with open(settings_file, 'w', encoding='utf-8') as file:
                        json.dump(settings, file, indent=4, ensure_ascii=False)
                    print(f"Created new settings file with {key}={value}")
                    return True
                except Exception as e:
                    print(f"Error creating settings file: {e}")
                    return False
        except Exception as e:
            print(f"Error saving game setting: {e}")
            return False

def show_splash_screen():
    """Show a startup splash screen with animated image transitions"""
    global screen, scale_x, scale_y

    # Store previous screen mode and dimensions
    prev_screen_info = {
        'mode': screen.get_flags(),
        'size': screen.get_size()
    }
    was_fullscreen = bool(prev_screen_info['mode'] & pygame.FULLSCREEN)

    # Switch to fullscreen for splash
    if not was_fullscreen:
        screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        # Update scaling factors for the splash screen
        scale_x = screen.get_width() / BASE_WIDTH
        scale_y = screen.get_height() / BASE_HEIGHT

    # Load and play background music
    try:
        music_path = "assets/splash_screen-background-music/Welcome-music.mp3"
        if os.path.exists(music_path):
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.play()
    except Exception as e:
        print(f"Error loading splash screen music: {e}")

    # Load all splash images from the directory - OPTIMIZED with batch loading
    splash_images = []

    # Function to load images in a more optimized way
    def load_splash_images():
        images = []
        try:
            splash_dir = "assets/Splash_screen"
            if os.path.exists(splash_dir):
                # Get all image files at once
                image_files = [f for f in os.listdir(splash_dir)
                              if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
                image_files.sort()  # Sort for consistent order

                # Process in batches for better memory management
                batch_size = 2  # Process 2 images at a time
                for i in range(0, len(image_files), batch_size):
                    batch = image_files[i:i+batch_size]
                    for img_file in batch:
                        try:
                            img_path = os.path.join(splash_dir, img_file)
                            img = pygame.image.load(img_path).convert_alpha()
                            # Scale image to fit screen maintaining aspect ratio
                            img = scale_image_to_fit(img, screen.get_size())
                            images.append(img)
                        except Exception as e:
                            print(f"Error loading splash image {img_file}: {e}")
        except Exception as e:
            print(f"Error loading splash images: {e}")
        return images

    # Start loading images
    splash_images = load_splash_images()
    has_splash_images = len(splash_images) > 0

    # Screen dimensions
    width, height = screen.get_size()
    center_x, center_y = width // 2, height // 2

    # Create base surface with gradient background
    splash_surface = pygame.Surface(screen.get_size())

    # Create a radial gradient background with optimized rendering
    max_radius = int(((width/2)**2 + (height/2)**2) ** 0.5)
    step_size = max(1, max_radius // 100)  # Use fewer steps for better performance

    for radius in range(max_radius, 0, -step_size):
        # Calculate color based on radius
        ratio = radius / max_radius
        color = (
            int(10 + (30-10) * (1-ratio**2)),  # R
            int(30 + (80-30) * (1-ratio**2)),  # G
            int(50 + (120-50) * (1-ratio**2))   # B
        )
        # Draw the circle
        pygame.draw.circle(splash_surface, color, (center_x, center_y), radius)

    # Create title text with glow effect
    title_font = pygame.font.SysFont("Arial", scaled_font_size(90), bold=True)
    subtitle_font = pygame.font.SysFont("Arial", scaled_font_size(24), italic=True)

    # Draw "WOW Games" title with enhanced 3D effect
    colors = [
        (255, 50, 50),    # Bright red for F
        (255, 90, 50),    # Reddish orange for u
        (255, 130, 50),   # Orange for k
        (255, 170, 50),   # Golden orange for k
        (255, 200, 50),   # Yellow for r
        (200, 200, 200),  # Silver for space (will be skipped in rendering)
        (50, 220, 100),   # Bright green for G
        (50, 180, 160),   # Teal for a
        (50, 140, 200),   # Sky blue for m
        (50, 100, 220),   # Blue for e
        (100, 80, 220)    # Purplish blue for s
    ]
    letters = "WOW Games"
    letter_x = center_x - (len(letters.replace(" ", "")) * scaled_font_size(25))  # Adjust spacing for new title
    letter_y = center_y - scaled_font_size(100)

    # Draw each letter with glow and shadows
    title_surface = pygame.Surface((width, height), pygame.SRCALPHA)
    letter_count = 0
    for i, letter in enumerate(letters):
        # Skip spaces but account for them in positioning
        if letter == " ":
            continue

        # Create glow effect
        glow_surface = pygame.Surface((scaled_font_size(120), scaled_font_size(120)), pygame.SRCALPHA)
        for offset in range(15, 0, -3):
            glow_alpha = int(200 - offset * 12)
            if glow_alpha < 0:
                glow_alpha = 0
            glow_color = (*colors[i], glow_alpha)
            glow_text = title_font.render(letter, True, glow_color)
            glow_rect = glow_text.get_rect(center=(glow_surface.get_width()//2, glow_surface.get_height()//2))
            glow_surface.blit(glow_text, (glow_rect.x - offset, glow_rect.y - offset))

        # Position and blit the glow
        title_surface.blit(glow_surface, (letter_x + letter_count*scaled_font_size(50) - glow_surface.get_width()//2 + scaled_font_size(40),
                                          letter_y - glow_surface.get_height()//2 + scaled_font_size(40)))

        # Shadow for 3D effect
        for offset in range(5, 0, -1):
            shadow_color = (20, 20, 20)
            shadow_surf = title_font.render(letter, True, shadow_color)
            shadow_rect = shadow_surf.get_rect(center=(letter_x + letter_count*scaled_font_size(50) + scaled_font_size(40),
                                                     letter_y + scaled_font_size(40)))
            title_surface.blit(shadow_surf, (shadow_rect.x + offset, shadow_rect.y + offset))

        # Main letter
        letter_surf = title_font.render(letter, True, colors[i])
        letter_rect = letter_surf.get_rect(center=(letter_x + letter_count*scaled_font_size(50) + scaled_font_size(40),
                                                 letter_y + scaled_font_size(40)))
        title_surface.blit(letter_surf, letter_rect)

        # Increment letter count for positioning (only for non-space characters)
        letter_count += 1

    # Subtitle
    subtitle_text = "The Ultimate Number Calling Game"
    subtitle_surf = subtitle_font.render(subtitle_text, True, (220, 220, 220))
    subtitle_rect = subtitle_surf.get_rect(center=(center_x, center_y + scaled_font_size(20)))
    title_surface.blit(subtitle_surf, subtitle_rect)

    # Version text
    version_font = pygame.font.SysFont("Arial", scaled_font_size(14))
    version_text = "Version 1.0.0"
    version_surf = version_font.render(version_text, True, (150, 150, 150))
    version_rect = version_surf.get_rect(bottomright=(width - 20, height - 20))

    # Creator text
    creator_font = pygame.font.SysFont("Arial", scaled_font_size(14))
    creator_text = "Created with ♥"
    creator_surf = creator_font.render(creator_text, True, (150, 150, 150))
    creator_rect = creator_surf.get_rect(bottomleft=(20, height - 20))

    # Animation parameters
    splash_clock = pygame.time.Clock()
    start_time = pygame.time.get_ticks()

    # Image transition parameters
    if has_splash_images:
        display_duration = 2000  # ms to display each image
        fade_duration = 1000     # ms for fade transition
        cycle_duration = display_duration + fade_duration
        total_image_duration = len(splash_images) * cycle_duration
    else:
        # No images, just use default duration
        total_image_duration = 0

    # Set splash duration - ensure minimum time but extend if we have images
    default_duration = 3000  # 3 seconds minimum
    splash_duration = max(default_duration, total_image_duration)

    # Pre-render loading text variations to avoid recreating them every frame
    loading_font = pygame.font.SysFont("Arial", scaled_font_size(16))
    loading_text = "Loading"
    loading_surfs = [
        loading_font.render(f"{loading_text}", True, (200, 200, 200)),
        loading_font.render(f"{loading_text}.", True, (200, 200, 200)),
        loading_font.render(f"{loading_text}..", True, (200, 200, 200)),
        loading_font.render(f"{loading_text}...", True, (200, 200, 200))
    ]
    loading_rects = [surf.get_rect(center=(center_x, center_y + scaled_font_size(80))) for surf in loading_surfs]
    dots_count = 0
    dots_timer = 0

    # Main splash screen loop
    running = True
    while running:
        current_time = pygame.time.get_ticks()
        elapsed = current_time - start_time
        delta_time = splash_clock.get_time()

        # Exit splash after duration or if user presses a key/clicks
        if elapsed >= splash_duration:
            running = False

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                running = False

        # Start with base gradient background
        screen.blit(splash_surface, (0, 0))

        # If we have splash images, show with transitions
        if has_splash_images:
            # Calculate which images to show based on time
            cycle_position = elapsed % total_image_duration
            image_index = int(cycle_position / cycle_duration)
            image_index = min(image_index, len(splash_images) - 1)  # Safety check

            cycle_progress = (cycle_position % cycle_duration) / cycle_duration

            # Determine if we're in a transition or display phase
            if cycle_progress < display_duration / cycle_duration:
                # Display phase - show current image
                current_img = splash_images[image_index]
                screen.blit(current_img, center_image(current_img, width, height))
            else:
                # Transition phase - blend between current and next
                next_image_index = (image_index + 1) % len(splash_images)

                # Calculate transition progress (0.0 to 1.0)
                transition_progress = (cycle_position % cycle_duration - display_duration) / fade_duration
                transition_progress = max(0, min(1, transition_progress))  # Clamp between 0-1

                # Create a transitional surface
                # First draw current image with fading alpha
                current_img = splash_images[image_index].copy()
                current_img.set_alpha(int(255 * (1 - transition_progress)))
                screen.blit(current_img, center_image(current_img, width, height))

                # Then draw next image with increasing alpha
                next_img = splash_images[next_image_index].copy()
                next_img.set_alpha(int(255 * transition_progress))
                screen.blit(next_img, center_image(next_img, width, height))

        # Apply pulsing effect to title (subtle scaling based on sine wave)
        pulse_scale = 1.0 + math.sin(elapsed / 500.0) * 0.02  # Scale between 0.98 and 1.02
        pulse_title = pygame.transform.smoothscale_by(title_surface, pulse_scale)
        pulse_rect = pulse_title.get_rect(center=(center_x, center_y - scaled_font_size(40)))

        # Draw the title
        screen.blit(pulse_title, pulse_rect)

        # Animate loading dots - OPTIMIZED to use pre-rendered surfaces
        dots_timer += delta_time
        if dots_timer > 300:  # Change dots every 300ms
            dots_count = (dots_count + 1) % 4
            dots_timer = 0

        # Use pre-rendered loading text
        screen.blit(loading_surfs[dots_count], loading_rects[dots_count])

        # Draw version and creator text
        screen.blit(version_surf, version_rect)
        screen.blit(creator_surf, creator_rect)

        # Update display
        pygame.display.flip()
        splash_clock.tick(20)  # Even lower frame rate for better performance

    # Stop the background music
    try:
        pygame.mixer.music.stop()
    except Exception as e:
        print(f"Error stopping splash screen music: {e}")

    # Restore previous screen mode if needed
    if not was_fullscreen:
        screen = pygame.display.set_mode(prev_screen_info['size'], prev_screen_info['mode'])
        # Restore scaling factors
        scale_x = prev_screen_info['size'][0] / BASE_WIDTH
        scale_y = prev_screen_info['size'][1] / BASE_HEIGHT

def center_image(image, screen_width, screen_height):
    """Return position to center an image on screen"""
    return ((screen_width - image.get_width()) // 2,
            (screen_height - image.get_height()) // 2)

def scale_image_to_fit(image, target_size):
    """Scale image to fit within target size while maintaining aspect ratio"""
    img_w, img_h = image.get_size()
    target_w, target_h = target_size

    # Calculate scaling factor (maintaining aspect ratio)
    scale_factor = min(target_w / img_w, target_h / img_h)

    # Calculate new dimensions
    new_w = int(img_w * scale_factor)
    new_h = int(img_h * scale_factor)

    # Scale the image
    return pygame.transform.smoothscale(image, (new_w, new_h))

def clear_all_json_data():
    """Clear all JSON data files to start fresh"""
    try:
        # Create data directory if it doesn't exist
        os.makedirs('data', exist_ok=True)

        # JSON files to clear
        json_files = [
            os.path.join('data', 'players.json'),
            os.path.join('data', 'current_session.json'),
            # Removed 'bingo_boards.json' from the list to preserve it
        ]

        # Delete each file if it exists
        for file_path in json_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Cleared {file_path}")

        # Also clear backup files
        for file_path in json_files:
            backup_path = file_path + '.bak'
            if os.path.exists(backup_path):
                os.remove(backup_path)
                print(f"Cleared backup {backup_path}")

        return True
    except Exception as e:
        print(f"Error clearing JSON data: {str(e)}")
        return False

def main():
    global scale_x, scale_y, title_font, header_font, number_font, large_number_font, button_font, lucky_number_font, background_img, screen

    # Start power management to keep screen always active (if enabled in settings)
    print("Initializing power management...")
    try:
        from settings_manager import SettingsManager
        settings_manager = SettingsManager()

        # Check if power management is enabled in settings
        power_enabled = settings_manager.get_setting('power_management', 'enabled', True)
        auto_start = settings_manager.get_setting('power_management', 'auto_start', True)

        if power_enabled and auto_start:
            if start_power_management():
                print("✓ Power management started - screen will stay always active")
            else:
                print("⚠ Warning: Power management failed to start")
        else:
            print("⚠ Power management disabled in settings - screen may dim/sleep")
    except Exception as e:
        print(f"⚠ Warning: Error checking power management settings: {e}")
        # Fallback to starting power management anyway
        if start_power_management():
            print("✓ Power management started (fallback) - screen will stay always active")
        else:
            print("⚠ Warning: Power management failed to start")

    # Clear all JSON data on startup to ensure fresh state
    clear_all_json_data()
    print("Starting fresh: JSON data cleared on application startup")

    # Create the game instance
    game = BingoGame()

    # Update scaling to ensure proper responsiveness at startup
    scale_x = SCREEN_WIDTH / BASE_WIDTH
    scale_y = SCREEN_HEIGHT / BASE_HEIGHT

    # Initialize fonts after scale factors are set
    title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
    header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
    number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
    large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
    button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
    lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

    # Show splash screen first
    show_splash_screen()

    # After splash screen, resize window to full screen dimensions
    screen_info = pygame.display.Info()
    full_width, full_height = screen_info.current_w, screen_info.current_h
    screen = pygame.display.set_mode((full_width, full_height), pygame.RESIZABLE)

    # Update scaling factors for full screen
    scale_x = full_width / BASE_WIDTH
    scale_y = full_height / BASE_HEIGHT

    # Recreate fonts with updated scaling factors
    title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
    header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
    number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
    large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
    button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
    lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

    # Update background image if available
    if background_img:
        background_img = pygame.transform.scale(background_img, (full_width, full_height))

    # Create the game instance first
    game = BingoGame()

    # Show board selection window after splash screen with screen mode consistency
    from Board_selection_fixed import show_board_selection
    # Ensure screen mode consistency before showing board selection
    screen_mode_manager = get_screen_mode_manager()
    screen = screen_mode_manager.ensure_consistent_mode(screen)
    selected_cartellas = show_board_selection(screen, game_instance=game)

    # Store a reference to the board selection for toggling visibility
    # Note: board_selection reference is set in show_board_selection function

    # Check if cartellas were selected
    if not selected_cartellas:
        # If no cartellas were selected, exit
        print("Main: No cartellas selected, exiting")
        pygame.quit()
        sys.exit()

    # Reset any navigation state that might have been set
    if hasattr(game, 'active_nav'):
        game.active_nav = "play"  # Default to play mode

    # Update game with selected cartella numbers
    if selected_cartellas:
        # --- FIX: Recreate Player objects for remembered/selected cartellas ---
        from player_storage import save_players_to_json
        from view_players import Player
        from player_storage import load_game_settings
        # Load bet amount from settings if available
        try:
            game_settings = load_game_settings()
            bet_amount = game_settings.get('bet_amount', 50)
        except Exception:
            bet_amount = 50
        players = [Player(cartela_no=cartella, bet_amount=bet_amount, deposited=0) for cartella in selected_cartellas]
        save_players_to_json(players)
        # --- END FIX ---
        # Update player list with selected cartella numbers
        game.players = load_players_from_json()

        # CRITICAL FIX: Refresh cartella preview after players are loaded
        if hasattr(game, 'cartella_preview'):
            game.cartella_preview.load_registered_cartellas()
            print(f"Cartella preview refreshed: {len(game.cartella_preview.registered_cartellas)} cartellas loaded")

        # Load game settings from JSON
        try:
            from player_storage import load_game_settings
            game_settings = load_game_settings()

            # Apply settings to the game
            if 'commission_percentage' in game_settings:
                game.commission_percentage = game_settings['commission_percentage']
                print(f"Main game: Loaded commission percentage from settings: {game.commission_percentage}%")

            if 'bet_amount' in game_settings:
                game.bet_amount = game_settings['bet_amount']
                print(f"Main game: Loaded bet amount from settings: {game.bet_amount} ETB")

            if 'prize_pool' in game_settings:
                # Store the prize pool from settings for validation
                settings_prize_pool = game_settings['prize_pool']
                print(f"Main game: Prize pool from settings: {settings_prize_pool} ETB")
        except Exception as e:
            print(f"Error loading game settings: {e}")

        # Calculate prize pool based on players and settings
        game.calculate_prize_pool()

        # Validate prize pool calculation
        if 'settings_prize_pool' in locals():
            if game.prize_pool != settings_prize_pool:
                print(f"WARNING: Prize pool mismatch! Calculated: {game.prize_pool}, Settings: {settings_prize_pool}")
                print(f"Using calculated prize pool: {game.prize_pool} ETB")

    # Integrate payment system if available
    if PAYMENT_SYSTEM_AVAILABLE:
        try:
            integrate_with_game(game)
            print("Payment system integrated successfully")
        except Exception as e:
            print(f"Error integrating payment system: {e}")

    clock = pygame.time.Clock()

    # Don't pre-call any numbers on startup
    game.called_numbers = []
    game.current_number = None

    # Try to load and play background music
    try:
        pygame.mixer.music.load("assets/bingo_music.mp3")
        pygame.mixer.music.set_volume(0.5)
        pygame.mixer.music.play(-1)  # Loop indefinitely
    except:
        pass

    # Track fullscreen state
    is_fullscreen = False
    current_w, current_h = SCREEN_WIDTH, SCREEN_HEIGHT

    # Animation timer for UI elements - REDUCED FREQUENCY TO PREVENT FLICKERING
    pygame.time.set_timer(pygame.USEREVENT, 200)  # ~5fps for animations to reduce flickering

    # Performance optimization - track when screen update is needed
    need_redraw = True

    # Track mouse position for hover updates to avoid checking every frame
    last_mouse_pos = pygame.mouse.get_pos()

    # Target frames per second - optimized for performance while maintaining responsiveness
    target_fps = 30  # Increased for smoother animations but with optimized rendering

    # Idle counter for further CPU optimization
    idle_counter = 0
    max_idle_count = 5  # Increased to allow more idle frames before aggressive CPU saving

    # Performance optimization - track UI state to avoid unnecessary redraws
    last_ui_state = {
        'called_numbers': [],
        'current_number': None,
        'prize_pool': 0,
        'game_started': False,
        'is_paused': False,
        'mouse_pos': (0, 0),
        'modal_visible': False
    }

    # Settings window optimization removed - settings now show simple messages

    # External display health check variables - GREATLY INCREASED INTERVAL TO PREVENT FLICKERING
    last_display_check = time.time()
    display_check_interval = 300.0  # Check every 5 minutes instead of 30 seconds to eliminate flickering
    display_manager = get_external_display_manager()

    # ANTI-FLICKERING FIX: Flag to temporarily disable display health checks during mode transitions
    screen_mode_transition_active = False
    screen_mode_transition_cooldown = 0

    # Power management monitoring
    last_power_check = time.time()
    power_check_interval = 300.0  # Check power management every 5 minutes

    running = True
    while running:
        # Start time measurement for frame
        frame_start_time = time.time()

        # Event handling - optimized to reduce CPU usage while maintaining responsiveness
        events = pygame.event.get()
        had_events = len(events) > 0

        # We'll still optimize for idle states, but in a way that doesn't break clickability
        # Instead of skipping frames entirely, we'll just ensure we redraw periodically
        if idle_counter > max_idle_count * 4 and not had_events and not game.game_started:
            # For idle states, we'll still process events but less frequently
            # This ensures clickable components remain responsive
            need_redraw = idle_counter % 30 == 0  # Force redraw every 30 idle frames instead of 10

        for event in events:
            if event.type == pygame.QUIT:
                # Check if we're in the main game page and the game has started
                if game.active_nav == "play" and game.game_started:
                    # Show exit confirmation dialog instead of immediately exiting
                    game.ui_handler.show_exit_confirmation = True
                    need_redraw = True
                else:
                    # Exit immediately if not in the main game page or game hasn't started
                    running = False

            # Handle window resize events with external display support
            elif event.type == pygame.VIDEORESIZE:
                if not is_fullscreen:  # Only process resize events in windowed mode
                    try:
                        # Use HDMI fixer for better resize handling
                        hdmi_fixer = get_hdmi_display_fixer()

                        # Update screen size with HDMI stability
                        current_w, current_h = event.w, event.h
                        new_screen = hdmi_fixer.handle_display_mode_switch(
                            screen, current_w, current_h, fullscreen=False
                        )

                        if new_screen:
                            screen = new_screen
                            print(f"Window resized with HDMI stability: {current_w}x{current_h}")
                        else:
                            print("HDMI resize failed, using fallback...")
                            # Fallback to external display manager
                            display_manager = get_external_display_manager()
                            screen, actual_w, actual_h = display_manager.create_compatible_display(
                                preferred_width=current_w,
                                preferred_height=current_h,
                                fullscreen=False
                            )

                    except Exception as e:
                        print(f"HDMI resize failed, using basic fallback: {e}")
                        # Fallback to original resize handling
                        current_w, current_h = event.w, event.h
                        screen = pygame.display.set_mode((current_w, current_h), pygame.RESIZABLE)

                    # Update scaling factors
                    game.update_scaling()
                    # Recreate fonts with new scaling
                    title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
                    header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
                    number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
                    large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
                    button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
                    lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)
                    # Update background image if available
                    if background_img:
                        background_img = pygame.transform.scale(background_img, (current_w, current_h))
                    need_redraw = True

            # Handle display change events (HDMI connect/disconnect)
            elif event.type == pygame.VIDEOEXPOSE:
                try:
                    # Handle display exposure events (can indicate display changes)
                    display_manager = get_external_display_manager()
                    if display_manager.detect_display_change_and_recover():
                        print("Display change detected and handled")
                        # CRITICAL FIX: Get the updated screen surface from display manager
                        try:
                            updated_screen = display_manager.current_display
                            if updated_screen and updated_screen != screen:
                                screen = updated_screen
                                print("Updated screen reference after VIDEOEXPOSE display recovery")
                                # Update screen mode manager with new screen reference
                                game.screen_mode_manager.set_screen_reference(screen)
                        except Exception as screen_update_error:
                            print(f"Error updating screen reference in VIDEOEXPOSE: {screen_update_error}")

                        # Update scaling and fonts after display recovery
                        try:
                            scale_x = screen.get_width() / BASE_WIDTH
                            scale_y = screen.get_height() / BASE_HEIGHT
                            game.update_scaling()
                            title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
                            header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
                            number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
                            large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
                            button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
                            lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)
                        except Exception as scaling_error:
                            print(f"Error updating scaling in VIDEOEXPOSE: {scaling_error}")
                        need_redraw = True
                except Exception as e:
                    print(f"Error handling display change: {e}")
                    # Try to recover the screen surface if the display change handling failed
                    try:
                        screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                        print("Screen surface recovered after VIDEOEXPOSE error")
                        game.screen_mode_manager.set_screen_reference(screen)
                    except Exception as recovery_error:
                        print(f"Failed to recover screen after VIDEOEXPOSE error: {recovery_error}")

            # Pass animation timer events to ViewPlayers popup
            elif event.type == pygame.USEREVENT:
                # If view players popup is active, let it handle animation
                if game.show_view_players and game.view_players:
                    game.view_players.handle_event(event)
                need_redraw = True

            # Handle all mouse events and route to settings window if visible
            elif event.type in (pygame.MOUSEBUTTONDOWN, pygame.MOUSEBUTTONUP, pygame.MOUSEMOTION, pygame.MOUSEWHEEL):
                # Process all mouse events normally to ensure clickable components work correctly
                # For mouse motion, we'll still optimize but in a way that doesn't break functionality
                if event.type == pygame.MOUSEMOTION:
                    # Only update UI on significant mouse movements to reduce CPU usage
                    # This approach maintains clickability while reducing unnecessary redraws
                    current_mouse_pos = pygame.mouse.get_pos()
                    if hasattr(game, '_last_processed_mouse_pos'):
                        # Calculate distance moved
                        dx = abs(current_mouse_pos[0] - game._last_processed_mouse_pos[0])
                        dy = abs(current_mouse_pos[1] - game._last_processed_mouse_pos[1])
                        # Only process if moved more than 5 pixels in any direction
                        if dx < 5 and dy < 5 and idle_counter > 3:
                            continue
                    # Update last processed position
                    game._last_processed_mouse_pos = current_mouse_pos
                # First check if UI handler should handle the event for dragging popup windows
                if hasattr(game, 'ui_handler'):
                    # Pass the event to the UI handler for dragging functionality
                    game.ui_handler.handle_mouse_event(event)
                    need_redraw = True

                # PRIORITY 1: Handle cartella preview overlay mouse events FIRST
                if hasattr(game, 'cartella_preview') and game.cartella_preview.active:
                    if game.cartella_preview.handle_event(event):
                        need_redraw = True
                        continue

                # Settings window event handling removed - settings now show simple messages
                # Handle mouse clicks for all navigation and game elements
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left mouse button
                    # First check navigation buttons (these should work regardless of active_nav)
                    if game.check_button_click(event.pos):
                        need_redraw = True
                        continue

                    # Then handle play-specific interactions only when in play mode
                    if game.active_nav == "play":
                        # Check if the advertising section was clicked
                        if hasattr(game, 'ad_section_rect') and game.ad_section_rect.collidepoint(event.pos):
                            # Check if Ctrl key is pressed for zoom functionality
                            ctrl_pressed = pygame.key.get_mods() & pygame.KMOD_CTRL

                            if ctrl_pressed:
                                # Toggle between zoomed and normal mode
                                game.ad_zoomed = not game.ad_zoomed

                                # Play click sound
                                if hasattr(game, 'button_click_sound') and game.button_click_sound:
                                    game.button_click_sound.play()

                                # Show feedback message
                                game.message = "Advertising section " + ("zoomed" if game.ad_zoomed else "normal size")
                                game.message_type = "info"
                                game.message_timer = 60  # Display for 2.4 seconds (at 25 FPS)
                                need_redraw = True
                                continue
                            else:
                                # Regular click - toggle the hidden state
                                settings_manager = game.settings_window.settings_manager
                                current_hidden = settings_manager.get_setting('advertising', 'hidden', False)
                                settings_manager.set_setting('advertising', 'hidden', not current_hidden)

                                # Play click sound
                                if hasattr(game, 'button_click_sound') and game.button_click_sound:
                                    game.button_click_sound.play()

                                # Show feedback message
                                game.message = "Advertising section " + ("hidden" if not current_hidden else "shown")
                                game.message_type = "info"
                                game.message_timer = 60  # Display for 2.4 seconds (at 25 FPS)
                                need_redraw = True
                                continue

            elif event.type == pygame.KEYDOWN:
                # PRIORITY 1: Handle F key and Escape key for screen mode toggling FIRST
                # These should work regardless of other UI states (except when typing in input fields)

                # F key: Toggle fullscreen mode (highest priority)
                if event.key == pygame.K_f:
                    # Only skip F key handling if actively typing in input fields
                    if not (game.input_active or game.bet_input_active or
                           (hasattr(game, 'ui_handler') and game.ui_handler.reason_input_active)):
                        # Toggle fullscreen with F key using screen mode manager
                        screen = game.screen_mode_manager.handle_f_key_toggle(screen)

                        # Update scaling after changing display mode
                        scale_x = screen.get_width() / BASE_WIDTH
                        scale_y = screen.get_height() / BASE_HEIGHT
                        game.update_scaling()

                        # Recreate fonts with new scaling
                        title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
                        header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
                        number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
                        large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
                        button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
                        lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

                        # Update background image if available
                        if background_img:
                            background_img = pygame.transform.scale(background_img, screen.get_size())

                        need_redraw = True
                        continue  # Skip other handlers for F key

                # Escape key: Exit fullscreen or close popups (high priority)
                if event.key == pygame.K_ESCAPE:
                    # HIGHEST PRIORITY: Handle cartella preview overlay Escape key FIRST
                    if hasattr(game, 'cartella_preview') and game.cartella_preview.active:
                        game.cartella_preview.hide()
                        need_redraw = True
                        print("Cartella preview closed via Escape key (highest priority)")
                        continue  # Skip all other Escape key handlers

                    # Second priority: check if settings window is visible and close it
                    elif hasattr(game, 'settings_window') and game.settings_window.visible:
                        game.settings_window.hide()
                        need_redraw = True
                        continue
                    # Then check if view players popup is active, close it
                    elif game.show_view_players and game.view_players:
                        game.view_players.hide()
                        game.show_view_players = False
                        need_redraw = True
                        continue
                    # Handle input field cancellation
                    elif game.input_active or game.bet_input_active:
                        # Let the input handler deal with this
                        if game.handle_input(event):
                            need_redraw = True
                            continue
                    # Handle modal input cancellation
                    elif hasattr(game, 'ui_handler') and game.ui_handler.reason_input_active:
                        if game.handle_input(event):
                            need_redraw = True
                            continue
                    # Otherwise exit fullscreen mode if in fullscreen using screen mode manager
                    else:
                        # ANTI-FLICKERING FIX: Only handle escape if actually in fullscreen mode
                        try:
                            current_flags = screen.get_flags()
                            is_fullscreen = bool(current_flags & pygame.FULLSCREEN)
                        except pygame.error as e:
                            print(f"Error getting screen flags during escape handling: {e}")
                            # If screen surface is invalid, try to recover
                            try:
                                display_manager = get_external_display_manager()
                                screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                                print("Screen surface recovered after escape key error")
                                # Update screen mode manager with new screen reference
                                game.screen_mode_manager.set_screen_reference(screen)
                                is_fullscreen = False  # Assume windowed mode after recovery
                            except Exception as recovery_error:
                                print(f"Failed to recover screen surface: {recovery_error}")
                                continue  # Skip this escape key event

                        if is_fullscreen:
                            # ANTI-FLICKERING FIX: Set transition flag to disable health checks
                            screen_mode_transition_active = True
                            screen_mode_transition_cooldown = time.time() + 5.0  # 5 second cooldown

                            try:
                                # Only process escape key if we're actually in fullscreen
                                screen, mode_changed = game.screen_mode_manager.handle_escape_key(screen)
                                if mode_changed:
                                    # Validate the new screen surface before proceeding
                                    try:
                                        # Test if screen surface is valid
                                        _ = screen.get_size()
                                        _ = screen.get_flags()

                                        # Update scaling after changing display mode
                                        scale_x = screen.get_width() / BASE_WIDTH
                                        scale_y = screen.get_height() / BASE_HEIGHT
                                        game.update_scaling()

                                        # Recreate fonts with new scaling
                                        title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
                                        header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
                                        number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
                                        large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
                                        button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
                                        lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)

                                        # Update background image if available
                                        if background_img:
                                            background_img = pygame.transform.scale(background_img, screen.get_size())

                                        need_redraw = True
                                        print("Screen mode change completed successfully")

                                    except pygame.error as screen_error:
                                        print(f"Screen surface invalid after mode change: {screen_error}")
                                        # Try to recover with a new screen surface
                                        try:
                                            display_manager = get_external_display_manager()
                                            screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                                            print("Screen surface recovered after mode change error")
                                            # Update screen mode manager with new screen reference
                                            game.screen_mode_manager.set_screen_reference(screen)
                                            need_redraw = True
                                        except Exception as recovery_error:
                                            print(f"Failed to recover screen after mode change: {recovery_error}")

                            except Exception as mode_change_error:
                                print(f"Error during screen mode change: {mode_change_error}")
                                # Try to recover with a safe screen mode
                                try:
                                    display_manager = get_external_display_manager()
                                    screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                                    print("Screen recovered to windowed mode after error")
                                    # Update screen mode manager with new screen reference
                                    game.screen_mode_manager.set_screen_reference(screen)
                                    need_redraw = True
                                except Exception as recovery_error:
                                    print(f"Failed to recover screen: {recovery_error}")

                        # If not in fullscreen, ignore the escape key to prevent flickering
                        continue  # Skip other handlers for Escape key

                # PRIORITY 2: Handle modal input (pause prompt, etc.)
                if hasattr(game, 'ui_handler') and game.ui_handler.reason_input_active:
                    if game.handle_input(event):
                        need_redraw = True
                        continue

                # PRIORITY 3: Handle normal input fields
                if game.input_active or game.bet_input_active:
                    if game.handle_input(event):
                        need_redraw = True
                        continue

                # PRIORITY 4: Handle cartella preview overlay events (NON-ESCAPE KEYS ONLY)
                # Note: Escape key for cartella preview is handled at highest priority above
                if hasattr(game, 'cartella_preview') and game.cartella_preview.active:
                    # Only handle non-Escape key events here to avoid conflicts
                    if event.key != pygame.K_ESCAPE and game.cartella_preview.handle_event(event):
                        need_redraw = True
                        continue

                # PRIORITY 5: Handle view players popup keyboard events
                if game.show_view_players and game.view_players:
                    if game.view_players.handle_event(event):
                        need_redraw = True
                        continue

                # Enhanced Bingo Favor Mode hotkey handling
                ctrl_pressed = pygame.key.get_mods() & pygame.KMOD_CTRL
                shift_pressed = pygame.key.get_mods() & pygame.KMOD_SHIFT
                alt_pressed = pygame.key.get_mods() & pygame.KMOD_ALT

                # Hotkey: Ctrl+P = Show cartella preview overlay
                if ctrl_pressed and event.key == pygame.K_p:
                    if hasattr(game, 'cartella_preview'):
                        game.cartella_preview.show()
                        need_redraw = True
                        print("Cartella preview activated via Ctrl+P hotkey")
                    continue

                # Hotkey 1: Ctrl+Shift+D = Force deactivate favor mode
                if ctrl_pressed and shift_pressed and event.key == pygame.K_d:
                    if hasattr(game, 'favor_mode'):
                        game.favor_mode.force_deactivate()
                        need_redraw = True
                    continue

                # Hotkey 2: Ctrl+Alt+[number] = Quick switch favor to cartella (real-time)
                if ctrl_pressed and alt_pressed:
                    if pygame.K_0 <= event.key <= pygame.K_9 or pygame.K_KP0 <= event.key <= pygame.K_KP9:
                        # Convert key to number (0-9)
                        if pygame.K_0 <= event.key <= pygame.K_9:
                            num = event.key - pygame.K_0
                        else:  # Numpad
                            num = event.key - pygame.K_KP0

                        if num > 0 and hasattr(game, 'favor_mode'):  # Don't allow 0
                            game.favor_mode.switch_target(num)
                            need_redraw = True
                        continue

                # Hotkey 3: Ctrl+Shift+[number] = Toggle favor mode (enhanced with multi-digit support)
                if ctrl_pressed and shift_pressed:
                    # Store the current time for double-digit input
                    current_time = time.time()

                    # Initialize the cartella number input buffer if it doesn't exist
                    if not hasattr(game, '_favor_mode_input_buffer'):
                        game._favor_mode_input_buffer = []
                        game._favor_mode_last_input_time = 0

                    # Check if the key is a number (0-9)
                    if pygame.K_0 <= event.key <= pygame.K_9 or pygame.K_KP0 <= event.key <= pygame.K_KP9:
                        # Convert key to number (0-9)
                        if pygame.K_0 <= event.key <= pygame.K_9:
                            num = event.key - pygame.K_0
                        else:  # Numpad
                            num = event.key - pygame.K_KP0

                        # Check if this is a continuation of a previous input (within 1.5 seconds)
                        if current_time - game._favor_mode_last_input_time < 1.5:
                            # Add this digit to the buffer
                            game._favor_mode_input_buffer.append(num)
                        else:
                            # Start a new input
                            game._favor_mode_input_buffer = [num]

                        # Update the last input time
                        game._favor_mode_last_input_time = current_time

                        # If we have digits in the buffer, use them to form the cartella number
                        if game._favor_mode_input_buffer:
                            # Convert the buffer to a cartella number
                            cartella_num = 0
                            for digit in game._favor_mode_input_buffer:
                                cartella_num = cartella_num * 10 + digit

                            # Only process if cartella number is valid (> 0)
                            if cartella_num > 0 and hasattr(game, 'favor_mode'):
                                # Use enhanced toggle functionality
                                game.favor_mode.toggle_favor(cartella_num)
                                need_redraw = True

                        continue

                # Hotkey 4: Alt+F = Quick toggle favor for cartella 1 (emergency hotkey)
                if alt_pressed and event.key == pygame.K_f:
                    if hasattr(game, 'favor_mode'):
                        game.favor_mode.toggle_favor(1)
                        need_redraw = True
                    continue

                if event.key == pygame.K_SPACE:
                    # Use space as a hotkey to start/pause the game (same as the start/pause button)
                    game.start_game()
                    need_redraw = True

                # Save current session data with S key
                if event.key == pygame.K_s:
                    game.save_session_data()
                    need_redraw = True

            # Handle USEREVENT for custom events (stats refresh logic removed)
            elif event.type == pygame.USEREVENT:
                # Stats refresh events are no longer processed since stats view is disabled
                pass

            # Forward mouse motion events to ViewPlayers for hover effects
            elif event.type == pygame.MOUSEMOTION:
                # Only update hover states if mouse moved significantly
                current_mouse_pos = event.pos
                dx = abs(current_mouse_pos[0] - last_mouse_pos[0])
                dy = abs(current_mouse_pos[1] - last_mouse_pos[1])

                # Only redraw if mouse moved more than a few pixels
                if dx > 2 or dy > 2:
                    last_mouse_pos = current_mouse_pos

                    # PRIORITY 1: Handle cartella preview overlay mouse motion events
                    if hasattr(game, 'cartella_preview') and game.cartella_preview.active:
                        if game.cartella_preview.handle_event(event):
                            need_redraw = True

                    # Update button hover states for animations
                    game.update_button_hover_states(current_mouse_pos)

                    # Handle view players popup hover events
                    if game.show_view_players and game.view_players:
                        game.view_players.handle_event(event)

                    need_redraw = True

        # Update cursor blink state for input fields - only when necessary
        if game.input_active or game.bet_input_active or (hasattr(game, 'ui_handler') and game.ui_handler.reason_input_active):
            redraw_needed_for_cursor = False

            # Cartella input cursor blink
            if game.input_active:
                game.input_cursor_timer += clock.get_time()
                if game.input_cursor_timer >= 500:  # Blink every 500ms
                    game.input_cursor_visible = not game.input_cursor_visible
                    game.input_cursor_timer = 0
                    redraw_needed_for_cursor = True

            # Bet amount input cursor blink
            if game.bet_input_active:
                game.bet_input_cursor_timer += clock.get_time()
                if game.bet_input_cursor_timer >= 500:  # Blink every 500ms
                    game.bet_input_cursor_visible = not game.bet_input_cursor_visible
                    game.bet_input_cursor_timer = 0
                    redraw_needed_for_cursor = True

            # Update reason input cursor blink for pause prompt
            if hasattr(game, 'ui_handler') and game.ui_handler.reason_input_active:
                redraw_needed = game.ui_handler.update_cursor_blink(clock.get_time())
                if redraw_needed:
                    need_redraw = True

            if redraw_needed_for_cursor:
                need_redraw = True

        # Settings window visibility tracking removed - settings now show simple messages

        # ANTI-FLICKERING FIX: Update transition cooldown
        current_time = time.time()
        if screen_mode_transition_active and current_time > screen_mode_transition_cooldown:
            screen_mode_transition_active = False
            print("Screen mode transition cooldown completed - re-enabling display health checks")

        # Periodic external display health check - REDUCED FREQUENCY TO PREVENT FLICKERING
        if current_time - last_display_check > display_check_interval and not screen_mode_transition_active:
            try:
                # Check for display issues and attempt recovery - BUT DON'T FORCE REDRAW UNLESS NECESSARY
                display_changed = display_manager.detect_display_change_and_recover()
                if display_changed:
                    print("Periodic display health check: Issues detected and resolved")
                    # CRITICAL FIX: Get the updated screen surface from display manager
                    try:
                        updated_screen = display_manager.current_display
                        if updated_screen and updated_screen != screen:
                            screen = updated_screen
                            print("Updated screen reference after display recovery")
                            # Update screen mode manager with new screen reference
                            game.screen_mode_manager.set_screen_reference(screen)
                    except Exception as screen_update_error:
                        print(f"Error updating screen reference: {screen_update_error}")

                    # Update scaling and fonts after recovery
                    try:
                        scale_x = screen.get_width() / BASE_WIDTH
                        scale_y = screen.get_height() / BASE_HEIGHT
                        game.update_scaling()
                        title_font = pygame.font.SysFont("Arial", scaled_font_size(60), bold=True)
                        header_font = pygame.font.SysFont("Arial", scaled_font_size(28), bold=True)
                        number_font = pygame.font.SysFont("Arial", scaled_font_size(24))
                        large_number_font = pygame.font.SysFont("Arial", scaled_font_size(80), bold=True)
                        button_font = pygame.font.SysFont("Arial", scaled_font_size(24), bold=True)
                        lucky_number_font = pygame.font.SysFont("Arial", scaled_font_size(22), bold=True)
                    except Exception as scaling_error:
                        print(f"Error updating scaling after display recovery: {scaling_error}")

                    # ONLY force redraw if display actually changed
                    need_redraw = True

                # ANTI-FLICKERING FIX: Disable HDMI-specific issues handler
                # This was causing unnecessary display operations and flickering
                # if hasattr(display_manager, 'handle_hdmi_connection_issues'):
                #     display_manager.handle_hdmi_connection_issues()

                last_display_check = current_time

            except Exception as e:
                print(f"Error during periodic display health check: {e}")
                # Try to recover the screen surface if the health check failed
                try:
                    screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                    print("Screen surface recovered after health check error")
                    game.screen_mode_manager.set_screen_reference(screen)
                except Exception as recovery_error:
                    print(f"Failed to recover screen after health check error: {recovery_error}")
                last_display_check = current_time  # Reset timer even on error
        elif screen_mode_transition_active:
            # Skip display health check during screen mode transitions
            last_display_check = current_time  # Reset timer to prevent immediate check after cooldown

        # Power management monitoring - ensure screen stays always active
        if current_time - last_power_check > power_check_interval:
            try:
                if not is_power_management_active():
                    print("Power management inactive - restarting...")
                    start_power_management()
                else:
                    # Refresh power management to ensure it stays active
                    from power_management import get_power_manager
                    power_manager = get_power_manager()
                    power_manager.prevent_screen_sleep()
                    power_manager.keep_window_active()
                last_power_check = current_time
            except Exception as e:
                print(f"Error in power management check: {e}")
                last_power_check = current_time

        # Update game state (process any pending delayed actions)
        game.update()

        # Check if UI state has changed to determine if redraw is needed
        current_ui_state = {
            'called_numbers': len(game.called_numbers),
            'current_number': game.current_number,
            'prize_pool': game.prize_pool,
            'game_started': game.game_started,
            'is_paused': hasattr(game.game_state, 'is_paused') and game.game_state.is_paused,
            'mouse_pos': pygame.mouse.get_pos(),
            'modal_visible': (hasattr(game.ui_handler, 'show_reset_confirmation') and game.ui_handler.show_reset_confirmation) or
                            (hasattr(game.ui_handler, 'show_exit_confirmation') and game.ui_handler.show_exit_confirmation) or
                            (hasattr(game.ui_handler, 'show_winner_display') and game.ui_handler.show_winner_display)
        }

        # Force redraw if UI state has changed
        if (current_ui_state['called_numbers'] != last_ui_state['called_numbers'] or
            current_ui_state['current_number'] != last_ui_state['current_number'] or
            current_ui_state['prize_pool'] != last_ui_state['prize_pool'] or
            current_ui_state['game_started'] != last_ui_state['game_started'] or
            current_ui_state['is_paused'] != last_ui_state['is_paused'] or
            current_ui_state['modal_visible'] != last_ui_state['modal_visible'] or
            game.force_redraw):
            need_redraw = True
            last_ui_state = current_ui_state.copy()
            game.force_redraw = False

        # PERFORMANCE OPTIMIZATION: Monitor frame performance
        frame_start_time = time.time()

        # Only draw the game when necessary to reduce CPU usage
        if need_redraw:
            try:
                # Validate screen surface before drawing
                try:
                    _ = screen.get_size()
                    _ = screen.get_flags()
                except pygame.error as screen_validation_error:
                    print(f"Screen surface invalid before drawing: {screen_validation_error}")
                    # Try to recover screen surface
                    try:
                        display_manager = get_external_display_manager()
                        screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                        print("Screen surface recovered before drawing")
                        # Update screen mode manager with new screen reference
                        game.screen_mode_manager.set_screen_reference(screen)
                        # Update scaling after recovery
                        scale_x = screen.get_width() / BASE_WIDTH
                        scale_y = screen.get_height() / BASE_HEIGHT
                        game.update_scaling()
                    except Exception as recovery_error:
                        print(f"Failed to recover screen before drawing: {recovery_error}")
                        idle_counter += 1
                        continue  # Skip this draw cycle

                # Always use the normal drawing path to ensure all clickable components work correctly
                try:
                    game.draw()
                except pygame.error as draw_error:
                    if "display Surface quit" in str(draw_error):
                        print(f"Display surface quit during draw: {draw_error}")
                        # Try to recover and redraw
                        try:
                            display_manager = get_external_display_manager()
                            screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                            print("Screen surface recovered during draw error")
                            # Update screen mode manager with new screen reference
                            game.screen_mode_manager.set_screen_reference(screen)
                            # Update scaling after recovery
                            scale_x = screen.get_width() / BASE_WIDTH
                            scale_y = screen.get_height() / BASE_HEIGHT
                            game.update_scaling()
                            # Try drawing again with recovered surface
                            game.draw()
                        except Exception as recovery_error:
                            print(f"Failed to recover during draw error: {recovery_error}")
                            idle_counter += 1
                            continue  # Skip this draw cycle
                    else:
                        print(f"Other draw error: {draw_error}")
                        raise  # Re-raise non-surface-quit errors
                except Exception as general_draw_error:
                    print(f"General error during draw: {general_draw_error}")
                    idle_counter += 1
                    continue  # Skip this draw cycle

                # Safely flip the display
                try:
                    pygame.display.flip()
                except pygame.error as flip_error:
                    if "display Surface quit" in str(flip_error):
                        print(f"Display surface quit during flip: {flip_error}")
                        # Try to recover
                        try:
                            display_manager = get_external_display_manager()
                            screen, _, _ = display_manager.create_compatible_display(fullscreen=False)
                            print("Screen surface recovered during flip error")
                            # Update screen mode manager with new screen reference
                            game.screen_mode_manager.set_screen_reference(screen)
                            pygame.display.flip()  # Try flip again
                        except Exception as recovery_error:
                            print(f"Failed to recover during flip error: {recovery_error}")
                    else:
                        print(f"Other flip error: {flip_error}")

                need_redraw = False
                idle_counter = 0  # Reset idle counter when we draw

            except Exception as overall_draw_error:
                print(f"Overall drawing error: {overall_draw_error}")
                idle_counter += 1
                # Don't set need_redraw = False so we'll try again next cycle
        else:
            idle_counter += 1

        # PERFORMANCE OPTIMIZATION: Update performance monitoring and control frame rate
        frame_time = time.time() - frame_start_time
        if hasattr(game, '_monitor_performance'):
            game._monitor_performance(frame_time)

        sleep_time = max(0, (1.0/target_fps) - frame_time)

        # If we've been idle for a while, sleep a bit longer to reduce CPU
        # But only if we're not in an interactive state where clicks might be expected
        if idle_counter > max_idle_count and not had_events and not game.input_active and not game.bet_input_active:
            # More aggressive CPU saving during prolonged idle periods
            # Reduced from 150ms to 120ms to improve responsiveness
            extra_sleep = min(0.12, sleep_time * 2)  # Up to 120ms extra sleep for CPU savings
            time.sleep(extra_sleep)

        # ANIMATION FIX: Adaptive frame timing for smooth animations
        # Use higher frame rate during active animations
        if need_redraw or (hasattr(game, 'current_number') and game.current_number):
            # Higher frame rate during active animations
            animation_target_fps = 60
        else:
            # Standard frame rate when idle
            animation_target_fps = target_fps

        # Normal frame timing with animation consideration
        time.sleep(sleep_time)
        clock.tick(animation_target_fps)

        # Force a redraw periodically to ensure UI remains responsive - SIGNIFICANTLY REDUCED TO PREVENT FLICKERING
        # Increased from 120 to 600 frames (20 seconds at 30 FPS) to eliminate flickering while maintaining responsiveness
        if idle_counter >= 600:
            need_redraw = True
            idle_counter = 0

        # Re-enable redraw if running much slower than target
        if clock.get_fps() < target_fps/2:
            need_redraw = True

        # Only force a redraw when mouse moves significantly to ensure UI responsiveness
        # while avoiding constant redraws for tiny mouse movements
        current_mouse_pos = pygame.mouse.get_pos()
        if hasattr(game, '_last_mouse_pos_check'):
            # Calculate distance moved
            dx = current_mouse_pos[0] - game._last_mouse_pos_check[0]
            dy = current_mouse_pos[1] - game._last_mouse_pos_check[1]
            distance_squared = dx*dx + dy*dy

            # Only redraw if mouse moved more than 5 pixels (25 squared)
            if distance_squared > 25:
                need_redraw = True
                game._last_mouse_pos_check = current_mouse_pos
        else:
            game._last_mouse_pos_check = current_mouse_pos

        # End of main game loop

    # Clean up modern advertising system
    print("Cleaning up modern advertising...")
    try:
        if hasattr(game, 'modern_advertising') and game.modern_advertising:
            game.modern_advertising.cleanup()
            print("✓ Modern advertising cleaned up successfully")
    except Exception as e:
        print(f"⚠ Warning: Modern advertising cleanup failed: {e}")

    # Clean up power management before exiting
    print("Cleaning up power management...")
    if stop_power_management():
        print("✓ Power management stopped - normal power settings restored")
    else:
        print("⚠ Warning: Power management cleanup failed")

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()