#!/usr/bin/env python3
"""
WOW Bingo Game - Build Without Visual Studio
============================================

This script tries to build the game without requiring Visual Studio Build Tools
by using PyInstaller and other alternative methods.

Usage:
    python build_no_vs.py [--verbose]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def check_pyinstaller():
    """Check if PyInstaller is available and install if needed."""
    try:
        import PyInstaller
        log("PyInstaller is available")
        return True
    except ImportError:
        log("Installing PyInstaller (no Visual Studio required)...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller>=6.0.0'],
                         check=True, capture_output=True)
            log("PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            log(f"Failed to install PyInstaller: {e}", "ERROR")
            return False

def build_with_pyinstaller(verbose=False):
    """Build using PyInstaller (no Visual Studio required)."""
    project_root = Path(__file__).parent.absolute()
    dist_dir = project_root / "dist"
    build_dir = project_root / "build"
    
    log("Building with PyInstaller (No Visual Studio Build Tools required)...")
    
    # Clean previous builds
    for dir_path in [dist_dir, build_dir]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
    
    dist_dir.mkdir(exist_ok=True)
    build_dir.mkdir(exist_ok=True)
    
    # PyInstaller command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # Create directory distribution
        '--windowed',  # No console window
        '--name=WOW_Bingo_Game',
        f'--distpath={dist_dir}',
        f'--workpath={build_dir}',
        '--clean',
        '--noconfirm',
    ]
    
    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.extend(['--icon', str(icon_path)])
    
    # Add data files
    if (project_root / "assets").exists():
        cmd.extend(['--add-data', f'{project_root / "assets"};assets'])
        log("Including assets directory")
    
    if (project_root / "data").exists():
        cmd.extend(['--add-data', f'{project_root / "data"};data'])
        log("Including data directory")
    
    # Hidden imports for game dependencies
    hidden_imports = [
        'pygame',
        'pygame._freetype',
        'pygame.mixer',
        'pygame.font',
        'pygame.image',
        'pygame.transform',
        'pyperclip',
        'sqlite3',
        'json',
        'datetime',
        'pathlib',
        'threading',
        'multiprocessing',
        'colorsys',
        'math',
        'random',
        'time',
        'os',
        'sys',
    ]
    
    for package in hidden_imports:
        cmd.extend(['--hidden-import', package])
    
    # Add main script
    cmd.append('main.py')
    
    # Execute PyInstaller
    log("Executing PyInstaller (this may take 5-10 minutes)...")
    if verbose:
        log(f"Command: {' '.join(cmd)}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, cwd=project_root,
                              capture_output=not verbose, text=True)
        
        if result.returncode == 0:
            build_time = time.time() - start_time
            log("PyInstaller build completed successfully")
            
            # Find executable
            executable_path = dist_dir / "WOW_Bingo_Game" / "WOW_Bingo_Game.exe"
            
            if executable_path.exists():
                size_mb = executable_path.stat().st_size / (1024 * 1024)
                log("=" * 60)
                log("BUILD COMPLETED SUCCESSFULLY!")
                log("=" * 60)
                log(f"Executable: {executable_path}")
                log(f"Size: {size_mb:.1f} MB")
                log(f"Build time: {build_time:.1f} seconds")
                log("=" * 60)
                log("✅ NO VISUAL STUDIO BUILD TOOLS REQUIRED!")
                log("✅ This executable works on any Windows PC")
                log("=" * 60)
                return True
            else:
                error("Executable not found after build")
        else:
            error(f"PyInstaller build failed with return code {result.returncode}")
            if result.stderr and not verbose:
                log(f"Error output: {result.stderr}")
            return False
    
    except Exception as e:
        error(f"Error during PyInstaller build: {e}")
        return False

def try_cx_freeze():
    """Try cx_Freeze as another alternative."""
    log("Trying cx_Freeze as alternative...")
    
    try:
        # Install cx_Freeze
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'cx_Freeze'],
                     check=True, capture_output=True)
        
        # Create simple setup script
        setup_content = '''
import sys
from cx_Freeze import setup, Executable

# Dependencies are automatically detected, but it might need help with some packages
build_options = {
    'packages': ['pygame', 'pyperclip', 'sqlite3', 'json', 'datetime'],
    'excludes': [],
    'include_files': [('assets', 'assets'), ('data', 'data')]
}

base = 'Win32GUI' if sys.platform == 'win32' else None

executables = [
    Executable('main.py', base=base, target_name='WOW_Bingo_Game.exe')
]

setup(
    name='WOW Bingo Game',
    version='1.0',
    description='WOW Bingo Game',
    options={'build_exe': build_options},
    executables=executables
)
'''
        
        # Write setup script
        with open('setup_cx.py', 'w') as f:
            f.write(setup_content)
        
        # Run cx_Freeze
        result = subprocess.run([sys.executable, 'setup_cx.py', 'build'],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            log("cx_Freeze build completed successfully")
            return True
        else:
            log("cx_Freeze build failed")
            return False
            
    except Exception as e:
        log(f"cx_Freeze attempt failed: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Build WOW Bingo Game without Visual Studio")
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.absolute()
    
    log("=" * 60)
    log("WOW Bingo Game - Build Without Visual Studio")
    log("=" * 60)
    log("This script builds your game without requiring Visual Studio Build Tools")
    log("=" * 60)
    
    # Check prerequisites
    if not (project_root / "main.py").exists():
        error("main.py not found!")
    
    if not (project_root / "assets").exists():
        error("assets directory not found!")
    
    # Try PyInstaller first (most reliable without VS)
    log("ATTEMPT 1: PyInstaller (Recommended - No Visual Studio needed)")
    log("-" * 60)
    
    if check_pyinstaller():
        if build_with_pyinstaller(args.verbose):
            return True
    
    # Try cx_Freeze as backup
    log("ATTEMPT 2: cx_Freeze (Alternative - No Visual Studio needed)")
    log("-" * 60)
    
    if try_cx_freeze():
        return True
    
    # All attempts failed
    log("=" * 60)
    log("ALL NO-VISUAL-STUDIO ATTEMPTS FAILED")
    log("=" * 60)
    log("Unfortunately, both PyInstaller and cx_Freeze failed.")
    log("You may need to:")
    log("1. Install missing Python packages: pip install -r requirements.txt")
    log("2. Try on a different Windows PC")
    log("3. Use an online build service")
    log("4. Install minimal build tools (smaller than full Visual Studio)")
    
    return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        log(f"Unexpected error: {e}", "ERROR")
        sys.exit(1)
