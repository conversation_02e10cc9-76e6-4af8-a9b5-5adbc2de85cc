"""
Bingo Favor Mode - Developer Cheat Mode

This module implements a developer cheat mode that guarantees a specific cartella will win the game.
It's activated by pressing Ctrl+Shift+[cartella number] and modifies the number calling algorithm
to prioritize numbers on the specified cartella.

This is strictly a developer feature for testing purposes and should not be accessible in production builds.
"""

import random
import logging
import time

# Set up logging
logger = logging.getLogger("bingo_favor_mode")
logger.setLevel(logging.DEBUG)

# Create console handler if not already added
if not logger.handlers:
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    logger.addHandler(ch)

class BingoFavorMode:
    """
    A developer cheat mode that guarantees a specific cartella will win the game.
    """

    def __init__(self, game):
        """
        Initialize the Enhanced Bingo Favor Mode.

        Args:
            game: Reference to the main game instance
        """
        self.game = game
        self.active = False
        self.target_cartella = None
        self.original_call_next_number = None

        # Enhanced visual indicators
        self.indicator_alpha = 0  # For visual indicator fade effect
        self.indicator_fade_direction = 1  # 1 for fade in, -1 for fade out
        self.indicator_pulse_time = 0  # For pulsing animation
        self.indicator_color = (0, 255, 0)  # Green for active
        self.indicator_radius = 24
        self.indicator_position = "bottom_left"  # Position of indicator

        # Enhanced activation states
        self._pending_activation = False  # Flag to indicate pending activation
        self._show_activation_animation = False
        self._activation_animation_start_time = 0
        self._show_deactivation_animation = False
        self._deactivation_animation_start_time = 0
        self._show_switch_animation = False
        self._switch_animation_start_time = 0

        # Real-time switching support
        self._previous_target = None
        self._switch_feedback_timer = 0

        # Enhanced hotkey support
        self._hotkey_feedback_timer = 0
        self._last_hotkey_action = None

        # Persistent indicator when active
        self._persistent_indicator = True
        self._indicator_blink_timer = 0
        self._indicator_visible = True

        # We'll save the original method when activating, not in init
        # This ensures we always get the current method, not the one at initialization

        # Hook into the game's start_game method to handle pending activations
        if hasattr(game, 'start_game'):
            # Save the original start_game method
            self.original_start_game = game.start_game
            # Override with our version that checks for pending activations
            game.start_game = self._start_game_with_favor_check
            logger.info("Hooked into game's start_game method")

        logger.info("Bingo Favor Mode initialized (DEVELOPER FEATURE)")

    def _start_game_with_favor_check(self):
        """
        Wrapper for the game's start_game method that checks for pending favor mode activations.
        """
        # Call the original start_game method
        result = self.original_start_game()

        # Check if we have a pending activation
        if hasattr(self, '_pending_activation') and self._pending_activation and self.active:
            logger.info(f"Applying pending favor mode activation for cartella {self.target_cartella}")
            # Now that the game has started, we can override the call_next_number method
            if hasattr(self.game, 'bingo_caller') and self.game.bingo_caller:
                # Save the current method
                self.original_call_next_number = self.game.bingo_caller.call_next_number
                # Override with our favored method
                self.game.bingo_caller.call_next_number = self._favored_call_next_number
                logger.info(f"DEVELOPER MODE: Bingo Favor Mode activated for cartella {self.target_cartella} (after game start)")
            self._pending_activation = False

        return result

    def activate(self, cartella_number, is_switch=False):
        """
        Activate favor mode for a specific cartella with enhanced real-time switching support.

        Args:
            cartella_number: The cartella number to favor
            is_switch: Whether this is a real-time switch to a different cartella

        Returns:
            bool: True if activation was successful, False otherwise
        """
        # Check if the cartella exists
        if not self._cartella_exists(cartella_number):
            logger.warning(f"Cannot activate Favor Mode: Cartella {cartella_number} does not exist")
            return False

        logger.info(f"DEVELOPER MODE: Attempting to {'switch' if is_switch else 'activate'} Bingo Favor Mode for cartella {cartella_number}")

        # Handle real-time switching
        if is_switch and self.active:
            self._previous_target = self.target_cartella
            self._show_switch_animation = True
            self._switch_animation_start_time = time.time()
            self._switch_feedback_timer = time.time()
            logger.info(f"DEVELOPER MODE: Switching favor from cartella {self._previous_target} to cartella {cartella_number}")
        else:
            # Regular activation
            self._show_activation_animation = True
            self._activation_animation_start_time = time.time()

        self.target_cartella = cartella_number
        self.active = True

        # Reset indicator states for new activation/switch
        self.indicator_alpha = 255
        self.indicator_pulse_time = time.time()
        self._indicator_visible = True
        self._indicator_blink_timer = time.time()

        # If the game hasn't started yet, we'll just store the target cartella
        # and wait for the game to start before overriding the call_next_number method
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            logger.info(f"DEVELOPER MODE: Bingo Favor Mode {'switched to' if is_switch else 'activated for'} cartella {cartella_number} (waiting for game to start)")
            # Store the target cartella for later use
            self._pending_activation = True
            return True

        # Save the original call_next_number method and override it (if not already done)
        if hasattr(self.game, 'bingo_caller') and self.game.bingo_caller:
            # Only save the original method if we haven't already
            if not self.original_call_next_number:
                self.original_call_next_number = self.game.bingo_caller.call_next_number
            # Override with our favored method
            self.game.bingo_caller.call_next_number = self._favored_call_next_number
            logger.info(f"DEVELOPER MODE: Bingo Favor Mode {'switched to' if is_switch else 'activated for'} cartella {cartella_number}")
            return True
        else:
            logger.warning("Cannot activate Favor Mode: Bingo caller not initialized yet")
            # We'll still mark it as active, but it won't do anything until the bingo caller is initialized
            self._pending_activation = True
            return True

    def deactivate(self):
        """
        Enhanced deactivate method with proper animation and cleanup.
        """
        if not self.active:
            return

        logger.info("DEVELOPER MODE: Deactivating Bingo Favor Mode")

        # Start deactivation animation
        self._show_deactivation_animation = True
        self._deactivation_animation_start_time = time.time()

        self.active = False
        self._previous_target = self.target_cartella  # Store for animation
        self.target_cartella = None
        self._pending_activation = False

        # Reset all animation states
        self._show_activation_animation = False
        self._show_switch_animation = False
        self._switch_feedback_timer = 0
        self._hotkey_feedback_timer = 0
        self._last_hotkey_action = "deactivated"

        # Restore the original call_next_number method
        if (hasattr(self.game, 'bingo_caller') and self.game.bingo_caller and
                self.original_call_next_number is not None):
            self.game.bingo_caller.call_next_number = self.original_call_next_number
            self.original_call_next_number = None

        logger.info("DEVELOPER MODE: Bingo Favor Mode deactivated")

    def reset(self):
        """
        Enhanced reset method with proper cleanup of all favor mode states.
        This ensures Favor Mode is always inactive at the start of a new game sequence,
        requiring manual activation per session.
        """
        logger.info("DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.")

        # Deactivate favor mode
        self.deactivate()

        # Clear all animation states
        self._show_activation_animation = False
        self._show_deactivation_animation = False
        self._show_switch_animation = False
        self._activation_animation_start_time = 0
        self._deactivation_animation_start_time = 0
        self._switch_animation_start_time = 0

        # Reset all timers and feedback states
        self._switch_feedback_timer = 0
        self._hotkey_feedback_timer = 0
        self._last_hotkey_action = None
        self._previous_target = None

        # Reset indicator states
        self.indicator_alpha = 0
        self.indicator_pulse_time = 0
        self._indicator_blink_timer = 0
        self._indicator_visible = True

        # Clear any temporary card
        if hasattr(self, '_temp_card'):
            delattr(self, '_temp_card')

        logger.info("DEVELOPER MODE: Bingo Favor Mode reset complete.")

    def switch_target(self, new_cartella_number):
        """
        Switch favor mode to a different cartella in real-time.

        Args:
            new_cartella_number: The new cartella number to favor

        Returns:
            bool: True if switch was successful, False otherwise
        """
        if not self.active:
            # If not active, just activate normally
            return self.activate(new_cartella_number)

        if self.target_cartella == new_cartella_number:
            # Already favoring this cartella, no need to switch
            logger.info(f"DEVELOPER MODE: Already favoring cartella {new_cartella_number}")
            return True

        # Perform real-time switch
        return self.activate(new_cartella_number, is_switch=True)

    def toggle_favor(self, cartella_number):
        """
        Toggle favor mode for a specific cartella (activate if inactive, deactivate if active for same cartella).

        Args:
            cartella_number: The cartella number to toggle favor for

        Returns:
            bool: True if operation was successful, False otherwise
        """
        if self.active and self.target_cartella == cartella_number:
            # Deactivate if already active for this cartella
            self.deactivate()
            return True
        else:
            # Activate or switch to this cartella
            return self.activate(cartella_number, is_switch=self.active)

    def force_deactivate(self):
        """
        Force deactivation of favor mode (for hotkey use).
        """
        if self.active:
            self._hotkey_feedback_timer = time.time()
            self.deactivate()
            # Set the action after deactivate to prevent it from being reset
            self._last_hotkey_action = "force_deactivated"
            return True
        return False

    def get_status_info(self):
        """
        Get current status information for debugging/display.

        Returns:
            dict: Status information
        """
        return {
            'active': self.active,
            'target_cartella': self.target_cartella,
            'previous_target': self._previous_target,
            'pending_activation': self._pending_activation,
            'last_action': self._last_hotkey_action,
            'switch_timer': time.time() - self._switch_feedback_timer if self._switch_feedback_timer > 0 else 0,
            'hotkey_timer': time.time() - self._hotkey_feedback_timer if self._hotkey_feedback_timer > 0 else 0
        }

    def _cartella_exists(self, cartella_number):
        """
        Check if a cartella exists in the game.

        Args:
            cartella_number: The cartella number to check

        Returns:
            bool: True if the cartella exists, False otherwise
        """
        # Before the game starts, allow any cartella number between 1 and 75
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            # Allow any positive cartella number before the game starts
            return cartella_number >= 1

        # Once the game has started, check if the cartella actually exists
        if hasattr(self.game, 'bingo_logic') and self.game.bingo_logic and hasattr(self.game.bingo_logic, 'player_cards'):
            player_cards_dict = self.game.bingo_logic.player_cards
            if cartella_number in player_cards_dict:
                return True
            if str(cartella_number) in player_cards_dict:
                return True
            logger.debug(f"Cartella {cartella_number} (and as str) not found in player_cards keys: {list(player_cards_dict.keys())}")
            return False # Explicitly false if not found by either type

        # If we can't verify (e.g., bingo_logic or player_cards not fully initialized), assume it exists for dev mode
        logger.debug(f"Cannot fully verify cartella {cartella_number} existence pre-game or due to missing attributes, assuming True for dev mode.")
        return True

    def _get_target_card(self):
        """
        Get the target cartella's card.

        Returns:
            BingoCard: The target cartella's card or None if not found
        """
        if not self.active or self.target_cartella is None:
            return None

        # If the game hasn't started yet, we can't get the card
        # But we'll create a temporary card for the target cartella
        if not hasattr(self.game, 'game_started') or not self.game.game_started:
            # Create a temporary card for the target cartella
            # This will be used until the game starts and real cards are available
            if not hasattr(self, '_temp_card'):
                # Import BingoCard if available, otherwise create a simple placeholder
                try:
                    from bingo_card import BingoCard
                    # Create a deterministic card based on the cartella number
                    self._temp_card = BingoCard(self.target_cartella)
                    logger.info(f"Created temporary card for cartella {self.target_cartella}")
                except ImportError:
                    # Create a simple placeholder with basic functionality
                    class SimpleCard:
                        def __init__(self, cartella_number):
                            self.cartella_number = cartella_number
                            self.marked = set()
                            # Create a simple 5x5 grid with numbers in the correct ranges
                            self.grid = []
                            for col in range(5):
                                column = []
                                start = col * 15 + 1
                                for i in range(5):
                                    # Use a deterministic pattern based on cartella number
                                    num = start + ((i + cartella_number) % 15)
                                    column.append(num)
                                self.grid.append(column)
                            # Set the center (free) space to 0
                            self.grid[2][2] = 0

                        def get_number_at(self, col, row):
                            return self.grid[col][row]

                    self._temp_card = SimpleCard(self.target_cartella)
                    logger.info(f"Created simple placeholder card for cartella {self.target_cartella}")

            return self._temp_card

        # Once the game has started, use the real card
        if hasattr(self.game, 'bingo_logic'):
            return self.game.bingo_logic.get_card_for_player(self.target_cartella)

        return None

    def _get_numbers_on_target_card(self):
        """
        Get all numbers on the target cartella's card.

        Returns:
            set: Set of numbers on the target card
        """
        card = self._get_target_card()
        if not card:
            return set()

        numbers = set()
        for col in range(5):
            for row in range(5):
                num = card.get_number_at(col, row)
                if num != 0:  # Skip the free space
                    numbers.add(num)
        return numbers

    def _get_unmarked_numbers_on_target_card(self):
        """
        Get all unmarked numbers on the target cartella's card.

        Returns:
            set: Set of unmarked numbers on the target card
        """
        card = self._get_target_card()
        if not card:
            return set()

        numbers = set()
        for col in range(5):
            for row in range(5):
                num = card.get_number_at(col, row)
                if num != 0 and num not in card.marked:  # Skip free space and marked numbers
                    numbers.add(num)
        return numbers

    def _would_cause_other_win(self, number):
        """
        Check if calling a number would cause another cartella to win.

        Args:
            number: The number to check

        Returns:
            bool: True if the number would cause another cartella to win, False otherwise
        """
        if not hasattr(self.game, 'bingo_logic'):
            return False

        # Get all player cards except the target
        for cartela_no, card in self.game.bingo_logic.player_cards.items():
            if cartela_no != self.target_cartella:
                # Create a copy of the card's marked numbers
                marked_copy = card.marked.copy()

                # Check if the number is on this card
                for col in range(5):
                    for row in range(5):
                        if card.grid[col][row] == number:
                            # Temporarily mark the number
                            marked_copy.add(number)

                            # Check if this would create a winning pattern
                            # We need to simulate the pattern check
                            if self._check_winning_patterns(card, marked_copy):
                                return True

        return False

    def _check_winning_patterns(self, card, marked_numbers):
        """
        Check if a card has any winning patterns with the given marked numbers.

        Args:
            card: The BingoCard to check
            marked_numbers: Set of marked numbers

        Returns:
            bool: True if the card has a winning pattern, False otherwise
        """
        # Check rows
        for row in range(5):
            if all(card.grid[col][row] in marked_numbers for col in range(5)):
                return True

        # Check columns
        for col in range(5):
            if all(card.grid[col][row] in marked_numbers for row in range(5)):
                return True

        # Check diagonals
        if all(card.grid[i][i] in marked_numbers for i in range(5)):
            return True

        if all(card.grid[i][4-i] in marked_numbers for i in range(5)):
            return True

        return False

    def _favored_call_next_number(self):
        """
        Enhanced: Call the next number, favoring the target cartella, but sometimes skip (jump) favored numbers, interleave with numbers from other players, and ensure the favored cartella still wins. The win should appear more natural and less predictable.
        """
        if hasattr(self.game.bingo_caller, 'paused') and self.game.bingo_caller.paused:
            return None
        if not self.active or self.target_cartella is None:
            if self.original_call_next_number:
                return self.original_call_next_number()
            return None
        all_numbers = set(range(1, self.game.bingo_caller.total_numbers + 1))
        called_numbers = set(self.game.bingo_caller.called_numbers)
        uncalled_numbers = all_numbers - called_numbers
        if not uncalled_numbers:
            return None
        target_unmarked = self._get_unmarked_numbers_on_target_card()
        available_target_numbers = target_unmarked.intersection(uncalled_numbers)
        # Find numbers that are on both the favored cartella and at least one other player's card
        shared_numbers = set()
        other_unmarked = set()
        if hasattr(self.game, 'bingo_logic') and hasattr(self.game.bingo_logic, 'player_cards'):
            for cartella_no, card in self.game.bingo_logic.player_cards.items():
                if cartella_no != self.target_cartella:
                    for col in range(5):
                        for row in range(5):
                            num = card.get_number_at(col, row)
                            if num in available_target_numbers:
                                shared_numbers.add(num)
                            if num in uncalled_numbers:
                                other_unmarked.add(num)
        number = None # Initialize chosen number to be called

        # Determine if a "naturalizing" jump call should be attempted
        jump_favored = False
        # Conditions for attempting a jump:
        # 1. Favored card is not too close to winning (needs > 2 numbers).
        # 2. There are enough uncalled numbers overall to allow for "detour" calls.
        favored_needs_more_than_two = len(available_target_numbers) > 2
        # Ensure there's a buffer of numbers beyond what the favored card might need plus a few extra (e.g., 3).
        enough_uncalled_overall = len(uncalled_numbers) > (len(available_target_numbers) + 3)

        if favored_needs_more_than_two and enough_uncalled_overall:
            jump_probability = 0.35  # Chance to make a "naturalizing" call
            if random.random() < jump_probability:
                jump_favored = True
                logger.debug(f"FAVOR MODE: Conditions met for attempting a naturalizing jump (Prob: {jump_probability*100}%, Needs: {len(available_target_numbers)}, Uncalled: {len(uncalled_numbers)})")
            else:
                logger.debug(f"FAVOR MODE: Conditions for jump met, but random chance ({jump_probability*100}%) failed. Proceeding to precise logic.")
        else:
            logger.debug(f"FAVOR MODE: Conditions for jump NOT met (Needs > 2: {favored_needs_more_than_two}, Uncalled Buffer: {enough_uncalled_overall}). Proceeding to precise logic.")
        # Always ensure the favored cartella can still win before others
        def would_favored_win_first(next_number):
            # Simulate marking this number and check if favored would win before others
            # Ensure game.bingo_logic and player_cards are available
            if not (hasattr(self.game, 'bingo_logic') and self.game.bingo_logic and hasattr(self.game.bingo_logic, 'player_cards')):
                logger.warning("would_favored_win_first: bingo_logic or player_cards not available.")
                return False # Cannot determine outcome

            card = self._get_target_card()
            if not card:
                return False
            marked = set(card.marked)
            marked.add(next_number)
            # Check if this would create a winning pattern
            if self._check_winning_patterns(card, marked):
                # Check if any other player would win with this number
                for cartella_no, other_card in self.game.bingo_logic.player_cards.items():
                    if cartella_no != self.target_cartella:
                        other_marked = set(other_card.marked)
                        if any(other_card.get_number_at(col, row) == next_number for col in range(5) for row in range(5)):
                            other_marked.add(next_number)
                        if self._check_winning_patterns(other_card, other_marked):
                            return False
                return True
            return False
        # --- Attempt a "Naturalizing Jump" if conditions met and jump_favored is True ---
        if jump_favored:
            logger.debug("FAVOR MODE: Attempting naturalizing jump.")
            # Option 1: Call a number only on other players' cards (not on favored)
            other_exclusive_safe_candidates = [
                n for n in (other_unmarked - available_target_numbers)
                if not self._would_cause_other_win(n)
            ]
            if other_exclusive_safe_candidates:
                number = random.choice(other_exclusive_safe_candidates)
                logger.info(f"FAVOR MODE (Naturalizing Jump - Other Exclusive): Chose {number}")

            # Option 2 (if no exclusive other found): Call a shared number that isn't immediately winning for favored
            if not number:
                # Get current marked numbers for the target card to check if a shared number would be a win
                target_card_for_jump_check = self._get_target_card()
                current_target_marked = set()
                if target_card_for_jump_check:
                    current_target_marked = target_card_for_jump_check.marked

                shared_non_critical_safe_candidates = [
                    n for n in shared_numbers
                    if not self._would_cause_other_win(n) and \
                       not (target_card_for_jump_check and self._check_winning_patterns(target_card_for_jump_check, current_target_marked | {n}))
                ]
                if shared_non_critical_safe_candidates:
                    number = random.choice(shared_non_critical_safe_candidates)
                    logger.info(f"FAVOR MODE (Naturalizing Jump - Shared Non-Critical): Chose {number}")

            if number:
                 logger.debug(f"FAVOR MODE: Naturalizing jump call selected: {number}")
            else:
                 logger.debug("FAVOR MODE: Naturalizing jump was favored, but no safe candidate found. Proceeding to precise logic.")

        # --- Main selection logic (executed if no jump taken or jump failed) ---
        if number is None: # Proceed if jump was not made or no suitable jump number found
            logger.debug("FAVOR MODE: Executing precise selection logic.")
            # Priority 1: Safest numbers on the target card (not shared)
            if available_target_numbers:
                safe_exclusive_target_numbers = [
                n for n in available_target_numbers
                if n not in shared_numbers and \
                   not self._would_cause_other_win(n) and \
                   would_favored_win_first(n)
            ]
            if safe_exclusive_target_numbers:
                number = random.choice(safe_exclusive_target_numbers)
                logger.debug(f"FAVOR MODE (Precise P1): Chose exclusive target number: {number}")

            # Priority 2: Safest shared numbers
            if not number and shared_numbers:
                safe_shared_target_numbers = [
                    n for n in shared_numbers
                    if not self._would_cause_other_win(n) and \
                       would_favored_win_first(n)
                ]
                if safe_shared_target_numbers:
                    number = random.choice(safe_shared_target_numbers)
                    logger.debug(f"FAVOR MODE (Precise P2): Chose shared target number: {number}")

            # Priority 3: Any number on target card that ensures favored wins first
            if not number and available_target_numbers:
                target_can_win_first_numbers = [
                    n for n in available_target_numbers
                    if would_favored_win_first(n) # Main condition: favored wins first
                ]
                if target_can_win_first_numbers:
                    number = random.choice(target_can_win_first_numbers)
                    logger.debug(f"FAVOR MODE (Precise P3): Chose any target number that wins first: {number}")

            # Fallback if no ideal number is found through prioritized precise logic
            if not number:
                logger.debug(f"FAVOR MODE (Precise Fallback): No ideal number found from P1-P3, using fallbacks.")
                # Fallback 1: Any uncalled number that doesn't cause another immediate win, but still lets favored win first
                safe_uncalled_fallback = [n for n in uncalled_numbers if not self._would_cause_other_win(n) and would_favored_win_first(n)]
                if safe_uncalled_fallback:
                    number = random.choice(safe_uncalled_fallback)
                    logger.debug(f"FAVOR MODE (Precise F1): Chose from safe_uncalled_fallback: {number}")
                else:
                    # Fallback 2: If target still needs numbers, pick one from its available numbers (less strict, as long as it doesn't make others win)
                    target_still_needs_safe = [n for n in available_target_numbers if not self._would_cause_other_win(n)]
                    if target_still_needs_safe:
                        number = random.choice(target_still_needs_safe)
                        logger.debug(f"FAVOR MODE (Precise F2): Chose from target_still_needs_safe: {number}")
                    # Fallback 3: Last resort - any uncalled number that doesn't make others win
                    elif uncalled_numbers:
                        final_resort_candidates = [n for n in uncalled_numbers if not self._would_cause_other_win(n)]
                        if final_resort_candidates:
                            number = random.choice(final_resort_candidates)
                            logger.debug(f"FAVOR MODE (Precise F3a): Chose from final_resort_candidates (non-other-win): {number}")
                        else: # Absolute last resort if all remaining numbers cause others to win (should be rare)
                            number = random.choice(list(uncalled_numbers))
                            logger.warning(f"FAVOR MODE (Precise F3b): Absolute last resort, chose from any uncalled: {number}. This might lead to non-favored win.")
                    else:
                        # This case should ideally not be reached if the initial check for uncalled_numbers is done.
                        logger.error("FAVOR MODE (Precise): No uncalled numbers left and no number chosen. Game might be stuck.")
                        if self.original_call_next_number:
                            return self.original_call_next_number()
                        return None # No number can be called

        # Ensure a number was chosen if uncalled_numbers is not empty and 'number' is still None
        if number is None and uncalled_numbers:
            logger.warning("FAVOR MODE (Ultimate Fallback): Number selection failed through all logic, picking random uncalled.")
            number = random.choice(list(uncalled_numbers))

        # If number is still None here, it means uncalled_numbers was empty at the start or something went very wrong.
        if number is None:
            logger.error("FAVOR MODE: CRITICAL - No number selected and uncalled_numbers might be empty. Returning to original caller.")
            if self.original_call_next_number:
                return self.original_call_next_number()
            return None


        # --- Finalize and call the chosen number ---
        self.game.bingo_caller.current_number = number
        self.game.bingo_caller.called_numbers.append(number)
        self.game.bingo_caller.last_call_time = time.time()
        letter = self.game.bingo_caller.get_letter_for_number(number)
        logger.info(f"FAVOR MODE: Calling {letter}{number} (favoring cartella {self.target_cartella})")
        self.game.bingo_caller.play_announcement(number)
        if self.game.bingo_caller.callback:
            try:
                self.game.bingo_caller.callback(number)
            except Exception as e:
                logger.error(f"Error in bingo caller callback: {e}")
        return number

    def draw_indicator(self, screen):
        """
        Enhanced indicator drawing with multiple animation types and persistent display.
        """
        import pygame
        current_time = time.time()

        # Draw activation animation (expanding green circle)
        if self._show_activation_animation:
            elapsed = current_time - self._activation_animation_start_time
            if elapsed < 1.5:  # 1.5 second animation
                progress = elapsed / 1.5
                radius = int(self.indicator_radius * (0.5 + 0.5 * progress))
                alpha = int(255 * (1.0 - progress * 0.3))  # Fade slightly

                center_x = 30 + radius
                center_y = screen.get_height() - 30 - radius

                # Draw expanding circle with gradient
                for i in range(radius, 0, -2):
                    color = (0, 255, 0, int(alpha * (i / radius)))
                    s = pygame.Surface((radius*2, radius*2), pygame.SRCALPHA)
                    pygame.draw.circle(s, color, (radius, radius), i)
                    screen.blit(s, (center_x - radius, center_y - radius))
            else:
                self._show_activation_animation = False

        # Draw switch animation (color change from red to green)
        if self._show_switch_animation:
            elapsed = current_time - self._switch_animation_start_time
            if elapsed < 1.0:  # 1 second animation
                progress = elapsed / 1.0
                # Interpolate from red to green
                red_component = int(255 * (1.0 - progress))
                green_component = int(255 * progress)
                color = (red_component, green_component, 0)

                center_x = 30 + self.indicator_radius
                center_y = screen.get_height() - 30 - self.indicator_radius

                # Draw switching circle
                for i in range(self.indicator_radius, 0, -2):
                    circle_color = (*color, int(255 * (i / self.indicator_radius)))
                    s = pygame.Surface((self.indicator_radius*2, self.indicator_radius*2), pygame.SRCALPHA)
                    pygame.draw.circle(s, circle_color, (self.indicator_radius, self.indicator_radius), i)
                    screen.blit(s, (center_x - self.indicator_radius, center_y - self.indicator_radius))
            else:
                self._show_switch_animation = False

        # Draw deactivation animation (shrinking red circle)
        if self._show_deactivation_animation:
            elapsed = current_time - self._deactivation_animation_start_time
            if elapsed < 1.0:  # 1 second animation
                progress = elapsed / 1.0
                radius = int(self.indicator_radius * (1.0 - progress))
                alpha = int(255 * (1.0 - progress))

                center_x = 30 + self.indicator_radius
                center_y = screen.get_height() - 30 - self.indicator_radius

                # Draw shrinking red circle
                for i in range(radius, 0, -2):
                    color = (255, 0, 0, int(alpha * (i / radius)))
                    s = pygame.Surface((self.indicator_radius*2, self.indicator_radius*2), pygame.SRCALPHA)
                    pygame.draw.circle(s, color, (self.indicator_radius, self.indicator_radius), i)
                    screen.blit(s, (center_x - self.indicator_radius, center_y - self.indicator_radius))
            else:
                self._show_deactivation_animation = False

        # Persistent indicator removed - only show temporary animations
