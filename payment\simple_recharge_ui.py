"""
Simple Recharge UI component for the payment system.

This module provides a simplified UI for recharging credits through vouchers.
"""

import pygame
import os
import pyperclip  # For clipboard operations

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (50, 50, 50)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
GOLD = (255, 215, 0)

class SimpleRechargeUI:
    """Simple UI component for recharging credits."""

    def __init__(self, screen, voucher_manager):
        """
        Initialize the recharge UI.

        Args:
            screen: Pygame screen surface
            voucher_manager: VoucherManager instance
        """
        self.screen = screen
        self.voucher_manager = voucher_manager
        self.visible = False
        self.voucher_input = ""
        self.input_active = False
        self.message = ""
        self.message_type = "info"  # info, success, error
        self.explanation = None
        self.message_timer = 0

        # Load sound effects
        self.load_sound_effects()

        # Initialize buttons and input areas
        self.buttons = {}
        self.input_field = None

    def load_sound_effects(self):
        """Load sound effects for UI interactions."""
        try:
            pygame.mixer.init()
            self.button_click_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'button_click.wav'))
            self.success_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'success.wav'))
            self.error_sound = pygame.mixer.Sound(os.path.join('assets', 'sounds', 'error.wav'))
        except:
            self.button_click_sound = None
            self.success_sound = None
            self.error_sound = None

    def show(self):
        """Show the recharge UI."""
        self.visible = True
        self.voucher_input = ""
        self.input_active = True
        self.message = ""
        self.explanation = None
        self.message_timer = 0
        print("Recharge UI shown, input field activated")

    def hide(self):
        """Hide the recharge UI."""
        self.visible = False
        self.input_active = False

    def handle_event(self, event):
        """
        Handle pygame events.

        Args:
            event: Pygame event

        Returns:
            bool: True if event was handled, False otherwise
        """
        if not self.visible:
            return False

        # Handle TEXTINPUT events for typing (this is crucial for input field functionality)
        if event.type == pygame.TEXTINPUT:
            if self.input_active:
                # Filter and add typed characters
                char = event.text.upper()
                if char.isalnum() or char == '-':
                    self.voucher_input += char
                    print(f"Added character: {char}, current input: {self.voucher_input}")
                    return True

        elif event.type == pygame.MOUSEBUTTONDOWN:
            # Check for button clicks
            pos = event.pos
            for button_id, button in self.buttons.items():
                if button.collidepoint(pos):
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    if button_id == "close":
                        self.hide()
                        return True
                    elif button_id == "redeem":
                        self.validate_voucher()
                        return True

            # Check for input field click
            if self.input_field and self.input_field.collidepoint(pos):
                self.input_active = True
                print("Input field activated via click")
                return True

            # Check if click is outside the dialog
            dialog_rect = self.get_dialog_rect()
            if not dialog_rect.collidepoint(pos):
                self.hide()
                return True

        elif event.type == pygame.KEYDOWN:
            if not self.input_active:
                if event.key == pygame.K_ESCAPE:
                    self.hide()
                    return True
                return False

            # Handle text input
            if event.key == pygame.K_RETURN:
                self.validate_voucher()
                return True
            elif event.key == pygame.K_BACKSPACE:
                self.voucher_input = self.voucher_input[:-1]
                return True
            elif event.key == pygame.K_ESCAPE:
                self.hide()
                return True

            # Handle clipboard operations
            # Check for Ctrl modifier from both event and current keyboard state
            ctrl_pressed = (pygame.key.get_mods() & pygame.KMOD_CTRL) or (hasattr(event, 'mod') and event.mod & pygame.KMOD_CTRL)

            # Ctrl+V to paste
            if event.key == pygame.K_v and ctrl_pressed:
                try:
                    import pyperclip
                    clipboard_text = pyperclip.paste().strip().upper()
                    # Filter out invalid characters
                    filtered_text = ''.join(c for c in clipboard_text if c.isalnum() or c == '-')
                    self.voucher_input = filtered_text
                    print(f"Pasted from clipboard: '{clipboard_text}' -> '{filtered_text}'")
                    return True
                except Exception as e:
                    print(f"Error pasting from clipboard: {e}")
                    return True

            # Ctrl+C to copy
            elif event.key == pygame.K_c and ctrl_pressed:
                try:
                    import pyperclip
                    pyperclip.copy(self.voucher_input)
                    self.show_message("Copied to clipboard!", "info")
                    print(f"Copied to clipboard: {self.voucher_input}")
                    return True
                except Exception as e:
                    print(f"Error copying to clipboard: {e}")
                    return True

            # Ctrl+A to select all (clear and prepare for new input)
            elif event.key == pygame.K_a and ctrl_pressed:
                # For voucher input, "select all" means clear the field for new input
                self.voucher_input = ""
                print("Selected all (cleared input field)")
                return True

            # Handle direct unicode input (fallback for older pygame versions)
            elif hasattr(event, 'unicode') and event.unicode:
                char = event.unicode.upper()
                if char.isalnum() or char == '-':
                    self.voucher_input += char
                    print(f"Added character via unicode: {char}")
                    return True

        return False

    def handle_click(self, pos):
        """Handle mouse click events."""
        if not self.visible:
            return False

        # Check for button clicks
        for button_id, button in self.buttons.items():
            if button.collidepoint(pos):
                # Play click sound
                if self.button_click_sound:
                    self.button_click_sound.play()

                if button_id == "close":
                    self.hide()
                    return True
                elif button_id == "redeem":
                    self.validate_voucher()
                    return True

        # Check for input field click
        if self.input_field and self.input_field.collidepoint(pos):
            self.input_active = True
            print("Input field activated via handle_click")
            return True

        # Check if click is outside the dialog
        dialog_rect = self.get_dialog_rect()
        if not dialog_rect.collidepoint(pos):
            self.hide()
            return True

        return False

    def validate_voucher(self):
        """Validate the entered voucher code."""
        # Trim whitespace from input
        cleaned_input = self.voucher_input.strip()

        if not cleaned_input:
            self.show_message("Please enter a voucher code.", "error")
            if self.error_sound:
                self.error_sound.play()
            return

        # Check for minimum length before sending to validation
        # Allow both compact vouchers (10-15 chars) and legacy vouchers (20+ chars)
        cleaned_length = len(cleaned_input.replace('-', ''))
        if cleaned_length < 10 or (15 < cleaned_length < 20):
            self.show_message("Invalid voucher format. Vouchers must be either 10-15 characters (compact) or 20+ characters (legacy).", "error")
            if self.error_sound:
                self.error_sound.play()
            return

        # Check for valid characters
        from .crypto_utils import CROCKFORD_ALPHABET
        for char in cleaned_input.upper().replace('-', ''):
            if char not in CROCKFORD_ALPHABET:
                self.show_message(f"Invalid character '{char}' in voucher code. Only letters and numbers are allowed.", "error")
                if self.error_sound:
                    self.error_sound.play()
                return

        # Validate voucher
        result = self.voucher_manager.validate_voucher(cleaned_input)

        if result["success"]:
            # Play success sound
            if self.success_sound:
                self.success_sound.play()

            # Create a more detailed success message
            detailed_message = (
                f"Added {result['amount']} credits! Share: {result['share']}% | "
                f"Balance: {result['new_balance']} credits"
            )

            # Show enhanced explanation of share percentage with formula and example
            explanation = (
                f"The share percentage ({result['share']}%) determines how much of the referee's "
                f"commission will be deducted as credit usage. Formula: Credits = Referee Commission × {result['share']}%. "
                f"Example: With 1000 ETB total bets and 20% commission, {round(1000 * 0.2 * result['share'] / 100)} credits would be used."
            )

            # Show detailed success message with explanation
            self.show_message(detailed_message, "success", explanation=explanation)

            # Clear input
            self.voucher_input = ""
        else:
            # Play error sound
            if self.error_sound:
                self.error_sound.play()

            # Show error message
            self.show_message(result["message"], "error")

    def show_message(self, message, message_type="info", explanation=None):
        """
        Show a message in the UI.

        Args:
            message: Message text
            message_type: Message type (info, success, error)
            explanation: Optional explanation text to show below the message
        """
        self.message = message
        self.message_type = message_type
        self.explanation = explanation
        self.message_timer = 300  # Show for 5 seconds at 60 FPS

    def get_dialog_rect(self):
        """Get the rectangle for the dialog."""
        screen_width, screen_height = self.screen.get_size()
        dialog_width = 500
        dialog_height = 300
        dialog_x = (screen_width - dialog_width) // 2
        dialog_y = (screen_height - dialog_height) // 2
        return pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)

    def update(self):
        """Update UI state."""
        if not self.visible:
            return

        # Update message timer
        if self.message_timer > 0:
            self.message_timer -= 1

    def draw(self):
        """Draw the recharge UI."""
        if not self.visible:
            return

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))  # Semi-transparent black
        self.screen.blit(overlay, (0, 0))

        # Draw dialog
        dialog_rect = self.get_dialog_rect()

        # Draw dialog background
        pygame.draw.rect(
            self.screen,
            (40, 40, 60),  # Dark blue-gray
            dialog_rect,
            border_radius=10
        )

        # Draw dialog border
        pygame.draw.rect(
            self.screen,
            (80, 80, 100),  # Light blue-gray
            dialog_rect,
            2,  # Border width
            border_radius=10
        )

        # Draw title
        title_font = pygame.font.SysFont("Arial", 24, bold=True)
        title_text = title_font.render("Recharge Credits", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=dialog_rect.centerx,
            y=dialog_rect.y + 20
        )
        self.screen.blit(title_text, title_rect)

        # Draw current balance
        balance_font = pygame.font.SysFont("Arial", 18)
        balance_text = balance_font.render(
            f"Current Balance: {self.voucher_manager.credits} credits",
            True, GOLD
        )
        balance_rect = balance_text.get_rect(
            centerx=dialog_rect.centerx,
            y=title_rect.bottom + 20
        )
        self.screen.blit(balance_text, balance_rect)

        # Draw input field label
        label_font = pygame.font.SysFont("Arial", 16)
        label_text = label_font.render("Enter Voucher Code:", True, WHITE)
        label_rect = label_text.get_rect(
            x=dialog_rect.x + 30,
            y=balance_rect.bottom + 30
        )
        self.screen.blit(label_text, label_rect)

        # Draw input field
        input_rect = pygame.Rect(
            dialog_rect.x + 30,
            label_rect.bottom + 10,
            dialog_rect.width - 60,
            40
        )
        pygame.draw.rect(self.screen, DARK_GRAY, input_rect, border_radius=5)
        pygame.draw.rect(self.screen, LIGHT_GRAY, input_rect, 2, border_radius=5)

        # Store input field
        self.input_field = input_rect

        # Draw input text
        input_font = pygame.font.SysFont("Arial", 18)
        input_text = self.voucher_input

        # Add cursor if active
        if self.input_active and pygame.time.get_ticks() % 1000 < 500:
            input_text += "|"

        # Render input text
        if input_text:
            text_surface = input_font.render(input_text, True, WHITE)
            text_rect = text_surface.get_rect(
                x=input_rect.x + 10,
                centery=input_rect.centery
            )
            self.screen.blit(text_surface, text_rect)

        # Draw redeem button
        redeem_button = pygame.Rect(
            dialog_rect.centerx - 75,
            input_rect.bottom + 20,
            150,
            40
        )
        pygame.draw.rect(self.screen, (0, 120, 60), redeem_button, border_radius=5)

        # Store redeem button
        self.buttons["redeem"] = redeem_button

        # Draw button text
        button_font = pygame.font.SysFont("Arial", 16, bold=True)
        button_text = button_font.render("Redeem Voucher", True, WHITE)
        button_text_rect = button_text.get_rect(center=redeem_button.center)
        self.screen.blit(button_text, button_text_rect)

        # Draw close button
        close_button = pygame.Rect(
            dialog_rect.right - 120,
            dialog_rect.y + 20,
            100,
            30
        )
        pygame.draw.rect(self.screen, (180, 60, 60), close_button, border_radius=5)

        # Store close button
        self.buttons["close"] = close_button

        # Draw button text
        close_text = button_font.render("Close", True, WHITE)
        close_text_rect = close_text.get_rect(center=close_button.center)
        self.screen.blit(close_text, close_text_rect)

        # Draw message if active
        if self.message and self.message_timer > 0:
            self.draw_message(dialog_rect)

    def draw_message(self, dialog_rect):
        """Draw an enhanced message box with visual effects."""
        # Calculate message box dimensions
        message_width = dialog_rect.width - 60

        # Increase height if we have an explanation
        if hasattr(self, 'explanation') and self.explanation:
            message_height = 140  # Increased height for messages with explanation
        else:
            message_height = 80  # Standard height for regular messages

        message_x = dialog_rect.x + 30
        message_y = dialog_rect.bottom - message_height - 30

        message_rect = pygame.Rect(message_x, message_y, message_width, message_height)

        # Determine colors based on message type
        if self.message_type == "error":
            bg_color1 = (150, 50, 50)
            bg_color2 = (100, 30, 30)
            border_color = (200, 100, 100)
            glow_color = (255, 100, 100, 80)
        elif self.message_type == "success":
            bg_color1 = (50, 150, 50)
            bg_color2 = (30, 100, 30)
            border_color = (100, 200, 100)
            glow_color = (100, 255, 100, 80)
        else:  # info
            bg_color1 = (50, 50, 150)
            bg_color2 = (30, 30, 100)
            border_color = (100, 100, 200)
            glow_color = (100, 100, 255, 80)

        # Draw message box with gradient background
        self.draw_gradient_background(message_rect, bg_color1, bg_color2, border_radius=10)

        # Add glow effect
        for i in range(3):
            alpha = 80 - i * 25
            glow_rect = pygame.Rect(
                message_rect.x - i,
                message_rect.y - i,
                message_rect.width + i * 2,
                message_rect.height + i * 2
            )

            # Create a color with alpha
            color_with_alpha = (glow_color[0], glow_color[1], glow_color[2], alpha)

            # Draw the glow
            self.draw_rounded_rect(glow_rect, color_with_alpha, border_radius=10 + i, width=2)

        # Draw border
        pygame.draw.rect(self.screen, border_color, message_rect, 2, border_radius=10)

        # Check if message contains multiple parts (for success messages with detailed info)
        if "Added" in self.message and "|" in self.message:
            # Split the message into parts
            parts = self.message.split("|")

            # Draw first part (Added X credits! Share: Y%)
            message_font = pygame.font.SysFont("Arial", 18, bold=True)
            first_part = parts[0].strip()
            first_text = message_font.render(first_part, True, WHITE)
            first_rect = first_text.get_rect(
                centerx=message_rect.centerx,
                y=message_rect.y + 15
            )
            self.screen.blit(first_text, first_rect)

            # Draw second part (Balance: Z credits)
            second_part = parts[1].strip()
            second_text = message_font.render(second_part, True, GOLD)
            second_rect = second_text.get_rect(
                centerx=message_rect.centerx,
                y=first_rect.bottom + 10
            )
            self.screen.blit(second_text, second_rect)

            # Draw explanation if available
            if hasattr(self, 'explanation') and self.explanation:
                # Use smaller font for explanation
                explanation_font = pygame.font.SysFont("Arial", 14)

                # Split explanation into multiple lines if needed
                max_width = message_width - 30
                words = self.explanation.split()
                lines = []
                current_line = []

                for word in words:
                    test_line = ' '.join(current_line + [word])
                    test_width = explanation_font.size(test_line)[0]

                    if test_width <= max_width:
                        current_line.append(word)
                    else:
                        lines.append(' '.join(current_line))
                        current_line = [word]

                if current_line:
                    lines.append(' '.join(current_line))

                # Draw explanation background
                explanation_bg_rect = pygame.Rect(
                    message_rect.x + 10,
                    second_rect.bottom + 5,
                    message_rect.width - 20,
                    message_rect.bottom - second_rect.bottom - 15
                )
                pygame.draw.rect(self.screen, (0, 0, 0, 50), explanation_bg_rect, border_radius=5)

                # Draw each line of the explanation
                y_offset = second_rect.bottom + 10
                for line in lines:
                    explanation_text = explanation_font.render(line, True, (220, 220, 220))
                    explanation_rect = explanation_text.get_rect(
                        centerx=message_rect.centerx,
                        y=y_offset
                    )
                    self.screen.blit(explanation_text, explanation_rect)
                    y_offset += explanation_rect.height + 2
        else:
            # Draw regular message text
            message_font = pygame.font.SysFont("Arial", 18, bold=True)
            message_text = message_font.render(self.message, True, WHITE)
            message_text_rect = message_text.get_rect(center=message_rect.center)
            self.screen.blit(message_text, message_text_rect)

    def draw_gradient_background(self, rect, color1, color2, border_radius=0):
        """Draw a rectangle with a vertical gradient"""
        # Create a surface with alpha channel
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Draw gradient by blending two colors over height
        for y in range(rect.height):
            # Calculate blend factor (0.0 to 1.0)
            t = y / max(1, rect.height - 1)

            # Linear interpolation of colors
            r = int(color1[0] * (1 - t) + color2[0] * t)
            g = int(color1[1] * (1 - t) + color2[1] * t)
            b = int(color1[2] * (1 - t) + color2[2] * t)

            # Draw a line of the gradient
            pygame.draw.line(surf, (r, g, b), (0, y), (rect.width, y))

        # Apply rounded corners if requested
        if border_radius > 0:
            # Create mask for rounded corners
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)

            # Apply mask
            surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Draw the surface to the screen
        self.screen.blit(surf, rect)

    def draw_rounded_rect(self, rect, color, border_radius=0, width=0):
        """Draw a rounded rectangle with alpha support"""
        if len(color) == 4:  # Color with alpha
            shape_surf = pygame.Surface(rect.size, pygame.SRCALPHA)
            pygame.draw.rect(shape_surf, color, shape_surf.get_rect(), width, border_radius=border_radius)
            self.screen.blit(shape_surf, rect)
        else:  # Color without alpha
            pygame.draw.rect(self.screen, color, rect, width, border_radius=border_radius)
