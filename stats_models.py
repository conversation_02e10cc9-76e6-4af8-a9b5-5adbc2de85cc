import os
import enum
import datetime
from sqlalchemy import create_engine, <PERSON><PERSON>n, Integer, String, Float, DateTime, Date, ForeignKey, Enum as SQLAlchemyEnum, func, or_, text
from sqlalchemy.orm import sessionmaker, relationship, declarative_base
from sqlalchemy.ext.hybrid import hybrid_property

# Constants
DATA_DIR = os.path.join('data')
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)
STATS_DB_URL = f"sqlite:///{os.path.join(DATA_DIR, 'stats.db')}"

# SQLAlchemy Setup
Base = declarative_base()
engine = create_engine(STATS_DB_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# --- SQLAlchemy Models ---

class GameStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    WON = "won"
    LOST = "lost"
    CANCELLED = "cancelled"

    @classmethod
    def from_string(cls, status_str):
        """
        Convert a string to the appropriate enum value, handling case variations.
        """
        if not status_str:
            return cls.PENDING

        # Normalize the string: lowercase and strip whitespace
        normalized = status_str.lower().strip()

        # Check each enum value
        for status in cls:
            if status.value.lower() == normalized:
                return status

        # If no match, use a direct attribute lookup with uppercase (handle 'won' -> WON)
        try:
            return cls[normalized.upper()]
        except (KeyError, AttributeError):
            # Default to PENDING if nothing matches
            print(f"Warning: Could not convert '{status_str}' to a valid GameStatus enum. Using PENDING.")
            return cls.PENDING

class GameHistory(Base):
    __tablename__ = "game_history"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, index=True)
    house = Column(String)
    stake = Column(Float)
    players = Column(Integer)
    total_calls = Column(Integer)
    commission_percent = Column(Float)
    fee = Column(Float)
    total_prize = Column(Float)
    date_time = Column(DateTime, default=datetime.datetime.utcnow, index=True)
    status = Column(SQLAlchemyEnum(GameStatus, create_constraint=True, case_sensitive=False), default=GameStatus.PENDING, index=True)

    @hybrid_property
    def stake_etb(self):
        return f"{self.stake:,.1f} ETB"

    @hybrid_property
    def total_prize_etb(self):
        return f"{self.total_prize:,.1f} ETB"

    @hybrid_property
    def fee_etb(self):
        return f"{self.fee:,.1f} ETB"

    @hybrid_property
    def commission_display(self):
        return f"{self.commission_percent:.1f}%"

    def __repr__(self):
        return f"<GameHistory(id={self.id}, user='{self.username}', prize={self.total_prize}, status='{self.status.value if self.status else 'N/A'}')>"

class DailyStat(Base):
    __tablename__ = "daily_stats"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, unique=True, index=True)
    earnings = Column(Float, default=0.0)
    games_played = Column(Integer, default=0)

    def __repr__(self):
        return f"<DailyStat(date='{self.date.strftime('%Y-%m-%d')}', earnings={self.earnings}, games={self.games_played})>"

class OverallSummary(Base):
    __tablename__ = "overall_summary"
    id = Column(Integer, primary_key=True)
    total_earnings = Column(Float, default=0.0)
    current_daily_games = Column(Integer, default=0)
    current_daily_earnings = Column(Float, default=0.0)
    wallet_balance = Column(Float, default=0.0)
    last_updated = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def __repr__(self):
        return f"<OverallSummary(total_earnings={self.total_earnings}, wallet={self.wallet_balance})>"

# Create tables if they don't exist
Base.metadata.create_all(bind=engine)