"""
UI Animation Module for the WOW Games application.

This module provides animation capabilities for UI elements, supporting
various easing functions and animation types.
"""

import math
import time

class AnimationManager:
    """
    Manages animations for UI elements with various easing functions.
    """
    
    def __init__(self):
        self.animations = {}
        self.completed_animations = set()
    
    def add_animation(self, element_id, property_name, start_value, end_value, 
                     duration=0.3, delay=0, easing="ease_out_quad", 
                     on_complete=None, auto_remove=True):
        """
        Add a new animation to the manager.
        
        Args:
            element_id: Unique identifier for the element
            property_name: Name of the property to animate (e.g., 'x', 'y', 'alpha')
            start_value: Starting value
            end_value: Ending value
            duration: Duration in seconds
            delay: Delay before starting in seconds
            easing: Easing function name
            on_complete: Callback function when animation completes
            auto_remove: Whether to automatically remove the animation when complete
        """
        animation_id = f"{element_id}_{property_name}"
        
        self.animations[animation_id] = {
            'element_id': element_id,
            'property': property_name,
            'start_value': start_value,
            'end_value': end_value,
            'duration': duration,
            'delay': delay,
            'easing': easing,
            'start_time': time.time() + delay,
            'on_complete': on_complete,
            'auto_remove': auto_remove,
            'completed': False
        }
        
        return animation_id
    
    def remove_animation(self, animation_id):
        """Remove an animation by its ID"""
        if animation_id in self.animations:
            del self.animations[animation_id]
    
    def remove_element_animations(self, element_id):
        """Remove all animations for a specific element"""
        to_remove = []
        # Use a copy of self.animations.items() to avoid "dictionary changed size during iteration" error
        for anim_id, anim in list(self.animations.items()):
            if anim['element_id'] == element_id:
                to_remove.append(anim_id)
        
        for anim_id in to_remove:
            if anim_id in self.animations:  # Check if still exists before removing
                del self.animations[anim_id]
    
    def update(self):
        """
        Update all animations based on current time.
        
        Returns:
            dict: Dictionary of current animation values by element_id and property
        """
        current_time = time.time()
        result = {}
        completed = []
        
        # Use a copy of self.animations.items() to avoid "dictionary changed size during iteration" error
        for anim_id, anim in list(self.animations.items()):
            # Skip if not started yet
            if current_time < anim['start_time']:
                continue
            
            # Calculate progress
            elapsed = current_time - anim['start_time']
            progress = min(1.0, elapsed / anim['duration'])
            
            # Apply easing function
            eased_progress = self._apply_easing(progress, anim['easing'])
            
            # Calculate current value
            current_value = anim['start_value'] + (anim['end_value'] - anim['start_value']) * eased_progress
            
            # Store result
            element_id = anim['element_id']
            property_name = anim['property']
            
            if element_id not in result:
                result[element_id] = {}
            
            result[element_id][property_name] = current_value
            
            # Check if animation is complete
            if progress >= 1.0 and not anim['completed']:
                anim['completed'] = True
                if anim['on_complete']:
                    anim['on_complete']()
                if anim['auto_remove']:
                    completed.append(anim_id)
                else:
                    # For non-auto-remove animations, store the final value
                    result[element_id][property_name] = anim['end_value']
        
        # Remove completed animations
        for anim_id in completed:
            self.completed_animations.add(anim_id)
            if anim_id in self.animations:  # Check if still exists before removing
                del self.animations[anim_id]
        
        return result
    
    def is_animating(self, element_id=None, property_name=None):
        """
        Check if an element or property is currently being animated.
        
        Args:
            element_id: Element ID to check, or None to check all
            property_name: Property name to check, or None to check all properties
            
        Returns:
            bool: True if animating, False otherwise
        """
        if element_id is None:
            return len(self.animations) > 0
        
        # Use a copy of self.animations.items() to avoid "dictionary changed size during iteration" error
        for anim_id, anim in list(self.animations.items()):
            if anim['element_id'] == element_id:
                if property_name is None or anim['property'] == property_name:
                    return True
        
        return False
    
    def get_value(self, element_id, property_name, default_value=0):
        """
        Get the current value of an animated property.
        
        Args:
            element_id: Element ID
            property_name: Property name
            default_value: Default value if not found
            
        Returns:
            Current value of the property
        """
        animation_id = f"{element_id}_{property_name}"
        
        if animation_id not in self.animations:
            return default_value
        
        anim = self.animations[animation_id]
        current_time = time.time()
        
        # If animation hasn't started yet, return start value
        if current_time < anim['start_time']:
            return anim['start_value']
        
        # Calculate progress
        elapsed = current_time - anim['start_time']
        progress = min(1.0, elapsed / anim['duration'])
        
        # Apply easing function
        eased_progress = self._apply_easing(progress, anim['easing'])
        
        # Calculate current value
        return anim['start_value'] + (anim['end_value'] - anim['start_value']) * eased_progress
    
    def _apply_easing(self, t, easing_type):
        """Apply easing function to the progress value"""
        if easing_type == "linear":
            return t
        elif easing_type == "ease_in_quad":
            return t * t
        elif easing_type == "ease_out_quad":
            return t * (2 - t)
        elif easing_type == "ease_in_out_quad":
            return 2 * t * t if t < 0.5 else -1 + (4 - 2 * t) * t
        elif easing_type == "ease_in_cubic":
            return t * t * t
        elif easing_type == "ease_out_cubic":
            return (t - 1) * (t - 1) * (t - 1) + 1
        elif easing_type == "ease_in_out_cubic":
            return 4 * t * t * t if t < 0.5 else (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
        elif easing_type == "ease_in_expo":
            return 0 if t == 0 else pow(2, 10 * (t - 1))
        elif easing_type == "ease_out_expo":
            return 1 if t == 1 else 1 - pow(2, -10 * t)
        elif easing_type == "ease_in_out_expo":
            if t == 0 or t == 1:
                return t
            if t < 0.5:
                return 0.5 * pow(2, 20 * t - 10)
            else:
                return 0.5 * (2 - pow(2, -20 * t + 10))
        elif easing_type == "ease_out_bounce":
            if t < (1/2.75):
                return 7.5625 * t * t
            elif t < (2/2.75):
                t -= 1.5/2.75
                return 7.5625 * t * t + 0.75
            elif t < (2.5/2.75):
                t -= 2.25/2.75
                return 7.5625 * t * t + 0.9375
            else:
                t -= 2.625/2.75
                return 7.5625 * t * t + 0.984375
        else:
            # Default to linear if unknown easing type
            return t


class UIAnimator:
    """
    Helper class for animating UI elements with common animation patterns.
    """
    
    def __init__(self, animation_manager):
        self.animation_manager = animation_manager
    
    def fade_in(self, element_id, duration=0.3, delay=0, on_complete=None):
        """Fade in animation (alpha 0 to 1)"""
        return self.animation_manager.add_animation(
            element_id, 'alpha', 0, 1, 
            duration=duration, delay=delay, 
            easing="ease_out_quad", on_complete=on_complete
        )
    
    def fade_out(self, element_id, duration=0.3, delay=0, on_complete=None):
        """Fade out animation (alpha 1 to 0)"""
        return self.animation_manager.add_animation(
            element_id, 'alpha', 1, 0, 
            duration=duration, delay=delay, 
            easing="ease_out_quad", on_complete=on_complete
        )
    
    def slide_in_right(self, element_id, start_x, end_x, duration=0.3, delay=0, on_complete=None):
        """Slide in from right animation"""
        return self.animation_manager.add_animation(
            element_id, 'x', start_x, end_x, 
            duration=duration, delay=delay, 
            easing="ease_out_cubic", on_complete=on_complete
        )
    
    def slide_in_left(self, element_id, start_x, end_x, duration=0.3, delay=0, on_complete=None):
        """Slide in from left animation"""
        return self.animation_manager.add_animation(
            element_id, 'x', start_x, end_x, 
            duration=duration, delay=delay, 
            easing="ease_out_cubic", on_complete=on_complete
        )
    
    def slide_in_top(self, element_id, start_y, end_y, duration=0.3, delay=0, on_complete=None):
        """Slide in from top animation"""
        return self.animation_manager.add_animation(
            element_id, 'y', start_y, end_y, 
            duration=duration, delay=delay, 
            easing="ease_out_cubic", on_complete=on_complete
        )
    
    def slide_in_bottom(self, element_id, start_y, end_y, duration=0.3, delay=0, on_complete=None):
        """Slide in from bottom animation"""
        return self.animation_manager.add_animation(
            element_id, 'y', start_y, end_y, 
            duration=duration, delay=delay, 
            easing="ease_out_cubic", on_complete=on_complete
        )
    
    def scale_in(self, element_id, duration=0.3, delay=0, on_complete=None):
        """Scale in animation (scale 0 to 1)"""
        self.animation_manager.add_animation(
            element_id, 'scale_x', 0, 1, 
            duration=duration, delay=delay, 
            easing="ease_out_back", on_complete=None
        )
        return self.animation_manager.add_animation(
            element_id, 'scale_y', 0, 1, 
            duration=duration, delay=delay, 
            easing="ease_out_back", on_complete=on_complete
        )
    
    def pulse(self, element_id, duration=0.5, delay=0, on_complete=None):
        """Pulse animation (scale 1 to 1.1 and back)"""
        self.animation_manager.add_animation(
            element_id, 'scale_x', 1, 1.1, 
            duration=duration/2, delay=delay, 
            easing="ease_out_quad", on_complete=None
        )
        self.animation_manager.add_animation(
            element_id, 'scale_y', 1, 1.1, 
            duration=duration/2, delay=delay, 
            easing="ease_out_quad", on_complete=None
        )
        self.animation_manager.add_animation(
            element_id, 'scale_x', 1.1, 1, 
            duration=duration/2, delay=delay+duration/2, 
            easing="ease_in_out_quad", on_complete=None
        )
        return self.animation_manager.add_animation(
            element_id, 'scale_y', 1.1, 1, 
            duration=duration/2, delay=delay+duration/2, 
            easing="ease_in_out_quad", on_complete=on_complete
        )
    
    def bounce_in(self, element_id, duration=0.5, delay=0, on_complete=None):
        """Bounce in animation"""
        return self.animation_manager.add_animation(
            element_id, 'scale', 0, 1, 
            duration=duration, delay=delay, 
            easing="ease_out_bounce", on_complete=on_complete
        )
    
    def highlight(self, element_id, duration=0.5, delay=0, on_complete=None):
        """Highlight animation (alpha 0.5 to 1 and back)"""
        self.animation_manager.add_animation(
            element_id, 'highlight', 0, 1, 
            duration=duration/2, delay=delay, 
            easing="ease_out_quad", on_complete=None
        )
        return self.animation_manager.add_animation(
            element_id, 'highlight', 1, 0, 
            duration=duration/2, delay=delay+duration/2, 
            easing="ease_in_quad", on_complete=on_complete
        )


# Create a singleton instance
_animation_manager = None

def get_animation_manager():
    """Get the singleton animation manager instance"""
    global _animation_manager
    if _animation_manager is None:
        _animation_manager = AnimationManager()
    return _animation_manager

def get_ui_animator():
    """Get a UI animator instance using the singleton animation manager"""
    return UIAnimator(get_animation_manager())