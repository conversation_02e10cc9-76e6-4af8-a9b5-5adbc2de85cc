# Bingo Game Logic

Enhanced bingo game logic for the Bingo Player and Referee app. This implementation adds advanced features while maintaining compatibility with the existing design.

## Components

The bingo game logic is split into three main components:

1. **BingoCard** (`bingo_card.py`) - Represents a player's 5x5 bingo card with methods to mark numbers and check for winning patterns.

2. **BingoCaller** (`bingo_caller.py`) - Manages the calling of bingo numbers with features like timed calling, call history, and announcements.

3. **BingoLogic** (`bingo_logic.py`) - Core game logic that manages the relationship between players, cards, and winning conditions.

4. **Game Integration** (`game_integration.py`) - Shows how to integrate the new components with the existing code without modifying it.

## How to Use

### 1. Import the Files

Make sure all four Python files are in your project directory.

### 2. Integrate with Existing Code

In your `main.py` file, add the following:

```python
from game_integration import integrate_game_logic

# After initializing your BingoGame instance
game = BingoGame()
bingo_logic, bingo_caller = integrate_game_logic(game)
```

This enhances the existing BingoGame class with the new functionality without modifying its design.

### 3. Using the Advanced Features

The integration adds several new capabilities:

- **Automatic Number Calling**: Press 'A' to toggle automatic number calling
- **Winner Declaration**: Press 'W' to manually declare the current cartela as a winner (for testing)
- **Winner Detection**: The system automatically checks for winning patterns when numbers are called
- **Multiple Winning Patterns**: Supports rows, columns, diagonals, four corners, and blackout patterns

## Game Rules

- Standard bingo rules apply (5x5 card with a free space in the middle)
- Players win by completing a valid pattern with called numbers
- Valid patterns include:
  - Any row
  - Any column
  - Either diagonal
  - Four corners
  - Full card (blackout/coverall)

## Technical Details

- The integration is done using composition rather than inheritance
- Original methods are preserved and enhanced, not replaced
- Player cards are associated with cartela numbers
- Game state is tracked to handle different phases (not started, in progress, winner declared, etc.)

## Keyboard Shortcuts

- **A**: Toggle automatic number calling
- **W**: Declare current cartela as winner (for testing)

## Console Output

When winners are detected, information is printed to the console:

```
We have winners! Cartela numbers: [32]
Cartela 32 won with pattern: Row 3
```

This helps with debugging and can be replaced with UI notifications as needed. 