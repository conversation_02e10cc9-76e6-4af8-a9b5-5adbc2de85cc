#!/usr/bin/env python

with open("Board_selection_fixed.py", "r", encoding="utf-8") as f:
    lines = f.readlines()

# Find the prize_pool line
prize_pool_line = -1
for i, line in enumerate(lines):
    if "self.prize_pool = 0" in line:
        prize_pool_line = i
        break

if prize_pool_line >= 0:
    # Count how many manual_override lines we have
    count = 0
    for i in range(prize_pool_line + 1, min(prize_pool_line + 5, len(lines))):
        if "self.prize_pool_manual_override = False" in lines[i]:
            count += 1
    
    # Remove all but the first manual_override line
    if count > 1:
        removed = 0
        i = prize_pool_line + 1
        while removed < count - 1 and i < len(lines):
            if "self.prize_pool_manual_override = False" in lines[i]:
                lines.pop(i)
                removed += 1
            else:
                i += 1

with open("Board_selection_fixed.py", "w", encoding="utf-8") as f:
    f.writelines(lines)

print(f"Fixed duplicate lines. Removed {count - 1} duplicate lines.") 