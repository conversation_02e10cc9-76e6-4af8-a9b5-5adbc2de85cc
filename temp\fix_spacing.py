import re

def fix_spacing():
    # Read the file content
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Look for the problematic section
    pattern = r'self\.show_message\("Invalid prize pool value", "error"\)(def show_board_selection)'
    replacement = r'self.show_message("Invalid prize pool value", "error")\n\n\1'
    
    # Make the replacement
    new_content = re.sub(pattern, replacement, content)
    
    # Write the updated content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Spacing fixed successfully!")

if __name__ == "__main__":
    fix_spacing() 