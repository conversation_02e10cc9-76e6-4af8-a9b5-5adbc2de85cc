"""
Hybrid Database Integration for the WOW Games application.

This module handles the integration of the hybrid database system
(SQLite + RethinkDB with offline sync) into the existing application.
"""

import os
import logging
import threading
import time
from datetime import datetime

# Import existing stats modules
try:
    from stats_db import get_stats_db_manager
    from stats_integration import get_stats_summary, get_game_history, migrate_legacy_stats, force_refresh_data
    from stats_event_hooks import register_stats_event_handler, unregister_stats_event_handler
    from stats_models import StatsSummary, GameHistoryItem
    STATS_MODULES_AVAILABLE = True
    print("Stats modules imported successfully")
except ImportError as e:
    STATS_MODULES_AVAILABLE = False
    print(f"Error importing stats modules: {e}")

# Import hybrid database manager
try:
    from db_hybrid import get_hybrid_db_manager
    HYBRID_DB_AVAILABLE = True
    print("Hybrid database manager imported successfully")
except ImportError as e:
    HYBRID_DB_AVAILABLE = False
    print(f"Error importing hybrid database manager: {e}")

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'hybrid_db_integration.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class HybridDatabaseIntegration:
    """
    Integration class for the hybrid database system.
    Provides backward compatibility with existing code.
    """

    def __init__(self):
        """Initialize the hybrid database integration."""
        self.hybrid_db = get_hybrid_db_manager() if HYBRID_DB_AVAILABLE else None
        self.event_handlers = {}
        self.background_thread = None
        self.running = False
        
        # Initialize background sync thread
        self._start_background_thread()
        
        logging.info("Hybrid database integration initialized")

    def _start_background_thread(self):
        """Start background thread for connectivity monitoring and sync."""
        def background_worker():
            self.running = True
            logging.info("Background thread started")
            
            # Check connectivity and sync status every 60 seconds
            while self.running:
                try:
                    # Check if we're online
                    if self.hybrid_db and not self.hybrid_db.is_online():
                        # Try to connect
                        self.hybrid_db.try_connect_online()
                    
                    # Sleep for 60 seconds
                    time.sleep(60)
                except Exception as e:
                    logging.error(f"Error in background thread: {e}")
                    time.sleep(60)
            
            logging.info("Background thread stopped")
        
        self.background_thread = threading.Thread(target=background_worker, daemon=True)
        self.background_thread.start()
        
    def stop_background_thread(self):
        """Stop the background thread."""
        self.running = False
        if self.background_thread:
            self.background_thread.join(timeout=5)
        logging.info("Background thread stopped")

    # ========== Stats API Compatibility Methods ==========
    
    def get_stats_summary(self):
        """
        Get a summary of statistics.
        
        Returns:
            StatsSummary: Summary of statistics
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_summary()
            return None
            
        try:
            # Get stats from hybrid DB
            total_earnings = self.hybrid_db.get_total_earnings()
            daily_earnings = self.hybrid_db.get_daily_earnings()
            daily_games = self.hybrid_db.get_daily_games_played()
            wallet_balance = self.hybrid_db.get_wallet_balance()
            
            # Create a StatsSummary object
            summary = StatsSummary(
                total_earnings=total_earnings,
                daily_earnings=daily_earnings,
                daily_games_played=daily_games,
                wallet_balance=wallet_balance
            )
            
            return summary
        except Exception as e:
            logging.error(f"Error getting stats summary: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_summary()
            return None

    def get_game_history(self, page=0, page_size=10):
        """
        Get game history with pagination.
        
        Args:
            page: Page number (0-based)
            page_size: Number of items per page
            
        Returns:
            dict: Dictionary with total_games, total_pages, and games
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_game_history(page, page_size)
            return {"total_games": 0, "total_pages": 0, "games": []}
            
        try:
            # Get game history from hybrid DB
            result = self.hybrid_db.get_game_history(page, page_size)
            
            # Convert to GameHistoryItem objects for compatibility
            if "games" in result:
                result["games"] = [GameHistoryItem(**game) for game in result["games"]]
                
            return result
        except Exception as e:
            logging.error(f"Error getting game history: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_game_history(page, page_size)
            return {"total_games": 0, "total_pages": 0, "games": []}

    def get_weekly_stats(self, end_date=None):
        """
        Get weekly statistics for the 7 days ending on the specified date.
        
        Args:
            end_date: End date string in format 'YYYY-MM-DD', defaults to today
            
        Returns:
            list: List of daily statistics for the week
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().get_weekly_stats(end_date)
            return []
            
        try:
            # Get weekly stats from hybrid DB
            return self.hybrid_db.get_weekly_stats(end_date)
        except Exception as e:
            logging.error(f"Error getting weekly stats: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().get_weekly_stats(end_date)
            return []

    def add_game_to_history(self, username, house, stake, players, total_calls,
                           commission_percent, fee, total_prize, details, status="completed"):
        """
        Add a game to the history.
        
        Args:
            username: Username of the player
            house: House pattern that won
            stake: Stake amount
            players: Number of players
            total_calls: Total number of calls
            commission_percent: Commission percentage
            fee: Fee amount
            total_prize: Total prize amount
            details: Game details as JSON string
            status: Game status
            
        Returns:
            int: ID of the new game history entry
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().add_game_to_history(
                    username, house, stake, players, total_calls,
                    commission_percent, fee, total_prize, details, status
                )
            return None
            
        try:
            # Add game to hybrid DB
            return self.hybrid_db.add_game_to_history(
                username, house, stake, players, total_calls,
                commission_percent, fee, total_prize, details, status
            )
        except Exception as e:
            logging.error(f"Error adding game to history: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().add_game_to_history(
                    username, house, stake, players, total_calls,
                    commission_percent, fee, total_prize, details, status
                )
            return None

    def update_daily_stats(self, date_str=None, games_played=0, earnings=0, winners=0, total_players=0):
        """
        Update daily statistics for a specific date.
        
        Args:
            date_str: Date string in format 'YYYY-MM-DD', defaults to today
            games_played: Number of games played to add
            earnings: Amount of earnings to add
            winners: Number of winners to add
            total_players: Number of total players to add
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().update_daily_stats(
                    date_str, games_played, earnings, winners, total_players
                )
            return False
            
        try:
            # Update stats in hybrid DB
            return self.hybrid_db.update_daily_stats(
                date_str, games_played, earnings, winners, total_players
            )
        except Exception as e:
            logging.error(f"Error updating daily stats: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return get_stats_db_manager().update_daily_stats(
                    date_str, games_played, earnings, winners, total_players
                )
            return False

    def force_refresh_data(self):
        """
        Force a refresh of all statistics data.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.hybrid_db:
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return force_refresh_data()
            return False
            
        try:
            # Sync stats in hybrid DB
            result = self.hybrid_db.sync_stats()
            
            # Also call the original implementation to update any cached data
            if STATS_MODULES_AVAILABLE:
                force_refresh_data()
                
            return result
        except Exception as e:
            logging.error(f"Error forcing refresh: {e}")
            
            # Fall back to original implementation
            if STATS_MODULES_AVAILABLE:
                return force_refresh_data()
            return False

    # ========== Event Handling ==========
    
    def register_event_handler(self, event_type, handler):
        """
        Register an event handler for real-time updates.
        
        Args:
            event_type: Type of event (e.g., 'game_added', 'stats_updated')
            handler: Function to call when the event occurs
            
        Returns:
            str: Handler ID
        """
        # Generate a unique handler ID
        handler_id = f"{event_type}_{datetime.now().timestamp()}"
        
        # Store the handler
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = {}
        self.event_handlers[event_type][handler_id] = handler
        
        # If we're online, subscribe to real-time changes
        if self.hybrid_db and self.hybrid_db.is_online():
            try:
                # Map event type to table
                table_map = {
                    'game_added': 'game_history',
                    'stats_updated': 'daily_stats',
                    'wallet_updated': 'wallet_transactions',
                    'admin_updated': 'admin_users'
                }
                
                if event_type in table_map:
                    # Create callback function
                    def callback(change):
                        # Call the handler with the change data
                        handler(change)
                    
                    # Subscribe to changes
                    subscription_id = self.hybrid_db.subscribe_to_changes(
                        table_map[event_type], callback
                    )
                    
                    # Store the subscription ID
                    self.event_handlers[event_type][handler_id] = {
                        'handler': handler,
                        'subscription_id': subscription_id
                    }
            except Exception as e:
                logging.error(f"Error subscribing to changes: {e}")
        
        # Also register with the original event system if available
        if STATS_MODULES_AVAILABLE:
            try:
                register_stats_event_handler(event_type, handler)
            except Exception as e:
                logging.error(f"Error registering with original event system: {e}")
        
        return handler_id

    def unregister_event_handler(self, event_type, handler_id):
        """
        Unregister an event handler.
        
        Args:
            event_type: Type of event
            handler_id: Handler ID returned by register_event_handler
            
        Returns:
            bool: True if successful, False otherwise
        """
        if event_type in self.event_handlers and handler_id in self.event_handlers[event_type]:
            # Get the handler
            handler_data = self.event_handlers[event_type][handler_id]
            
            # If it's a subscription, unsubscribe
            if isinstance(handler_data, dict) and 'subscription_id' in handler_data:
                if self.hybrid_db and self.hybrid_db.is_online():
                    try:
                        self.hybrid_db.unsubscribe_from_changes(handler_data['subscription_id'])
                    except Exception as e:
                        logging.error(f"Error unsubscribing from changes: {e}")
            
            # Remove the handler
            del self.event_handlers[event_type][handler_id]
            
            # If the event type has no more handlers, remove it
            if not self.event_handlers[event_type]:
                del self.event_handlers[event_type]
            
            # Also unregister with the original event system if available
            if STATS_MODULES_AVAILABLE:
                try:
                    if isinstance(handler_data, dict) and 'handler' in handler_data:
                        unregister_stats_event_handler(event_type, handler_data['handler'])
                    else:
                        unregister_stats_event_handler(event_type, handler_data)
                except Exception as e:
                    logging.error(f"Error unregistering with original event system: {e}")
            
            return True
        
        return False

    # ========== Connection Status ==========
    
    def is_online(self):
        """
        Check if we're in online mode (connected to RethinkDB).
        
        Returns:
            bool: True if online, False if offline
        """
        if not self.hybrid_db:
            return False
            
        return self.hybrid_db.is_online()

    def force_online_mode(self):
        """
        Force the database to operate in online mode.
        
        Returns:
            bool: True if successfully switched to online mode, False otherwise
        """
        if not self.hybrid_db:
            return False
            
        return self.hybrid_db.force_online_mode()

    def force_offline_mode(self):
        """
        Force the database to operate in offline mode.
        
        Returns:
            bool: True if successfully switched to offline mode, False otherwise
        """
        if not self.hybrid_db:
            return False
            
        self.hybrid_db.force_offline_mode()
        return True

# Singleton instance
_hybrid_db_integration = None

def get_hybrid_db_integration():
    """
    Get the singleton instance of HybridDatabaseIntegration.
    
    Returns:
        HybridDatabaseIntegration: Hybrid database integration instance
    """
    global _hybrid_db_integration
    
    if _hybrid_db_integration is None:
        _hybrid_db_integration = HybridDatabaseIntegration()
        
    return _hybrid_db_integration

# For backward compatibility
def get_stats_summary_hybrid():
    """
    Get a summary of statistics using the hybrid database.
    
    Returns:
        StatsSummary: Summary of statistics
    """
    integration = get_hybrid_db_integration()
    return integration.get_stats_summary()

def get_game_history_hybrid(page=0, page_size=10):
    """
    Get game history with pagination using the hybrid database.
    
    Args:
        page: Page number (0-based)
        page_size: Number of items per page
        
    Returns:
        dict: Dictionary with total_games, total_pages, and games
    """
    integration = get_hybrid_db_integration()
    return integration.get_game_history(page, page_size)

def force_refresh_data_hybrid():
    """
    Force a refresh of all statistics data using the hybrid database.
    
    Returns:
        bool: True if successful, False otherwise
    """
    integration = get_hybrid_db_integration()
    return integration.force_refresh_data()