#!/usr/bin/env python3
"""
Executable Issues Diagnostic Tool
=================================

This script diagnoses common issues that prevent compiled executables
from running on subsequent launches.

Common causes:
1. Database file locks (SQLite)
2. Temporary files not cleaned up
3. Process/port conflicts
4. File permissions issues
5. Registry/settings corruption
6. Memory-mapped files not released
7. Pygame/audio device conflicts
"""

import os
import sys
import time
import sqlite3
import subprocess
import psutil
import json
from pathlib import Path
from typing import List, Dict, Optional

class ExecutableDiagnostic:
    """Diagnose and fix executable runtime issues."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.executable_path = self.project_root / "dist" / "WOWBingoGame.exe"
        self.issues_found = []
        self.fixes_applied = []
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with level indicator."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def check_database_locks(self) -> bool:
        """Check for SQLite database locks."""
        self.log("Checking database locks...")
        
        db_files = [
            self.project_root / "data" / "game_stats.db",
            self.project_root / "data" / "players.db",
            self.project_root / "data" / "settings.db"
        ]
        
        issues = []
        
        for db_file in db_files:
            if db_file.exists():
                try:
                    # Try to open database
                    conn = sqlite3.connect(str(db_file), timeout=1.0)
                    
                    # Check for locks by trying to begin immediate transaction
                    cursor = conn.cursor()
                    cursor.execute("BEGIN IMMEDIATE")
                    cursor.execute("ROLLBACK")
                    
                    conn.close()
                    self.log(f"✓ Database OK: {db_file.name}")
                    
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e).lower():
                        issues.append(f"Database locked: {db_file}")
                        self.log(f"✗ Database locked: {db_file.name}", "ERROR")
                    else:
                        self.log(f"✗ Database error: {db_file.name} - {e}", "ERROR")
                        issues.append(f"Database error: {db_file} - {e}")
                except Exception as e:
                    self.log(f"✗ Database access error: {db_file.name} - {e}", "ERROR")
                    issues.append(f"Database access error: {db_file} - {e}")
                    
        if issues:
            self.issues_found.extend(issues)
            return False
        return True
        
    def check_process_conflicts(self) -> bool:
        """Check for conflicting processes."""
        self.log("Checking for process conflicts...")
        
        conflicts = []
        
        # Check for existing game processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'WOWBingoGame' in proc.info['name']:
                    conflicts.append(f"Existing game process: PID {proc.info['pid']}")
                    self.log(f"✗ Found existing game process: PID {proc.info['pid']}", "WARNING")
                    
                # Check for Python processes running main.py
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline and 'python' in cmdline.lower():
                        conflicts.append(f"Python game process: PID {proc.info['pid']}")
                        self.log(f"✗ Found Python game process: PID {proc.info['pid']}", "WARNING")
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        # Check for port conflicts
        port_conflicts = []
        common_ports = [5000, 8081, 28015]  # Web dashboard, RethinkDB admin, RethinkDB
        
        for port in common_ports:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    port_conflicts.append(f"Port {port} in use by PID {conn.pid}")
                    self.log(f"✗ Port {port} in use by PID {conn.pid}", "WARNING")
                    
        if conflicts or port_conflicts:
            self.issues_found.extend(conflicts + port_conflicts)
            return False
        return True
        
    def check_temporary_files(self) -> bool:
        """Check for problematic temporary files."""
        self.log("Checking temporary files...")
        
        temp_patterns = [
            "*.tmp",
            "*.lock",
            "*.pid",
            "*~",
            "*.bak"
        ]
        
        problematic_files = []
        
        # Check project directory
        for pattern in temp_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    problematic_files.append(str(file_path))
                    self.log(f"✗ Found temp file: {file_path}", "WARNING")
                    
        # Check for SQLite WAL and SHM files
        for db_file in self.project_root.rglob("*.db"):
            wal_file = db_file.with_suffix('.db-wal')
            shm_file = db_file.with_suffix('.db-shm')
            
            if wal_file.exists():
                problematic_files.append(str(wal_file))
                self.log(f"✗ Found SQLite WAL file: {wal_file}", "WARNING")
                
            if shm_file.exists():
                problematic_files.append(str(shm_file))
                self.log(f"✗ Found SQLite SHM file: {shm_file}", "WARNING")
                
        if problematic_files:
            self.issues_found.extend([f"Temp file: {f}" for f in problematic_files])
            return False
        return True
        
    def check_file_permissions(self) -> bool:
        """Check file permissions."""
        self.log("Checking file permissions...")
        
        critical_paths = [
            self.project_root / "data",
            self.project_root / "assets",
            self.executable_path
        ]
        
        permission_issues = []
        
        for path in critical_paths:
            if path.exists():
                try:
                    if path.is_file():
                        # Test read access
                        with open(path, 'rb') as f:
                            f.read(1)
                    else:
                        # Test directory access
                        list(path.iterdir())
                        
                    self.log(f"✓ Permissions OK: {path.name}")
                    
                except PermissionError:
                    permission_issues.append(f"Permission denied: {path}")
                    self.log(f"✗ Permission denied: {path}", "ERROR")
                except Exception as e:
                    permission_issues.append(f"Access error: {path} - {e}")
                    self.log(f"✗ Access error: {path} - {e}", "ERROR")
                    
        if permission_issues:
            self.issues_found.extend(permission_issues)
            return False
        return True
        
    def check_settings_corruption(self) -> bool:
        """Check for corrupted settings files."""
        self.log("Checking settings files...")
        
        settings_files = [
            self.project_root / "data" / "settings.json",
            self.project_root / "data" / "game_config.json"
        ]
        
        corruption_issues = []
        
        for settings_file in settings_files:
            if settings_file.exists():
                try:
                    with open(settings_file, 'r') as f:
                        json.load(f)
                    self.log(f"✓ Settings OK: {settings_file.name}")
                except json.JSONDecodeError:
                    corruption_issues.append(f"Corrupted JSON: {settings_file}")
                    self.log(f"✗ Corrupted JSON: {settings_file.name}", "ERROR")
                except Exception as e:
                    corruption_issues.append(f"Settings error: {settings_file} - {e}")
                    self.log(f"✗ Settings error: {settings_file.name} - {e}", "ERROR")
                    
        if corruption_issues:
            self.issues_found.extend(corruption_issues)
            return False
        return True
        
    def fix_database_locks(self) -> bool:
        """Fix database lock issues."""
        self.log("Fixing database locks...")
        
        db_files = [
            self.project_root / "data" / "game_stats.db",
            self.project_root / "data" / "players.db",
            self.project_root / "data" / "settings.db"
        ]
        
        fixed = []
        
        for db_file in db_files:
            if db_file.exists():
                # Remove WAL and SHM files
                wal_file = db_file.with_suffix('.db-wal')
                shm_file = db_file.with_suffix('.db-shm')
                
                for temp_file in [wal_file, shm_file]:
                    if temp_file.exists():
                        try:
                            temp_file.unlink()
                            fixed.append(f"Removed {temp_file.name}")
                            self.log(f"✓ Removed: {temp_file.name}")
                        except Exception as e:
                            self.log(f"✗ Could not remove {temp_file.name}: {e}", "ERROR")
                            
                # Try to repair database
                try:
                    conn = sqlite3.connect(str(db_file))
                    conn.execute("PRAGMA integrity_check")
                    conn.execute("VACUUM")
                    conn.close()
                    fixed.append(f"Repaired {db_file.name}")
                    self.log(f"✓ Repaired: {db_file.name}")
                except Exception as e:
                    self.log(f"✗ Could not repair {db_file.name}: {e}", "ERROR")
                    
        if fixed:
            self.fixes_applied.extend(fixed)
            return True
        return False
        
    def fix_process_conflicts(self) -> bool:
        """Fix process conflicts."""
        self.log("Fixing process conflicts...")
        
        fixed = []
        
        # Kill existing game processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                should_kill = False
                
                if proc.info['name'] and 'WOWBingoGame' in proc.info['name']:
                    should_kill = True
                    reason = "WOW Bingo Game process"
                    
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline and 'python' in cmdline.lower():
                        should_kill = True
                        reason = "Python game process"
                        
                if should_kill:
                    proc.terminate()
                    try:
                        proc.wait(timeout=5)
                    except psutil.TimeoutExpired:
                        proc.kill()
                        
                    fixed.append(f"Killed {reason}: PID {proc.info['pid']}")
                    self.log(f"✓ Killed {reason}: PID {proc.info['pid']}")
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        if fixed:
            self.fixes_applied.extend(fixed)
            return True
        return False
        
    def fix_temporary_files(self) -> bool:
        """Fix temporary file issues."""
        self.log("Cleaning temporary files...")
        
        temp_patterns = [
            "*.tmp",
            "*.lock",
            "*.pid",
            "*~",
            "*.bak",
            "*.db-wal",
            "*.db-shm"
        ]
        
        fixed = []
        
        for pattern in temp_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    try:
                        file_path.unlink()
                        fixed.append(f"Removed {file_path.name}")
                        self.log(f"✓ Removed: {file_path.name}")
                    except Exception as e:
                        self.log(f"✗ Could not remove {file_path.name}: {e}", "ERROR")
                        
        if fixed:
            self.fixes_applied.extend(fixed)
            return True
        return False
        
    def run_diagnostic(self) -> Dict:
        """Run complete diagnostic."""
        self.log("=" * 60)
        self.log("WOW Bingo Game - Executable Issues Diagnostic")
        self.log("=" * 60)
        
        checks = [
            ("Database Locks", self.check_database_locks),
            ("Process Conflicts", self.check_process_conflicts),
            ("Temporary Files", self.check_temporary_files),
            ("File Permissions", self.check_file_permissions),
            ("Settings Corruption", self.check_settings_corruption)
        ]
        
        results = {}
        
        for check_name, check_func in checks:
            self.log(f"\n--- {check_name} ---")
            try:
                result = check_func()
                results[check_name] = result
                if result:
                    self.log(f"{check_name}: PASSED ✓")
                else:
                    self.log(f"{check_name}: ISSUES FOUND ✗", "WARNING")
            except Exception as e:
                results[check_name] = False
                self.log(f"{check_name}: ERROR - {e}", "ERROR")
                
        return results
        
    def apply_fixes(self) -> bool:
        """Apply fixes for found issues."""
        if not self.issues_found:
            self.log("No issues found, no fixes needed")
            return True
            
        self.log("\n" + "=" * 60)
        self.log("APPLYING FIXES")
        self.log("=" * 60)
        
        fixes = [
            ("Database Locks", self.fix_database_locks),
            ("Process Conflicts", self.fix_process_conflicts),
            ("Temporary Files", self.fix_temporary_files)
        ]
        
        success = True
        
        for fix_name, fix_func in fixes:
            self.log(f"\n--- {fix_name} ---")
            try:
                if fix_func():
                    self.log(f"{fix_name}: FIXED ✓")
                else:
                    self.log(f"{fix_name}: NO ACTION NEEDED")
            except Exception as e:
                self.log(f"{fix_name}: FIX FAILED - {e}", "ERROR")
                success = False
                
        return success
        
    def generate_report(self, results: Dict) -> None:
        """Generate diagnostic report."""
        self.log("\n" + "=" * 60)
        self.log("DIAGNOSTIC REPORT")
        self.log("=" * 60)
        
        passed_checks = sum(1 for result in results.values() if result)
        total_checks = len(results)
        
        self.log(f"Checks passed: {passed_checks}/{total_checks}")
        
        if self.issues_found:
            self.log(f"\nIssues found ({len(self.issues_found)}):")
            for issue in self.issues_found:
                self.log(f"  • {issue}")
                
        if self.fixes_applied:
            self.log(f"\nFixes applied ({len(self.fixes_applied)}):")
            for fix in self.fixes_applied:
                self.log(f"  • {fix}")
                
        if passed_checks == total_checks and not self.issues_found:
            self.log("\n✅ NO ISSUES FOUND - Executable should run normally")
        elif self.fixes_applied:
            self.log("\n🔧 FIXES APPLIED - Try running the executable again")
        else:
            self.log("\n⚠️ ISSUES REMAIN - Manual intervention may be required")

def main():
    """Main entry point."""
    try:
        diagnostic = ExecutableDiagnostic()
        
        # Run diagnostic
        results = diagnostic.run_diagnostic()
        
        # Apply fixes if issues found
        if diagnostic.issues_found:
            diagnostic.apply_fixes()
            
        # Generate report
        diagnostic.generate_report(results)
        
        # Exit code based on results
        if not diagnostic.issues_found:
            sys.exit(0)  # No issues
        elif diagnostic.fixes_applied:
            sys.exit(1)  # Issues fixed, retry needed
        else:
            sys.exit(2)  # Issues remain
            
    except KeyboardInterrupt:
        print("\nDiagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Diagnostic failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
