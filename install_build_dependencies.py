#!/usr/bin/env python3
"""
WOW Bingo Game - Build Dependencies Installer
=============================================

This script automatically installs all required dependencies for building
the WOW Bingo Game executable.

Usage:
    python install_build_dependencies.py

Features:
- Installs PyInstaller and all build tools
- Installs game dependencies
- Verifies installation success
- Provides detailed progress feedback
"""

import os
import sys
import subprocess
import time
from pathlib import Path

class DependencyInstaller:
    """Installs all build dependencies for WOW Bingo Game."""
    
    def __init__(self):
        self.start_time = time.time()
        self.installed_packages = []
        self.failed_packages = []
        
    def log(self, message: str, level: str = "INFO") -> None:
        """Log a message with timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        elapsed = time.time() - self.start_time
        
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "PROGRESS": "🔄"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] [{elapsed:6.1f}s] {prefix} {message}")
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        self.log("Checking Python version...")
        
        if sys.version_info < (3, 7):
            self.log(f"Python {sys.version_info.major}.{sys.version_info.minor} is too old!", "ERROR")
            self.log("Please install Python 3.7 or higher from https://python.org", "ERROR")
            return False
        
        self.log(f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} ✓", "SUCCESS")
        return True
    
    def upgrade_pip(self) -> bool:
        """Upgrade pip to latest version."""
        self.log("Upgrading pip to latest version...", "PROGRESS")
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True, capture_output=True, text=True)
            
            # Get pip version
            result = subprocess.run([
                sys.executable, '-m', 'pip', '--version'
            ], capture_output=True, text=True, check=True)
            
            self.log(f"Pip upgraded successfully: {result.stdout.strip()}", "SUCCESS")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to upgrade pip: {e}", "WARNING")
            self.log("Continuing with existing pip version...", "INFO")
            return True  # Don't fail if pip upgrade fails
    
    def install_package(self, package_name: str, display_name: str = None) -> bool:
        """Install a single package."""
        if display_name is None:
            display_name = package_name
            
        self.log(f"Installing {display_name}...", "PROGRESS")
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', package_name
            ], check=True, capture_output=True, text=True)
            
            self.log(f"Successfully installed {display_name}", "SUCCESS")
            self.installed_packages.append(display_name)
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install {display_name}: {e}", "ERROR")
            self.failed_packages.append(display_name)
            return False
    
    def install_build_tools(self) -> bool:
        """Install build tools."""
        self.log("Installing build tools...", "INFO")
        
        build_tools = [
            ("pyinstaller>=6.0.0", "PyInstaller (Primary build tool)"),
            ("nuitka>=2.7.0", "Nuitka (Alternative build tool)"),
            ("wheel>=0.37.0", "Wheel (Package building)"),
            ("setuptools>=60.0.0", "Setuptools (Package tools)"),
        ]
        
        success_count = 0
        for package, description in build_tools:
            if self.install_package(package, description):
                success_count += 1
        
        self.log(f"Build tools installation: {success_count}/{len(build_tools)} successful", "INFO")
        return success_count >= 1  # At least PyInstaller should be installed
    
    def install_game_dependencies(self) -> bool:
        """Install game dependencies."""
        self.log("Installing game dependencies...", "INFO")
        
        game_deps = [
            ("pygame>=2.5.0", "Pygame (Game engine)"),
            ("pyperclip>=1.8.2", "Pyperclip (Clipboard support)"),
            ("psutil>=5.9.0", "Psutil (System utilities)"),
            ("pillow>=10.0.0", "Pillow (Image processing)"),
            ("cryptography>=41.0.0", "Cryptography (Security)"),
            ("pydantic>=2.0.0", "Pydantic (Data validation)"),
            ("requests>=2.31.0", "Requests (HTTP client)"),
        ]
        
        success_count = 0
        for package, description in game_deps:
            if self.install_package(package, description):
                success_count += 1
        
        self.log(f"Game dependencies installation: {success_count}/{len(game_deps)} successful", "INFO")
        return success_count >= 4  # Most core dependencies should be installed
    
    def install_optional_dependencies(self) -> bool:
        """Install optional dependencies."""
        self.log("Installing optional dependencies...", "INFO")
        
        optional_deps = [
            ("flet>=0.24.0", "Flet (Modern UI framework)"),
            ("pydub>=0.25.1", "Pydub (Audio processing)"),
            ("numpy>=1.24.0", "NumPy (Numerical computing)"),
            ("loguru>=0.7.0", "Loguru (Advanced logging)"),
            ("rethinkdb>=2.4.10", "RethinkDB (Database)"),
        ]
        
        success_count = 0
        for package, description in optional_deps:
            if self.install_package(package, description):
                success_count += 1
            else:
                self.log(f"Optional dependency {description} failed - continuing...", "WARNING")
        
        self.log(f"Optional dependencies installation: {success_count}/{len(optional_deps)} successful", "INFO")
        return True  # Optional dependencies don't affect success
    
    def install_windows_dependencies(self) -> bool:
        """Install Windows-specific dependencies."""
        if os.name != 'nt':
            self.log("Skipping Windows-specific dependencies (not on Windows)", "INFO")
            return True
            
        self.log("Installing Windows-specific dependencies...", "INFO")
        
        windows_deps = [
            ("pywin32>=306", "PyWin32 (Windows API)"),
            ("pywin32-ctypes>=0.2.2", "PyWin32-ctypes (Windows types)"),
        ]
        
        success_count = 0
        for package, description in windows_deps:
            if self.install_package(package, description):
                success_count += 1
        
        self.log(f"Windows dependencies installation: {success_count}/{len(windows_deps)} successful", "INFO")
        return True  # Windows deps are optional
    
    def verify_installation(self) -> bool:
        """Verify that key packages are properly installed."""
        self.log("Verifying installation...", "INFO")
        
        critical_packages = [
            ("PyInstaller", "pyinstaller"),
            ("Pygame", "pygame"),
            ("Pyperclip", "pyperclip"),
            ("Pillow", "PIL"),
        ]
        
        verification_success = True
        for display_name, import_name in critical_packages:
            try:
                if import_name == "pyinstaller":
                    # Special check for PyInstaller
                    result = subprocess.run([
                        sys.executable, '-m', 'PyInstaller', '--version'
                    ], capture_output=True, text=True, check=True)
                    version = result.stdout.strip()
                    self.log(f"{display_name} {version} ✓", "SUCCESS")
                else:
                    # Regular import check
                    __import__(import_name)
                    self.log(f"{display_name} ✓", "SUCCESS")
                    
            except (ImportError, subprocess.CalledProcessError, FileNotFoundError):
                self.log(f"{display_name} ❌", "ERROR")
                verification_success = False
        
        return verification_success
    
    def install_all_dependencies(self) -> bool:
        """Install all dependencies."""
        self.log("=" * 80, "INFO")
        self.log("WOW Bingo Game - Build Dependencies Installer", "INFO")
        self.log("=" * 80, "INFO")
        
        # Check Python version
        if not self.check_python_version():
            return False
        
        # Upgrade pip
        self.upgrade_pip()
        
        # Install dependencies in order
        steps = [
            ("Build Tools", self.install_build_tools),
            ("Game Dependencies", self.install_game_dependencies),
            ("Optional Dependencies", self.install_optional_dependencies),
            ("Windows Dependencies", self.install_windows_dependencies),
        ]
        
        overall_success = True
        for step_name, step_func in steps:
            self.log(f"Step: {step_name}", "INFO")
            if not step_func():
                self.log(f"Step {step_name} had issues", "WARNING")
                # Don't fail completely, continue with other steps
        
        # Verify installation
        verification_success = self.verify_installation()
        
        # Final summary
        self.log("=" * 80, "INFO")
        self.log("INSTALLATION SUMMARY", "INFO")
        self.log("=" * 80, "INFO")
        
        total_time = time.time() - self.start_time
        self.log(f"Total installation time: {total_time:.1f} seconds", "INFO")
        self.log(f"Successfully installed: {len(self.installed_packages)} packages", "SUCCESS")
        
        if self.installed_packages:
            self.log("Installed packages:", "INFO")
            for package in self.installed_packages:
                self.log(f"  ✅ {package}", "SUCCESS")
        
        if self.failed_packages:
            self.log(f"Failed to install: {len(self.failed_packages)} packages", "WARNING")
            for package in self.failed_packages:
                self.log(f"  ❌ {package}", "ERROR")
        
        if verification_success:
            self.log("🎉 All critical dependencies verified successfully!", "SUCCESS")
            self.log("You can now build the game using:", "INFO")
            self.log("  python build_executable.py", "INFO")
            self.log("  or double-click: build_game.bat", "INFO")
        else:
            self.log("⚠️ Some critical dependencies failed verification", "WARNING")
            self.log("Try running this script again or install manually", "WARNING")
        
        return verification_success


def main():
    """Main entry point."""
    installer = DependencyInstaller()
    
    try:
        success = installer.install_all_dependencies()
        
        if success:
            print("\n" + "="*60)
            print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("You can now build your game executable!")
            print("\nNext steps:")
            print("1. Run: python build_executable.py")
            print("2. Or double-click: build_game.bat")
            print("3. Or run the complete workflow: build_and_package.bat")
        else:
            print("\n" + "="*60)
            print("⚠️ INSTALLATION HAD SOME ISSUES")
            print("="*60)
            print("Some packages may have failed to install.")
            print("You can try building anyway, or run this script again.")
        
        input("\nPress Enter to exit...")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\nInstallation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error during installation: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
