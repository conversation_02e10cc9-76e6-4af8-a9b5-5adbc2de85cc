import random
from bingo_card import Bing<PERSON><PERSON><PERSON>, create_card_for_player

class BingoGameState:
    """Enum-like class to track game states"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    WINNER_DECLARED = "winner_declared"
    GAME_OVER = "game_over"

class BingoLogic:
    """
    Core bingo game logic, managing the relationship between players, cards,
    and tracking winners.
    """

    def __init__(self, game):
        """
        Initialize the BingoLogic with a reference to the main game instance

        Args:
            game: Reference to the main BingoGame instance
        """
        self.game = game
        self.player_cards = {}  # Maps cartela_no to BingoCard
        self.winners = []  # List of winners (cartela_no)
        self.game_state = BingoGameState.NOT_STARTED
        self.winning_patterns = [
            "Row 1", "Row 2", "Row 3", "Row 4", "Row 5",
            "Column 1", "Column 2", "Column 3", "Column 4", "Column 5",
            "Diagonal (top-left to bottom-right)",
            "Diagonal (top-right to bottom-left)",
            "Four Corners",
            "Blackout/Coverall"
        ]

    def initialize_game(self):
        """Initialize or reset the game state"""
        self.winners = []
        self.game_state = BingoGameState.NOT_STARTED

        # Reset existing cards
        for card in self.player_cards.values():
            card.reset()

    def start_game(self):
        """Start a new bingo game"""
        self.initialize_game()
        self.game_state = BingoGameState.IN_PROGRESS

        # Ensure all players have cards
        self.ensure_all_players_have_cards()

    def ensure_all_players_have_cards(self):
        """Make sure all players in the game have bingo cards"""
        for player in self.game.players:
            if player.cartela_no not in self.player_cards:
                # Create a new card for this player
                self.player_cards[player.cartela_no] = create_card_for_player(player.cartela_no)

    def get_card_for_player(self, cartela_no):
        """Get the BingoCard for a player, creating one if it doesn't exist"""
        if cartela_no not in self.player_cards:
            self.player_cards[cartela_no] = create_card_for_player(cartela_no)
        return self.player_cards[cartela_no]

    def process_called_number(self, number):
        """
        Process a newly called number, marking cards and checking for winners

        Returns:
            list: List of winning cartela_no's (empty if no winners)
        """
        if self.game_state != BingoGameState.IN_PROGRESS:
            return []

        # Make a copy of the keys to avoid "dictionary changed size during iteration" error
        cartela_numbers = list(self.player_cards.keys())

        # Mark the number on all player cards
        for cartela_no in cartela_numbers:
            if cartela_no in self.player_cards:  # Check if still exists
                card = self.player_cards[cartela_no]
                card.mark_number(number)

        # Check for winners after marking
        return self.check_for_winners()

    def check_for_winners(self):
        """
        Check all player cards for winners

        Returns:
            list: List of winning cartela_no's (empty if no winners)
        """
        new_winners = []

        # Get the current list of called numbers from the game
        called_numbers = self.game.called_numbers

        # Skip if no numbers called yet
        if not called_numbers:
            return []

        # Make a copy of the keys to avoid "dictionary changed size during iteration" error
        # This happens if get_card_for_player is called during pattern checking
        cartela_numbers = list(self.player_cards.keys())

        # Check each player's card
        for cartela_no in cartela_numbers:
            # Skip cards that have already won
            if cartela_no in self.winners:
                continue

            # Get the card (safely)
            if cartela_no not in self.player_cards:
                continue

            card = self.player_cards[cartela_no]

            # Check for winning patterns
            if card.check_winning_patterns(called_numbers):
                # Found a winner!
                self.winners.append(cartela_no)
                new_winners.append(cartela_no)

        # If we have any winners, update game state
        if new_winners:
            self.game_state = BingoGameState.WINNER_DECLARED

            # ENHANCED: Automatically trigger game completion recording for detected winners
            try:
                # Check if we have a game state handler to record the completion
                if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'record_automatic_winner'):
                    for winner_cartella in new_winners:
                        print(f"AUTO-DETECTED WINNER: Cartella #{winner_cartella}")
                        self.game.game_state.record_automatic_winner(winner_cartella)
                else:
                    print(f"AUTO-DETECTED WINNERS: {new_winners} - but no game state handler available")
            except Exception as e:
                print(f"Error recording automatic winner: {e}")

        return new_winners

    def get_winning_pattern(self, cartela_no):
        """Get the winning pattern for a specific cartela number"""
        if cartela_no in self.player_cards and self.player_cards[cartela_no].has_won:
            return self.player_cards[cartela_no].winning_pattern
        return None

    def calculate_prize_distribution(self):
        """
        Calculate prize distribution among winners

        Returns:
            dict: Mapping of cartela_no to prize amount
        """
        prizes = {}

        if not self.winners:
            return prizes

        # Get the prize pool from the game
        total_prize = self.game.prize_pool

        # Divide prize equally among winners
        per_winner = total_prize / len(self.winners)

        for winner in self.winners:
            prizes[winner] = per_winner

        return prizes

    def declare_winner(self, cartela_no):
        """
        Manually declare a winner (for verification or testing)

        Returns:
            bool: True if successful, False if already a winner or invalid
        """
        if cartela_no not in self.player_cards:
            return False

        if cartela_no in self.winners:
            return False

        card = self.player_cards[cartela_no]
        called_numbers = self.game.called_numbers

        # Verify that the card has a winning pattern
        if card.check_winning_patterns(called_numbers):
            self.winners.append(cartela_no)
            self.game_state = BingoGameState.WINNER_DECLARED
            return True

        return False

    def end_game(self):
        """End the current game"""
        self.game_state = BingoGameState.GAME_OVER

    def get_game_statistics(self):
        """Get statistics about the current/completed game"""
        stats = {
            "state": self.game_state,
            "total_players": len(self.player_cards),
            "called_numbers_count": len(self.game.called_numbers) if hasattr(self.game, "called_numbers") else 0,
            "winners_count": len(self.winners),
            "winners": self.winners.copy()
        }
        return stats