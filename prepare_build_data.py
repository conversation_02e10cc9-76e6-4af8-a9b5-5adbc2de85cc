#!/usr/bin/env python3
"""
Prepare Build Data Script
=========================
This script ensures all required data files exist for Nuitka compilation.
It creates missing files with default values to prevent build failures.
"""

import os
import json
import sqlite3
from pathlib import Path

def ensure_directory(path):
    """Ensure directory exists."""
    Path(path).mkdir(parents=True, exist_ok=True)
    print(f"✓ Directory ensured: {path}")

def create_json_file(filepath, default_data):
    """Create JSON file if it doesn't exist."""
    if not os.path.exists(filepath):
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, indent=2)
        print(f"✓ Created: {filepath}")
    else:
        print(f"✓ Exists: {filepath}")

def create_empty_db(filepath, tables=None):
    """Create empty SQLite database if it doesn't exist."""
    if not os.path.exists(filepath):
        conn = sqlite3.connect(filepath)
        if tables:
            for table_sql in tables:
                conn.execute(table_sql)
        conn.commit()
        conn.close()
        print(f"✓ Created: {filepath}")
    else:
        print(f"✓ Exists: {filepath}")

def create_empty_file(filepath, content=""):
    """Create empty file if it doesn't exist."""
    if not os.path.exists(filepath):
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ Created: {filepath}")
    else:
        print(f"✓ Exists: {filepath}")

def main():
    """Main function to prepare build data."""
    print("🔧 Preparing Build Data for Nuitka Compilation")
    print("=" * 50)
    
    # Ensure data directory exists
    ensure_directory("data")
    ensure_directory("data/cache")
    ensure_directory("data/metrics")
    
    # Create essential JSON files
    print("\n📄 Creating JSON Files...")
    
    create_json_file("data/admin_sessions.json", {
        "sessions": {},
        "last_cleanup": None,
        "session_timeout": 3600,
        "max_sessions": 10
    })
    
    create_json_file("data/players.json", [])
    
    create_json_file("data/game_settings.json", {
        "commission_percentage": 20.0,
        "bet_amount": 50,
        "prize_pool": 0,
        "prize_pool_manual_override": False
    })
    
    create_json_file("data/ui_state.json", {
        "selected_cartella_numbers": [],
        "bet_amount": 50,
        "prize_pool": 0,
        "prize_pool_manual_override": False
    })
    
    create_json_file("data/settings.json", {
        "audio": {
            "master_volume": 0.7,
            "sound_effects": True,
            "background_music": True
        },
        "display": {
            "fullscreen": False,
            "resolution": "1024x768"
        },
        "game": {
            "auto_save": True,
            "remember_cartella_numbers": False
        }
    })
    
    create_json_file("data/stats.json", {
        "total_games": 0,
        "total_earnings": 0,
        "daily_games": 0,
        "daily_earnings": 0
    })
    
    create_json_file("data/usage_log.json", {
        "sessions": [],
        "total_sessions": 0,
        "last_session": None
    })
    
    create_json_file("data/cache/stats_cache.json", {
        "last_updated": None,
        "cached_data": {}
    })
    
    create_json_file("data/metrics/stats_metrics.json", {
        "performance": {},
        "usage": {},
        "errors": []
    })
    
    # Create essential database files
    print("\n🗄️ Creating Database Files...")
    
    # Stats database
    stats_tables = [
        """CREATE TABLE IF NOT EXISTS game_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date_time TEXT,
            username TEXT,
            stake REAL,
            total_prize REAL,
            fee REAL,
            status TEXT,
            house TEXT
        )""",
        """CREATE TABLE IF NOT EXISTS daily_stats (
            date TEXT PRIMARY KEY,
            games_played INTEGER DEFAULT 0,
            total_earnings REAL DEFAULT 0
        )"""
    ]
    create_empty_db("data/stats.db", stats_tables)
    create_empty_db("data/stats_new.db", stats_tables)
    
    # External PCs database
    external_tables = [
        """CREATE TABLE IF NOT EXISTS connections (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address TEXT,
            connection_time TEXT,
            status TEXT
        )"""
    ]
    create_empty_db("data/external_pcs.db", external_tables)
    
    # Vouchers database
    voucher_tables = [
        """CREATE TABLE IF NOT EXISTS vouchers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            amount REAL,
            created_at TEXT,
            redeemed_at TEXT,
            status TEXT DEFAULT 'active'
        )"""
    ]
    create_empty_db("data/vouchers.db", voucher_tables)
    
    # Create log files
    print("\n📝 Creating Log Files...")
    create_empty_file("data/game_stats_integration.log", "# Game Stats Integration Log\n")
    create_empty_file("data/thread_safe_db.log", "# Thread Safe DB Log\n")
    
    # Create key file for voucher system
    print("\n🔐 Creating Security Files...")
    if not os.path.exists("data/voucher_secret.key"):
        # Create a simple key file (in production, this should be more secure)
        with open("data/voucher_secret.key", "wb") as f:
            f.write(b"default_secret_key_for_voucher_system_change_in_production")
        print("✓ Created: data/voucher_secret.key")
    else:
        print("✓ Exists: data/voucher_secret.key")
    
    print("\n✅ Build Data Preparation Complete!")
    print("All required files have been created or verified.")
    print("You can now run the Nuitka build script safely.")

if __name__ == "__main__":
    main()
