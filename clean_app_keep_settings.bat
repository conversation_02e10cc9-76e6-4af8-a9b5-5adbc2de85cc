@echo off
echo ================================================================
echo    ENHANCED Bingo Game App Cleaner (Keep Settings)
echo ================================================================
echo.
echo This script will COMPREHENSIVELY clean ALL app data while preserving your settings:
echo.
echo ✓ Player data and cartellas
echo ✓ Game history and statistics
echo ✓ Total earnings (reset to 0)
echo ✓ All databases and backups
echo ✓ Payment/voucher data and credits
echo ✓ Cache files and directories
echo ✓ Log files
echo ✓ Export files
echo ✓ Temporary files
echo ✓ Configuration files
echo.
echo ⚠️  WARNING: This will permanently delete ALL game data and history!
echo ✅ Your settings will be preserved.
echo 📝 Bingo boards will be preserved.
echo.
set /p CONFIRM=Are you sure you want to proceed? (Y/N):

if /i "%CONFIRM%" neq "Y" (
    echo.
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting enhanced cleaning process...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Run the enhanced clean_app.py script with --keep-settings flag
python clean_app.py --keep-settings

echo.
echo ================================================================
echo    🎉 ENHANCED CLEANING COMPLETE! 🎉
echo ================================================================
echo.
echo ✅ ALL app data has been comprehensively cleaned
echo ✅ Your settings have been preserved
echo ✅ Game history and total earnings have been reset
echo ✅ All databases, cache, and logs have been cleared
echo.
echo 🚀 You can now start the game with a completely fresh state!
echo.
pause
