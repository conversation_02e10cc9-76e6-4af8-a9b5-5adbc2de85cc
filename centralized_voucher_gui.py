#!/usr/bin/env python3
"""
Centralized Voucher Manager GUI

User-friendly interface for the centralized voucher generation system.
Allows managers to generate vouchers for external PCs using their UUIDs.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List

try:
    from centralized_voucher_manager import CentralizedVoucherManager
    MANAGER_AVAILABLE = True
except ImportError:
    MANAGER_AVAILABLE = False
    print("Error: centralized_voucher_manager.py not found")

class CentralizedVoucherGUI:
    """GUI for centralized voucher management."""

    def __init__(self):
        """Initialize the GUI."""
        if not MANAGER_AVAILABLE:
            messagebox.showerror("Error", "Centralized voucher manager not available")
            return

        self.manager = CentralizedVoucherManager()
        self.root = tk.Tk()
        self.setup_gui()

    def setup_gui(self):
        """Set up the main GUI."""
        self.root.title("Centralized Voucher Manager")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs
        self.create_generate_tab()
        self.create_batch_tab()
        self.create_pcs_tab()
        self.create_history_tab()
        self.create_validation_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Load initial data
        self.refresh_all_data()

    def create_generate_tab(self):
        """Create the single voucher generation tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Generate Single Voucher")

        # Main container
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Generate Single Voucher",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Input frame
        input_frame = ttk.LabelFrame(main_frame, text="Voucher Parameters", padding=15)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # Target UUID
        ttk.Label(input_frame, text="Target Machine UUID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.single_uuid_var = tk.StringVar()
        uuid_frame = ttk.Frame(input_frame)
        uuid_frame.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        self.single_uuid_entry = ttk.Entry(uuid_frame, textvariable=self.single_uuid_var, width=40)
        self.single_uuid_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(uuid_frame, text="Select PC", command=self.select_pc_for_single,
                  width=12).pack(side=tk.RIGHT, padx=(5, 0))

        # Amount
        ttk.Label(input_frame, text="Credit Amount:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.single_amount_var = tk.StringVar(value="100")
        ttk.Entry(input_frame, textvariable=self.single_amount_var, width=20).grid(
            row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Days valid
        ttk.Label(input_frame, text="Days Valid:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.single_days_var = tk.StringVar(value="30")
        ttk.Entry(input_frame, textvariable=self.single_days_var, width=20).grid(
            row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Share percentage
        ttk.Label(input_frame, text="Share %:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.single_share_var = tk.StringVar(value="80")
        ttk.Entry(input_frame, textvariable=self.single_share_var, width=20).grid(
            row=3, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Generator type
        ttk.Label(input_frame, text="Generator Type:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.single_generator_var = tk.StringVar(value="compact")
        generator_combo = ttk.Combobox(input_frame, textvariable=self.single_generator_var,
                                     values=list(self.manager.generators.keys()), state="readonly")
        generator_combo.grid(row=4, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Notes
        ttk.Label(input_frame, text="Notes:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.single_notes_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.single_notes_var, width=40).grid(
            row=5, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        input_frame.columnconfigure(1, weight=1)

        # Generate button
        generate_btn = ttk.Button(main_frame, text="Generate Voucher",
                                command=self.generate_single_voucher,
                                style="Accent.TButton")
        generate_btn.pack(pady=10)

        # Result frame
        result_frame = ttk.LabelFrame(main_frame, text="Generated Voucher", padding=15)
        result_frame.pack(fill=tk.BOTH, expand=True)

        self.single_result_text = tk.Text(result_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.single_result_text.yview)
        self.single_result_text.configure(yscrollcommand=scrollbar.set)

        self.single_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Copy button
        ttk.Button(result_frame, text="Copy Voucher",
                  command=self.copy_single_voucher).pack(pady=(10, 0))

    def create_batch_tab(self):
        """Create the batch generation tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Generate Batch")

        # Main container
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Generate Voucher Batch",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Input frame
        input_frame = ttk.LabelFrame(main_frame, text="Batch Parameters", padding=15)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # Target UUID
        ttk.Label(input_frame, text="Target Machine UUID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.batch_uuid_var = tk.StringVar()
        uuid_frame = ttk.Frame(input_frame)
        uuid_frame.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        self.batch_uuid_entry = ttk.Entry(uuid_frame, textvariable=self.batch_uuid_var, width=40)
        self.batch_uuid_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(uuid_frame, text="Select PC", command=self.select_pc_for_batch,
                  width=12).pack(side=tk.RIGHT, padx=(5, 0))

        # Count
        ttk.Label(input_frame, text="Voucher Count:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.batch_count_var = tk.StringVar(value="10")
        ttk.Entry(input_frame, textvariable=self.batch_count_var, width=20).grid(
            row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Amount per voucher
        ttk.Label(input_frame, text="Amount per Voucher:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.batch_amount_var = tk.StringVar(value="100")
        ttk.Entry(input_frame, textvariable=self.batch_amount_var, width=20).grid(
            row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Days valid
        ttk.Label(input_frame, text="Days Valid:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.batch_days_var = tk.StringVar(value="30")
        ttk.Entry(input_frame, textvariable=self.batch_days_var, width=20).grid(
            row=3, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Share percentage
        ttk.Label(input_frame, text="Share %:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.batch_share_var = tk.StringVar(value="80")
        ttk.Entry(input_frame, textvariable=self.batch_share_var, width=20).grid(
            row=4, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Generator type
        ttk.Label(input_frame, text="Generator Type:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.batch_generator_var = tk.StringVar(value="compact")
        generator_combo = ttk.Combobox(input_frame, textvariable=self.batch_generator_var,
                                     values=list(self.manager.generators.keys()), state="readonly")
        generator_combo.grid(row=5, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # Batch name
        ttk.Label(input_frame, text="Batch Name:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.batch_name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.batch_name_var, width=40).grid(
            row=6, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        # Export path
        ttk.Label(input_frame, text="Export Path:").grid(row=7, column=0, sticky=tk.W, pady=5)
        export_frame = ttk.Frame(input_frame)
        export_frame.grid(row=7, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        self.batch_export_var = tk.StringVar()
        ttk.Entry(export_frame, textvariable=self.batch_export_var).pack(
            side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(export_frame, text="Browse", command=self.browse_export_path,
                  width=10).pack(side=tk.RIGHT, padx=(5, 0))

        input_frame.columnconfigure(1, weight=1)

        # Generate button
        generate_btn = ttk.Button(main_frame, text="Generate Batch",
                                command=self.generate_batch_vouchers,
                                style="Accent.TButton")
        generate_btn.pack(pady=10)

        # Progress bar
        self.batch_progress = ttk.Progressbar(main_frame, mode='determinate')
        self.batch_progress.pack(fill=tk.X, pady=(0, 10))

        # Result frame
        result_frame = ttk.LabelFrame(main_frame, text="Batch Results", padding=15)
        result_frame.pack(fill=tk.BOTH, expand=True)

        self.batch_result_text = tk.Text(result_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.batch_result_text.yview)
        self.batch_result_text.configure(yscrollcommand=scrollbar.set)

        self.batch_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_pcs_tab(self):
        """Create the external PCs management tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="External PCs")

        # Main container
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="External PC Management",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Add PC frame
        add_frame = ttk.LabelFrame(main_frame, text="Register New PC", padding=15)
        add_frame.pack(fill=tk.X, pady=(0, 20))

        # UUID
        ttk.Label(add_frame, text="UUID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.new_pc_uuid_var = tk.StringVar()
        ttk.Entry(add_frame, textvariable=self.new_pc_uuid_var, width=40).grid(
            row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        # Name
        ttk.Label(add_frame, text="Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.new_pc_name_var = tk.StringVar()
        ttk.Entry(add_frame, textvariable=self.new_pc_name_var, width=40).grid(
            row=1, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        # Description
        ttk.Label(add_frame, text="Description:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.new_pc_desc_var = tk.StringVar()
        ttk.Entry(add_frame, textvariable=self.new_pc_desc_var, width=40).grid(
            row=2, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        # Notes
        ttk.Label(add_frame, text="Notes:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.new_pc_notes_var = tk.StringVar()
        ttk.Entry(add_frame, textvariable=self.new_pc_notes_var, width=40).grid(
            row=3, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        add_frame.columnconfigure(1, weight=1)

        # Add button
        ttk.Button(add_frame, text="Register PC", command=self.register_new_pc).grid(
            row=4, column=1, sticky=tk.E, pady=(10, 0))

        # PCs list frame
        list_frame = ttk.LabelFrame(main_frame, text="Registered PCs", padding=15)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Treeview for PCs
        columns = ("UUID", "Name", "Description", "Total Vouchers", "Last Generated")
        self.pcs_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

        for col in columns:
            self.pcs_tree.heading(col, text=col)
            self.pcs_tree.column(col, width=150)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.pcs_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.pcs_tree.xview)
        self.pcs_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.pcs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Buttons frame
        buttons_frame = ttk.Frame(list_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Refresh", command=self.refresh_pcs_list).pack(side=tk.LEFT)
        ttk.Button(buttons_frame, text="Select for Generation",
                  command=self.select_pc_from_list).pack(side=tk.LEFT, padx=(10, 0))

    def create_history_tab(self):
        """Create the voucher history tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="History")

        # Main container
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Voucher Generation History",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Filter frame
        filter_frame = ttk.LabelFrame(main_frame, text="Filters", padding=15)
        filter_frame.pack(fill=tk.X, pady=(0, 20))

        # UUID filter
        ttk.Label(filter_frame, text="Filter by UUID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.history_uuid_var = tk.StringVar()
        uuid_frame = ttk.Frame(filter_frame)
        uuid_frame.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        ttk.Entry(uuid_frame, textvariable=self.history_uuid_var, width=30).pack(
            side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(uuid_frame, text="Filter", command=self.filter_history).pack(
            side=tk.RIGHT, padx=(5, 0))
        ttk.Button(uuid_frame, text="Clear", command=self.clear_history_filter).pack(
            side=tk.RIGHT, padx=(5, 0))

        # Limit
        ttk.Label(filter_frame, text="Limit:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.history_limit_var = tk.StringVar(value="100")
        ttk.Entry(filter_frame, textvariable=self.history_limit_var, width=10).grid(
            row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        filter_frame.columnconfigure(1, weight=1)

        # History list frame
        list_frame = ttk.LabelFrame(main_frame, text="Voucher History", padding=15)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Treeview for history
        columns = ("Voucher Code", "Target UUID", "Amount", "Share", "Generator", "Generated", "Status")
        self.history_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == "Voucher Code":
                self.history_tree.column(col, width=200)
            elif col == "Target UUID":
                self.history_tree.column(col, width=150)
            else:
                self.history_tree.column(col, width=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.history_tree.xview)
        self.history_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Buttons frame
        buttons_frame = ttk.Frame(list_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Refresh", command=self.refresh_history).pack(side=tk.LEFT)
        ttk.Button(buttons_frame, text="Copy Voucher", command=self.copy_selected_voucher).pack(
            side=tk.LEFT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Export History", command=self.export_history).pack(
            side=tk.LEFT, padx=(10, 0))

    def create_validation_tab(self):
        """Create the voucher validation tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Validation")

        # Main container
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="Voucher Validation",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Input frame
        input_frame = ttk.LabelFrame(main_frame, text="Validation Parameters", padding=15)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # Voucher code
        ttk.Label(input_frame, text="Voucher Code:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.validate_voucher_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.validate_voucher_var, width=40).grid(
            row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        # Target UUID
        ttk.Label(input_frame, text="Target UUID:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.validate_uuid_var = tk.StringVar()
        uuid_frame = ttk.Frame(input_frame)
        uuid_frame.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=(10, 0))

        ttk.Entry(uuid_frame, textvariable=self.validate_uuid_var, width=40).pack(
            side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(uuid_frame, text="Select PC", command=self.select_pc_for_validation,
                  width=12).pack(side=tk.RIGHT, padx=(5, 0))

        input_frame.columnconfigure(1, weight=1)

        # Validate button
        validate_btn = ttk.Button(main_frame, text="Validate Voucher",
                                command=self.validate_voucher,
                                style="Accent.TButton")
        validate_btn.pack(pady=10)

        # Result frame
        result_frame = ttk.LabelFrame(main_frame, text="Validation Result", padding=15)
        result_frame.pack(fill=tk.BOTH, expand=True)

        self.validation_result_text = tk.Text(result_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.validation_result_text.yview)
        self.validation_result_text.configure(yscrollcommand=scrollbar.set)

        self.validation_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def generate_single_voucher(self):
        """Generate a single voucher."""
        try:
            uuid = self.single_uuid_var.get().strip()
            amount = int(self.single_amount_var.get())
            days = int(self.single_days_var.get())
            share = int(self.single_share_var.get())
            generator_type = self.single_generator_var.get()
            notes = self.single_notes_var.get().strip()

            if not uuid:
                messagebox.showerror("Error", "Please enter a target UUID")
                return

            self.status_var.set("Generating voucher...")
            self.root.update()

            voucher = self.manager.generate_voucher(
                uuid, amount, generator_type, days, share, notes
            )

            if voucher:
                result = f"✓ Voucher Generated Successfully!\n\n"
                result += f"Voucher Code: {voucher}\n"
                result += f"Target UUID: {uuid}\n"
                result += f"Amount: {amount} credits\n"
                result += f"Share: {share}%\n"
                result += f"Valid for: {days} days\n"
                result += f"Generator: {generator_type}\n"
                if notes:
                    result += f"Notes: {notes}\n"
                result += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

                self.single_result_text.delete(1.0, tk.END)
                self.single_result_text.insert(1.0, result)
                self.status_var.set("Voucher generated successfully")

                # Store the voucher for copying
                self.last_generated_voucher = voucher

            else:
                messagebox.showerror("Error", "Failed to generate voucher")
                self.status_var.set("Failed to generate voucher")

        except ValueError as e:
            messagebox.showerror("Error", f"Invalid input: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Error generating voucher: {e}")
            self.status_var.set("Error")

    def copy_single_voucher(self):
        """Copy the generated voucher to clipboard."""
        if hasattr(self, 'last_generated_voucher'):
            self.root.clipboard_clear()
            self.root.clipboard_append(self.last_generated_voucher)
            self.status_var.set("Voucher copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No voucher to copy")

    def select_pc_for_single(self):
        """Open PC selection dialog for single voucher."""
        self.open_pc_selection_dialog(self.single_uuid_var)

    def select_pc_for_batch(self):
        """Open PC selection dialog for batch generation."""
        self.open_pc_selection_dialog(self.batch_uuid_var)

    def open_pc_selection_dialog(self, target_var):
        """Open a dialog to select a registered PC."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Select External PC")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # PC list
        frame = ttk.Frame(dialog)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(frame, text="Select a registered PC:", font=("Arial", 12)).pack(pady=(0, 10))

        # Listbox with scrollbar
        listbox_frame = ttk.Frame(frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        listbox = tk.Listbox(listbox_frame)
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Load PCs
        pcs = self.manager.get_registered_pcs()
        pc_data = {}

        for pc in pcs:
            display_text = f"{pc['uuid'][:8]}... - {pc['name'] or 'Unnamed'}"
            listbox.insert(tk.END, display_text)
            pc_data[display_text] = pc['uuid']

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def select_pc():
            selection = listbox.curselection()
            if selection:
                selected_text = listbox.get(selection[0])
                target_var.set(pc_data[selected_text])
                dialog.destroy()
            else:
                messagebox.showwarning("Warning", "Please select a PC")

        ttk.Button(button_frame, text="Select", command=select_pc).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.RIGHT, padx=(0, 10))

    def refresh_all_data(self):
        """Refresh all data in the GUI."""
        self.refresh_pcs_list()
        self.status_var.set("Data refreshed")

    def refresh_pcs_list(self):
        """Refresh the PCs list."""
        # Clear existing items
        for item in self.pcs_tree.get_children():
            self.pcs_tree.delete(item)

        # Load PCs
        pcs = self.manager.get_registered_pcs()
        for pc in pcs:
            self.pcs_tree.insert("", tk.END, values=(
                pc['uuid'][:16] + "..." if len(pc['uuid']) > 16 else pc['uuid'],
                pc['name'] or "Unnamed",
                pc['description'] or "",
                pc['total_vouchers_generated'],
                pc['last_voucher_generated'] or "Never"
            ))

    def register_new_pc(self):
        """Register a new external PC."""
        try:
            uuid = self.new_pc_uuid_var.get().strip()
            name = self.new_pc_name_var.get().strip()
            description = self.new_pc_desc_var.get().strip()
            notes = self.new_pc_notes_var.get().strip()

            if not uuid:
                messagebox.showerror("Error", "Please enter a UUID")
                return

            if self.manager.register_external_pc(uuid, name, description, notes):
                messagebox.showinfo("Success", "PC registered successfully")
                # Clear form
                self.new_pc_uuid_var.set("")
                self.new_pc_name_var.set("")
                self.new_pc_desc_var.set("")
                self.new_pc_notes_var.set("")
                # Refresh list
                self.refresh_pcs_list()
            else:
                messagebox.showerror("Error", "Failed to register PC")

        except Exception as e:
            messagebox.showerror("Error", f"Error registering PC: {e}")

    def select_pc_from_list(self):
        """Select a PC from the list for voucher generation."""
        selection = self.pcs_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a PC from the list")
            return

        item = self.pcs_tree.item(selection[0])
        uuid_display = item['values'][0]

        # Get full UUID from database
        pcs = self.manager.get_registered_pcs()
        for pc in pcs:
            if pc['uuid'].startswith(uuid_display.replace("...", "")):
                # Set in both single and batch tabs
                self.single_uuid_var.set(pc['uuid'])
                self.batch_uuid_var.set(pc['uuid'])
                self.status_var.set(f"Selected PC: {pc['name'] or 'Unnamed'}")
                break

    def generate_batch_vouchers(self):
        """Generate a batch of vouchers."""
        try:
            uuid = self.batch_uuid_var.get().strip()
            count = int(self.batch_count_var.get())
            amount = int(self.batch_amount_var.get())
            days = int(self.batch_days_var.get())
            share = int(self.batch_share_var.get())
            generator_type = self.batch_generator_var.get()
            batch_name = self.batch_name_var.get().strip()
            export_path = self.batch_export_var.get().strip()

            if not uuid:
                messagebox.showerror("Error", "Please enter a target UUID")
                return

            if count <= 0 or count > 1000:
                messagebox.showerror("Error", "Count must be between 1 and 1000")
                return

            # Set up progress bar
            self.batch_progress['maximum'] = count
            self.batch_progress['value'] = 0

            self.status_var.set(f"Generating batch of {count} vouchers...")
            self.root.update()

            # Generate batch
            vouchers = self.manager.generate_batch(
                uuid, count, amount, generator_type, days, share, batch_name, export_path
            )

            if vouchers:
                result = f"✓ Batch Generated Successfully!\n\n"
                result += f"Generated: {len(vouchers)} vouchers\n"
                result += f"Target UUID: {uuid}\n"
                result += f"Amount per voucher: {amount} credits\n"
                result += f"Total value: {len(vouchers) * amount} credits\n"
                result += f"Share: {share}%\n"
                result += f"Valid for: {days} days\n"
                result += f"Generator: {generator_type}\n"
                if batch_name:
                    result += f"Batch name: {batch_name}\n"
                if export_path:
                    result += f"Exported to: {export_path}\n"
                result += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

                # Show first few vouchers
                result += "Sample vouchers:\n"
                for i, voucher in enumerate(vouchers[:5]):
                    result += f"{i+1}. {voucher}\n"

                if len(vouchers) > 5:
                    result += f"... and {len(vouchers) - 5} more\n"

                self.batch_result_text.delete(1.0, tk.END)
                self.batch_result_text.insert(1.0, result)
                self.status_var.set(f"Batch of {len(vouchers)} vouchers generated successfully")

                # Complete progress bar
                self.batch_progress['value'] = count

            else:
                messagebox.showerror("Error", "Failed to generate batch")
                self.status_var.set("Failed to generate batch")

        except ValueError as e:
            messagebox.showerror("Error", f"Invalid input: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Error generating batch: {e}")
            self.status_var.set("Error")

    def browse_export_path(self):
        """Browse for export file path."""
        filename = filedialog.asksaveasfilename(
            title="Save Batch Export",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.batch_export_var.set(filename)

    def filter_history(self):
        """Filter voucher history by UUID."""
        self.refresh_history()

    def clear_history_filter(self):
        """Clear history filter."""
        self.history_uuid_var.set("")
        self.refresh_history()

    def refresh_history(self):
        """Refresh the voucher history."""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        try:
            # Get filter parameters
            filter_uuid = self.history_uuid_var.get().strip() or None
            limit = int(self.history_limit_var.get()) if self.history_limit_var.get().strip() else 100

            # Load history
            history = self.manager.get_voucher_history(filter_uuid, limit)

            for record in history:
                # Format timestamp
                try:
                    generated_time = datetime.fromisoformat(record['generated_timestamp'].replace('Z', '+00:00'))
                    formatted_time = generated_time.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_time = record['generated_timestamp']

                self.history_tree.insert("", tk.END, values=(
                    record['voucher_code'][:20] + "..." if len(record['voucher_code']) > 20 else record['voucher_code'],
                    record['target_uuid'][:16] + "..." if len(record['target_uuid']) > 16 else record['target_uuid'],
                    record['amount'],
                    f"{record['share']}%",
                    record['generator_type'],
                    formatted_time,
                    record['status']
                ))

            self.status_var.set(f"Loaded {len(history)} history records")

        except Exception as e:
            messagebox.showerror("Error", f"Error loading history: {e}")

    def copy_selected_voucher(self):
        """Copy selected voucher code to clipboard."""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a voucher from the history")
            return

        item = self.history_tree.item(selection[0])
        voucher_display = item['values'][0]

        # Get full voucher code from database
        history = self.manager.get_voucher_history(limit=1000)
        for record in history:
            if record['voucher_code'].startswith(voucher_display.replace("...", "")):
                self.root.clipboard_clear()
                self.root.clipboard_append(record['voucher_code'])
                self.status_var.set("Voucher code copied to clipboard")
                return

        messagebox.showerror("Error", "Could not find full voucher code")

    def export_history(self):
        """Export voucher history to file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Voucher History",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if not filename:
                return

            # Get filter parameters
            filter_uuid = self.history_uuid_var.get().strip() or None
            limit = int(self.history_limit_var.get()) if self.history_limit_var.get().strip() else 1000

            # Load history
            history = self.manager.get_voucher_history(filter_uuid, limit)

            if filename.endswith('.csv'):
                # Export as CSV
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Voucher Code', 'Target UUID', 'Amount', 'Share', 'Generator Type',
                                   'Generated Timestamp', 'Status', 'Notes'])
                    for record in history:
                        writer.writerow([
                            record['voucher_code'],
                            record['target_uuid'],
                            record['amount'],
                            record['share'],
                            record['generator_type'],
                            record['generated_timestamp'],
                            record['status'],
                            record['notes'] or ''
                        ])
            else:
                # Export as JSON
                export_data = {
                    "export_info": {
                        "exported_at": datetime.now().isoformat(),
                        "filter_uuid": filter_uuid,
                        "record_count": len(history)
                    },
                    "history": history
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2)

            messagebox.showinfo("Success", f"History exported to {filename}")
            self.status_var.set(f"Exported {len(history)} records to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Error exporting history: {e}")

    def select_pc_for_validation(self):
        """Open PC selection dialog for validation."""
        self.open_pc_selection_dialog(self.validate_uuid_var)

    def validate_voucher(self):
        """Validate a voucher code."""
        try:
            voucher_code = self.validate_voucher_var.get().strip()
            target_uuid = self.validate_uuid_var.get().strip()

            if not voucher_code:
                messagebox.showerror("Error", "Please enter a voucher code")
                return

            if not target_uuid:
                messagebox.showerror("Error", "Please enter a target UUID")
                return

            self.status_var.set("Validating voucher...")
            self.root.update()

            # Validate voucher
            result = self.manager.validate_voucher_for_uuid(voucher_code, target_uuid)

            # Format result
            validation_text = f"Voucher Validation Result\n"
            validation_text += f"=" * 50 + "\n\n"
            validation_text += f"Voucher Code: {voucher_code}\n"
            validation_text += f"Target UUID: {target_uuid}\n"
            validation_text += f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            if result.get("valid"):
                validation_text += "✓ VOUCHER IS VALID\n\n"
                validation_text += f"Amount: {result.get('amount', 'N/A')} credits\n"
                validation_text += f"Share: {result.get('share', 'N/A')}%\n"

                expiry = result.get('expiry', 0)
                if expiry == 0:
                    validation_text += "Expiry: Never expires\n"
                else:
                    try:
                        expiry_date = datetime.fromtimestamp(expiry)
                        validation_text += f"Expiry: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    except:
                        validation_text += f"Expiry: {expiry}\n"

                validation_text += f"Message: {result.get('message', 'Valid voucher')}\n"
            else:
                validation_text += "✗ VOUCHER IS INVALID\n\n"
                validation_text += f"Reason: {result.get('message', 'Unknown error')}\n"

            self.validation_result_text.delete(1.0, tk.END)
            self.validation_result_text.insert(1.0, validation_text)

            if result.get("valid"):
                self.status_var.set("Voucher is valid")
            else:
                self.status_var.set("Voucher is invalid")

        except Exception as e:
            messagebox.showerror("Error", f"Error validating voucher: {e}")
            self.status_var.set("Validation error")

    def run(self):
        """Run the GUI."""
        self.root.mainloop()


def main():
    """Main function."""
    if not MANAGER_AVAILABLE:
        print("Error: Centralized voucher manager not available")
        return

    app = CentralizedVoucherGUI()
    app.run()


if __name__ == "__main__":
    main()
