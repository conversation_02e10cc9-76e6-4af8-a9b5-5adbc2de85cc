#!/usr/bin/env python3
"""
<PERSON><PERSON>t to insert historical stats data into the database for display on the stats page.
This script will insert the provided earnings data with proper dates.
"""

import sqlite3
import os
import sys
from datetime import datetime

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

def ensure_database_exists():
    """Ensure the stats database exists and has the correct schema."""
    # Create data directory if it doesn't exist
    os.makedirs(os.path.dirname(STATS_DB_PATH), exist_ok=True)
    
    # Create and initialize the database
    conn = sqlite3.connect(STATS_DB_PATH)
    cursor = conn.cursor()
    
    # Create daily_stats table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_stats (
        date TEXT PRIMARY KEY,
        games_played INTEGER DEFAULT 0,
        earnings REAL DEFAULT 0,
        winners INTEGER DEFAULT 0,
        total_players INTEGER DEFAULT 0
    )
    ''')
    
    conn.commit()
    conn.close()
    print(f"✅ Database initialized at: {STATS_DB_PATH}")

def insert_stats_data():
    """Insert the provided historical stats data."""
    
    # Data to insert (date: earnings)
    stats_data = [
        ('2025-05-26', 500.0),  # Monday
        ('2025-05-27', 500.0),  # Tuesday  
        ('2025-05-28', 0.0),    # Wednesday (zero)
        ('2025-05-29', 180.0),  # Thursday
        ('2025-05-30', 600.0),  # Friday
        ('2025-05-31', 750.0),  # Saturday
        ('2025-06-01', 950.0),  # Sunday
    ]
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("📊 Inserting historical stats data...")
        
        for date_str, earnings in stats_data:
            # Check if record already exists
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (date_str,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                cursor.execute('''
                UPDATE daily_stats 
                SET earnings = ?, games_played = CASE WHEN ? > 0 THEN 1 ELSE 0 END
                WHERE date = ?
                ''', (earnings, earnings, date_str))
                print(f"   📝 Updated {date_str}: {earnings} ETB")
            else:
                # Insert new record
                games_played = 1 if earnings > 0 else 0
                cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, ?, ?, ?, ?)
                ''', (date_str, games_played, earnings, 0, 0))
                print(f"   ✅ Inserted {date_str}: {earnings} ETB")
        
        conn.commit()
        conn.close()
        print("✅ All data inserted successfully!")
        
    except Exception as e:
        print(f"❌ Error inserting data: {e}")
        if 'conn' in locals():
            conn.close()
        return False
    
    return True

def verify_data():
    """Verify the inserted data."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("\n📋 Verifying inserted data:")
        cursor.execute('''
        SELECT date, earnings, games_played 
        FROM daily_stats 
        WHERE date BETWEEN '2025-05-26' AND '2025-06-01'
        ORDER BY date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   Date       | Earnings | Games")
            print("   -----------|----------|------")
            for date_str, earnings, games in results:
                # Convert date to show day name
                try:
                    dt = datetime.strptime(date_str, '%Y-%m-%d')
                    day_name = dt.strftime('%a')
                    print(f"   {date_str} ({day_name}) | {earnings:>8.1f} | {games:>5}")
                except:
                    print(f"   {date_str}     | {earnings:>8.1f} | {games:>5}")
        else:
            print("   No data found in the specified date range.")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying data: {e}")

def main():
    """Main function to run the data insertion."""
    print("🎯 Historical Stats Data Insertion Script")
    print("=" * 50)
    
    # Ensure database exists
    ensure_database_exists()
    
    # Insert the data
    success = insert_stats_data()
    
    if success:
        # Verify the data
        verify_data()
        print("\n🎉 Data insertion completed successfully!")
        print("💡 The stats page should now display this historical data.")
    else:
        print("\n❌ Data insertion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
