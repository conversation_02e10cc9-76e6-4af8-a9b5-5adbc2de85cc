"""
Test script to verify game statistics recording.
This script simulates a game completion and ensures data is properly recorded to the database.
"""

import os
import sys
import time
from datetime import datetime
import json

# Ensure we can import from the current directory
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_db_recording():
    """Test direct recording to thread_safe_db"""
    print("\n" + "="*80)
    print("TESTING DIRECT THREAD_SAFE_DB RECORDING")
    print("="*80)
    
    try:
        import thread_safe_db
        
        # Ensure database exists
        thread_safe_db.ensure_database_exists()
        
        # Get current record count
        conn = thread_safe_db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM game_history')
        initial_count = cursor.fetchone()[0]
        print(f"Initial game_history record count: {initial_count}")
        
        # Create sample game data (non-demo mode)
        game_data = {
            "winner_name": "Test Winner",
            "winner_cartella": 123,
            "claim_type": "Full House",
            "game_duration": 300,
            "player_count": 25,  # More than 2 players to ensure it's not treated as demo
            "prize_amount": 1000,
            "commission_percentage": 20,
            "called_numbers": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            "is_demo_mode": False,  # Explicitly set to False
            "date_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Record the game
        print(f"Recording game data: {json.dumps(game_data, indent=2)}")
        result = thread_safe_db.record_game_completed(game_data)
        print(f"Record result: {result}")
        
        # Verify the record was added
        cursor.execute('SELECT COUNT(*) FROM game_history')
        new_count = cursor.fetchone()[0]
        print(f"New game_history record count: {new_count}")
        
        # Get the latest record
        cursor.execute('SELECT id, date_time, username, players, total_prize FROM game_history ORDER BY id DESC LIMIT 1')
        latest = cursor.fetchone()
        if latest:
            print(f"Latest record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Players={latest[3]}, Prize={latest[4]}")
            
        # Check if count increased
        if new_count > initial_count:
            print("SUCCESS: New record was added to the database!")
        else:
            print("ERROR: Record count did not increase!")
            
        return result
            
    except Exception as e:
        print(f"Error in test_direct_db_recording: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_recording():
    """Test recording through GameStatsIntegration"""
    print("\n" + "="*80)
    print("TESTING GAME_STATS_INTEGRATION RECORDING")
    print("="*80)
    
    try:
        from game_stats_integration import GameStatsIntegration
        import thread_safe_db
        
        # Get current record count
        conn = thread_safe_db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM game_history')
        initial_count = cursor.fetchone()[0]
        print(f"Initial game_history record count: {initial_count}")
        
        # Create sample game data (non-demo mode)
        game_data = {
            "winner_name": "Integration Test Winner",
            "winner_cartella": 456,
            "claim_type": "First Line",
            "game_duration": 250,
            "player_count": 30,  # More than 2 players to ensure it's not treated as demo
            "prize_amount": 1500,
            "commission_percentage": 20,
            "called_numbers": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
            "is_demo_mode": False,  # Explicitly set to False
            "date_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Record the game through the integration layer
        print(f"Recording game data through integration: {json.dumps(game_data, indent=2)}")
        result = GameStatsIntegration.record_game_completed(game_data)
        print(f"Integration record result: {result}")
        
        # Force refresh data
        print("Forcing refresh of all stats data")
        refresh_result = GameStatsIntegration.force_refresh_data()
        print(f"Force refresh result: {refresh_result}")
        
        # Verify the record was added
        conn = thread_safe_db.get_connection()  # Get a fresh connection
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM game_history')
        new_count = cursor.fetchone()[0]
        print(f"New game_history record count: {new_count}")
        
        # Get the latest record
        cursor.execute('SELECT id, date_time, username, players, total_prize FROM game_history ORDER BY id DESC LIMIT 1')
        latest = cursor.fetchone()
        if latest:
            print(f"Latest record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Players={latest[3]}, Prize={latest[4]}")
            
        # Check if count increased
        if new_count > initial_count:
            print("SUCCESS: New record was added to the database through integration!")
        else:
            print("ERROR: Record count did not increase through integration!")
            
        return result
            
    except Exception as e:
        print(f"Error in test_integration_recording: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_demo_mode_override():
    """Test demo mode override for games with more than 2 players"""
    print("\n" + "="*80)
    print("TESTING DEMO MODE OVERRIDE")
    print("="*80)
    
    try:
        from game_stats_integration import GameStatsIntegration
        import thread_safe_db
        
        # Get current record count
        conn = thread_safe_db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM game_history')
        initial_count = cursor.fetchone()[0]
        print(f"Initial game_history record count: {initial_count}")
        
        # Create sample game data with contradictory demo mode flag
        game_data = {
            "winner_name": "Demo Override Test",
            "winner_cartella": 789,
            "claim_type": "Full House",
            "game_duration": 280,
            "player_count": 25,  # More than 2 players, so should NOT be treated as demo
            "prize_amount": 1250,
            "commission_percentage": 20,
            "called_numbers": [21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
            "is_demo_mode": True,  # Intentionally set wrong to test override
            "date_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Record the game through the integration layer
        print(f"Recording game data with contradictory demo flag: {json.dumps(game_data, indent=2)}")
        result = GameStatsIntegration.record_game_completed(game_data)
        print(f"Integration record result: {result}")
        
        # Verify the record was added
        conn = thread_safe_db.get_connection()  # Get a fresh connection
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM game_history')
        new_count = cursor.fetchone()[0]
        print(f"New game_history record count: {new_count}")
        
        # Get the latest record
        cursor.execute('SELECT id, date_time, username, players, total_prize FROM game_history ORDER BY id DESC LIMIT 1')
        latest = cursor.fetchone()
        if latest:
            print(f"Latest record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}, Players={latest[3]}, Prize={latest[4]}")
            
        # Check if count increased despite demo mode flag
        if new_count > initial_count:
            print("SUCCESS: Demo mode was correctly overridden for a game with many players!")
            return True
        else:
            print("ERROR: Demo mode was not overridden correctly!")
            return False
            
    except Exception as e:
        print(f"Error in test_demo_mode_override: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("\n" + "*"*80)
    print("GAME STATISTICS RECORDING TEST")
    print("*"*80)
    
    # Run all tests
    direct_result = test_direct_db_recording()
    time.sleep(1)  # Small delay between tests
    
    integration_result = test_integration_recording()
    time.sleep(1)  # Small delay between tests
    
    override_result = test_demo_mode_override()
    
    # Print summary
    print("\n" + "*"*80)
    print("TEST RESULTS SUMMARY")
    print("*"*80)
    print(f"Direct DB Recording: {'SUCCESS' if direct_result else 'FAILED'}")
    print(f"Integration Recording: {'SUCCESS' if integration_result else 'FAILED'}")
    print(f"Demo Mode Override: {'SUCCESS' if override_result else 'FAILED'}")
    
    overall = direct_result and integration_result and override_result
    print(f"\nOverall Test Result: {'SUCCESS' if overall else 'FAILED'}")