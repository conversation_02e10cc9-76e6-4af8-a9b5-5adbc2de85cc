"""
Simple Sync Status Checker

Quick diagnostic to check if RethinkDB sync is working.
"""

import sys

def check_rethinkdb_connection():
    """Check if RethinkDB is connected."""
    try:
        from rethink_db import get_rethink_db_manager
        rethink_db = get_rethink_db_manager()
        connected = rethink_db.is_connected()
        print(f"RethinkDB Connected: {connected}")
        
        if connected:
            tables = rethink_db.list_tables()
            print(f"Available tables: {len(tables)}")
            print(f"Tables: {', '.join(tables)}")
            
            # Check game_history count
            try:
                count = rethink_db.count_records('game_history')
                print(f"Game history records in RethinkDB: {count}")
            except Exception as e:
                print(f"Error counting game_history: {e}")
        
        return connected
    except Exception as e:
        print(f"RethinkDB Error: {e}")
        return False

def check_sqlite_data():
    """Check SQLite data."""
    try:
        from stats_db import get_stats_db_manager
        local_db = get_stats_db_manager()
        
        with local_db.get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM game_history')
            count = cursor.fetchone()[0]
            print(f"Game history records in SQLite: {count}")
            
            # Get latest record
            cursor.execute('SELECT * FROM game_history ORDER BY id DESC LIMIT 1')
            latest = cursor.fetchone()
            if latest:
                print(f"Latest record ID: {latest[0]}")
            else:
                print("No records in SQLite")
        
        return True
    except Exception as e:
        print(f"SQLite Error: {e}")
        return False

def check_sync_manager():
    """Check sync manager status."""
    try:
        from sync_manager import get_sync_manager
        sync_manager = get_sync_manager()
        
        print(f"Sync manager running: {sync_manager.sync_running}")
        print(f"Remote DB available: {sync_manager.remote_db is not None}")
        
        if sync_manager.remote_db:
            print(f"Remote DB connected: {sync_manager.remote_db.is_connected()}")
        
        # Check sync stats
        stats = sync_manager.sync_stats
        print(f"Total synced: {stats['total_synced']}")
        print(f"Sync errors: {stats['sync_errors']}")
        if stats['last_error']:
            print(f"Last error: {stats['last_error']}")
        
        return True
    except Exception as e:
        print(f"Sync Manager Error: {e}")
        return False

def test_simple_record():
    """Test adding a simple record."""
    try:
        import thread_safe_db
        
        print("\nTesting simple record addition...")
        
        test_data = {
            'winner_name': 'SyncTest',
            'winner_cartella': 99,
            'claim_type': 'Test',
            'game_duration': 60,
            'player_count': 1,
            'prize_amount': 100,
            'commission_percentage': 10,
            'called_numbers': [1, 2, 3, 4, 5],
            'is_demo_mode': False
        }
        
        result = thread_safe_db.record_game_completed(test_data)
        print(f"Record added: {result}")
        
        return result
    except Exception as e:
        print(f"Record Test Error: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("🔍 SYNC STATUS DIAGNOSTIC")
    print("=" * 50)
    
    print("\n1. Checking RethinkDB Connection...")
    rethink_ok = check_rethinkdb_connection()
    
    print("\n2. Checking SQLite Data...")
    sqlite_ok = check_sqlite_data()
    
    print("\n3. Checking Sync Manager...")
    sync_ok = check_sync_manager()
    
    print("\n4. Testing Record Addition...")
    record_ok = test_simple_record()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"RethinkDB: {'✅' if rethink_ok else '❌'}")
    print(f"SQLite: {'✅' if sqlite_ok else '❌'}")
    print(f"Sync Manager: {'✅' if sync_ok else '❌'}")
    print(f"Record Test: {'✅' if record_ok else '❌'}")
    
    if all([rethink_ok, sqlite_ok, sync_ok]):
        print("\n✅ Sync system appears to be working!")
        print("💡 If dashboard doesn't show real-time data:")
        print("   1. Refresh the dashboard page")
        print("   2. Check dashboard logs")
        print("   3. Restart the dashboard")
    else:
        print("\n❌ Sync system has issues!")
        print("💡 Try:")
        print("   1. Restart RethinkDB")
        print("   2. Restart the game")
        print("   3. Check error logs")

if __name__ == "__main__":
    main()
