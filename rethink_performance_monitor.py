"""
RethinkDB Performance Monitor for WOW Games application.

This module provides performance monitoring, metrics collection,
and optimization recommendations for the RethinkDB integration.
"""

import os
import time
import json
import threading
import logging
from datetime import datetime, timedelta
from collections import defaultdict, deque

# Try importing rethinkdb
try:
    import rethinkdb
    r = rethinkdb.r
    from rethinkdb.errors import ReqlDriverError, ReqlTimeoutError
    RETHINKDB_AVAILABLE = True
except ImportError:
    RETHINKDB_AVAILABLE = False

class PerformanceMonitor:
    """
    Performance monitor for RethinkDB operations.
    
    Tracks query performance, connection health, and provides
    optimization recommendations.
    """
    
    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics = {
            'query_times': deque(maxlen=1000),  # Last 1000 query times
            'connection_events': deque(maxlen=100),  # Last 100 connection events
            'error_counts': defaultdict(int),
            'table_access_counts': defaultdict(int),
            'slow_queries': deque(maxlen=50),  # Last 50 slow queries
        }
        
        self.thresholds = {
            'slow_query_ms': 1000,  # Queries slower than 1 second
            'connection_timeout_ms': 5000,  # Connection timeout
            'max_error_rate': 0.05,  # 5% error rate threshold
        }
        
        self.monitoring_enabled = True
        self.start_time = time.time()
        
        # Setup logging
        self.logger = logging.getLogger('rethink_performance')
        handler = logging.FileHandler(os.path.join('data', 'rethink_performance.log'))
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
        self.logger.info("Performance monitor initialized")
    
    def record_query(self, query_type, table, duration_ms, success=True, error=None):
        """
        Record a database query for performance analysis.
        
        Args:
            query_type (str): Type of query (SELECT, INSERT, UPDATE, DELETE)
            table (str): Table name
            duration_ms (float): Query duration in milliseconds
            success (bool): Whether the query was successful
            error (str): Error message if query failed
        """
        if not self.monitoring_enabled:
            return
            
        timestamp = time.time()
        
        # Record query time
        self.metrics['query_times'].append({
            'timestamp': timestamp,
            'type': query_type,
            'table': table,
            'duration_ms': duration_ms,
            'success': success
        })
        
        # Track table access
        self.metrics['table_access_counts'][table] += 1
        
        # Record errors
        if not success and error:
            self.metrics['error_counts'][error] += 1
            self.logger.warning(f"Query error: {error} (table: {table}, type: {query_type})")
        
        # Record slow queries
        if duration_ms > self.thresholds['slow_query_ms']:
            self.metrics['slow_queries'].append({
                'timestamp': timestamp,
                'type': query_type,
                'table': table,
                'duration_ms': duration_ms,
                'error': error
            })
            self.logger.warning(f"Slow query detected: {query_type} on {table} took {duration_ms:.2f}ms")
    
    def record_connection_event(self, event_type, success=True, error=None):
        """
        Record a connection event.
        
        Args:
            event_type (str): Type of event (CONNECT, DISCONNECT, RECONNECT)
            success (bool): Whether the event was successful
            error (str): Error message if event failed
        """
        if not self.monitoring_enabled:
            return
            
        self.metrics['connection_events'].append({
            'timestamp': time.time(),
            'type': event_type,
            'success': success,
            'error': error
        })
        
        if not success and error:
            self.metrics['error_counts'][f"CONNECTION_{event_type}"] += 1
            self.logger.error(f"Connection event failed: {event_type} - {error}")
        else:
            self.logger.info(f"Connection event: {event_type}")
    
    def get_performance_summary(self):
        """
        Get a summary of performance metrics.
        
        Returns:
            dict: Performance summary
        """
        now = time.time()
        uptime = now - self.start_time
        
        # Calculate query statistics
        recent_queries = [q for q in self.metrics['query_times'] if now - q['timestamp'] < 3600]  # Last hour
        
        if recent_queries:
            avg_query_time = sum(q['duration_ms'] for q in recent_queries) / len(recent_queries)
            successful_queries = sum(1 for q in recent_queries if q['success'])
            error_rate = 1 - (successful_queries / len(recent_queries))
        else:
            avg_query_time = 0
            error_rate = 0
        
        # Get most accessed tables
        top_tables = sorted(self.metrics['table_access_counts'].items(), 
                          key=lambda x: x[1], reverse=True)[:5]
        
        # Get recent slow queries
        recent_slow = [q for q in self.metrics['slow_queries'] if now - q['timestamp'] < 3600]
        
        # Get recent connection events
        recent_connections = [e for e in self.metrics['connection_events'] if now - e['timestamp'] < 3600]
        
        summary = {
            'uptime_seconds': uptime,
            'total_queries': len(self.metrics['query_times']),
            'recent_queries_count': len(recent_queries),
            'avg_query_time_ms': avg_query_time,
            'error_rate': error_rate,
            'slow_queries_count': len(recent_slow),
            'top_tables': top_tables,
            'recent_connection_events': len(recent_connections),
            'total_errors': sum(self.metrics['error_counts'].values()),
            'monitoring_enabled': self.monitoring_enabled
        }
        
        return summary
    
    def get_optimization_recommendations(self):
        """
        Get optimization recommendations based on performance data.
        
        Returns:
            list: List of optimization recommendations
        """
        recommendations = []
        summary = self.get_performance_summary()
        
        # Check error rate
        if summary['error_rate'] > self.thresholds['max_error_rate']:
            recommendations.append({
                'type': 'HIGH_ERROR_RATE',
                'message': f"High error rate detected: {summary['error_rate']:.2%}",
                'suggestion': "Check connection stability and query syntax"
            })
        
        # Check slow queries
        if summary['slow_queries_count'] > 10:
            recommendations.append({
                'type': 'SLOW_QUERIES',
                'message': f"{summary['slow_queries_count']} slow queries in the last hour",
                'suggestion': "Consider adding indexes or optimizing query structure"
            })
        
        # Check average query time
        if summary['avg_query_time_ms'] > 500:
            recommendations.append({
                'type': 'HIGH_LATENCY',
                'message': f"Average query time is {summary['avg_query_time_ms']:.2f}ms",
                'suggestion': "Consider connection pooling or query optimization"
            })
        
        # Check table access patterns
        if summary['top_tables']:
            most_accessed = summary['top_tables'][0]
            if most_accessed[1] > 1000:  # More than 1000 accesses
                recommendations.append({
                    'type': 'HOT_TABLE',
                    'message': f"Table '{most_accessed[0]}' has {most_accessed[1]} accesses",
                    'suggestion': "Consider caching or read replicas for this table"
                })
        
        return recommendations
    
    def export_metrics(self, filepath=None):
        """
        Export performance metrics to a JSON file.
        
        Args:
            filepath (str): Path to export file (optional)
            
        Returns:
            str: Path to exported file
        """
        if filepath is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = os.path.join('data', f'rethink_metrics_{timestamp}.json')
        
        # Convert deques to lists for JSON serialization
        export_data = {
            'summary': self.get_performance_summary(),
            'recommendations': self.get_optimization_recommendations(),
            'query_times': list(self.metrics['query_times']),
            'connection_events': list(self.metrics['connection_events']),
            'error_counts': dict(self.metrics['error_counts']),
            'table_access_counts': dict(self.metrics['table_access_counts']),
            'slow_queries': list(self.metrics['slow_queries']),
            'thresholds': self.thresholds,
            'export_timestamp': time.time()
        }
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        self.logger.info(f"Metrics exported to {filepath}")
        return filepath
    
    def reset_metrics(self):
        """Reset all performance metrics."""
        self.metrics = {
            'query_times': deque(maxlen=1000),
            'connection_events': deque(maxlen=100),
            'error_counts': defaultdict(int),
            'table_access_counts': defaultdict(int),
            'slow_queries': deque(maxlen=50),
        }
        self.start_time = time.time()
        self.logger.info("Performance metrics reset")
    
    def enable_monitoring(self, enabled=True):
        """Enable or disable performance monitoring."""
        self.monitoring_enabled = enabled
        self.logger.info(f"Performance monitoring {'enabled' if enabled else 'disabled'}")

# Global instance
_performance_monitor = None
_monitor_lock = threading.RLock()

def get_performance_monitor():
    """
    Get the global performance monitor instance.
    
    Returns:
        PerformanceMonitor: The performance monitor instance
    """
    global _performance_monitor
    
    with _monitor_lock:
        if _performance_monitor is None:
            _performance_monitor = PerformanceMonitor()
        
        return _performance_monitor

def monitor_query(query_type, table):
    """
    Decorator for monitoring RethinkDB queries.
    
    Args:
        query_type (str): Type of query
        table (str): Table name
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not RETHINKDB_AVAILABLE:
                return func(*args, **kwargs)
                
            monitor = get_performance_monitor()
            start_time = time.time()
            success = True
            error = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration_ms = (time.time() - start_time) * 1000
                monitor.record_query(query_type, table, duration_ms, success, error)
        
        return wrapper
    return decorator
