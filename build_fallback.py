#!/usr/bin/env python3
"""
WOW Bingo Game - Fallback Build Script
======================================

This script provides a fallback build solution using PyInstaller
when Nuitka encounters issues. PyInstaller is more reliable across
different system configurations.

Usage:
    python build_fallback.py [--onefile] [--verbose]
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def error(message):
    """Log an error and exit."""
    log(message, "ERROR")
    sys.exit(1)

def main():
    """Main build function using PyInstaller."""
    parser = argparse.ArgumentParser(description="Fallback PyInstaller build for WOW Bingo Game")
    parser.add_argument('--onefile', action='store_true', help='Create single-file executable')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()

    project_root = Path(__file__).parent.absolute()
    dist_dir = project_root / "dist"
    build_dir = project_root / "build"
    
    log("=" * 60)
    log("WOW Bingo Game - Fallback PyInstaller Build")
    log("=" * 60)

    # Check prerequisites
    log("Checking prerequisites...")
    
    if not (project_root / "main.py").exists():
        error("main.py not found!")
    
    if not (project_root / "assets").exists():
        error("assets directory not found!")

    # Check Python version
    if sys.version_info < (3, 7):
        error("Python 3.7 or higher is required")

    log("Prerequisites check passed")

    # Install PyInstaller if needed
    try:
        import PyInstaller
        log("PyInstaller is available")
    except ImportError:
        log("Installing PyInstaller...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller>=6.0.0'],
                         check=True, capture_output=not args.verbose)
            log("PyInstaller installed successfully")
        except subprocess.CalledProcessError as e:
            error(f"Failed to install PyInstaller: {e}")

    # Prepare build environment
    log("Preparing build environment...")
    
    for dir_path in [dist_dir, build_dir]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            log(f"Cleaned {dir_path.name} directory")
    
    dist_dir.mkdir(exist_ok=True)
    build_dir.mkdir(exist_ok=True)

    # Build command with PyInstaller
    log("Building with PyInstaller (fallback mode)...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile' if args.onefile else '--onedir',
        '--windowed',  # No console window
        '--name=WOW_Bingo_Game',
        f'--distpath={dist_dir}',
        f'--workpath={build_dir}',
        '--clean',
        '--noconfirm',
    ]

    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.extend(['--icon', str(icon_path)])

    # Add data files
    if (project_root / "assets").exists():
        cmd.extend(['--add-data', f'{project_root / "assets"};assets'])
        log("Including assets directory")

    if (project_root / "data").exists():
        cmd.extend(['--add-data', f'{project_root / "data"};data'])
        log("Including data directory")

    # Hidden imports for common packages
    hidden_imports = [
        'pygame',
        'pygame._freetype',
        'pygame.mixer',
        'pygame.font',
        'pyperclip',
        'sqlite3',
        'json',
        'datetime',
        'pathlib',
        'threading',
        'multiprocessing',
    ]

    for package in hidden_imports:
        cmd.extend(['--hidden-import', package])

    # Add main script
    cmd.append('main.py')

    # Execute PyInstaller
    log("Executing PyInstaller (this may take 5-10 minutes)...")
    if args.verbose:
        log(f"Command: {' '.join(cmd)}")

    start_time = time.time()

    try:
        result = subprocess.run(cmd, cwd=project_root,
                              capture_output=not args.verbose, text=True)

        if result.returncode == 0:
            build_time = time.time() - start_time
            log("PyInstaller build completed successfully")
            
            # Find executable
            executable_path = None
            if args.onefile:
                executable_path = dist_dir / "WOW_Bingo_Game.exe"
            else:
                executable_path = dist_dir / "WOW_Bingo_Game" / "WOW_Bingo_Game.exe"
            
            if executable_path and executable_path.exists():
                size_mb = executable_path.stat().st_size / (1024 * 1024)
                log("=" * 60)
                log("BUILD COMPLETED SUCCESSFULLY!")
                log("=" * 60)
                log(f"Executable: {executable_path}")
                log(f"Size: {size_mb:.1f} MB")
                log(f"Build time: {build_time:.1f} seconds")
                log("=" * 60)
                log("Note: This is a PyInstaller build (fallback from Nuitka)")
                log("The executable should work reliably on all Windows systems.")
                log("=" * 60)
                return True
            else:
                error("Executable not found after build")
        else:
            error(f"PyInstaller build failed with return code {result.returncode}")
            if result.stderr and not args.verbose:
                log(f"Error output: {result.stderr}")
            return False

    except Exception as e:
        error(f"Error during PyInstaller build: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        log(f"Unexpected error: {e}", "ERROR")
        sys.exit(1)
