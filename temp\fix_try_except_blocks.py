import re

def fix_try_except_blocks():
    with open('Board_selection_fixed.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Fix the nested try block without except - around line 5417
    # First find the problematic section
    pattern1 = r'try:\s+import json\s+import os\s+\n\s+try:'
    replacement1 = r'try:\n            import json\n            import os\n\n            # Load boards from file'
    content = re.sub(pattern1, replacement1, content)
    
    # Fix the misplaced except around line 5440
    pattern2 = r'(\s+# We\'ll continue to the deterministic generation below\s+)except Exception as e:\s+pass\s+except Exception as e:'
    replacement2 = r'\1pass\n            except Exception as e:'
    content = re.sub(pattern2, replacement2, content)
    
    # Fix another malformed except section
    pattern3 = r'except Exception as e:\s+print\(f"Error loading board from JSON file: \{e\}"\)'
    replacement3 = r'except Exception as e:\n            print(f"Error loading board from JSON file: {e}")'
    content = re.sub(pattern3, replacement3, content)
    
    # Write the fixed content back to the file
    with open('Board_selection_fixed.py', 'w', encoding='utf-8') as file:
        file.write(content)
    
    print("Try-except blocks fixed successfully!")

if __name__ == "__main__":
    fix_try_except_blocks() 