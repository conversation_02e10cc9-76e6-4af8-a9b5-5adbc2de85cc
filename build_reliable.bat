@echo off
setlocal enabledelayedexpansion

REM WOW Bingo Game - Reliable Build Script
REM ======================================
REM
REM This script uses a simplified approach with proper error handling
REM to ensure reliable builds across different Windows systems.

echo.
echo ================================================================================
echo WOW Bingo Game - Reliable Build System
echo ================================================================================
echo.

REM Check if Python is available
echo Checking Python installation...
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking project files...

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found in current directory
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

REM Check if assets directory exists
if not exist "assets" (
    echo ERROR: assets directory not found
    echo Please ensure the assets directory exists
    pause
    exit /b 1
)

echo Project files verified. Starting build process...
echo.

REM Try Simple Nuitka Build First
echo ================================================================================
echo ATTEMPTING: Simple Nuitka Build
echo ================================================================================
echo.

if exist "nuitka_build_simple.py" (
    echo Running simple Nuitka build...
    python nuitka_build_simple.py --verbose
    
    REM Check if build succeeded by looking for executable
    if exist "dist\WOW_Bingo_Game.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: Nuitka build completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOW_Bingo_Game.exe
        goto :build_success
    )
    
    if exist "dist\WOW_Bingo_Game\WOW_Bingo_Game.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: Nuitka build completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOW_Bingo_Game\WOW_Bingo_Game.exe
        goto :build_success
    )
    
    echo Simple Nuitka build did not create expected executable.
) else (
    echo nuitka_build_simple.py not found, skipping Nuitka build.
)

echo.
echo ================================================================================
echo ATTEMPTING: PyInstaller Fallback Build
echo ================================================================================
echo.

REM Try PyInstaller as fallback
if exist "build_fallback.py" (
    echo Running PyInstaller fallback build...
    python build_fallback.py --verbose
    
    REM Check if build succeeded by looking for executable
    if exist "dist\WOW_Bingo_Game.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: PyInstaller build completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOW_Bingo_Game.exe
        goto :build_success
    )
    
    if exist "dist\WOW_Bingo_Game\WOW_Bingo_Game.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: PyInstaller build completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOW_Bingo_Game\WOW_Bingo_Game.exe
        goto :build_success
    )
    
    echo PyInstaller build did not create expected executable.
) else (
    echo build_fallback.py not found, skipping PyInstaller build.
)

echo.
echo ================================================================================
echo ATTEMPTING: Original Build System
echo ================================================================================
echo.

REM Try original build system as last resort
if exist "build_executable.py" (
    echo Running original build system...
    python build_executable.py
    
    REM Check if build succeeded by looking for executable
    if exist "dist\WOW_Bingo_Game.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: Original build system completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOW_Bingo_Game.exe
        goto :build_success
    )
    
    if exist "dist\WOWBingoGame.exe" (
        echo.
        echo ================================================================================
        echo SUCCESS: Original build system completed successfully!
        echo ================================================================================
        echo.
        echo Executable created: dist\WOWBingoGame.exe
        goto :build_success
    )
    
    echo Original build system did not create expected executable.
) else (
    echo build_executable.py not found, skipping original build.
)

REM All builds failed
echo.
echo ================================================================================
echo ALL BUILD ATTEMPTS FAILED
echo ================================================================================
echo.
echo No build method was able to create a working executable.
echo.
echo Common solutions:
echo 1. Install Visual Studio Build Tools:
echo    https://visualstudio.microsoft.com/visual-cpp-build-tools/
echo.
echo 2. Install Python dependencies:
echo    pip install -r requirements.txt
echo.
echo 3. Free up disk space (need at least 5GB)
echo.
echo 4. Close other applications to free memory
echo.
echo 5. Try running as Administrator
echo.
pause
exit /b 1

:build_success
echo.
echo ================================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================================
echo.
echo Your executable is ready for distribution!
echo.
echo Next steps:
echo 1. Test the executable by double-clicking it
echo 2. Copy the dist folder to distribute your game
echo 3. The executable runs on any Windows PC without Python
echo.
echo Build completed at: %date% %time%
echo.
pause
exit /b 0
