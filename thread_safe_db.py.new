"""
Thread-Safe Database Access Module

This module provides thread-safe database access functions for the WOW Games application.
It ensures that SQLite connections are properly managed across threads.
"""

import os
import sqlite3
import threading
import logging
from datetime import datetime, timedelta
import json

# Constants
DATA_DIR = os.path.join('data')
STATS_DB_PATH = os.path.join(DATA_DIR, 'stats.db')
STATS_JSON_PATH = os.path.join(DATA_DIR, 'stats.json')

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'thread_safe_db.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Thread-local storage for database connections
_thread_local = threading.local()

def get_connection():
    """
    Get a database connection for the current thread.

    Returns:
        sqlite3.Connection: Database connection
    """
    if not hasattr(_thread_local, 'connection'):
        # Create a new connection for this thread
        _thread_local.connection = sqlite3.connect(STATS_DB_PATH)

        # Enable foreign keys
        _thread_local.connection.execute('PRAGMA foreign_keys = ON')

        # Set busy timeout to avoid database locked errors
        _thread_local.connection.execute('PRAGMA busy_timeout = 5000')

        # Use WAL mode for better concurrency
        _thread_local.connection.execute('PRAGMA journal_mode = WAL')

        # Row factory for dictionary-like access
        _thread_local.connection.row_factory = sqlite3.Row

        logging.info(f"Created new database connection in thread {threading.current_thread().ident}")

    return _thread_local.connection

def close_connection():
    """Close the database connection for the current thread."""
    if hasattr(_thread_local, 'connection'):
        _thread_local.connection.close()
        delattr(_thread_local, 'connection')
        logging.info(f"Closed database connection in thread {threading.current_thread().ident}")

def ensure_database_exists():
    """
    Ensure the stats database exists and has the required tables.
    This should be called at game startup.
    """
    # Ensure data directory exists
    os.makedirs(DATA_DIR, exist_ok=True)

    # Get connection for this thread
    conn = get_connection()
    cursor = conn.cursor()

    # Create daily_stats table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_stats (
        date TEXT PRIMARY KEY,
        games_played INTEGER DEFAULT 0,
        earnings REAL DEFAULT 0,
        winners INTEGER DEFAULT 0,
        total_players INTEGER DEFAULT 0
    )
    ''')

    # Create game_history table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS game_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date_time TEXT,
        username TEXT,
        house TEXT,
        stake REAL,
        players INTEGER,
        total_calls INTEGER,
        commission_percent REAL,
        fee REAL,
        total_prize REAL,
        details TEXT,
        status TEXT
    )
    ''')

    # Create wallet_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS wallet_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date_time TEXT,
        amount REAL,
        transaction_type TEXT,
        description TEXT,
        balance_after REAL
    )
    ''')

    # Create indices for better performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_game_history_date_time ON game_history(date_time)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date_time ON wallet_transactions(date_time)')

    # Commit changes
    conn.commit()

    logging.info("Database schema initialized successfully")
    print("Stats database initialized successfully")

def record_game_started(player_count, bet_amount=50, is_demo_mode=False):
    """
    Record a game start event in the stats database.

    Args:
        player_count: Number of players in the game
        bet_amount: Bet amount per player
        is_demo_mode: Boolean indicating if the game is in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if is_demo_mode:
        print("Game started in demo mode - statistics not updated")
        return False

    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        today = datetime.now().strftime('%Y-%m-%d')

        # Update daily stats
        cursor.execute('''
        INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
        VALUES (?, 0, 0, 0, 0)
        ''', (today,))

        # Update player count if needed
        cursor.execute('''
        UPDATE daily_stats SET total_players = total_players + ? WHERE date = ?
        ''', (player_count, today))

        # Commit changes
        conn.commit()

        logging.info(f"Game start event recorded in database (players: {player_count})")
        print("Game start event recorded in database")
        return True
    except Exception as e:
        logging.error(f"Error recording game start event in database: {e}")
        print(f"Error recording game start event in database: {e}")

        # Fallback to JSON storage
        try:
            # Load current statistics
            stats_data = _load_json_stats()

            # Format current time
            current_time = datetime.now().strftime("%H:%M")

            # Create activity entry
            activity = {
                "time": current_time,
                "event": f"Game started with {player_count} players"
            }

            # Add to recent activity (keep most recent 10)
            recent_activity = stats_data.get("recent_activity", [])
            recent_activity.insert(0, activity)  # Add at beginning
            stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

            # Update player count if needed
            stats_data["player_count"] = max(stats_data["player_count"], player_count)

            # Save updated statistics
            _save_json_stats(stats_data)

            logging.info("Game start event added to JSON stats (fallback)")
            print("Game start event added to JSON stats")
            return True
        except Exception as json_e:
            logging.error(f"Error saving to JSON stats: {json_e}")
            print(f"Error saving to JSON stats: {json_e}")
            return False

def record_game_completed(game_data):
    """
    Record a completed game in the stats database.

    Args:
        game_data: Dictionary containing game data
            - winner_name: Name of the winner
            - winner_cartella: Cartella number of the winner
            - claim_type: Type of claim (e.g., 'Full House', 'First Line')
            - game_duration: Duration of the game in seconds
            - player_count: Number of players in the game
            - prize_amount: Prize amount for this game
            - commission_percentage: Commission percentage for this game
            - called_numbers: List of numbers called during the game
            - is_demo_mode: Boolean indicating if the game was in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Debug print to help diagnose issues
    print("=" * 50)
    print("RECORDING GAME COMPLETED IN THREAD_SAFE_DB")
    print(f"Game data: {game_data}")
    print("=" * 50)

    # Skip if in demo mode
    is_demo_mode = game_data.get("is_demo_mode", False)
    if is_demo_mode:
        print("Game was in demo mode - statistics not updated")
        return False

    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        today = datetime.now().strftime('%Y-%m-%d')

        # Update daily stats
        cursor.execute('''
        INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
        VALUES (?, 0, 0, 0, 0)
        ''', (today,))

        # Increment games played
        cursor.execute('''
        UPDATE daily_stats SET games_played = games_played + 1 WHERE date = ?
        ''', (today,))

        # Calculate fee if prize amount is available
        fee = 0
        if 'prize_amount' in game_data and game_data['prize_amount'] is not None:
            fee = game_data.get('prize_amount', 0) * (game_data.get('commission_percentage', 20) / 100)
            cursor.execute('''
            UPDATE daily_stats SET earnings = earnings + ? WHERE date = ?
            ''', (fee, today))

        # Add winner if available
        if 'winner_name' in game_data and game_data['winner_name']:
            cursor.execute('''
            UPDATE daily_stats SET winners = winners + 1 WHERE date = ?
            ''', (today,))

        # Insert into game history
        cursor.execute('''
        INSERT INTO game_history (
            date_time, username, house, stake, players, total_calls,
            commission_percent, fee, total_prize, details, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            game_data.get('winner_name', 'Unknown'),
            game_data.get('house', 'Main House'),
            game_data.get('stake', 0),
            game_data.get('player_count', 0),
            len(game_data.get('called_numbers', [])),
            game_data.get('commission_percentage', 20),
            fee,
            game_data.get('prize_amount', 0),
            json.dumps(game_data),
            'completed'
        ))

        # Get the ID of the new record
        game_id = cursor.lastrowid

        # Commit changes
        conn.commit()

        logging.info(f"Game statistics recorded in database (ID: {game_id})")
        print(f"Game statistics recorded in database (ID: {game_id})")
        return True
    except Exception as e:
        import traceback
        import os
        db_path = os.environ.get('STATS_DB_PATH', 'data/stats.db')
        logging.error(f"Error recording game statistics in database: {e}")
        print(f"Error recording game statistics in database: {e}")
        print(f"Database path: {db_path}")
        traceback.print_exc()
        # Self-healing: if 'no such table' in error, try to initialize DB and retry ONCE
        if 'no such table' in str(e):
            try:
                print("No such table error detected. Attempting to initialize database and retry...")
                ensure_database_exists()
                # Retry once
                return record_game_completed(game_data)
            except Exception as init_e:
                print(f"Failed to initialize database: {init_e}")
                traceback.print_exc()
        elif 'database is locked' in str(e):
            print("Database is locked. Please ensure no other process is using the database.")
        # Fallback to JSON storage
        try:
            # Load current statistics
            stats_data = _load_json_stats()

            # Update statistics with new game data
            stats_data["games_played"] += 1
            stats_data["total_winners"] += 1

            # Update prize pool
            if "prize_amount" in game_data and game_data["prize_amount"] > 0:
                stats_data["total_prize_pool"] += game_data["prize_amount"]

            # Update player count
            if "player_count" in game_data and game_data["player_count"] > 0:
                stats_data["player_count"] = max(stats_data["player_count"], game_data["player_count"])

            # Update average game duration
            if "game_duration" in game_data and game_data["game_duration"] > 0:
                current_avg = stats_data["average_game_duration"]
                games_played = stats_data["games_played"]

                if games_played > 1:
                    # Calculate new average
                    total_duration = current_avg * (games_played - 1)
                    total_duration += game_data["game_duration"]
                    stats_data["average_game_duration"] = total_duration / games_played
                else:
                    # First game, just use its duration
                    stats_data["average_game_duration"] = game_data["game_duration"]

            # Update number frequencies
            if "called_numbers" in game_data and game_data["called_numbers"]:
                number_freq = stats_data.get("number_frequencies", {})
                for num in game_data["called_numbers"]:
                    if str(num) in number_freq:
                        number_freq[str(num)] += 1
                    else:
                        number_freq[str(num)] = 1
                stats_data["number_frequencies"] = number_freq

            # Add to recent activity
            if "winner_name" in game_data and "claim_type" in game_data:
                # Format current time
                current_time = datetime.now().strftime("%H:%M")

                # Create activity entry
                activity = {
                    "time": current_time,
                    "event": f"Player '{game_data['winner_name']}' won with {game_data['claim_type']}"
                }

                # Add to recent activity (keep most recent 10)
                recent_activity = stats_data.get("recent_activity", [])
                recent_activity.insert(0, activity)  # Add at beginning
                stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

            # Save updated statistics
            _save_json_stats(stats_data)

            logging.info("Game statistics updated in JSON stats (fallback)")
            print("Game statistics updated in JSON stats")
            return True
        except Exception as json_e:
            logging.error(f"Error saving to JSON stats: {json_e}")
            print(f"Error saving to JSON stats: {json_e}")
            return False

def get_game_history(page=0, page_size=10):
    """
    Get game history from the database.

    Args:
        page: Page number (0-based)
        page_size: Number of records per page

    Returns:
        tuple: (list of game history records, total pages)
    """
    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Calculate offset
        offset = page * page_size

        # Get total count for pagination
        cursor.execute('SELECT COUNT(*) FROM game_history')
        result = cursor.fetchone()
        total_games = result[0] if result else 0
        total_pages = max(1, (total_games + page_size - 1) // page_size)

        # Get game history for this page
        cursor.execute('''
        SELECT id, date_time, username, house, stake, players, total_calls,
               commission_percent, fee, total_prize, details, status
        FROM game_history
        ORDER BY date_time DESC
        LIMIT ? OFFSET ?
        ''', (page_size, offset))

        results = cursor.fetchall()

        # Convert to list of dictionaries
        history = []
        for row in results:
            history.append({
                'id': row['id'],
                'date_time': row['date_time'],
                'username': row['username'],
                'house': row['house'],
                'stake': row['stake'],
                'players': row['players'],
                'total_calls': row['total_calls'],
                'commission_percent': row['commission_percent'],
                'fee': row['fee'],
                'total_prize': row['total_prize'],
                'details': row['details'],
                'status': row['status']
            })

        logging.info(f"Got game history page {page}: {len(history)} records")
        return history, total_pages
    except Exception as e:
        logging.error(f"Error getting game history: {e}")
        return [], 1

def get_weekly_stats():
    """
    Get weekly statistics from the database.

    Returns:
        list: List of daily statistics for the week
    """
    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        end_date = datetime.now()

        # Calculate start date (6 days before end date to get 7 days total)
        start_date = end_date - timedelta(days=6)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        # Debug print
        print(f"Getting weekly stats from {start_date_str} to {end_date_str}")

        # Make sure the daily_stats table exists
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            date TEXT PRIMARY KEY,
            games_played INTEGER DEFAULT 0,
            earnings REAL DEFAULT 0,
            winners INTEGER DEFAULT 0,
            total_players INTEGER DEFAULT 0
        )
        ''')

        # Generate the full week with default values
        weekly_stats = []
        current_date = start_date

        # Insert default records for the week if they don't exist
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')

            # Insert default record if it doesn't exist
            cursor.execute('''
            INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
            VALUES (?, 0, 0, 0, 0)
            ''', (date_str,))

            current_date += timedelta(days=1)

        # Commit the changes
        conn.commit()

        # Get weekly stats
        cursor.execute('''
        SELECT date, games_played, earnings, winners, total_players
        FROM daily_stats
        WHERE date BETWEEN ? AND ?
        ORDER BY date
        ''', (start_date_str, end_date_str))

        results = cursor.fetchall()
        print(f"Query returned {len(results)} rows")

        # Convert to dictionary format
        stats_by_date = {}
        for row in results:
            stats_by_date[row['date']] = {
                'date': row['date'],
                'games_played': row['games_played'] or 0,
                'earnings': row['earnings'] or 0,
                'winners': row['winners'] or 0,
                'total_players': row['total_players'] or 0
            }
            print(f"Row from database: {row['date']} - {row['games_played']} games, {row['earnings']} earnings")

        # Generate the full week with values from database or defaults
        weekly_stats = []
        current_date = start_date

        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            if date_str in stats_by_date:
                weekly_stats.append(stats_by_date[date_str])
            else:
                # Default values for dates with no data (should not happen now)
                weekly_stats.append({
                    'date': date_str,
                    'games_played': 0,
                    'earnings': 0,
                    'winners': 0,
                    'total_players': 0
                })
            current_date += timedelta(days=1)

        logging.info(f"Got weekly stats: {len(weekly_stats)} days")
        print(f"Final weekly stats: {weekly_stats}")
        return weekly_stats
    except Exception as e:
        logging.error(f"Error getting weekly stats: {e}")
        print(f"Error getting weekly stats: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_summary_stats():
    """
    Get summary statistics from the database.

    Returns:
        dict: Dictionary of summary statistics
    """
    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        total_earnings = result[0] if result and result[0] is not None else 0

        # Get today's date
        today = datetime.now().strftime('%Y-%m-%d')

        # Get daily earnings
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_earnings = result[0] if result and result[0] is not None else 0

        # Get daily games played
        cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_games = result[0] if result and result[0] is not None else 0

        # Get wallet balance
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0

        logging.info("Got summary stats")
        return {
            'total_earnings': total_earnings,
            'daily_earnings': daily_earnings,
            'daily_games': daily_games,
            'wallet_balance': wallet_balance
        }
    except Exception as e:
        logging.error(f"Error getting summary stats: {e}")
        return {
            'total_earnings': 0,
            'daily_earnings': 0,
            'daily_games': 0,
            'wallet_balance': 0
        }

def _load_json_stats():
    """
    Load statistics from JSON file.

    Returns:
        dict: Statistics data
    """
    stats_data = {
        "games_played": 0,
        "total_winners": 0,
        "total_prize_pool": 0,
        "player_count": 0,
        "average_game_duration": 0,
        "top_players": [],
        "number_frequencies": {},
        "session_start_time": datetime.now().timestamp(),
        "recent_activity": []
    }

    # Load existing stats if available
    if os.path.exists(STATS_JSON_PATH):
        try:
            with open(STATS_JSON_PATH, 'r') as f:
                loaded_stats = json.load(f)
                # Update stats_data with loaded values
                for key, value in loaded_stats.items():
                    if key in stats_data:
                        stats_data[key] = value
        except Exception as e:
            logging.error(f"Error loading statistics from JSON: {e}")

    return stats_data

def _save_json_stats(stats_data):
    """
    Save statistics to JSON file.

    Args:
        stats_data: Dictionary containing statistics data
    """
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(STATS_JSON_PATH), exist_ok=True)

    # Save the stats data to file
    with open(STATS_JSON_PATH, 'w') as f:
        json.dump(stats_data, f, indent=4)

    logging.info(f"Statistics saved to {STATS_JSON_PATH}")

# Initialize database when module is imported
ensure_database_exists()
