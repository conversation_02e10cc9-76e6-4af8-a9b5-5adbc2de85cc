"""
WOW Games HDMI Display Fix Launcher

This script applies comprehensive HDMI display fixes before launching the main game.
Use this script instead of running main.py directly when experiencing HDMI display issues.
"""

import os
import sys
import time

def print_banner():
    """Print startup banner"""
    print("=" * 70)
    print("WOW GAMES - HDMI DISPLAY FIX LAUNCHER")
    print("=" * 70)
    print("This launcher applies comprehensive fixes for HDMI display issues")
    print("including flickering, improper rendering, and mode switching problems.")
    print()

def apply_pre_pygame_fixes():
    """Apply fixes that must be done before pygame initialization"""
    print("Step 1: Applying SDL environment variable fixes...")
    
    # Import and apply SDL fixes BEFORE pygame
    import sdl_display_fix
    
    print("✓ SDL environment variables configured")
    print()

def test_display_compatibility():
    """Test display compatibility before launching main game"""
    print("Step 2: Testing display compatibility...")
    
    try:
        # Import pygame AFTER SDL fixes
        import pygame
        
        # Initialize pygame display
        pygame.init()
        
        # Import our display managers
        from external_display_manager import get_external_display_manager
        from hdmi_display_fixer import get_hdmi_display_fixer
        
        # Test display detection
        display_manager = get_external_display_manager()
        display_info = display_manager.detect_displays()
        
        print(f"✓ Display driver: {display_info.get('driver', 'Unknown')}")
        print(f"✓ Available modes: {len(display_info.get('available_modes', []))}")
        print(f"✓ External display detected: {display_info.get('external_detected', False)}")
        
        # Test display creation
        screen, width, height = display_manager.create_compatible_display(
            preferred_width=1280,
            preferred_height=720,
            fullscreen=False
        )
        
        print(f"✓ Test display created: {width}x{height}")
        
        # Test HDMI stability fixes
        hdmi_fixer = get_hdmi_display_fixer()
        stability_result = hdmi_fixer.apply_hdmi_stability_fixes(screen)
        
        print(f"✓ HDMI stability test: {'Passed' if stability_result else 'Warning - may have issues'}")
        
        # Clean up test display
        pygame.quit()
        
        print("✓ Display compatibility test completed successfully")
        print()
        return True
        
    except Exception as e:
        print(f"✗ Display compatibility test failed: {e}")
        print("  The game may still work, but display issues are likely.")
        print()
        return False

def launch_main_game():
    """Launch the main game with all fixes applied"""
    print("Step 3: Launching main game with HDMI fixes...")
    print()
    
    try:
        # Import and run the main game
        import main
        
        # The main game will now use all the applied fixes
        print("Game launched successfully with HDMI display fixes applied.")
        
    except Exception as e:
        print(f"Error launching main game: {e}")
        print()
        print("Troubleshooting suggestions:")
        print("1. Check that all required files are present")
        print("2. Verify HDMI cable connection")
        print("3. Try running 'python test_external_display.py' for detailed diagnostics")
        print("4. Update graphics drivers")
        print("5. Try a different HDMI port or cable")

def show_troubleshooting_info():
    """Show troubleshooting information"""
    print()
    print("=" * 70)
    print("TROUBLESHOOTING INFORMATION")
    print("=" * 70)
    print()
    print("If you're still experiencing HDMI display issues:")
    print()
    print("1. HARDWARE CHECKS:")
    print("   - Ensure HDMI cable is properly connected")
    print("   - Try a different HDMI cable")
    print("   - Try a different HDMI port on your display")
    print("   - Verify external display is powered on and set to correct input")
    print()
    print("2. WINDOWS SETTINGS:")
    print("   - Right-click desktop → Display settings")
    print("   - Ensure external display is detected")
    print("   - Try 'Extend' or 'Duplicate' display modes")
    print("   - Check display resolution and refresh rate settings")
    print()
    print("3. GRAPHICS DRIVERS:")
    print("   - Update to latest graphics drivers")
    print("   - Restart computer after driver update")
    print("   - Check manufacturer website for latest drivers")
    print()
    print("4. DIAGNOSTIC TOOLS:")
    print("   - Run: python test_external_display.py")
    print("   - Run: python sdl_display_fix.py")
    print("   - Check console output for error messages")
    print()
    print("5. ALTERNATIVE SOLUTIONS:")
    print("   - Try running in windowed mode instead of fullscreen")
    print("   - Try different screen resolutions in game settings")
    print("   - Disable Windows display scaling")
    print("   - Try running as administrator")

def main():
    """Main launcher function"""
    print_banner()
    
    # Step 1: Apply pre-pygame fixes
    apply_pre_pygame_fixes()
    
    # Step 2: Test display compatibility
    compatibility_ok = test_display_compatibility()
    
    if not compatibility_ok:
        print("⚠️  Display compatibility issues detected!")
        print("   The game will still launch, but you may experience display problems.")
        print()
        
        response = input("Continue anyway? (y/n): ").lower().strip()
        if response != 'y' and response != 'yes':
            print("Launch cancelled. Run 'python test_external_display.py' for diagnostics.")
            return
        print()
    
    # Step 3: Launch main game
    try:
        launch_main_game()
    except KeyboardInterrupt:
        print("\nGame interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        show_troubleshooting_info()
    
    print("\nGame session ended.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nLauncher interrupted by user.")
    except Exception as e:
        print(f"\nLauncher error: {e}")
        show_troubleshooting_info()
    
    print("\nPress Enter to exit...")
    try:
        input()
    except:
        pass
