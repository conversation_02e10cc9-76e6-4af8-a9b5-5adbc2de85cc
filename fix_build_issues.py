#!/usr/bin/env python3
"""
Fix Build Issues Script
=======================

This script fixes common build issues encountered with the WOW Bingo Game build system.
"""

import os
import sys
import shutil
from pathlib import Path

def log(message, level="INFO"):
    """Log a message."""
    print(f"[{level}] {message}")

def fix_empty_directories():
    """Remove or fix empty directories that cause build issues."""
    log("Fixing empty directories...")
    
    project_root = Path(__file__).parent
    
    # Directories that might be empty and cause issues
    problematic_dirs = ['keys', 'instant_loading']
    
    for dir_name in problematic_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            # Check if directory is empty or has only __pycache__
            contents = list(dir_path.rglob('*'))
            python_files = [f for f in contents if f.suffix == '.py' and '__pycache__' not in str(f)]
            
            if not python_files:
                log(f"Directory '{dir_name}' has no Python files - will be excluded from build")
                # Create a marker file to indicate the directory should be excluded
                marker_file = dir_path / '.exclude_from_build'
                marker_file.touch()
                log(f"Created exclusion marker: {marker_file}")

def fix_nuitka_config():
    """Update Nuitka configuration to use newer options."""
    log("Checking Nuitka configuration...")
    
    project_root = Path(__file__).parent
    config_file = project_root / "nuitka-config.yml"
    
    if config_file.exists():
        log("Found nuitka-config.yml - updating deprecated options...")
        
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Replace deprecated options
        content = content.replace('disable-console: yes', 'windows-console-mode: disable')
        
        # Write back
        with open(config_file, 'w') as f:
            f.write(content)
        
        log("Updated nuitka-config.yml")

def clean_problematic_files():
    """Clean files that might cause build issues."""
    log("Cleaning problematic files...")
    
    project_root = Path(__file__).parent
    
    # Remove crash reports
    crash_reports = list(project_root.glob("nuitka-crash-report*.xml"))
    for report in crash_reports:
        report.unlink()
        log(f"Removed crash report: {report.name}")
    
    # Clean __pycache__ directories
    pycache_dirs = list(project_root.rglob("__pycache__"))
    for cache_dir in pycache_dirs:
        shutil.rmtree(cache_dir)
        log(f"Removed cache directory: {cache_dir}")

def create_minimal_requirements():
    """Create a minimal requirements file for building."""
    log("Creating minimal requirements file...")
    
    project_root = Path(__file__).parent
    minimal_req_file = project_root / "minimal_requirements.txt"
    
    minimal_requirements = """# Minimal requirements for building WOW Bingo Game
nuitka>=1.8.0
pygame>=2.0.0
pyperclip>=1.8.2
"""
    
    with open(minimal_req_file, 'w') as f:
        f.write(minimal_requirements)
    
    log(f"Created {minimal_req_file}")

def check_main_script():
    """Check if main script has any obvious issues."""
    log("Checking main script...")
    
    project_root = Path(__file__).parent
    main_script = project_root / "main.py"
    
    if not main_script.exists():
        log("main.py not found!", "ERROR")
        return False
    
    # Check for common issues
    with open(main_script, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    issues = []
    
    # Check for problematic imports
    problematic_imports = ['kivy', 'flask', 'matplotlib', 'sqlalchemy']
    for imp in problematic_imports:
        if f"import {imp}" in content or f"from {imp}" in content:
            issues.append(f"Found potentially problematic import: {imp}")
    
    if issues:
        log("Found potential issues in main.py:", "WARNING")
        for issue in issues:
            log(f"  - {issue}", "WARNING")
    else:
        log("main.py looks good")
    
    return True

def create_build_test_script():
    """Create a simple test script to verify the build works."""
    log("Creating build test script...")
    
    project_root = Path(__file__).parent
    test_script = project_root / "test_simple_build.py"
    
    test_content = '''#!/usr/bin/env python3
"""
Simple build test script
"""

import subprocess
import sys
from pathlib import Path

def test_simple_build():
    """Test the simple build script."""
    print("Testing simple build...")
    
    try:
        result = subprocess.run([
            sys.executable, 'nuitka_simple_build.py', '--debug'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("Simple build test PASSED")
            return True
        else:
            print("Simple build test FAILED")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("Build test timed out")
        return False
    except Exception as e:
        print(f"Build test error: {e}")
        return False

if __name__ == "__main__":
    test_simple_build()
'''
    
    with open(test_script, 'w') as f:
        f.write(test_content)
    
    log(f"Created {test_script}")

def main():
    """Main function to fix all build issues."""
    log("=" * 50)
    log("WOW Bingo Game - Build Issues Fix")
    log("=" * 50)
    
    try:
        # Run all fixes
        fix_empty_directories()
        fix_nuitka_config()
        clean_problematic_files()
        create_minimal_requirements()
        check_main_script()
        create_build_test_script()
        
        log("=" * 50)
        log("BUILD FIXES COMPLETED")
        log("=" * 50)
        log("")
        log("Next steps:")
        log("1. Try the simple build first:")
        log("   python nuitka_simple_build.py")
        log("")
        log("2. If that fails, install minimal requirements:")
        log("   pip install -r minimal_requirements.txt")
        log("")
        log("3. Use the batch script option 7 (Simple Build)")
        log("")
        log("4. If you still have issues, check the crash report:")
        log("   nuitka-crash-report.xml")
        
    except Exception as e:
        log(f"Error during fixes: {e}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
