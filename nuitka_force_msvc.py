#!/usr/bin/env python3
"""
WOW Bingo Game - Force MSVC Detection for Nuitka
================================================

This script forces <PERSON><PERSON>ka to use a specific MSVC installation
when automatic detection fails.
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def find_msvc_installations():
    """Find all MSVC installations."""
    msvc_paths = []
    
    # Common Visual Studio paths
    vs_roots = [
        "C:\\Program Files\\Microsoft Visual Studio",
        "C:\\Program Files (x86)\\Microsoft Visual Studio",
    ]
    
    for vs_root in vs_roots:
        if Path(vs_root).exists():
            for year in ["2022", "2019"]:
                for edition in ["BuildTools", "Community", "Professional", "Enterprise"]:
                    msvc_path = Path(vs_root) / year / edition / "VC" / "Tools" / "MSVC"
                    if msvc_path.exists():
                        # Find MSVC versions
                        for version_dir in msvc_path.iterdir():
                            if version_dir.is_dir():
                                msvc_paths.append({
                                    'path': str(msvc_path / version_dir),
                                    'version': version_dir.name,
                                    'year': year,
                                    'edition': edition
                                })
    
    return msvc_paths

def find_windows_sdk():
    """Find Windows SDK."""
    sdk_paths = [
        "C:\\Program Files (x86)\\Windows Kits\\10",
        "C:\\Program Files\\Windows Kits\\10",
    ]
    
    for sdk_path in sdk_paths:
        sdk_path = Path(sdk_path)
        if sdk_path.exists():
            include_path = sdk_path / "Include"
            if include_path.exists():
                # Find SDK versions
                versions = [d.name for d in include_path.iterdir() 
                           if d.is_dir() and d.name.startswith("10.")]
                if versions:
                    return {
                        'path': str(sdk_path),
                        'versions': versions,
                        'latest': max(versions)
                    }
    return None

def setup_environment_for_msvc(msvc_info, sdk_info):
    """Setup environment variables for MSVC."""
    print(f"Setting up environment for MSVC {msvc_info['version']} ({msvc_info['year']} {msvc_info['edition']})")
    
    msvc_path = Path(msvc_info['path'])
    
    # Set MSVC environment variables
    os.environ['VCINSTALLDIR'] = str(msvc_path.parent.parent)
    os.environ['VCToolsInstallDir'] = str(msvc_path)
    os.environ['VCToolsVersion'] = msvc_info['version']
    
    # Add MSVC bin to PATH
    msvc_bin = msvc_path / "bin" / "Hostx64" / "x64"
    if msvc_bin.exists():
        current_path = os.environ.get('PATH', '')
        os.environ['PATH'] = str(msvc_bin) + os.pathsep + current_path
        print(f"Added to PATH: {msvc_bin}")
    
    # Set Windows SDK environment
    if sdk_info:
        sdk_path = Path(sdk_info['path'])
        os.environ['WindowsSdkDir'] = str(sdk_path)
        os.environ['WindowsSDKVersion'] = sdk_info['latest'] + "\\"
        
        # Add SDK bin to PATH
        sdk_bin = sdk_path / "bin" / sdk_info['latest'] / "x64"
        if sdk_bin.exists():
            current_path = os.environ.get('PATH', '')
            os.environ['PATH'] = str(sdk_bin) + os.pathsep + current_path
            print(f"Added to PATH: {sdk_bin}")
    
    print("Environment setup complete")

def build_with_forced_msvc():
    """Build with forced MSVC detection."""
    project_root = Path(__file__).parent.absolute()
    dist_dir = project_root / "dist"
    
    print("=" * 60)
    print("WOW Bingo Game - Force MSVC Detection")
    print("=" * 60)
    
    # Find MSVC installations
    msvc_installations = find_msvc_installations()
    if not msvc_installations:
        print("ERROR: No MSVC installations found!")
        print("Please install Visual Studio Build Tools")
        return False
    
    print(f"Found {len(msvc_installations)} MSVC installation(s):")
    for i, msvc in enumerate(msvc_installations):
        print(f"  {i+1}. MSVC {msvc['version']} ({msvc['year']} {msvc['edition']})")
    
    # Use the first (usually latest) installation
    selected_msvc = msvc_installations[0]
    print(f"Using: MSVC {selected_msvc['version']} ({selected_msvc['year']} {selected_msvc['edition']})")
    
    # Find Windows SDK
    sdk_info = find_windows_sdk()
    if sdk_info:
        print(f"Found Windows SDK: {sdk_info['latest']}")
    else:
        print("WARNING: Windows SDK not found")
    
    # Setup environment
    setup_environment_for_msvc(selected_msvc, sdk_info)
    
    # Prepare build environment
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir(exist_ok=True)
    
    # Build command with explicit MSVC settings
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',
        '--output-filename=WOW_Bingo_Game.exe',
        f'--output-dir={dist_dir}',
        '--assume-yes-for-downloads',
        '--windows-console-mode=disable',
        '--show-progress',
        '--show-memory',
        '--msvc=latest',
        '--jobs=2',
        
        # Windows metadata
        '--windows-company-name=WOW Games',
        '--windows-product-name=WOW Bingo Game',
        '--windows-file-version=1.0.0',
        '--windows-product-version=1.0.0',
    ]
    
    # Add icon if available
    icon_path = project_root / "assets" / "app_logo.ico"
    if icon_path.exists():
        cmd.append(f'--windows-icon-from-ico={icon_path}')
    
    # Include data directories
    for data_dir in ['assets', 'data']:
        data_path = project_root / data_dir
        if data_path.exists():
            cmd.append(f'--include-data-dir={data_path}={data_dir}')
    
    # Include essential packages
    essential_packages = ['pygame', 'pyperclip', 'sqlite3', 'json', 'datetime']
    for package in essential_packages:
        cmd.append(f'--include-package={package}')
    
    # Add main script
    cmd.append('main.py')
    
    # Execute Nuitka
    print("\nExecuting Nuitka with forced MSVC detection...")
    print("This may take 10-20 minutes...")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, cwd=project_root, text=True)
        
        if result.returncode == 0:
            build_time = time.time() - start_time
            
            # Find executable
            executable_path = None
            possible_paths = [
                dist_dir / "WOW_Bingo_Game.exe",
                dist_dir / "WOW_Bingo_Game" / "WOW_Bingo_Game.exe",
            ]
            
            for path in possible_paths:
                if path.exists():
                    executable_path = path
                    break
            
            if executable_path:
                size_mb = executable_path.stat().st_size / (1024 * 1024)
                print("=" * 60)
                print("BUILD SUCCESS with Forced MSVC Detection!")
                print("=" * 60)
                print(f"Executable: {executable_path}")
                print(f"Size: {size_mb:.1f} MB")
                print(f"Build time: {build_time:.1f} seconds")
                print(f"Used MSVC: {selected_msvc['version']} ({selected_msvc['year']} {selected_msvc['edition']})")
                print("=" * 60)
                return True
            else:
                print("ERROR: Executable not found after build")
                return False
        else:
            print(f"ERROR: Nuitka build failed with return code {result.returncode}")
            return False
    
    except Exception as e:
        print(f"ERROR: Build failed with exception: {e}")
        return False

def main():
    """Main function."""
    # Check prerequisites
    if not Path("main.py").exists():
        print("ERROR: main.py not found!")
        return False
    
    if not Path("assets").exists():
        print("ERROR: assets directory not found!")
        return False
    
    # Try to install Nuitka if needed
    try:
        subprocess.run([sys.executable, '-m', 'nuitka', '--version'],
                     capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Installing Nuitka...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka>=2.4.0'],
                     check=True)
    
    return build_with_forced_msvc()

if __name__ == "__main__":
    try:
        success = main()
        input(f"\nBuild {'succeeded' if success else 'failed'}. Press Enter to exit...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
