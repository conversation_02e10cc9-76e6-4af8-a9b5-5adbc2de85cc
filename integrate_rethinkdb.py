"""
RethinkDB Integration Script for the WOW Games application.

This script integrates the RethinkDB hybrid database system into the
existing application by updating the necessary imports and patching
existing code to use the hybrid database integration.
"""

import os
import sys
import re
import glob
import shutil
import argparse
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'integrate_rethinkdb.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def backup_file(file_path):
    """Create a backup of a file before modifying it."""
    try:
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join('backup', 'rethinkdb_integration', datetime.now().strftime('%Y%m%d_%H%M%S'))
        os.makedirs(backup_dir, exist_ok=True)

        # Copy file to backup directory
        backup_path = os.path.join(backup_dir, os.path.basename(file_path))
        shutil.copy2(file_path, backup_path)

        logging.info(f"Created backup of {file_path} at {backup_path}")
        return True
    except Exception as e:
        logging.error(f"Error creating backup of {file_path}: {e}")
        return False

def patch_file(file_path, replacements):
    """Patch a file with the specified replacements."""
    try:
        # Create backup
        if not backup_file(file_path):
            return False

        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Apply replacements
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logging.info(f"Patched {file_path} with {len(replacements)} replacements")
        return True
    except Exception as e:
        logging.error(f"Error patching {file_path}: {e}")
        return False

def find_files_to_patch():
    """Find files that need to be patched to use the hybrid database."""
    try:
        # List of files to check
        files_to_check = [
            'stats_integration.py',
            'game_stats_integration.py',
            'stats_page.py',
            'main.py',
            'settings_page.py'
        ]

        # Add any Python files that import stats_db or stats_integration
        for file_path in glob.glob('**/*.py', recursive=True):
            # Skip files we already know about
            if os.path.basename(file_path) in files_to_check:
                continue

            # Check if file imports stats_db or stats_integration
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                if 'from stats_db import' in content or 'import stats_db' in content or \
                   'from stats_integration import' in content or 'import stats_integration' in content:
                    files_to_check.append(file_path)

        # Check if files exist
        files_to_patch = []
        for file_name in files_to_check:
            if os.path.exists(file_name):
                files_to_patch.append(file_name)

        return files_to_patch
    except Exception as e:
        logging.error(f"Error finding files to patch: {e}")
        return []

def patch_stats_integration():
    """Patch stats_integration.py to use the hybrid database."""
    if not os.path.exists('stats_integration.py'):
        logging.warning("stats_integration.py not found, skipping")
        return False

    replacements = [
        # Import hybrid DB integration
        (r'import stats_db\s+from stats_db import get_stats_db_manager',
         'import stats_db\nfrom stats_db import get_stats_db_manager\n\n# Import hybrid DB integration\ntry:\n    from hybrid_db_integration import get_hybrid_db_integration\n    HYBRID_DB_AVAILABLE = True\n    print("Hybrid database integration loaded")\nexcept ImportError:\n    HYBRID_DB_AVAILABLE = False\n    print("Hybrid database not available, using SQLite only")'),

        # Update get_stats_summary
        (r'def get_stats_summary\(\):(.*?)return summary',
         'def get_stats_summary():\\1# Use hybrid DB if available\\n    if HYBRID_DB_AVAILABLE:\\n        try:\\n            return get_hybrid_db_integration().get_stats_summary()\\n        except Exception as e:\\n            print(f"Error using hybrid DB for stats summary: {e}")\\n            # Fall back to original implementation\\n    return summary'),

        # Update get_game_history
        (r'def get_game_history\(page=0, page_size=10\):(.*?)return result',
         'def get_game_history(page=0, page_size=10):\\1# Use hybrid DB if available\\n    if HYBRID_DB_AVAILABLE:\\n        try:\\n            return get_hybrid_db_integration().get_game_history(page, page_size)\\n        except Exception as e:\\n            print(f"Error using hybrid DB for game history: {e}")\\n            # Fall back to original implementation\\n    return result'),

        # Update force_refresh_data
        (r'def force_refresh_data\(\):(.*?)return True',
         'def force_refresh_data():\\1# Use hybrid DB if available\\n    if HYBRID_DB_AVAILABLE:\\n        try:\\n            return get_hybrid_db_integration().force_refresh_data()\\n        except Exception as e:\\n            print(f"Error using hybrid DB for force refresh: {e}")\\n            # Fall back to original implementation\\n    return True')
    ]

    return patch_file('stats_integration.py', replacements)

def patch_game_stats_integration():
    """Patch game_stats_integration.py to use the hybrid database."""
    if not os.path.exists('game_stats_integration.py'):
        logging.warning("game_stats_integration.py not found, skipping")
        return False

    replacements = [
        # Import hybrid DB integration
        (r'import stats_db\s+from stats_db import get_stats_db_manager',
         'import stats_db\nfrom stats_db import get_stats_db_manager\n\n# Import hybrid DB integration\ntry:\n    from hybrid_db_integration import get_hybrid_db_integration\n    HYBRID_DB_AVAILABLE = True\n    print("Hybrid database integration loaded")\nexcept ImportError:\n    HYBRID_DB_AVAILABLE = False\n    print("Hybrid database not available, using SQLite only")'),

        # Update integrate_with_game_state
        (r'def integrate_with_game_state\(game_state\):(.*?)stats_db = get_stats_db_manager\(\)',
         'def integrate_with_game_state(game_state):\\1# Use hybrid DB if available\\n    if HYBRID_DB_AVAILABLE:\\n        stats_db = get_hybrid_db_integration()\\n    else:\\n        stats_db = get_stats_db_manager()')
    ]

    return patch_file('game_stats_integration.py', replacements)

def patch_stats_page():
    """Patch stats_page.py to use the hybrid database."""
    if not os.path.exists('stats_page.py'):
        logging.warning("stats_page.py not found, skipping")
        return False

    # Check if the file is already patched
    with open('stats_page.py', 'r', encoding='utf-8') as f:
        content = f.read()
        if 'HYBRID_DB_AVAILABLE' in content:
            logging.info("stats_page.py already patched, skipping")
            return True

    replacements = [
        # Import hybrid DB integration - only add if not already present
        (r'(# Import hybrid database integration\ntry:\n    from hybrid_db_integration import get_hybrid_db_integration\n    HYBRID_DB_AVAILABLE = True\nexcept ImportError:\n    HYBRID_DB_AVAILABLE = False)',
         '\\1'),  # No-op if already present

        # Add import after existing imports if not present
        (r'(from common_header import draw_wow_bingo_header\n)',
         '\\1\n# Import hybrid database integration\ntry:\n    from hybrid_db_integration import get_hybrid_db_integration\n    HYBRID_DB_AVAILABLE = True\nexcept ImportError:\n    HYBRID_DB_AVAILABLE = False\n    print("Hybrid database not available, using SQLite only")\n')
    ]

    return patch_file('stats_page.py', replacements)

def patch_main():
    """Patch main.py to use the hybrid database."""
    if not os.path.exists('main.py'):
        logging.warning("main.py not found, skipping")
        return False

    replacements = [
        # Import hybrid DB integration at the end of imports
        (r'(from stats_integration import get_stats_summary, get_game_history, migrate_legacy_stats, force_refresh_data)',
         '\\1\n\n# Import hybrid DB integration\ntry:\n    from hybrid_db_integration import get_hybrid_db_integration\n    HYBRID_DB_AVAILABLE = True\n    print("Hybrid database integration loaded")\nexcept ImportError:\n    HYBRID_DB_AVAILABLE = False\n    print("Hybrid database not available, using SQLite only")'),

        # Add connectivity indicator to draw method
        (r'def draw\(self\):(.*?)(# Draw the background)',
         'def draw(self):\\1# Draw connectivity indicator if hybrid DB is available\\n        if HYBRID_DB_AVAILABLE:\\n            try:\\n                db_integration = get_hybrid_db_integration()\\n                is_online = db_integration.is_online()\\n                status_color = (0, 200, 0) if is_online else (200, 0, 0)\\n                status_text = "Online" if is_online else "Offline"\\n                \\n                # Draw indicator in top-right corner\\n                pygame.draw.circle(self.screen, status_color, (self.screen.get_width() - 20, 20), 8)\\n                \\n                # Draw text label\\n                font = pygame.font.SysFont("Arial", 16)\\n                text = font.render(status_text, True, (255, 255, 255))\\n                text_rect = text.get_rect(midright=(self.screen.get_width() - 30, 20))\\n                self.screen.blit(text, text_rect)\\n            except Exception as e:\\n                print(f"Error drawing connectivity indicator: {e}")\\n        \\n        \\2')
    ]

    return patch_file('main.py', replacements)

def create_integration_script():
    """Create a script to launch the game with RethinkDB integration."""
    try:
        script_content = """@echo off
echo Starting WOW Games with RethinkDB integration...

REM Check if RethinkDB server is running
python setup_rethinkdb.py --check

REM Start RethinkDB server if it's not running
IF %ERRORLEVEL% NEQ 0 (
    echo RethinkDB server is not running, attempting to start...
    python setup_rethinkdb.py --start
    timeout /t 5
)

REM Check RethinkDB status
python rethink_status.py --status

REM Start the game
python main.py
"""

        # Write to file
        with open('start_with_rethinkdb.bat', 'w') as f:
            f.write(script_content)

        logging.info("Created start_with_rethinkdb.bat")
        return True
    except Exception as e:
        logging.error(f"Error creating integration script: {e}")
        return False

def install_requirements():
    """Install required packages."""
    try:
        import subprocess

        # Install rethinkdb package
        print("Installing rethinkdb package...")
        subprocess.run([sys.executable, "-m", "pip", "install", "rethinkdb"])

        logging.info("Installed required packages")
        return True
    except Exception as e:
        logging.error(f"Error installing required packages: {e}")
        return False

def main():
    """Main function for the integration script."""
    parser = argparse.ArgumentParser(description='RethinkDB Integration Script for WOW Games')

    # Define command-line arguments
    parser.add_argument('--install', action='store_true', help='Install required packages')
    parser.add_argument('--patch', action='store_true', help='Patch existing files to use hybrid database')
    parser.add_argument('--script', action='store_true', help='Create integration script')
    parser.add_argument('--all', action='store_true', help='Perform all integration steps')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')

    args = parser.parse_args()

    # If no arguments provided, show help
    if len(sys.argv) == 1:
        parser.print_help()
        return False

    # Define steps to perform
    steps = []

    if args.install or args.all:
        steps.append(('Install required packages', install_requirements))

    if args.patch or args.all:
        steps.append(('Patch stats_integration.py', patch_stats_integration))
        steps.append(('Patch game_stats_integration.py', patch_game_stats_integration))
        steps.append(('Patch stats_page.py', patch_stats_page))
        steps.append(('Patch main.py', patch_main))

    if args.script or args.all:
        steps.append(('Create integration script', create_integration_script))

    # Perform steps
    success = True

    print("\n=== RethinkDB Integration for WOW Games ===\n")

    if args.dry_run:
        print("DRY RUN - No changes will be made\n")

    for step_name, step_func in steps:
        print(f"Step: {step_name}")

        if args.dry_run:
            print("  Would execute this step\n")
        else:
            try:
                result = step_func()
                if result:
                    print("  Success\n")
                else:
                    print("  Failed\n")
                    success = False
            except Exception as e:
                print(f"  Error: {e}\n")
                logging.error(f"Error in {step_name}: {e}")
                success = False

    if success:
        print("Integration completed successfully!")

        if not args.dry_run:
            print("\nNext steps:")
            print("1. Run the setup script to initialize RethinkDB:")
            print("   python setup_rethinkdb.py --init")
            print("\n2. Migrate data from SQLite to RethinkDB:")
            print("   python setup_rethinkdb.py --migrate")
            print("\n3. Use the status utility to check the connection:")
            print("   python rethink_status.py")
            print("\n4. Start the game with RethinkDB integration:")
            print("   start_with_rethinkdb.bat")
    else:
        print("Integration failed. Check the log file for details.")

    return success

if __name__ == '__main__':
    sys.exit(0 if main() else 1)