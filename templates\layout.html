<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WOW Games RethinkDB Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav {
            background-color: #444;
            overflow: hidden;
        }
        .nav a {
            float: left;
            display: block;
            color: white;
            text-align: center;
            padding: 14px 16px;
            text-decoration: none;
        }
        .nav a:hover {
            background-color: #555;
        }
        .nav a.active {
            background-color: #4CAF50;
        }
        .content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status.online {
            background-color: #4CAF50;
        }
        .status.offline {
            background-color: #f44336;
        }
        .dashboard-summary {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        .summary-card {
            flex: 1;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin: 10px;
            padding: 20px;
            min-width: 200px;
        }
        .summary-card h3 {
            margin-top: 0;
            color: #444;
        }
        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination a {
            display: inline-block;
            padding: 8px 16px;
            text-decoration: none;
            background-color: #f2f2f2;
            color: black;
            border-radius: 5px;
            margin: 0 5px;
        }
        .pagination a.active {
            background-color: #4CAF50;
            color: white;
        }
        .pagination a:hover:not(.active) {
            background-color: #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"], input[type="number"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .alert-danger {
            background-color: #f2dede;
            color: #a94442;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WOW Games RethinkDB Dashboard</h1>
    </div>
    <div class="nav">
        <a href="/" {% if active_page == 'home' %}class="active"{% endif %}>Home</a>
        <a href="/daily-stats" {% if active_page == 'daily-stats' %}class="active"{% endif %}>Daily Stats</a>
        <a href="/game-history" {% if active_page == 'game-history' %}class="active"{% endif %}>Game History</a>
        <a href="/wallet" {% if active_page == 'wallet' %}class="active"{% endif %}>Wallet</a>
        <a href="/performance" {% if active_page == 'performance' %}class="active"{% endif %}>Performance</a>
        <a href="/backups" {% if active_page == 'backups' %}class="active"{% endif %}>Backups</a>
        <a href="/sync-status" {% if active_page == 'sync-status' %}class="active"{% endif %}>Sync Status</a>
        <a href="/settings" {% if active_page == 'settings' %}class="active"{% endif %}>Settings</a>
    </div>
    <div class="container">
        {% if connection_status == True %}
            <div class="status online"></div> Connected to RethinkDB
        {% else %}
            <div class="status offline"></div> Not connected to RethinkDB
        {% endif %}

        <div class="content">
            {% block content %}{% endblock %}
        </div>
    </div>
</body>
</html>
