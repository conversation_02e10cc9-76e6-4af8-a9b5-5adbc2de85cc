import pygame
import math
import time
import json
import os
from pygame import gfxdraw

class CartellaPreviewOverlay:
    """
    Professional 3D Cartella Numbers Preview Overlay

    Features:
    - Displays 3 cartella numbers simultaneously with center zoom
    - Automatic sliding animation with speed control
    - Modern 3D visual effects with perspective scaling
    - Game pause/resume functionality
    - Professional overlay design with smooth animations
    """

    def __init__(self, screen, game_instance):
        self.screen = screen
        self.game = game_instance
        self.active = False

        # Animation state - ENHANCED CENTER-FOCUSED DESIGN
        self.current_index = 0
        self.transition_progress = 0.0  # Renamed from slide_progress for clarity
        self.slide_speed = 1.0  # Adjustable speed (0.5 to 3.0)
        self.auto_slide = True
        self.last_slide_time = 0
        self.slide_interval = 2000  # 2 seconds between slides

        # Center-focused transition system
        self.transition_type = "fade"  # Options: "fade", "scale", "flip"
        self.center_transition_active = False
        self.previous_center_number = None
        self.transition_direction = 1  # 1 for forward, -1 for backward

        # Visual settings - ENHANCED CENTER-FOCUSED DESIGN WITH IMPROVED BLUR
        self.overlay_alpha = 240  # Increased from 200 for stronger background blur
        self.card_width = 280
        self.card_height = 200
        self.card_spacing = 50

        # Enhanced center number prominence
        self.center_scale = 1.6  # Increased scale for center number (was 1.3)
        self.side_scale = 0.7    # Reduced scale for side numbers (was 0.8)
        self.side_alpha = 140    # Slightly increased for better visibility with stronger blur
        self.center_alpha = 255  # Full alpha for center number

        # Fixed positioning system
        self.side_number_spacing = 300  # Fixed distance from center for side numbers

        # Transition overlap prevention
        self.transition_clear_background = True  # Clear center area during transitions
        self.transition_z_order_separation = 5   # Pixel separation for z-order rendering

        # Colors
        self.bg_color = (0, 0, 30, self.overlay_alpha)
        self.card_bg_color = (40, 60, 100)
        self.card_border_color = (100, 150, 255)
        self.text_color = (255, 255, 255)
        self.number_color = (255, 220, 100)
        self.center_highlight = (255, 200, 50)

        # Control panel
        self.control_panel_height = 80
        self.button_width = 120
        self.button_height = 35

        # Game state management
        self.was_game_paused = False
        self.game_was_running = False

        # Registered cartella data
        self.registered_cartellas = []
        self.cartella_boards = {}

        # Animation easing
        self.ease_type = "ease_in_out"

        # Initialize fonts
        self.init_fonts()

        # Load cartella data
        self.load_registered_cartellas()

    def init_fonts(self):
        """Initialize fonts with proper scaling"""
        screen_width, screen_height = self.screen.get_size()
        scale_factor = min(screen_width / 1024, screen_height / 768)

        self.title_font = pygame.font.SysFont("Arial", int(32 * scale_factor), bold=True)
        self.header_font = pygame.font.SysFont("Arial", int(24 * scale_factor), bold=True)
        self.number_font = pygame.font.SysFont("Arial", int(18 * scale_factor), bold=True)
        self.button_font = pygame.font.SysFont("Arial", int(16 * scale_factor), bold=True)
        self.control_font = pygame.font.SysFont("Arial", int(14 * scale_factor))

    def load_registered_cartellas(self):
        """Load registered cartella numbers (numbers only, no board data needed)"""
        self.registered_cartellas = []

        # Get registered cartella numbers from players
        if hasattr(self.game, 'players') and self.game.players:
            print(f"Cartella Preview: Found {len(self.game.players)} players")
            for player in self.game.players:
                # Try different possible attribute names for cartella number
                cartella_num = None
                for attr_name in ['cartela_no', 'cartella_no', 'cartella_number', 'card_number', 'number']:
                    if hasattr(player, attr_name):
                        cartella_num = getattr(player, attr_name)
                        break

                if cartella_num is not None:
                    self.registered_cartellas.append(cartella_num)
        else:
            print("Cartella Preview: No players found in game object")

            # Fallback: Try to load players directly from JSON file
            try:
                from player_storage import load_players_from_json
                fallback_players = load_players_from_json()
                if fallback_players:
                    print(f"Cartella Preview: Loaded {len(fallback_players)} players from JSON fallback")
                    for player in fallback_players:
                        if hasattr(player, 'cartela_no'):
                            self.registered_cartellas.append(player.cartela_no)
                else:
                    print("Cartella Preview: No players found in JSON file either")
            except Exception as e:
                print(f"Cartella Preview: Error loading players from JSON: {e}")

        # Sort cartella numbers for consistent display
        self.registered_cartellas.sort()

        print(f"Final: Loaded {len(self.registered_cartellas)} registered cartellas: {self.registered_cartellas}")

    def load_board_for_cartella(self, cartella_number):
        """Load board data for a specific cartella number"""
        try:
            # Try to load from existing boards file
            boards_file = os.path.join('data', 'bingo_boards.json')
            if os.path.exists(boards_file):
                with open(boards_file, 'r') as file:
                    boards_data = json.load(file)
                    cartella_key = str(cartella_number)
                    if cartella_key in boards_data:
                        return boards_data[cartella_key]

            # If not found, generate deterministic board
            return self.generate_deterministic_board(cartella_number)

        except Exception as e:
            print(f"Error loading board for cartella {cartella_number}: {e}")
            return self.create_fallback_board()

    def generate_deterministic_board(self, cartella_number):
        """Generate a deterministic board based on cartella number"""
        import random

        # Use cartella number as seed for consistent results
        random.seed(cartella_number)

        board = []

        # For each column (B, I, N, G, O)
        for col in range(5):
            column_values = []
            # Range for this column: col*15+1 to col*15+15
            min_val = col * 15 + 1
            max_val = min_val + 14

            # Generate 5 unique random numbers for this column
            values = list(range(min_val, max_val + 1))
            random.shuffle(values)
            column_values = values[:5]

            # Set the center square (N column, 3rd row) to 0 (free space)
            if col == 2:
                column_values[2] = 0

            board.append(column_values)

        # Reset random seed
        random.seed()

        return board

    def create_fallback_board(self):
        """Create a fallback board if loading fails"""
        board = []

        for col in range(5):
            column = []
            for row in range(5):
                if col == 2 and row == 2:
                    # Free space in center
                    number = 0
                else:
                    # Sequential numbers for each column
                    number = col * 15 + row + 1
                column.append(number)
            board.append(column)

        return board

    def show(self):
        """Show the preview overlay"""
        # Always reload cartella data first
        self.load_registered_cartellas()

        if not self.registered_cartellas:
            # Show message if no cartellas are registered
            self.show_no_cartellas_message()
            return

        # Store game state for pause/resume functionality
        if hasattr(self.game, 'game_started') and self.game.game_started:
            self.game_was_running = True
            if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'is_paused'):
                self.was_game_paused = self.game.game_state.is_paused
                if not self.was_game_paused:
                    # Pause the game
                    self.game.game_state.pause_game()
                    print("Game paused for cartella preview")

        self.active = True
        self.current_index = 0
        self.transition_progress = 0.0
        self.center_transition_active = False
        self.previous_center_number = None
        self.last_slide_time = pygame.time.get_ticks()

    def hide(self):
        """Hide the preview overlay"""
        self.active = False

        # Resume game if it was running
        if self.game_was_running and not self.was_game_paused:
            if hasattr(self.game, 'game_state'):
                self.game.game_state.resume_game()
                print("Game resumed after cartella preview")

        self.game_was_running = False
        self.was_game_paused = False

    def show_no_cartellas_message(self):
        """Show message when no cartellas are registered"""
        screen_width, screen_height = self.screen.get_size()

        # Create semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill(self.bg_color)
        self.screen.blit(overlay, (0, 0))

        # Message
        message = "No Registered Cartellas Found"
        subtitle = "Please register cartella numbers first"

        # Render text
        title_surf = self.title_font.render(message, True, self.text_color)
        subtitle_surf = self.header_font.render(subtitle, True, (200, 200, 200))

        # Center text
        title_rect = title_surf.get_rect(center=(screen_width // 2, screen_height // 2 - 30))
        subtitle_rect = subtitle_surf.get_rect(center=(screen_width // 2, screen_height // 2 + 20))

        self.screen.blit(title_surf, title_rect)
        self.screen.blit(subtitle_surf, subtitle_rect)

        # Debug info (if available)
        if hasattr(self, 'game') and hasattr(self.game, 'players'):
            debug_font = pygame.font.SysFont("Arial", 16)
            player_count = len(self.game.players) if self.game.players else 0
            cartella_count = len(self.registered_cartellas) if hasattr(self, 'registered_cartellas') else 0
            debug_text = f"Debug: Found {player_count} players, {cartella_count} cartellas"
            debug_surf = debug_font.render(debug_text, True, (150, 150, 150))
            debug_rect = debug_surf.get_rect(center=(screen_width // 2, screen_height // 2 + 60))
            self.screen.blit(debug_surf, debug_rect)

        # Close button
        close_button_rect = pygame.Rect(screen_width // 2 - 60, screen_height // 2 + 100, 120, 40)
        pygame.draw.rect(self.screen, self.card_border_color, close_button_rect, border_radius=8)

        close_text = self.button_font.render("Close", True, self.text_color)
        close_text_rect = close_text.get_rect(center=close_button_rect.center)
        self.screen.blit(close_text, close_text_rect)

        pygame.display.flip()

        # Wait for click or key press
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    waiting = False
                elif event.type == pygame.KEYDOWN:
                    if event.key in (pygame.K_ESCAPE, pygame.K_RETURN, pygame.K_SPACE):
                        waiting = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if close_button_rect.collidepoint(event.pos):
                        waiting = False

    def update(self):
        """Update animation state - ENHANCED CENTER-FOCUSED SYSTEM"""
        if not self.active or not self.registered_cartellas:
            return

        current_time = pygame.time.get_ticks()

        # Auto-slide animation with dynamic speed - CENTER-FOCUSED
        if self.auto_slide and len(self.registered_cartellas) > 1:
            time_since_last_slide = current_time - self.last_slide_time

            # Calculate dynamic slide interval based on speed
            # Base interval is 2000ms, adjusted by speed (higher speed = shorter interval)
            dynamic_slide_interval = max(500, int(self.slide_interval / self.slide_speed))

            # Debug output for speed application (only occasionally to avoid spam)
            if hasattr(self, '_debug_counter'):
                self._debug_counter += 1
            else:
                self._debug_counter = 0

            if self._debug_counter % 60 == 0:  # Print every 60 frames (~2 seconds)
                print(f"Auto-slide active: Speed={self.slide_speed:.1f}x, Interval={dynamic_slide_interval}ms")

            if time_since_last_slide >= dynamic_slide_interval and not self.center_transition_active:
                # Start next center transition
                self.start_center_transition(1)  # Forward direction
                self.last_slide_time = current_time

        # Update center transition progress with improved speed calculation
        if self.center_transition_active:
            # Calculate progress based on speed - more responsive calculation
            # Base progress increment adjusted by speed multiplier
            base_increment = 0.03  # Slightly faster for center transitions
            progress_increment = base_increment * self.slide_speed
            self.transition_progress = min(1.0, self.transition_progress + progress_increment)

            # Complete transition when progress reaches 1.0
            if self.transition_progress >= 1.0:
                self.complete_center_transition()

    def next_cartella(self):
        """Move to next cartella - CENTER-FOCUSED TRANSITION"""
        if len(self.registered_cartellas) > 1 and not self.center_transition_active:
            self.start_center_transition(1)  # Forward direction

    def previous_cartella(self):
        """Move to previous cartella - CENTER-FOCUSED TRANSITION"""
        if len(self.registered_cartellas) > 1 and not self.center_transition_active:
            self.start_center_transition(-1)  # Backward direction

    def start_center_transition(self, direction):
        """Start a center-focused transition"""
        if self.center_transition_active:
            return  # Already transitioning

        # Store current center number for transition
        self.previous_center_number = self.registered_cartellas[self.current_index]

        # Update index
        if direction > 0:
            self.current_index = (self.current_index + 1) % len(self.registered_cartellas)
        else:
            self.current_index = (self.current_index - 1) % len(self.registered_cartellas)

        # Start transition
        self.center_transition_active = True
        self.transition_progress = 0.0
        self.transition_direction = direction

    def complete_center_transition(self):
        """Complete the center transition"""
        self.center_transition_active = False
        self.transition_progress = 0.0
        self.previous_center_number = None

    def ease_in_out(self, t):
        """Smooth easing function"""
        return t * t * (3.0 - 2.0 * t)

    def ease_out_back(self, t):
        """Back easing for more dynamic animation"""
        c1 = 1.70158
        c3 = c1 + 1
        return 1 + c3 * pow(t - 1, 3) + c1 * pow(t - 1, 2)

    def get_eased_progress(self):
        """Get eased animation progress for center transitions"""
        if self.ease_type == "ease_in_out":
            return self.ease_in_out(self.transition_progress)
        elif self.ease_type == "ease_out_back":
            return self.ease_out_back(self.transition_progress)
        else:
            return self.transition_progress

    def get_fade_alpha(self, is_incoming=True):
        """Get alpha value for fade transition"""
        progress = self.get_eased_progress()
        if is_incoming:
            return int(255 * progress)  # Fade in
        else:
            return int(255 * (1.0 - progress))  # Fade out

    def get_scale_factor(self, is_incoming=True):
        """Get scale factor for scale transition"""
        progress = self.get_eased_progress()
        base_scale = self.get_animated_center_scale()

        if is_incoming:
            # Scale up from 0.5 to full size
            scale_progress = 0.5 + (0.5 * progress)
            return base_scale * scale_progress
        else:
            # Scale down from full size to 0.5
            scale_progress = 1.0 - (0.5 * progress)
            return base_scale * scale_progress

    def get_animated_center_scale(self):
        """Get animated scale for center number with breathing effect"""
        # Base scale
        base_scale = 1.3

        # Breathing animation - subtle scale variation
        breath_cycle = (pygame.time.get_ticks() / 800.0) % 2.0  # 1.6 second cycle
        if breath_cycle > 1.0:
            breath_cycle = 2.0 - breath_cycle

        # Smooth breathing effect (0.05 scale variation)
        breath_factor = 0.05 * (0.5 + 0.5 * breath_cycle)  # 0 to 0.05

        return base_scale + breath_factor

    def draw(self):
        """Draw the preview overlay"""
        if not self.active:
            return

        screen_width, screen_height = self.screen.get_size()

        # Draw enhanced semi-transparent background with stronger blur effect
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)

        # Create layered blur effect for better visual separation
        # Base layer - darker for stronger separation
        base_color = (0, 0, 20, self.overlay_alpha)
        overlay.fill(base_color)

        # Add subtle gradient for depth
        gradient_overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        for y in range(0, screen_height, 4):  # Every 4 pixels for performance
            alpha_factor = 0.8 + 0.2 * (y / screen_height)  # Slight gradient
            gradient_alpha = int(30 * alpha_factor)
            gradient_color = (10, 20, 40, gradient_alpha)
            pygame.draw.line(gradient_overlay, gradient_color, (0, y), (screen_width, y), 4)

        # Composite the overlays
        self.screen.blit(overlay, (0, 0))
        self.screen.blit(gradient_overlay, (0, 0))

        # Draw title
        title_text = f"Registered Cartella Numbers ({len(self.registered_cartellas)})"
        title_surf = self.title_font.render(title_text, True, self.text_color)
        title_rect = title_surf.get_rect(center=(screen_width // 2, 60))
        self.screen.blit(title_surf, title_rect)

        # Draw hotkey hint
        hotkey_text = "Hotkey: Ctrl+P"
        hotkey_font = pygame.font.SysFont("Arial", 16, italic=True)
        hotkey_surf = hotkey_font.render(hotkey_text, True, (180, 180, 180))
        hotkey_rect = hotkey_surf.get_rect(center=(screen_width // 2, 85))
        self.screen.blit(hotkey_surf, hotkey_rect)

        if not self.registered_cartellas:
            return

        # Calculate card positions
        center_x = screen_width // 2
        center_y = screen_height // 2 - 40

        # Draw 3 cards (previous, current, next)
        self.draw_three_cards(center_x, center_y)

        # Draw control panel
        self.draw_control_panel()

        # Draw navigation indicators
        self.draw_navigation_indicators()

    def draw_three_cards(self, center_x, center_y):
        """Draw three cartella number displays with CENTER-FOCUSED FIXED POSITIONING"""
        if not self.registered_cartellas:
            return

        total_cartellas = len(self.registered_cartellas)

        # Calculate indices for three visible numbers
        prev_index = (self.current_index - 1) % total_cartellas
        curr_index = self.current_index
        next_index = (self.current_index + 1) % total_cartellas

        # FIXED POSITIONING SYSTEM - No horizontal sliding
        positions = [
            # Left number (previous) - FIXED POSITION
            {
                'x': center_x - self.side_number_spacing,  # Fixed left position
                'y': center_y + 30,  # Slightly lower than center
                'scale': self.side_scale,
                'alpha': self.side_alpha,
                'index': prev_index,
                'depth': 0.3,
                'is_side': True
            },
            # Right number (next) - FIXED POSITION
            {
                'x': center_x + self.side_number_spacing,  # Fixed right position
                'y': center_y + 30,  # Slightly lower than center
                'scale': self.side_scale,
                'alpha': self.side_alpha,
                'index': next_index,
                'depth': 0.3,
                'is_side': True
            }
        ]

        # Draw side numbers first (they never change position)
        for pos in positions:
            self.draw_cartella_number(pos)

        # Draw center number(s) with transition effects
        self.draw_center_number_with_transition(center_x, center_y - 20)

    def draw_center_number_with_transition(self, center_x, center_y):
        """Draw center number with smooth transition effects - FIXED OVERLAP ISSUES"""
        if not self.registered_cartellas:
            return

        current_number = self.registered_cartellas[self.current_index]

        # Clear center area during transitions to prevent overlap artifacts
        if self.center_transition_active and self.transition_clear_background:
            self.clear_center_area(center_x, center_y)

        if self.center_transition_active and self.previous_center_number is not None:
            # Draw transition between previous and current numbers with proper z-order
            if self.transition_type == "fade":
                self.draw_fade_transition_fixed(center_x, center_y, current_number, self.previous_center_number)
            elif self.transition_type == "scale":
                self.draw_scale_transition_fixed(center_x, center_y, current_number, self.previous_center_number)
            else:  # Default to fade
                self.draw_fade_transition_fixed(center_x, center_y, current_number, self.previous_center_number)
        else:
            # Draw normal center number
            center_position = {
                'x': center_x,
                'y': center_y,
                'scale': self.get_animated_center_scale(),
                'alpha': self.center_alpha,
                'index': self.current_index,
                'depth': 1.0,
                'is_side': False
            }
            self.draw_cartella_number(center_position)

    def clear_center_area(self, center_x, center_y):
        """Clear the center area to prevent overlap artifacts during transitions"""
        # Calculate the area that needs to be cleared based on the largest possible center number
        max_scale = self.get_animated_center_scale()
        base_font_size = 120
        max_font_size = int(base_font_size * max_scale)

        # Create a temporary font to measure the maximum possible size
        temp_font = pygame.font.SysFont("Arial", max_font_size, bold=True)
        temp_text = temp_font.render("999", True, (255, 255, 255))  # Largest possible cartella number
        text_rect = temp_text.get_rect(center=(center_x, center_y))

        # Expand the clear area slightly to account for glow effects
        padding = 40
        clear_rect = pygame.Rect(
            text_rect.left - padding,
            text_rect.top - padding,
            text_rect.width + (padding * 2),
            text_rect.height + (padding * 2)
        )

        # Create a surface with the same background as the overlay
        clear_surface = pygame.Surface((clear_rect.width, clear_rect.height), pygame.SRCALPHA)
        base_color = (0, 0, 20, self.overlay_alpha)
        clear_surface.fill(base_color)

        # Blit the clear surface to remove any previous rendering artifacts
        self.screen.blit(clear_surface, clear_rect.topleft)

    def draw_fade_transition_fixed(self, center_x, center_y, current_number, previous_number):
        """Draw fade transition between two numbers - FIXED OVERLAP ISSUES"""
        progress = self.get_eased_progress()

        # Calculate alpha values with proper blending to prevent overlap artifacts
        outgoing_alpha = max(0, int(255 * (1.0 - progress)))
        incoming_alpha = max(0, int(255 * progress))

        # Ensure we don't have both numbers at high alpha simultaneously
        if progress < 0.5:
            # First half: fade out previous number
            outgoing_alpha = int(255 * (1.0 - (progress * 2)))
            incoming_alpha = 0
        else:
            # Second half: fade in new number
            outgoing_alpha = 0
            incoming_alpha = int(255 * ((progress - 0.5) * 2))

        # Draw outgoing number (fading out) with z-order separation
        if outgoing_alpha > 10:  # Only draw if significantly visible
            outgoing_position = {
                'x': center_x,
                'y': center_y - self.transition_z_order_separation,  # Slightly behind
                'scale': self.get_animated_center_scale(),
                'alpha': outgoing_alpha,
                'number': previous_number,
                'depth': 0.9,  # Behind incoming number
                'is_side': False,
                'is_transition': True
            }
            self.draw_cartella_number_direct(outgoing_position)

        # Draw incoming number (fading in) with z-order separation
        if incoming_alpha > 10:  # Only draw if significantly visible
            incoming_position = {
                'x': center_x,
                'y': center_y + self.transition_z_order_separation,  # Slightly in front
                'scale': self.get_animated_center_scale(),
                'alpha': incoming_alpha,
                'number': current_number,
                'depth': 1.1,  # In front of outgoing number
                'is_side': False,
                'is_transition': True
            }
            self.draw_cartella_number_direct(incoming_position)

    def draw_scale_transition_fixed(self, center_x, center_y, current_number, previous_number):
        """Draw scale transition between two numbers - FIXED OVERLAP ISSUES"""
        progress = self.get_eased_progress()

        # Calculate scale and alpha values with proper separation
        if progress < 0.5:
            # First half: scale down and fade out previous number
            scale_progress = progress * 2  # 0 to 1
            outgoing_scale = self.get_animated_center_scale() * (1.0 - (scale_progress * 0.5))  # Scale down to 50%
            outgoing_alpha = int(255 * (1.0 - scale_progress))
            incoming_scale = 0
            incoming_alpha = 0
        else:
            # Second half: scale up and fade in new number
            scale_progress = (progress - 0.5) * 2  # 0 to 1
            outgoing_scale = 0
            outgoing_alpha = 0
            incoming_scale = self.get_animated_center_scale() * (0.5 + (scale_progress * 0.5))  # Scale up from 50%
            incoming_alpha = int(255 * scale_progress)

        # Draw outgoing number (scaling down and fading out)
        if outgoing_alpha > 10 and outgoing_scale > 0.1:
            outgoing_position = {
                'x': center_x,
                'y': center_y - self.transition_z_order_separation,  # Slightly behind
                'scale': outgoing_scale,
                'alpha': outgoing_alpha,
                'number': previous_number,
                'depth': 0.9,  # Behind incoming number
                'is_side': False,
                'is_transition': True
            }
            self.draw_cartella_number_direct(outgoing_position)

        # Draw incoming number (scaling up and fading in)
        if incoming_alpha > 10 and incoming_scale > 0.1:
            incoming_position = {
                'x': center_x,
                'y': center_y + self.transition_z_order_separation,  # Slightly in front
                'scale': incoming_scale,
                'alpha': incoming_alpha,
                'number': current_number,
                'depth': 1.1,  # In front of outgoing number
                'is_side': False,
                'is_transition': True
            }
            self.draw_cartella_number_direct(incoming_position)

    def draw_cartella_number(self, position):
        """Draw a single cartella number with 3D effects - ENHANCED CENTER-FOCUSED"""
        cartella_num = self.registered_cartellas[position['index']]
        self.draw_cartella_number_direct({**position, 'number': cartella_num})

    def draw_cartella_number_direct(self, position):
        """Draw a cartella number directly with specified number"""
        cartella_num = position.get('number', 0)
        is_side = position.get('is_side', False)

        # Calculate font size based on scale
        base_font_size = 120
        font_size = int(base_font_size * position['scale'])

        # Create font
        number_font = pygame.font.SysFont("Arial", font_size, bold=True)

        # Determine colors based on position with enhanced center styling
        if not is_side and position['scale'] > 1.0:  # Center number - ENHANCED APPEALING COLORS
            # Dynamic color animation based on time for center number
            time_factor = (pygame.time.get_ticks() / 1000.0) % 2.0  # 2-second cycle

            # Create a smooth color transition between vibrant colors
            if time_factor < 1.0:
                # Transition from bright gold to electric blue
                t = time_factor
                r = int(255 * (1 - t) + 0 * t)      # 255 → 0
                g = int(215 * (1 - t) + 191 * t)    # 215 → 191
                b = int(0 * (1 - t) + 255 * t)      # 0 → 255
            else:
                # Transition from electric blue to bright gold
                t = time_factor - 1.0
                r = int(0 * (1 - t) + 255 * t)      # 0 → 255
                g = int(191 * (1 - t) + 215 * t)    # 191 → 215
                b = int(255 * (1 - t) + 0 * t)      # 255 → 0

            text_color = (r, g, b)
            shadow_color = (max(0, r-150), max(0, g-150), max(0, b-150))  # Darker version
            glow_color = (min(255, r+50), min(255, g+50), min(255, b+50), 120)  # Brighter glow
        else:  # Side numbers - MORE SUBDUED FOR LESS DISTRACTION
            text_color = (140, 140, 160)  # More muted light blue
            shadow_color = (30, 30, 50)  # Darker shadow
            glow_color = (80, 80, 120, 20)  # Very subtle glow

        # Apply alpha
        text_color = (*text_color[:3], position['alpha'])

        # Position
        x = int(position['x'])
        y = int(position['y'])

        # Create text surface
        text_surf = number_font.render(str(cartella_num), True, text_color[:3])
        text_rect = text_surf.get_rect(center=(x, y))

        # Draw enhanced glow effect for center number only - IMPROVED ALPHA BLENDING
        if not is_side and position['scale'] > 1.0:  # Only for center number - ENHANCED GLOW
            # Pulsing glow effect based on time
            pulse_factor = (pygame.time.get_ticks() / 300.0) % 2.0  # Faster pulse
            if pulse_factor > 1.0:
                pulse_factor = 2.0 - pulse_factor
            pulse_intensity = 0.5 + (pulse_factor * 0.5)  # 0.5 to 1.0

            # Scale glow intensity based on the number's alpha for smooth transitions
            alpha_factor = position['alpha'] / 255.0
            adjusted_pulse_intensity = pulse_intensity * alpha_factor

            # Multiple glow layers with different colors and sizes - IMPROVED ALPHA
            glow_layers = [
                {'size': 25, 'alpha': int(60 * adjusted_pulse_intensity), 'color': glow_color[:3]},
                {'size': 18, 'alpha': int(80 * adjusted_pulse_intensity), 'color': glow_color[:3]},
                {'size': 12, 'alpha': int(100 * adjusted_pulse_intensity), 'color': glow_color[:3]},
                {'size': 6, 'alpha': int(120 * adjusted_pulse_intensity), 'color': (255, 255, 255)}  # White core
            ]

            for layer in glow_layers:
                if layer['alpha'] > 5:  # Only draw if significantly visible
                    glow_size = layer['size']
                    glow_surf = pygame.Surface((text_surf.get_width() + glow_size * 2,
                                              text_surf.get_height() + glow_size * 2), pygame.SRCALPHA)

                    # Create radial gradient glow with improved blending
                    center_glow = (glow_surf.get_width() // 2, glow_surf.get_height() // 2)
                    for radius in range(glow_size, 0, -2):
                        radius_alpha = int(layer['alpha'] * (radius / glow_size))
                        if radius_alpha > 2:  # Only draw if visible
                            color = (*layer['color'], radius_alpha)
                            pygame.draw.circle(glow_surf, color, center_glow, radius)

                    glow_rect = glow_surf.get_rect(center=(x, y))
                    self.screen.blit(glow_surf, glow_rect)

        # Draw shadow
        shadow_offset = max(2, int(4 * position['scale']))
        shadow_surf = number_font.render(str(cartella_num), True, shadow_color)
        shadow_rect = shadow_surf.get_rect(center=(x + shadow_offset, y + shadow_offset))

        # Apply alpha to shadow
        if position['alpha'] < 255:
            shadow_surf.set_alpha(position['alpha'] // 2)

        self.screen.blit(shadow_surf, shadow_rect)

        # Draw main text
        if position['alpha'] < 255:
            text_surf.set_alpha(position['alpha'])

        self.screen.blit(text_surf, text_rect)

        # Draw enhanced 3D highlight and effects for center number only - IMPROVED ALPHA BLENDING
        if not is_side and position['scale'] > 1.0:
            # Multiple highlight layers for 3D depth
            highlight_font = pygame.font.SysFont("Arial", font_size, bold=True)

            # Animated highlight intensity
            highlight_pulse = (pygame.time.get_ticks() / 400.0) % 2.0
            if highlight_pulse > 1.0:
                highlight_pulse = 2.0 - highlight_pulse
            highlight_intensity = 0.6 + (highlight_pulse * 0.4)  # 0.6 to 1.0

            # Scale highlight intensity based on the number's alpha for smooth transitions
            alpha_factor = position['alpha'] / 255.0
            adjusted_highlight_intensity = highlight_intensity * alpha_factor

            # Top-left highlight (main 3D effect) - IMPROVED ALPHA
            highlight_alpha = int(120 * adjusted_highlight_intensity)
            if highlight_alpha > 5:  # Only draw if significantly visible
                highlight_color = (255, 255, 255, highlight_alpha)
                highlight_surf = highlight_font.render(str(cartella_num), True, highlight_color[:3])
                highlight_rect = highlight_surf.get_rect(center=(x - 3, y - 3))
                highlight_surf.set_alpha(highlight_color[3])
                self.screen.blit(highlight_surf, highlight_rect)

            # Secondary highlight for extra depth - IMPROVED ALPHA
            highlight2_alpha = int(80 * adjusted_highlight_intensity)
            if highlight2_alpha > 5:  # Only draw if significantly visible
                highlight2_color = (255, 255, 200, highlight2_alpha)
                highlight2_surf = highlight_font.render(str(cartella_num), True, highlight2_color[:3])
                highlight2_rect = highlight2_surf.get_rect(center=(x - 1, y - 1))
                highlight2_surf.set_alpha(highlight2_color[3])
                self.screen.blit(highlight2_surf, highlight2_rect)

            # Subtle rim light effect - IMPROVED ALPHA
            rim_alpha = int(60 * adjusted_highlight_intensity)
            if rim_alpha > 5:  # Only draw if significantly visible
                rim_color = (*text_color[:3], rim_alpha)
                rim_surf = highlight_font.render(str(cartella_num), True, rim_color[:3])
                rim_surf.set_alpha(rim_color[3])

                # Draw rim light in multiple directions for outline effect
                rim_offsets = [(1, 0), (-1, 0), (0, 1), (0, -1), (1, 1), (-1, -1), (1, -1), (-1, 1)]
                for offset_x, offset_y in rim_offsets:
                    rim_rect = rim_surf.get_rect(center=(x + offset_x, y + offset_y))
                    self.screen.blit(rim_surf, rim_rect)

    # Old board drawing methods removed - we only display cartella numbers now

    def draw_control_panel(self):
        """Draw the control panel at the bottom"""
        screen_width, screen_height = self.screen.get_size()

        # Control panel background
        panel_y = screen_height - self.control_panel_height - 20
        panel_rect = pygame.Rect(0, panel_y, screen_width, self.control_panel_height)

        # Semi-transparent background
        panel_surface = pygame.Surface((screen_width, self.control_panel_height), pygame.SRCALPHA)
        panel_surface.fill((20, 40, 80, 180))
        self.screen.blit(panel_surface, (0, panel_y))

        # Control buttons
        button_y = panel_y + 10
        center_x = screen_width // 2

        # Previous button
        prev_rect = pygame.Rect(center_x - 200, button_y, self.button_width, self.button_height)
        self.draw_control_button(prev_rect, "◀ Previous", "prev")

        # Play/Pause button
        play_text = "⏸ Pause" if self.auto_slide else "▶ Play"
        play_rect = pygame.Rect(center_x - 60, button_y, self.button_width, self.button_height)
        self.draw_control_button(play_rect, play_text, "play_pause")

        # Next button
        next_rect = pygame.Rect(center_x + 80, button_y, self.button_width, self.button_height)
        self.draw_control_button(next_rect, "Next ▶", "next")

        # Speed control with enhanced visual feedback
        speed_text = f"Speed: {self.slide_speed:.1f}x"

        # Color-code the speed text based on speed value
        if self.slide_speed < 1.0:
            speed_color = (255, 150, 150)  # Light red for slow
        elif self.slide_speed > 2.0:
            speed_color = (150, 255, 150)  # Light green for fast
        else:
            speed_color = self.text_color  # Normal color for normal speed

        speed_surf = self.control_font.render(speed_text, True, speed_color)
        speed_rect = speed_surf.get_rect(center=(center_x, button_y + 50))

        # Add a subtle glow effect for the speed text
        if self.slide_speed != 1.0:  # Only glow when speed is not normal
            glow_surf = self.control_font.render(speed_text, True, (255, 255, 100))
            glow_rect = glow_surf.get_rect(center=(center_x + 1, button_y + 51))
            glow_surf.set_alpha(60)
            self.screen.blit(glow_surf, glow_rect)

        self.screen.blit(speed_surf, speed_rect)

        # Speed buttons - made larger for better clickability
        speed_down_rect = pygame.Rect(center_x - 90, button_y + 30, 40, 30)
        speed_up_rect = pygame.Rect(center_x + 50, button_y + 30, 40, 30)

        self.draw_control_button(speed_down_rect, "−", "speed_down")
        self.draw_control_button(speed_up_rect, "+", "speed_up")

        # Close button
        close_rect = pygame.Rect(screen_width - 100, button_y, 80, self.button_height)
        self.draw_control_button(close_rect, "✕ Close", "close")

        # Store button rects for click detection
        self.button_rects = {
            'prev': prev_rect,
            'play_pause': play_rect,
            'next': next_rect,
            'speed_down': speed_down_rect,
            'speed_up': speed_up_rect,
            'close': close_rect
        }

        # Store hover states for buttons
        if not hasattr(self, 'button_hover_states'):
            self.button_hover_states = {
                'prev': False,
                'play_pause': False,
                'next': False,
                'speed_down': False,
                'speed_up': False,
                'close': False
            }

    def draw_control_button(self, rect, text, button_type):
        """Draw a control button with hover effects"""
        # Check if button is being hovered
        is_hovered = hasattr(self, 'button_hover_states') and self.button_hover_states.get(button_type, False)

        # Button background with hover effect
        if button_type == "close":
            base_color = (200, 50, 50)
            hover_color = (255, 70, 70)
        elif button_type in ["speed_down", "speed_up"]:
            base_color = (80, 120, 160)
            hover_color = (100, 140, 180)
        else:
            base_color = self.card_border_color
            hover_color = (120, 170, 255)

        # Use hover color if hovered, otherwise base color
        button_color = hover_color if is_hovered else base_color

        # Draw button with subtle shadow for 3D effect
        if is_hovered:
            # Draw shadow
            shadow_rect = pygame.Rect(rect.x + 2, rect.y + 2, rect.width, rect.height)
            pygame.draw.rect(self.screen, (0, 0, 0, 100), shadow_rect, border_radius=5)

        pygame.draw.rect(self.screen, button_color, rect, border_radius=5)

        # Border color changes on hover
        border_color = (255, 255, 255) if not is_hovered else (255, 255, 100)
        pygame.draw.rect(self.screen, border_color, rect, width=2, border_radius=5)

        # Button text with slight glow on hover
        font = self.control_font if button_type in ["speed_down", "speed_up"] else self.button_font
        text_color = self.text_color if not is_hovered else (255, 255, 255)

        if is_hovered:
            # Draw text glow
            glow_surf = font.render(text, True, (255, 255, 100))
            glow_rect = glow_surf.get_rect(center=(rect.centerx + 1, rect.centery + 1))
            glow_surf.set_alpha(100)
            self.screen.blit(glow_surf, glow_rect)

        text_surf = font.render(text, True, text_color)
        text_rect = text_surf.get_rect(center=rect.center)
        self.screen.blit(text_surf, text_rect)

    def draw_navigation_indicators(self):
        """Draw navigation dots to show current position"""
        if len(self.registered_cartellas) <= 1:
            return

        screen_width, screen_height = self.screen.get_size()

        # Calculate indicator positions
        total_indicators = len(self.registered_cartellas)
        indicator_size = 8
        indicator_spacing = 20
        total_width = total_indicators * indicator_spacing

        start_x = (screen_width - total_width) // 2
        indicator_y = screen_height - 140

        # Draw indicators
        for i in range(total_indicators):
            x = start_x + i * indicator_spacing

            if i == self.current_index:
                # Active indicator
                color = self.center_highlight
                size = indicator_size + 2
            else:
                # Inactive indicator
                color = (100, 100, 100)
                size = indicator_size

            pygame.draw.circle(self.screen, color, (x, indicator_y), size)
            pygame.draw.circle(self.screen, (255, 255, 255), (x, indicator_y), size, width=2)

    def handle_event(self, event):
        """Handle input events"""
        if not self.active:
            return False

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.hide()
                return True
            elif event.key == pygame.K_LEFT:
                self.previous_cartella()
                return True
            elif event.key == pygame.K_RIGHT:
                self.next_cartella()
                return True
            elif event.key == pygame.K_SPACE:
                self.auto_slide = not self.auto_slide
                return True
            elif event.key == pygame.K_MINUS or event.key == pygame.K_KP_MINUS:
                # Speed down with keyboard
                old_speed = self.slide_speed
                self.slide_speed = max(0.5, self.slide_speed - 0.1)
                print(f"Speed decreased (keyboard): {old_speed:.1f}x → {self.slide_speed:.1f}x")
                return True
            elif event.key == pygame.K_PLUS or event.key == pygame.K_KP_PLUS or event.key == pygame.K_EQUALS:
                # Speed up with keyboard
                old_speed = self.slide_speed
                self.slide_speed = min(3.0, self.slide_speed + 0.1)
                print(f"Speed increased (keyboard): {old_speed:.1f}x → {self.slide_speed:.1f}x")
                return True

        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                return self.handle_button_click(event.pos)

        elif event.type == pygame.MOUSEMOTION:
            # Handle hover effects for buttons
            return self.handle_mouse_hover(event.pos)

        return False

    def handle_mouse_hover(self, pos):
        """Handle mouse hover effects for buttons"""
        if not hasattr(self, 'button_rects') or not hasattr(self, 'button_hover_states'):
            return False

        # Check if mouse is over any button
        hover_changed = False
        for button_type, rect in self.button_rects.items():
            was_hovered = self.button_hover_states[button_type]
            is_hovered = rect.collidepoint(pos)

            if was_hovered != is_hovered:
                self.button_hover_states[button_type] = is_hovered
                hover_changed = True

        return hover_changed

    def handle_button_click(self, pos):
        """Handle button clicks with sound effects"""
        if not hasattr(self, 'button_rects'):
            return False

        for button_type, rect in self.button_rects.items():
            if rect.collidepoint(pos):
                # Play button click sound if available
                if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                    self.game.button_click_sound.play()

                # Handle button actions
                if button_type == "prev":
                    self.previous_cartella()
                elif button_type == "next":
                    self.next_cartella()
                elif button_type == "play_pause":
                    self.auto_slide = not self.auto_slide
                elif button_type == "speed_down":
                    old_speed = self.slide_speed
                    self.slide_speed = max(0.5, self.slide_speed - 0.1)
                    print(f"Speed decreased: {old_speed:.1f}x → {self.slide_speed:.1f}x")
                elif button_type == "speed_up":
                    old_speed = self.slide_speed
                    self.slide_speed = min(3.0, self.slide_speed + 0.1)
                    print(f"Speed increased: {old_speed:.1f}x → {self.slide_speed:.1f}x")
                elif button_type == "close":
                    self.hide()
                return True

        return False
