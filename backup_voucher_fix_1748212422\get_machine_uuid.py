#!/usr/bin/env python3
"""
Get Machine UUID

This script retrieves and displays the machine UUID, which can be used
for registering external PCs with the voucher system.
"""

import os
import sys
import platform
import subprocess
import uuid
import hashlib

def get_machine_uuid():
    """
    Get the machine's UUID from BIOS or other system identifiers.

    Returns:
        str: UUID string
    """
    try:
        system = platform.system().lower()
        
        if system == 'windows':
            # Try to import wmi for Windows
            try:
                import wmi
                c = wmi.WMI()
                for system in c.Win32_ComputerSystemProduct():
                    if system.UUID:
                        return system.UUID.upper()
            except ImportError:
                # If wmi is not available, use wmic command
                result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'], 
                                    capture_output=True, text=True, check=True)
                
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    return lines[1].strip().upper()
            
            # Try to get from Windows registry
            try:
                import winreg
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography") as key:
                    machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                    return machine_guid.upper()
            except:
                pass
        
        elif system == 'linux':
            # Try to get machine-id on Linux
            if os.path.exists('/etc/machine-id'):
                with open('/etc/machine-id', 'r') as f:
                    return f.read().strip().upper()
        
        elif system == 'darwin':  # macOS
            # Use ioreg to get IOPlatformUUID on macOS
            result = subprocess.run(['ioreg', '-rd1', '-c', 'IOPlatformExpertDevice'],
                                 capture_output=True, text=True, check=True)
            
            for line in result.stdout.split('\n'):
                if 'IOPlatformUUID' in line:
                    return line.split('"')[3].strip().upper()
        
        # Fallback: Use MAC address
        mac = uuid.getnode()
        mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                          for elements in range(0, 8*6, 8)][::-1])
        return hashlib.sha256(mac_str.encode()).hexdigest()[:32].upper()
    
    except Exception as e:
        print(f"Error getting machine UUID: {e}")
        # Last resort: Generate a random UUID
        return str(uuid.uuid4()).upper()

def main():
    """Main function."""
    try:
        machine_uuid = get_machine_uuid()
        
        print("\nMachine UUID:")
        print("=" * 40)
        print(machine_uuid)
        print("=" * 40)
        print("\nUse this UUID to register this PC with the voucher system.")
        print("You can copy this UUID and provide it to the voucher generator.")
        
        # Save to file for convenience
        with open('machine_uuid.txt', 'w') as f:
            f.write(machine_uuid)
        
        print("\nThe UUID has also been saved to 'machine_uuid.txt' for your convenience.")
        
        return 0
    
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
