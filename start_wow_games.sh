#!/bin/bash

# WOW Games Complete Launcher for Linux/Mac
# This script starts all components of WOW Games with RethinkDB integration

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Print header
echo
echo "================================================================================"
echo "                        WOW GAMES COMPLETE LAUNCHER"
echo "                     with RethinkDB Integration"
echo "================================================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python is not installed or not in PATH"
    echo "Please install Python and try again."
    exit 1
fi

# Determine Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

print_status "Python found: $PYTHON_CMD"

# Check if we're in the right directory
if [ ! -f "main.py" ]; then
    print_error "main.py not found"
    echo "Please run this script from the WOW Games directory."
    exit 1
fi

print_status "WOW Games directory confirmed"
echo

# Make the script executable if it isn't already
chmod +x "$0"

# Run the Python launcher
print_info "Starting WOW Games Complete Launcher..."
echo
$PYTHON_CMD start_wow_games_complete.py

# If we get here, the launcher has finished
echo
echo "================================================================================"
echo "                           LAUNCHER FINISHED"
echo "================================================================================"
echo

# Wait for user input before closing (optional)
read -p "Press Enter to exit..."
