#!/usr/bin/env python3
"""
Voucher System Diagnostic Script

This script diagnoses common voucher system issues and provides solutions.
"""

import os
import sys
import json
from datetime import datetime

def diagnose_uuid_issues():
    """Diagnose UUID detection issues."""
    print("Diagnosing UUID detection...")
    
    methods = []
    
    # Test get_machine_uuid
    try:
        from get_machine_uuid import get_machine_uuid
        uuid1 = get_machine_uuid()
        methods.append(("get_machine_uuid", uuid1, "SUCCESS"))
    except Exception as e:
        methods.append(("get_machine_uuid", str(e), "ERROR"))
    
    # Test crypto_utils
    try:
        from payment.crypto_utils import CryptoUtils
        uuid2 = CryptoUtils.get_machine_uuid()
        methods.append(("CryptoUtils", uuid2, "SUCCESS"))
    except Exception as e:
        methods.append(("CryptoUtils", str(e), "ERROR"))
    
    # Test voucher_processor
    try:
        from payment.voucher_processor import VoucherProcessor
        processor = VoucherProcessor()
        uuid3 = processor.machine_uuid
        methods.append(("VoucherProcessor", uuid3, "SUCCESS"))
    except Exception as e:
        methods.append(("VoucherProcessor", str(e), "ERROR"))
    
    print("UUID Detection Results:")
    for method, result, status in methods:
        print(f"  {method}: {status} - {result}")
    
    return methods

def diagnose_database_issues():
    """Diagnose database connectivity issues."""
    print("\nDiagnosing database issues...")
    
    try:
        from payment.voucher_manager import VoucherManager, get_data_dir
        
        data_dir = get_data_dir()
        print(f"Data directory: {data_dir}")
        print(f"Data directory exists: {os.path.exists(data_dir)}")
        print(f"Data directory writable: {os.access(data_dir, os.W_OK)}")
        
        manager = VoucherManager()
        print(f"VoucherManager created successfully")
        print(f"Initialization errors: {len(manager.initialization_errors)}")
        
        for error in manager.initialization_errors:
            print(f"  Error: {error}")
        
        return True
        
    except Exception as e:
        print(f"Database diagnosis failed: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("VOUCHER SYSTEM DIAGNOSTIC")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print(f"Python: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run diagnostics
    uuid_results = diagnose_uuid_issues()
    db_result = diagnose_database_issues()
    
    # Generate report
    report = {
        "timestamp": datetime.now().isoformat(),
        "uuid_detection": uuid_results,
        "database_status": db_result
    }
    
    with open("voucher_diagnostic_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("\nDiagnostic complete. Report saved to voucher_diagnostic_report.json")

if __name__ == "__main__":
    main()
