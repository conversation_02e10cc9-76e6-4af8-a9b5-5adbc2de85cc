"""
Simple integration for the payment system.

This module provides a simplified integration with the stats page.
"""

import pygame
from .simple_recharge_ui import SimpleRechargeUI
from . import get_voucher_manager

def integrate_with_stats_page(stats_page):
    """
    Integrate the payment system with the stats page.

    Args:
        stats_page: StatsPage instance

    Returns:
        SimpleRechargeUI: The recharge UI component
    """
    # Get the voucher manager
    voucher_manager = get_voucher_manager()

    # Create the recharge UI component
    recharge_ui = SimpleRechargeUI(
        stats_page.screen,
        voucher_manager
    )

    # Store original methods
    original_draw = stats_page.draw
    original_update = stats_page.update
    original_handle_event = getattr(stats_page, 'handle_event', None)

    # Enhanced draw method
    def enhanced_draw():
        # Call original draw method
        original_draw()

        # Draw credit display
        draw_credit_display(stats_page, voucher_manager)

        # Draw recharge UI if visible
        if recharge_ui.visible:
            recharge_ui.draw()

    # Enhanced update method
    def enhanced_update(dt=0):
        # Call original update method
        original_update(dt)

        # Update recharge UI
        if recharge_ui.visible:
            recharge_ui.update()

    # Enhanced handle_event method
    def enhanced_handle_event(event):
        # PRIORITY 1: Check if recharge UI is visible and handle ALL events for it first
        if recharge_ui.visible:
            # Handle all keyboard events (including TEXTINPUT) when recharge UI is visible
            if event.type in (pygame.KEYDOWN, pygame.TEXTINPUT):
                if recharge_ui.handle_event(event):
                    return True
            # Handle mouse events for recharge UI
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if recharge_ui.handle_click(event.pos):
                    return True

        # PRIORITY 2: Check if recharge button was clicked (only when recharge UI is not visible)
        if not recharge_ui.visible and event.type == pygame.MOUSEBUTTONDOWN:
            if hasattr(stats_page, 'recharge_button') and stats_page.recharge_button.collidepoint(event.pos):
                # Play click sound
                if hasattr(stats_page, 'button_click_sound') and stats_page.button_click_sound:
                    stats_page.button_click_sound.play()

                # Show recharge UI
                recharge_ui.show()
                return True

        # PRIORITY 3: Call original handle_event method only if recharge UI didn't handle the event
        if not recharge_ui.visible and original_handle_event:
            return original_handle_event(event)

        return False

    # Replace methods
    stats_page.draw = enhanced_draw
    stats_page.update = enhanced_update
    stats_page.handle_event = enhanced_handle_event

    # Add recharge UI to stats page
    stats_page.recharge_ui = recharge_ui

    return recharge_ui

def draw_credit_display(stats_page, voucher_manager):
    """
    Draw an enhanced credit display on the stats page.

    Args:
        stats_page: StatsPage instance
        voucher_manager: VoucherManager instance
    """
    # Get screen dimensions
    screen_width = stats_page.screen.get_width()

    # Create credit display rect (larger and more prominent)
    credit_rect = pygame.Rect(
        screen_width - 250,
        100,
        230,
        120
    )

    # Draw credit display background with gradient
    draw_gradient_background(
        stats_page.screen,
        credit_rect,
        (30, 40, 70),  # Dark blue
        (50, 60, 100),  # Lighter blue
        border_radius=15
    )

    # Draw border with glow effect
    for i in range(3):
        alpha = 100 - i * 30
        border_color = (100, 150, 255, alpha)
        border_rect = pygame.Rect(
            credit_rect.x - i,
            credit_rect.y - i,
            credit_rect.width + i * 2,
            credit_rect.height + i * 2
        )
        draw_rounded_rect(
            stats_page.screen,
            border_color,
            border_rect,
            15 + i,
            2
        )

    # Draw credit label with icon
    label_font = pygame.font.SysFont("Arial", 18, bold=True)
    label_text = label_font.render("Credits Available:", True, (220, 220, 255))
    label_rect = label_text.get_rect(
        x=credit_rect.x + 15,
        y=credit_rect.y + 15
    )
    stats_page.screen.blit(label_text, label_rect)

    # Draw credit icon (coin)
    coin_radius = 12
    coin_x = label_rect.right + 10
    coin_y = label_rect.centery
    pygame.draw.circle(
        stats_page.screen,
        (255, 215, 0),  # Gold
        (coin_x, coin_y),
        coin_radius
    )
    pygame.draw.circle(
        stats_page.screen,
        (200, 170, 0),  # Darker gold
        (coin_x, coin_y),
        coin_radius,
        2
    )

    # Draw credit amount with larger font
    amount_font = pygame.font.SysFont("Arial", 32, bold=True)
    amount_text = amount_font.render(
        str(voucher_manager.credits),
        True,
        (255, 215, 0)  # Gold
    )
    amount_rect = amount_text.get_rect(
        x=credit_rect.x + 25,
        y=label_rect.bottom + 10
    )
    stats_page.screen.blit(amount_text, amount_rect)

    # Get voucher history for share percentage
    voucher_history = voucher_manager.get_voucher_history(1)
    if voucher_history:
        # Draw share percentage info
        share_font = pygame.font.SysFont("Arial", 14)
        share_text = share_font.render(
            f"Share: {voucher_history[0]['share']}%",
            True,
            (180, 180, 220)
        )
        share_rect = share_text.get_rect(
            x=amount_rect.right + 15,
            centery=amount_rect.centery
        )
        stats_page.screen.blit(share_text, share_rect)

    # Draw recharge button with gradient and glow effect
    button_rect = pygame.Rect(
        credit_rect.centerx - 60,
        credit_rect.bottom - 40,
        120,
        30
    )

    # Draw button with gradient
    draw_gradient_background(
        stats_page.screen,
        button_rect,
        (0, 120, 60),  # Dark green
        (0, 180, 90),  # Light green
        border_radius=15
    )

    # Add glow effect to button
    for i in range(2):
        alpha = 80 - i * 30
        glow_color = (100, 255, 150, alpha)
        glow_rect = pygame.Rect(
            button_rect.x - i,
            button_rect.y - i,
            button_rect.width + i * 2,
            button_rect.height + i * 2
        )
        draw_rounded_rect(
            stats_page.screen,
            glow_color,
            glow_rect,
            15 + i,
            2
        )

    # Draw button text
    button_font = pygame.font.SysFont("Arial", 16, bold=True)
    button_text = button_font.render("RECHARGE", True, (255, 255, 255))
    button_text_rect = button_text.get_rect(center=button_rect.center)
    stats_page.screen.blit(button_text, button_text_rect)

    # Store button for hit detection
    stats_page.recharge_button = button_rect

def draw_gradient_background(surface, rect, color1, color2, border_radius=0):
    """Draw a rectangle with a vertical gradient"""
    # Create a surface with alpha channel
    surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

    # Draw gradient by blending two colors over height
    for y in range(rect.height):
        # Calculate blend factor (0.0 to 1.0)
        t = y / max(1, rect.height - 1)

        # Linear interpolation of colors
        r = int(color1[0] * (1 - t) + color2[0] * t)
        g = int(color1[1] * (1 - t) + color2[1] * t)
        b = int(color1[2] * (1 - t) + color2[2] * t)

        # Draw a line of the gradient
        pygame.draw.line(surf, (r, g, b), (0, y), (rect.width, y))

    # Apply rounded corners if requested
    if border_radius > 0:
        # Create mask for rounded corners
        mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)

        # Apply mask
        surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

    # Draw the surface to the screen
    surface.blit(surf, rect)

def draw_rounded_rect(surface, color, rect, border_radius=0, width=0):
    """Draw a rounded rectangle with alpha support"""
    if len(color) == 4:  # Color with alpha
        shape_surf = pygame.Surface(rect.size, pygame.SRCALPHA)
        pygame.draw.rect(shape_surf, color, shape_surf.get_rect(), width, border_radius=border_radius)
        surface.blit(shape_surf, rect)
    else:  # Color without alpha
        pygame.draw.rect(surface, color, rect, width, border_radius=border_radius)
