"""
External Display Manager for WOW Games

This module provides enhanced support for external displays connected via HDMI,
addressing common issues with display initialization, mode switching, and
multi-monitor compatibility.
"""

import pygame
import os
import sys
import time
from typing import Tuple, List, Optional, Dict, Any
from hdmi_display_fixer import get_hdmi_display_fixer


class ExternalDisplayManager:
    """
    Manages external display connections and resolves common HDMI display issues
    """

    def __init__(self):
        self.display_info = {}
        self.available_modes = []
        self.current_display = None
        self.fallback_modes = [
            (1920, 1080), (1680, 1050), (1600, 900), (1366, 768),
            (1280, 720), (1024, 768), (800, 600)
        ]
        self.display_flags_priority = [
            pygame.RESIZABLE | pygame.DOUBLEBUF | pygame.HWSURFACE,
            pygame.RESIZABLE | pygame.DOUBLEBUF,
            pygame.RESIZABLE | pygame.HWSURFACE,
            pygame.RESIZABLE,
            pygame.DOUBLEBUF,
            0  # No flags as last resort
        ]

    def detect_displays(self) -> Dict[str, Any]:
        """
        Detect available displays and their capabilities

        Returns:
            Dict containing display information
        """
        display_info = {
            'primary_display': None,
            'available_modes': [],
            'driver': None,
            'external_detected': False,
            'refresh_rates': [],
            'color_depths': []
        }

        try:
            # Get display driver information
            display_info['driver'] = pygame.display.get_driver()
            print(f"Display driver: {display_info['driver']}")

            # Get primary display info
            try:
                screen_info = pygame.display.Info()
                display_info['primary_display'] = {
                    'width': screen_info.current_w,
                    'height': screen_info.current_h,
                    'depth': screen_info.bitsize if hasattr(screen_info, 'bitsize') else 32
                }
                print(f"Primary display: {screen_info.current_w}x{screen_info.current_h}")
            except Exception as e:
                print(f"Warning: Could not get primary display info: {e}")
                display_info['primary_display'] = {'width': 1920, 'height': 1080, 'depth': 32}

            # Get available display modes
            try:
                modes = pygame.display.list_modes()
                if modes == -1:
                    print("All display modes supported")
                    display_info['available_modes'] = self.fallback_modes
                elif modes:
                    display_info['available_modes'] = list(set(modes))  # Remove duplicates
                    print(f"Available modes: {len(display_info['available_modes'])}")
                else:
                    print("No display modes available, using fallback")
                    display_info['available_modes'] = self.fallback_modes
            except Exception as e:
                print(f"Error getting display modes: {e}")
                display_info['available_modes'] = self.fallback_modes

            # Detect external display (heuristic based on resolution changes)
            primary = display_info['primary_display']
            if primary and display_info['available_modes']:
                max_mode = max(display_info['available_modes'], key=lambda x: x[0] * x[1])
                if max_mode[0] > primary['width'] or max_mode[1] > primary['height']:
                    display_info['external_detected'] = True
                    print("External display detected (higher resolution available)")

        except Exception as e:
            print(f"Error during display detection: {e}")
            # Provide safe defaults
            display_info['primary_display'] = {'width': 1920, 'height': 1080, 'depth': 32}
            display_info['available_modes'] = self.fallback_modes

        self.display_info = display_info
        return display_info

    def create_compatible_display(self, preferred_width: int = None,
                                preferred_height: int = None,
                                fullscreen: bool = False) -> Tuple[pygame.Surface, int, int]:
        """
        Create a display surface compatible with external monitors

        Args:
            preferred_width: Preferred window width
            preferred_height: Preferred window height
            fullscreen: Whether to create in fullscreen mode

        Returns:
            Tuple of (surface, actual_width, actual_height)
        """
        # Detect displays if not already done
        if not self.display_info:
            self.detect_displays()

        # Determine target resolution
        if preferred_width and preferred_height:
            target_width, target_height = preferred_width, preferred_height
        else:
            primary = self.display_info.get('primary_display', {'width': 1920, 'height': 1080})
            target_width = int(primary['width'] * 0.85)
            target_height = int(primary['height'] * 0.85)

        # Validate against available modes
        validated_width, validated_height = self._validate_resolution(target_width, target_height)

        # Create display with progressive fallback
        surface = None
        actual_width, actual_height = validated_width, validated_height

        for flags in self.display_flags_priority:
            try:
                if fullscreen:
                    # For fullscreen, try native resolution first
                    primary = self.display_info.get('primary_display', {'width': 1920, 'height': 1080})
                    surface = pygame.display.set_mode((primary['width'], primary['height']),
                                                    flags | pygame.FULLSCREEN)
                    actual_width, actual_height = primary['width'], primary['height']
                else:
                    surface = pygame.display.set_mode((validated_width, validated_height), flags)

                print(f"Display created successfully with flags: {flags}")
                print(f"Resolution: {actual_width}x{actual_height}")
                break

            except pygame.error as e:
                print(f"Failed to create display with flags {flags}: {e}")
                continue

        # Last resort fallback
        if surface is None:
            try:
                surface = pygame.display.set_mode((1024, 768))
                actual_width, actual_height = 1024, 768
                print("Emergency fallback display created: 1024x768")
            except pygame.error as e:
                raise RuntimeError(f"Could not create any display mode: {e}")

        # Apply additional optimizations for external displays
        self._optimize_for_external_display(surface)

        # Apply HDMI-specific stability fixes
        hdmi_fixer = get_hdmi_display_fixer()
        if not hdmi_fixer.apply_hdmi_stability_fixes(surface):
            print("Warning: HDMI stability fixes failed")

        self.current_display = surface
        return surface, actual_width, actual_height

    def _validate_resolution(self, width: int, height: int) -> Tuple[int, int]:
        """
        Validate and adjust resolution against available modes

        Args:
            width: Requested width
            height: Requested height

        Returns:
            Tuple of (validated_width, validated_height)
        """
        available_modes = self.display_info.get('available_modes', self.fallback_modes)

        # If exact match exists, use it
        if (width, height) in available_modes:
            return width, height

        # Find closest compatible mode
        best_mode = None
        min_diff = float('inf')

        for mode_width, mode_height in available_modes:
            if mode_width >= width and mode_height >= height:
                diff = (mode_width - width) + (mode_height - height)
                if diff < min_diff:
                    min_diff = diff
                    best_mode = (mode_width, mode_height)

        if best_mode:
            return best_mode

        # If no larger mode found, find the largest available
        largest_mode = max(available_modes, key=lambda x: x[0] * x[1])
        return min(width, largest_mode[0]), min(height, largest_mode[1])

    def _optimize_for_external_display(self, surface: pygame.Surface):
        """
        Apply optimizations specific to external displays

        Args:
            surface: The display surface to optimize
        """
        try:
            # Set comprehensive SDL environment variables for external display stability
            os.environ['SDL_VIDEO_WINDOW_POS'] = 'centered'
            os.environ['SDL_VIDEO_CENTERED'] = '1'
            os.environ['SDL_VIDEO_MINIMIZE_ON_FOCUS_LOSS'] = '0'

            # Critical SDL variables for external display compatibility
            os.environ['SDL_VIDEO_ALLOW_SCREENSAVER'] = '1'
            os.environ['SDL_HINT_VIDEO_ALLOW_SCREENSAVER'] = '1'

            # Force specific video driver if needed
            if sys.platform.startswith('win'):
                # On Windows, ensure we use the best available driver
                os.environ['SDL_VIDEODRIVER'] = 'windows'

            # Additional stability settings
            os.environ['SDL_HINT_RENDER_DRIVER'] = 'direct3d'
            os.environ['SDL_HINT_RENDER_VSYNC'] = '1'

            # Force display stabilization sequence
            try:
                # Clear the surface first
                surface.fill((0, 0, 0))
                pygame.display.flip()

                # Multiple refresh cycles to ensure stability
                for i in range(3):
                    time.sleep(0.05)  # 50ms delay between refreshes
                    pygame.display.flip()

                # Final stabilization
                time.sleep(0.1)
                pygame.display.flip()

            except Exception as flip_error:
                print(f"Warning: Display flip optimization failed: {flip_error}")

            print("Applied comprehensive external display optimizations")

        except Exception as e:
            print(f"Warning: Could not apply all optimizations: {e}")

    def force_display_reset(self) -> bool:
        """
        Force a complete display reset to resolve persistent issues

        Returns:
            True if reset was successful
        """
        try:
            print("Forcing complete display reset...")

            # Store current display info
            current_size = None
            current_fullscreen = False

            if self.current_display:
                current_size = self.current_display.get_size()
                current_fullscreen = bool(self.current_display.get_flags() & pygame.FULLSCREEN)

            # Force pygame to reinitialize display subsystem
            pygame.display.quit()
            time.sleep(0.2)  # Allow time for cleanup
            pygame.display.init()

            # Re-detect displays after reset
            self.display_info = {}
            self.detect_displays()

            # Recreate display with current settings
            if current_size:
                new_surface, _, _ = self.create_compatible_display(
                    preferred_width=current_size[0],
                    preferred_height=current_size[1],
                    fullscreen=current_fullscreen
                )
            else:
                # Use recommended settings
                width, height = self.get_recommended_resolution()
                new_surface, _, _ = self.create_compatible_display(
                    preferred_width=width,
                    preferred_height=height,
                    fullscreen=False
                )

            print("Display reset completed successfully")
            return True

        except Exception as e:
            print(f"Error during display reset: {e}")
            return False

    def handle_hdmi_connection_issues(self) -> bool:
        """
        Handle common HDMI connection issues like flickering and improper rendering

        Returns:
            True if issues were resolved
        """
        try:
            print("Attempting to resolve HDMI connection issues...")

            # Force display refresh
            if self.current_display:
                try:
                    # Clear the display
                    self.current_display.fill((0, 0, 0))
                    pygame.display.flip()

                    # Small delay to allow display to stabilize
                    time.sleep(0.2)

                    # Force another refresh
                    pygame.display.flip()

                    print("Display refresh completed")
                    return True

                except Exception as e:
                    print(f"Error during display refresh: {e}")

            return False

        except Exception as e:
            print(f"Error handling HDMI connection issues: {e}")
            return False

    def detect_display_change_and_recover(self) -> bool:
        """
        Detect display changes and attempt automatic recovery

        Returns:
            True if recovery was successful
        """
        try:
            # Store current display state
            old_info = self.display_info.copy() if self.display_info else {}

            # Re-detect displays to check for changes
            new_info = self.detect_displays()

            # Check if significant changes occurred
            display_changed = self._display_changed(old_info, new_info)

            if display_changed:
                print("Significant display change detected, attempting recovery...")

                # ANTI-FLICKERING FIX: Check if this is a legitimate screen mode change
                # Don't trigger recovery for normal fullscreen/windowed transitions
                if self._is_legitimate_mode_change(old_info, new_info):
                    print("Legitimate screen mode change detected - skipping aggressive recovery")
                    return False  # Don't trigger recovery cycle

                # Force a complete display reset for major changes
                if self.force_display_reset():
                    print("Display reset successful")
                    return True
                else:
                    print("Display reset failed, trying alternative recovery...")

                    # Alternative recovery method
                    if self.current_display:
                        current_size = self.current_display.get_size()
                        is_fullscreen = bool(self.current_display.get_flags() & pygame.FULLSCREEN)

                        # Try to recreate display with enhanced stability
                        try:
                            # Clear current display first
                            self.current_display.fill((0, 0, 0))
                            pygame.display.flip()
                            time.sleep(0.1)

                            # Recreate with better flags
                            new_surface, _, _ = self.create_compatible_display(
                                preferred_width=current_size[0],
                                preferred_height=current_size[1],
                                fullscreen=is_fullscreen
                            )

                            # Apply comprehensive HDMI fixes
                            self.handle_hdmi_connection_issues()

                            print("Alternative display recovery completed")
                            return True

                        except Exception as recovery_error:
                            print(f"Alternative recovery failed: {recovery_error}")

            # ANTI-FLICKERING FIX: Disable aggressive HDMI stability fixes
            # These were causing unnecessary display operations and flickering
            # Only apply gentle fixes if absolutely necessary
            elif self.current_display and False:  # Disabled for now
                try:
                    # Gentle stability refresh (DISABLED)
                    pass
                except:
                    pass

            return display_changed

        except Exception as e:
            print(f"Error during display recovery: {e}")
            return False

    def handle_display_change(self) -> bool:
        """
        Handle display configuration changes (e.g., HDMI connect/disconnect)

        Returns:
            True if display change was handled successfully
        """
        try:
            # Re-detect displays
            old_info = self.display_info.copy()
            new_info = self.detect_displays()

            # Check if significant changes occurred
            if self._display_changed(old_info, new_info):
                print("Display configuration change detected")
                return True

            return False

        except Exception as e:
            print(f"Error handling display change: {e}")
            return False

    def _display_changed(self, old_info: Dict, new_info: Dict) -> bool:
        """
        Check if display configuration has changed significantly

        Args:
            old_info: Previous display information
            new_info: Current display information

        Returns:
            True if significant change detected
        """
        # ANTI-FLICKERING FIX: Be much more conservative about detecting changes
        if not old_info and not new_info:
            return False  # Both empty, no change

        if not old_info or not new_info:
            # Only consider it a change if we have a substantial difference
            # Don't trigger on first initialization
            if not old_info and new_info:
                # First time initialization - not a change
                return False
            return True

        old_primary = old_info.get('primary_display', {})
        new_primary = new_info.get('primary_display', {})

        # ANTI-FLICKERING FIX: Only trigger on MAJOR resolution changes (not minor variations)
        old_width = old_primary.get('width', 0)
        old_height = old_primary.get('height', 0)
        new_width = new_primary.get('width', 0)
        new_height = new_primary.get('height', 0)

        # Only consider it a change if resolution differs by more than 100 pixels in either dimension
        width_diff = abs(old_width - new_width)
        height_diff = abs(old_height - new_height)

        if width_diff > 100 or height_diff > 100:
            print(f"Major resolution change detected: {old_width}x{old_height} -> {new_width}x{new_height}")
            return True

        # ANTI-FLICKERING FIX: Be more tolerant of mode count variations
        old_modes = len(old_info.get('available_modes', []))
        new_modes = len(new_info.get('available_modes', []))
        if abs(old_modes - new_modes) > 5:  # Increased tolerance from 2 to 5
            print(f"Major mode count change detected: {old_modes} -> {new_modes}")
            return True

        return False

    def _is_legitimate_mode_change(self, old_info: Dict, new_info: Dict) -> bool:
        """
        Check if the display change is a legitimate screen mode change (fullscreen/windowed)
        rather than an actual hardware display change that needs recovery

        Args:
            old_info: Previous display information
            new_info: Current display information

        Returns:
            True if this is a legitimate mode change that doesn't need recovery
        """
        if not old_info or not new_info:
            return False

        old_primary = old_info.get('primary_display', {})
        new_primary = new_info.get('primary_display', {})

        old_width = old_primary.get('width', 0)
        old_height = old_primary.get('height', 0)
        new_width = new_primary.get('width', 0)
        new_height = new_primary.get('height', 0)

        # Common legitimate transitions:
        # 1920x1080 (fullscreen) <-> 1680x1050 (windowed)
        # 1920x1080 (fullscreen) <-> 1632x918 (windowed)
        # etc.

        legitimate_transitions = [
            # Fullscreen to windowed transitions
            ((1920, 1080), (1680, 1050)),
            ((1920, 1080), (1632, 918)),
            ((1920, 1080), (1600, 900)),
            ((1920, 1080), (1536, 864)),
            # Reverse transitions
            ((1680, 1050), (1920, 1080)),
            ((1632, 918), (1920, 1080)),
            ((1600, 900), (1920, 1080)),
            ((1536, 864), (1920, 1080)),
        ]

        current_transition = ((old_width, old_height), (new_width, new_height))

        if current_transition in legitimate_transitions:
            print(f"Legitimate screen mode transition detected: {old_width}x{old_height} -> {new_width}x{new_height}")
            return True

        return False

    def get_recommended_resolution(self) -> Tuple[int, int]:
        """
        Get recommended resolution for current display setup

        Returns:
            Tuple of (width, height)
        """
        if not self.display_info:
            self.detect_displays()

        primary = self.display_info.get('primary_display', {'width': 1920, 'height': 1080})

        # For external displays, use 85% of native resolution for windowed mode
        recommended_width = int(primary['width'] * 0.85)
        recommended_height = int(primary['height'] * 0.85)

        # Ensure minimum usable size
        recommended_width = max(1024, recommended_width)
        recommended_height = max(768, recommended_height)

        return recommended_width, recommended_height


# Global instance
_display_manager = None

def get_external_display_manager() -> ExternalDisplayManager:
    """
    Get the global external display manager instance

    Returns:
        ExternalDisplayManager instance
    """
    global _display_manager
    if _display_manager is None:
        _display_manager = ExternalDisplayManager()
    return _display_manager
