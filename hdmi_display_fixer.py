"""
HDMI Display Fixer for WOW Games

This module provides specialized fixes for HDMI display issues including
flickering, improper rendering, and display mode switching problems.
"""

import pygame
import time
import os
import sys
from typing import Optional, Tuple


class HDMIDisplayFixer:
    """
    Specialized fixer for HDMI display issues
    """
    
    def __init__(self):
        self.last_stable_mode = None
        self.stability_check_count = 0
        self.max_stability_checks = 5
        self.display_error_count = 0
        self.max_display_errors = 3
        
    def apply_hdmi_stability_fixes(self, surface: pygame.Surface) -> bool:
        """
        Apply comprehensive HDMI stability fixes
        
        Args:
            surface: The display surface to stabilize
            
        Returns:
            True if fixes were applied successfully
        """
        try:
            print("Applying HDMI stability fixes...")
            
            # Phase 1: Clear and stabilize the display
            self._clear_and_stabilize_display(surface)
            
            # Phase 2: Apply multiple refresh cycles
            self._apply_refresh_cycles(surface)
            
            # Phase 3: Force display synchronization
            self._force_display_sync(surface)
            
            # Phase 4: Verify display stability
            if self._verify_display_stability(surface):
                print("HDMI stability fixes applied successfully")
                self.display_error_count = 0  # Reset error count on success
                return True
            else:
                print("Display stability verification failed")
                self.display_error_count += 1
                return False
                
        except Exception as e:
            print(f"Error applying HDMI stability fixes: {e}")
            self.display_error_count += 1
            return False
    
    def _clear_and_stabilize_display(self, surface: pygame.Surface):
        """Clear display and apply initial stabilization"""
        try:
            # Clear with black background
            surface.fill((0, 0, 0))
            pygame.display.flip()
            time.sleep(0.1)
            
            # Clear with different color to force refresh
            surface.fill((1, 1, 1))
            pygame.display.flip()
            time.sleep(0.05)
            
            # Final clear
            surface.fill((0, 0, 0))
            pygame.display.flip()
            time.sleep(0.1)
            
        except Exception as e:
            print(f"Error in display clearing: {e}")
    
    def _apply_refresh_cycles(self, surface: pygame.Surface):
        """Apply multiple refresh cycles for stability"""
        try:
            # Multiple refresh cycles with varying delays
            refresh_delays = [0.05, 0.1, 0.05, 0.15, 0.05]
            
            for i, delay in enumerate(refresh_delays):
                # Alternate between slight color variations to force refresh
                color_value = 2 if i % 2 == 0 else 0
                surface.fill((color_value, color_value, color_value))
                pygame.display.flip()
                time.sleep(delay)
            
            # Final stabilization
            surface.fill((0, 0, 0))
            pygame.display.flip()
            
        except Exception as e:
            print(f"Error in refresh cycles: {e}")
    
    def _force_display_sync(self, surface: pygame.Surface):
        """Force display synchronization"""
        try:
            # Force multiple display updates to ensure synchronization
            for _ in range(3):
                pygame.display.flip()
                time.sleep(0.02)
            
            # Force a complete display update
            pygame.display.update()
            time.sleep(0.1)
            
            # Final sync
            pygame.display.flip()
            
        except Exception as e:
            print(f"Error in display sync: {e}")
    
    def _verify_display_stability(self, surface: pygame.Surface) -> bool:
        """Verify that the display is stable"""
        try:
            # Simple stability test - try to draw and flip
            test_rect = pygame.Rect(0, 0, 10, 10)
            pygame.draw.rect(surface, (255, 255, 255), test_rect)
            pygame.display.flip()
            time.sleep(0.05)
            
            # Clear the test
            pygame.draw.rect(surface, (0, 0, 0), test_rect)
            pygame.display.flip()
            
            return True
            
        except Exception as e:
            print(f"Display stability verification failed: {e}")
            return False
    
    def handle_display_mode_switch(self, old_surface: pygame.Surface, 
                                 new_width: int, new_height: int, 
                                 fullscreen: bool = False) -> Optional[pygame.Surface]:
        """
        Handle display mode switching with HDMI stability
        
        Args:
            old_surface: Current display surface
            new_width: Target width
            new_height: Target height
            fullscreen: Whether to use fullscreen mode
            
        Returns:
            New display surface or None if failed
        """
        try:
            print(f"Handling display mode switch to {new_width}x{new_height} (fullscreen: {fullscreen})")
            
            # Store current mode as last stable
            if old_surface:
                self.last_stable_mode = {
                    'size': old_surface.get_size(),
                    'fullscreen': bool(old_surface.get_flags() & pygame.FULLSCREEN)
                }
            
            # Clear current display before switching
            if old_surface:
                old_surface.fill((0, 0, 0))
                pygame.display.flip()
                time.sleep(0.1)
            
            # Attempt mode switch with progressive fallback
            new_surface = self._attempt_mode_switch(new_width, new_height, fullscreen)
            
            if new_surface:
                # Apply stability fixes to new surface
                if self.apply_hdmi_stability_fixes(new_surface):
                    return new_surface
                else:
                    print("Stability fixes failed, attempting recovery...")
                    return self._attempt_recovery(new_surface)
            else:
                print("Mode switch failed, attempting recovery...")
                return self._attempt_recovery(old_surface)
                
        except Exception as e:
            print(f"Error in display mode switch: {e}")
            return self._attempt_recovery(old_surface)
    
    def _attempt_mode_switch(self, width: int, height: int, fullscreen: bool) -> Optional[pygame.Surface]:
        """Attempt display mode switch with multiple strategies"""
        
        # Strategy 1: Standard mode switch
        try:
            flags = pygame.FULLSCREEN if fullscreen else pygame.RESIZABLE | pygame.DOUBLEBUF
            surface = pygame.display.set_mode((width, height), flags)
            print(f"Standard mode switch successful: {width}x{height}")
            return surface
        except Exception as e:
            print(f"Standard mode switch failed: {e}")
        
        # Strategy 2: Mode switch with basic flags
        try:
            flags = pygame.FULLSCREEN if fullscreen else pygame.RESIZABLE
            surface = pygame.display.set_mode((width, height), flags)
            print(f"Basic mode switch successful: {width}x{height}")
            return surface
        except Exception as e:
            print(f"Basic mode switch failed: {e}")
        
        # Strategy 3: Mode switch with no flags
        try:
            flags = pygame.FULLSCREEN if fullscreen else 0
            surface = pygame.display.set_mode((width, height), flags)
            print(f"No-flags mode switch successful: {width}x{height}")
            return surface
        except Exception as e:
            print(f"No-flags mode switch failed: {e}")
        
        return None
    
    def _attempt_recovery(self, surface: pygame.Surface) -> Optional[pygame.Surface]:
        """Attempt to recover from display mode switch failure"""
        try:
            print("Attempting display recovery...")
            
            # Try to restore last stable mode
            if self.last_stable_mode:
                stable_size = self.last_stable_mode['size']
                stable_fullscreen = self.last_stable_mode['fullscreen']
                
                print(f"Restoring last stable mode: {stable_size} (fullscreen: {stable_fullscreen})")
                
                recovery_surface = self._attempt_mode_switch(
                    stable_size[0], stable_size[1], stable_fullscreen
                )
                
                if recovery_surface:
                    self.apply_hdmi_stability_fixes(recovery_surface)
                    return recovery_surface
            
            # Fallback to safe mode
            print("Falling back to safe display mode...")
            safe_surface = self._attempt_mode_switch(1024, 768, False)
            
            if safe_surface:
                self.apply_hdmi_stability_fixes(safe_surface)
                return safe_surface
            
            return surface  # Return original surface as last resort
            
        except Exception as e:
            print(f"Recovery attempt failed: {e}")
            return surface
    
    def is_display_unstable(self) -> bool:
        """Check if display is showing signs of instability"""
        return self.display_error_count >= self.max_display_errors
    
    def reset_stability_tracking(self):
        """Reset stability tracking counters"""
        self.display_error_count = 0
        self.stability_check_count = 0
        print("Display stability tracking reset")


# Global instance
_hdmi_fixer = None

def get_hdmi_display_fixer() -> HDMIDisplayFixer:
    """Get the global HDMI display fixer instance"""
    global _hdmi_fixer
    if _hdmi_fixer is None:
        _hdmi_fixer = HDMIDisplayFixer()
    return _hdmi_fixer
