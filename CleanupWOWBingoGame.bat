@echo off
:: WOW Bingo Game Cleanup Script
:: Run this if the game won't start

echo WOW Bingo Game - Emergency Cleanup
echo ===================================

echo Stopping all game processes...
taskkill /f /im "WOWBingoGame.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *main.py*" >nul 2>&1

echo Cleaning temporary files...
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.tmp" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.lock" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.pid" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.db-wal" >nul 2>&1
del /q "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\data\*.db-shm" >nul 2>&1

echo Resetting audio devices...
python -c "import pygame; pygame.mixer.quit(); pygame.quit()" >nul 2>&1

echo Cleanup completed!
echo You can now try running the game again.
pause
