#!/usr/bin/env python3
"""
Simple test script to verify Nuitka compilation works
"""

import sys
import os
import time

def main():
    print("=" * 50)
    print("WOW Bingo Game - Test Compilation")
    print("=" * 50)
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Current directory: {os.getcwd()}")
    print(f"Script path: {__file__}")
    print("=" * 50)
    print("Test completed successfully!")
    print("=" * 50)
    
    # Keep window open for a moment
    time.sleep(2)

if __name__ == "__main__":
    main()
