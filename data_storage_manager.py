#!/usr/bin/env python3
"""
Data Storage Manager for WOW Bingo Game
=======================================

This module provides intelligent data storage management for the WOW Bingo Game,
handling different deployment scenarios (development, portable executable, installed application).

Features:
- Automatic detection of runtime environment
- Proper data directory selection based on deployment type
- Cross-platform compatibility
- User data protection and persistence
- Backup and migration capabilities
"""

import os
import sys
import json
import shutil
import platform
from pathlib import Path
from typing import Optional, Dict, Any, List

class DataStorageManager:
    """Manages data storage locations for different deployment scenarios."""
    
    def __init__(self):
        self.app_name = "WOW Bingo Game"
        self.app_vendor = "WOW Games"
        self.data_version = "1.0"
        
        # Detect runtime environment
        self.is_frozen = getattr(sys, 'frozen', False)
        self.is_portable = self._detect_portable_mode()
        
        # Initialize storage paths
        self._init_storage_paths()
        
    def _detect_portable_mode(self) -> bool:
        """Detect if running in portable mode."""
        if self.is_frozen:
            # Check for portable indicator file
            exe_dir = Path(sys.executable).parent
            portable_indicators = [
                exe_dir / "portable.txt",
                exe_dir / "portable.ini",
                exe_dir / "data",  # If data folder exists next to exe
            ]
            return any(indicator.exists() for indicator in portable_indicators)
        return False
        
    def _init_storage_paths(self) -> None:
        """Initialize storage paths based on deployment type."""
        if self.is_frozen:
            if self.is_portable:
                # Portable mode: store data next to executable
                self.base_dir = Path(sys.executable).parent
                self.data_dir = self.base_dir / "data"
                self.storage_type = "portable"
            else:
                # Installed mode: use system-appropriate directories
                self.base_dir = self._get_user_data_dir()
                self.data_dir = self.base_dir / "data"
                self.storage_type = "installed"
        else:
            # Development mode: use project directory
            self.base_dir = Path(__file__).parent
            self.data_dir = self.base_dir / "data"
            self.storage_type = "development"
            
        # Ensure data directory exists
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    def _get_user_data_dir(self) -> Path:
        """Get platform-appropriate user data directory."""
        system = platform.system()
        
        if system == "Windows":
            # Use AppData/Roaming for user-specific application data
            appdata = os.environ.get('APPDATA')
            if appdata:
                return Path(appdata) / self.app_vendor / self.app_name
            else:
                # Fallback to user profile
                return Path.home() / "AppData" / "Roaming" / self.app_vendor / self.app_name
                
        elif system == "Darwin":  # macOS
            return Path.home() / "Library" / "Application Support" / self.app_name
            
        else:  # Linux and other Unix-like systems
            # Use XDG Base Directory specification
            xdg_data_home = os.environ.get('XDG_DATA_HOME')
            if xdg_data_home:
                return Path(xdg_data_home) / self.app_name.lower().replace(' ', '-')
            else:
                return Path.home() / ".local" / "share" / self.app_name.lower().replace(' ', '-')
                
    def get_data_path(self, filename: str) -> Path:
        """Get full path for a data file."""
        return self.data_dir / filename
        
    def get_config_path(self, filename: str) -> Path:
        """Get full path for a configuration file."""
        return self.data_dir / filename
        
    def get_database_path(self, filename: str) -> Path:
        """Get full path for a database file."""
        return self.data_dir / filename
        
    def get_backup_dir(self) -> Path:
        """Get backup directory path."""
        backup_dir = self.data_dir / "backups"
        backup_dir.mkdir(exist_ok=True)
        return backup_dir
        
    def get_export_dir(self) -> Path:
        """Get export directory path."""
        export_dir = self.data_dir / "exports"
        export_dir.mkdir(exist_ok=True)
        return export_dir
        
    def get_temp_dir(self) -> Path:
        """Get temporary directory path."""
        temp_dir = self.data_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        return temp_dir
        
    def migrate_data_from_old_location(self, old_data_dir: Path) -> bool:
        """Migrate data from old location to new location."""
        if not old_data_dir.exists():
            return False
            
        try:
            print(f"Migrating data from {old_data_dir} to {self.data_dir}")
            
            # Copy all files from old location
            for item in old_data_dir.iterdir():
                if item.is_file():
                    dest = self.data_dir / item.name
                    if not dest.exists():  # Don't overwrite existing files
                        shutil.copy2(item, dest)
                        print(f"Migrated: {item.name}")
                elif item.is_dir() and item.name not in ['temp', 'cache']:
                    dest_dir = self.data_dir / item.name
                    if not dest_dir.exists():
                        shutil.copytree(item, dest_dir)
                        print(f"Migrated directory: {item.name}")
                        
            return True
            
        except Exception as e:
            print(f"Error migrating data: {e}")
            return False
            
    def create_portable_indicator(self) -> bool:
        """Create portable mode indicator file."""
        if self.is_frozen:
            try:
                exe_dir = Path(sys.executable).parent
                portable_file = exe_dir / "portable.txt"
                with open(portable_file, 'w') as f:
                    f.write(f"WOW Bingo Game - Portable Mode\n")
                    f.write(f"Created: {self._get_timestamp()}\n")
                    f.write(f"Data stored in: {self.data_dir}\n")
                return True
            except Exception as e:
                print(f"Error creating portable indicator: {e}")
                return False
        return False
        
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about current storage configuration."""
        return {
            "storage_type": self.storage_type,
            "is_frozen": self.is_frozen,
            "is_portable": self.is_portable,
            "base_dir": str(self.base_dir),
            "data_dir": str(self.data_dir),
            "platform": platform.system(),
            "data_version": self.data_version,
            "writable": os.access(self.data_dir, os.W_OK),
            "size_mb": self._get_directory_size_mb(self.data_dir)
        }
        
    def _get_directory_size_mb(self, directory: Path) -> float:
        """Get directory size in MB."""
        try:
            total_size = sum(f.stat().st_size for f in directory.rglob('*') if f.is_file())
            return round(total_size / (1024 * 1024), 2)
        except:
            return 0.0
            
    def _get_timestamp(self) -> str:
        """Get current timestamp string."""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def cleanup_temp_files(self) -> int:
        """Clean up temporary files and return count of files removed."""
        temp_patterns = ['*.tmp', '*.lock', '*.pid', '*.db-wal', '*.db-shm']
        removed_count = 0
        
        try:
            for pattern in temp_patterns:
                for file_path in self.data_dir.rglob(pattern):
                    if file_path.is_file():
                        try:
                            file_path.unlink()
                            removed_count += 1
                        except:
                            pass
                            
            # Clean temp directory
            temp_dir = self.get_temp_dir()
            for item in temp_dir.iterdir():
                try:
                    if item.is_file():
                        item.unlink()
                        removed_count += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        removed_count += 1
                except:
                    pass
                    
        except Exception as e:
            print(f"Error during cleanup: {e}")
            
        return removed_count
        
    def create_backup(self, backup_name: Optional[str] = None) -> Optional[Path]:
        """Create a backup of all data."""
        if backup_name is None:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
            
        try:
            backup_dir = self.get_backup_dir()
            backup_path = backup_dir / backup_name
            
            # Create backup directory
            backup_path.mkdir(exist_ok=True)
            
            # Copy all data files (excluding backups and temp)
            for item in self.data_dir.iterdir():
                if item.name not in ['backups', 'temp', 'cache']:
                    dest = backup_path / item.name
                    if item.is_file():
                        shutil.copy2(item, dest)
                    elif item.is_dir():
                        shutil.copytree(item, dest)
                        
            return backup_path
            
        except Exception as e:
            print(f"Error creating backup: {e}")
            return None

# Global instance
_storage_manager = None

def get_storage_manager() -> DataStorageManager:
    """Get the global storage manager instance."""
    global _storage_manager
    if _storage_manager is None:
        _storage_manager = DataStorageManager()
    return _storage_manager

def get_data_path(filename: str) -> str:
    """Get data file path (backward compatibility function)."""
    return str(get_storage_manager().get_data_path(filename))

def get_database_path(filename: str) -> str:
    """Get database file path (backward compatibility function)."""
    return str(get_storage_manager().get_database_path(filename))

def migrate_legacy_data() -> bool:
    """Migrate data from legacy 'data' directory if needed."""
    storage = get_storage_manager()
    
    # Only migrate if we're in installed mode and legacy data exists
    if storage.storage_type == "installed":
        # Check for legacy data in executable directory or current directory
        possible_legacy_dirs = [
            Path(sys.executable).parent / "data" if storage.is_frozen else None,
            Path.cwd() / "data",
            Path(__file__).parent / "data"
        ]
        
        for legacy_dir in possible_legacy_dirs:
            if legacy_dir and legacy_dir.exists() and legacy_dir != storage.data_dir:
                return storage.migrate_data_from_old_location(legacy_dir)
                
    return False

def print_storage_info():
    """Print current storage configuration."""
    storage = get_storage_manager()
    info = storage.get_storage_info()
    
    print("=" * 60)
    print("WOW Bingo Game - Data Storage Information")
    print("=" * 60)
    print(f"Storage Type: {info['storage_type'].title()}")
    print(f"Platform: {info['platform']}")
    print(f"Executable: {'Yes' if info['is_frozen'] else 'No'}")
    print(f"Portable Mode: {'Yes' if info['is_portable'] else 'No'}")
    print(f"Data Directory: {info['data_dir']}")
    print(f"Writable: {'Yes' if info['writable'] else 'No'}")
    print(f"Data Size: {info['size_mb']} MB")
    print("=" * 60)

if __name__ == "__main__":
    # Test the storage manager
    print_storage_info()
    
    # Test file operations
    storage = get_storage_manager()
    test_file = storage.get_data_path("test.txt")
    
    try:
        with open(test_file, 'w') as f:
            f.write("Test data storage")
        print(f"✓ Successfully wrote to: {test_file}")
        
        with open(test_file, 'r') as f:
            content = f.read()
        print(f"✓ Successfully read: {content}")
        
        os.unlink(test_file)
        print("✓ Test file cleaned up")
        
    except Exception as e:
        print(f"✗ Error testing storage: {e}")
