#!/usr/bin/env python

with open("Board_selection_fixed.py", "r", encoding="utf-8") as f:
    lines = f.readlines()

# The specific issue is on line 3356-3357:
# There's a line that adds self.prize_pool_manual_override
# And then an indented return statement that doesn't belong there

# Delete the wrongly indented return statement on line 3357
# First verify it's a return statement with indentation
if len(lines) > 3356 and lines[3356].strip() == "return" and lines[3356].startswith("    "):
    print(f"Found indented return at line 3357: '{lines[3356].strip()}'")
    # Remove the line
    lines.pop(3356)
    print("Removed indented return statement")
else:
    # Let's try to find it nearby
    for i in range(3350, 3360):
        if i < len(lines) and lines[i].strip() == "return" and lines[i].startswith("    "):
            print(f"Found indented return at line {i+1}: '{lines[i].strip()}'")
            lines.pop(i)
            print(f"Removed indented return statement from line {i+1}")
            break

with open("Board_selection_fixed.py", "w", encoding="utf-8") as f:
    f.writelines(lines)

print("Fixed indentation error") 