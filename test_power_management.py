#!/usr/bin/env python3
"""
Test script for Power Management System

This script tests the power management functionality to ensure
the screen stays always active and prevents Windows from dimming
or turning off the display.
"""

import sys
import time
import threading
from power_management import (
    get_power_manager, 
    start_power_management, 
    stop_power_management, 
    is_power_management_active
)

def test_power_manager_creation():
    """Test power manager instance creation"""
    print("1. Testing power manager creation...")
    
    try:
        power_manager = get_power_manager()
        print(f"✓ Power manager created: {type(power_manager).__name__}")
        
        # Test Windows API initialization
        if power_manager.user32 and power_manager.kernel32:
            print("✓ Windows API initialized successfully")
        else:
            print("⚠ Windows API not available (not on Windows or missing ctypes)")
        
        return True
    except Exception as e:
        print(f"✗ Error creating power manager: {e}")
        return False

def test_power_management_start_stop():
    """Test starting and stopping power management"""
    print("\n2. Testing power management start/stop...")
    
    try:
        # Test starting
        print("Starting power management...")
        if start_power_management():
            print("✓ Power management started successfully")
        else:
            print("⚠ Power management start returned False")
        
        # Check if active
        if is_power_management_active():
            print("✓ Power management is active")
        else:
            print("✗ Power management is not active")
        
        # Wait a moment
        time.sleep(2)
        
        # Test stopping
        print("Stopping power management...")
        if stop_power_management():
            print("✓ Power management stopped successfully")
        else:
            print("⚠ Power management stop returned False")
        
        # Check if inactive
        if not is_power_management_active():
            print("✓ Power management is inactive")
        else:
            print("✗ Power management is still active")
        
        return True
    except Exception as e:
        print(f"✗ Error in start/stop test: {e}")
        return False

def test_screen_sleep_prevention():
    """Test screen sleep prevention functionality"""
    print("\n3. Testing screen sleep prevention...")
    
    try:
        power_manager = get_power_manager()
        
        # Test prevent screen sleep
        if power_manager.prevent_screen_sleep():
            print("✓ Screen sleep prevention activated")
        else:
            print("⚠ Screen sleep prevention failed or not available")
        
        # Test restore power settings
        if power_manager.restore_power_settings():
            print("✓ Power settings restored")
        else:
            print("⚠ Power settings restore failed or not available")
        
        return True
    except Exception as e:
        print(f"✗ Error in screen sleep prevention test: {e}")
        return False

def test_window_management():
    """Test window management functionality"""
    print("\n4. Testing window management...")
    
    try:
        power_manager = get_power_manager()
        
        # Note: This test requires pygame to be initialized with a window
        print("Testing window management (requires pygame window)...")
        
        # Try to keep window active (will fail gracefully without pygame window)
        result = power_manager.keep_window_active()
        if result:
            print("✓ Window management successful")
        else:
            print("⚠ Window management failed (expected without pygame window)")
        
        # Test user activity simulation
        if power_manager.simulate_user_activity():
            print("✓ User activity simulation successful")
        else:
            print("⚠ User activity simulation failed or not available")
        
        return True
    except Exception as e:
        print(f"✗ Error in window management test: {e}")
        return False

def test_background_thread():
    """Test background keep-alive thread functionality"""
    print("\n5. Testing background keep-alive thread...")
    
    try:
        # Start power management (which starts the background thread)
        start_power_management()
        
        power_manager = get_power_manager()
        
        # Check if thread is running
        if power_manager.keep_alive_thread and power_manager.keep_alive_thread.is_alive():
            print("✓ Background keep-alive thread is running")
        else:
            print("⚠ Background keep-alive thread is not running")
        
        # Let it run for a few seconds
        print("Letting background thread run for 5 seconds...")
        time.sleep(5)
        
        # Check if still running
        if power_manager.keep_alive_thread and power_manager.keep_alive_thread.is_alive():
            print("✓ Background thread still running after 5 seconds")
        else:
            print("⚠ Background thread stopped running")
        
        # Stop power management
        stop_power_management()
        
        # Wait for thread to stop
        time.sleep(1)
        
        # Check if thread stopped
        if not power_manager.keep_alive_thread or not power_manager.keep_alive_thread.is_alive():
            print("✓ Background thread stopped successfully")
        else:
            print("⚠ Background thread did not stop")
        
        return True
    except Exception as e:
        print(f"✗ Error in background thread test: {e}")
        return False

def test_integration_with_pygame():
    """Test integration with pygame (optional)"""
    print("\n6. Testing pygame integration (optional)...")
    
    try:
        import pygame
        pygame.init()
        
        # Create a small test window
        screen = pygame.display.set_mode((400, 300))
        pygame.display.set_caption("Power Management Test")
        
        print("✓ Pygame window created")
        
        # Start power management
        start_power_management()
        
        power_manager = get_power_manager()
        
        # Test window management with actual pygame window
        if power_manager.keep_window_active():
            print("✓ Window management with pygame window successful")
        else:
            print("⚠ Window management with pygame window failed")
        
        # Clean up
        stop_power_management()
        pygame.quit()
        
        print("✓ Pygame integration test completed")
        return True
        
    except ImportError:
        print("⚠ Pygame not available - skipping pygame integration test")
        return True
    except Exception as e:
        print(f"✗ Error in pygame integration test: {e}")
        return False

def main():
    """Run all power management tests"""
    print("POWER MANAGEMENT SYSTEM TEST")
    print("=" * 50)
    
    if sys.platform != "win32":
        print("⚠ Warning: Power management is designed for Windows")
        print("Some features may not work on other platforms")
    
    tests = [
        test_power_manager_creation,
        test_power_management_start_stop,
        test_screen_sleep_prevention,
        test_window_management,
        test_background_thread,
        test_integration_with_pygame
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Power management system is working correctly.")
        print("\nThe system will:")
        print("- Prevent Windows from dimming the screen")
        print("- Prevent Windows from turning off the display")
        print("- Keep the application window active")
        print("- Simulate user activity to prevent screensaver")
    else:
        print("⚠ Some tests failed. Check the output above for details.")
    
    print("\nTo use in your application:")
    print("1. Import: from power_management import start_power_management, stop_power_management")
    print("2. Start: start_power_management() at application startup")
    print("3. Stop: stop_power_management() before application exit")

if __name__ == "__main__":
    main()
