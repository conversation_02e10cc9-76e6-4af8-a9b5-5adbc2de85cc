# Optimized Build Requirements for WOW Bingo Game
# ===============================================
# This file contains all packages required for building a standalone executable
# that can run on any Windows PC without Python installation

# Primary build tool (REQUIRED)
pyinstaller>=6.0.0

# Alternative build tools (OPTIONAL)
nuitka>=2.7.0
auto-py-to-exe>=2.40.0

# Core game dependencies (REQUIRED)
pygame>=2.5.0
pygame-ce>=2.4.0
pyperclip>=1.8.2
psutil>=5.9.0
pillow>=10.0.0

# Modern UI framework (REQUIRED for new UI)
flet>=0.24.0

# Audio processing (REQUIRED)
pydub>=0.25.1

# Database support (REQUIRED)
sqlite3-utils>=3.36.0
rethinkdb>=2.4.10

# Cryptography for voucher system (REQUIRED)
cryptography>=41.0.0

# Configuration and utilities (REQUIRED)
pydantic>=2.0.0
pydantic-settings>=2.0.0
requests>=2.31.0
loguru>=0.7.0

# Performance optimization (RECOMMENDED)
numpy>=1.24.0
numba>=0.58.0

# Type hints (RECOMMENDED)
typing-extensions>=4.7.0

# Build optimization tools (RECOMMENDED for Windows)
wheel>=0.37.0
setuptools>=60.0.0
orderedset>=4.1.0
zstandard>=0.21.0

# Development tools (OPTIONAL)
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
isort>=5.12.0

# Windows-specific dependencies (REQUIRED for Windows builds)
pywin32>=306; sys_platform == "win32"
pywin32-ctypes>=0.2.2; sys_platform == "win32"
