import pygame
from pygame import Rect
import math
import time
import json
import random
import pattern_utils
from amharic_support import AmharicSupport

class GameUIHandler:
    """Handles UI components for game state transitions and overlays"""

    def __init__(self, game):
        """
        Initialize the game UI handler

        Args:
            game: Reference to the main BingoGame instance
        """
        self.game = game

        # Input states for pause reason prompt
        self.reason_input_active = False
        self.reason_input_text = ""
        self.reason_cursor_visible = True
        self.reason_cursor_timer = 0

        # Store modal windows hit areas
        self.modal_hit_areas = {}

        # Reset confirmation dialog state
        self.show_reset_confirmation = False

        # Exit confirmation dialog state
        self.show_exit_confirmation = False

        # CRITICAL FIX: Add flags to track button states
        # This helps prevent conflicts between reset and resume buttons
        self.reset_recently_canceled = False
        self.reset_cancel_time = 0
        self.reset_button_locked = False
        self.resume_button_locked = False
        self.button_lock_time = 0
        self.button_lock_duration = 1000  # 1 second lock duration

        # CRITICAL FIX: Add flag to track post-reset-cancel state
        # This helps prevent the resume button from resetting the game after canceling a reset
        self.post_reset_cancel_state = False
        self.post_reset_cancel_time = 0
        self.post_reset_cancel_duration = 500  # 500ms duration (reduced from 5000ms)

        # Window dragging variables
        self.dragging = False
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        self.popup_position = None  # Will store the current position of the popup

        # Winner animation variables
        self.winner_particles = []
        self.winner_animation_start_time = time.time()

    def get_scale_factors(self):
        """
        Get the current scale factors, with fallbacks if not available as instance variables

        Returns:
            tuple: (scale_x, scale_y) scaling factors
        """
        # Try to get scale factors from game instance
        if hasattr(self.game, 'scale_x') and hasattr(self.game, 'scale_y'):
            scale_x = self.game.scale_x
            scale_y = self.game.scale_y
            print(f"CRITICAL FIX: Got scale factors from game instance: scale_x={scale_x}, scale_y={scale_y}")
            return scale_x, scale_y

        # Fallback to module globals if available
        try:
            import main
            scale_x = main.scale_x
            scale_y = main.scale_y
            print(f"CRITICAL FIX: Got scale factors from main module: scale_x={scale_x}, scale_y={scale_y}")
            return scale_x, scale_y
        except (ImportError, AttributeError) as e:
            # Last resort fallback - use 1.0 (no scaling)
            print(f"CRITICAL FIX: Using fallback scale factors (1.0, 1.0) due to error: {e}")
            return 1.0, 1.0

    def draw_pause_reason_prompt(self, screen):
        """Draw the pause reason prompt overlay with modern UI styling"""
        if not hasattr(self.game, 'game_state') or not self.game.game_state.show_pause_reason_prompt:
            return

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Automatically set focus to the input field when the prompt appears
        # We need to track if this is the first time we're showing the prompt
        if not hasattr(self, 'pause_prompt_just_opened') or not self.pause_prompt_just_opened:
            self.reason_input_active = True
            self.reason_cursor_visible = True
            self.reason_cursor_timer = 0
            self.pause_prompt_just_opened = True

        # Draw semi-transparent overlay with blur effect
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create prompt box - increased height to accommodate admin buttons
        box_width = int(screen_width * 0.5)
        box_height = int(screen_height * 0.4)  # Increased from 0.3 to 0.4
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # Create a more visually appealing background with enhanced gradient and glow effects
        # First, add a subtle outer glow effect
        outer_glow_size = 15
        outer_glow_color = (100, 50, 150, 30)  # Subtle purple glow with alpha
        outer_glow_surface = pygame.Surface((box_width + outer_glow_size*2, box_height + outer_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, box_width + outer_glow_size*2, box_height + outer_glow_size*2), border_radius=25)
        screen.blit(outer_glow_surface, (box_x - outer_glow_size, box_y - outer_glow_size))

        # Add a second inner glow for depth
        inner_glow_size = 8
        inner_glow_color = (120, 60, 180, 50)  # Brighter purple glow with alpha
        inner_glow_surface = pygame.Surface((box_width + inner_glow_size*2, box_height + inner_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, box_width + inner_glow_size*2, box_height + inner_glow_size*2), border_radius=22)
        screen.blit(inner_glow_surface, (box_x - inner_glow_size, box_y - inner_glow_size))

        # Draw main box with enhanced gradient background
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (40, 20, 70),  # Richer dark purple
            (80, 30, 120),  # Richer medium purple
            20  # Border radius
        )

        # Add header bar with enhanced gradient and visual effects
        header_height = int(55 * min(scale_x, scale_y))
        header_rect = Rect(box_x, box_y, box_width, header_height)

        # Draw the header with enhanced gradient
        self.draw_gradient_rect(
            screen,
            header_rect,
            (60, 30, 100),  # Dark purple
            (100, 40, 160),  # Lighter purple
            border_radius=10  # Rounded corners at top
        )

        # Title text with shadow for depth
        title_font = pygame.font.SysFont("Arial", int(36 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 10, 30)  # Dark shadow color
        shadow_text = title_font.render("Pause Reason", True, shadow_color)
        shadow_rect = shadow_text.get_rect(centerx=header_rect.centerx + shadow_offset, centery=header_rect.centery + shadow_offset)
        screen.blit(shadow_text, shadow_rect)

        # Main title text
        title_text = title_font.render("Pause Reason", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=header_rect.centerx, centery=header_rect.centery)
        screen.blit(title_text, title_rect)

        # Description text - simplified for clarity with enhanced styling
        desc_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)))
        desc_text = [
            "Enter a player claim number (1-1200):",
            "• Enter cartella number to validate a claim"
        ]

        # Create a modern info bar for the description
        info_bar_height = int(70 * min(scale_x, scale_y))
        info_bar_rect = Rect(
            box_x + int(20 * min(scale_x, scale_y)),
            header_rect.bottom + int(15 * min(scale_x, scale_y)),
            box_width - int(40 * min(scale_x, scale_y)),
            info_bar_height
        )

        # Draw info bar with subtle gradient
        self.draw_gradient_rect(
            screen,
            info_bar_rect,
            (50, 25, 85),  # Slightly lighter than background
            (70, 35, 110),
            10  # Rounded corners
        )

        # Draw description text with improved positioning
        for i, line in enumerate(desc_text):
            text_surf = desc_font.render(line, True, (255, 255, 255))
            text_rect = text_surf.get_rect(
                x=info_bar_rect.x + 15,
                y=info_bar_rect.y + 10 + i * 30
            )
            screen.blit(text_surf, text_rect)

        # Input field with enhanced styling
        input_rect = Rect(
            box_x + int(30 * min(scale_x, scale_y)),
            info_bar_rect.bottom + int(20 * min(scale_x, scale_y)),
            box_width - int(60 * min(scale_x, scale_y)),
            int(45 * min(scale_x, scale_y))  # Slightly taller for better visibility
        )

        # Draw with different color when active and add glow effect
        if self.reason_input_active:
            # Add glow effect for active input
            input_glow_size = 5
            input_glow_color = (100, 180, 255, 40)  # Blue glow with alpha
            input_glow_surface = pygame.Surface((input_rect.width + input_glow_size*2, input_rect.height + input_glow_size*2), pygame.SRCALPHA)
            pygame.draw.rect(input_glow_surface, input_glow_color, (0, 0, input_rect.width + input_glow_size*2, input_rect.height + input_glow_size*2), border_radius=12)
            screen.blit(input_glow_surface, (input_rect.x - input_glow_size, input_rect.y - input_glow_size))

            bg_color = (60, 60, 90)
            border_color = (100, 200, 255)
        else:
            bg_color = (50, 50, 70)
            border_color = (180, 180, 200)

        # Draw input field with gradient
        self.draw_gradient_rect(
            screen,
            input_rect,
            bg_color,
            (bg_color[0]+10, bg_color[1]+10, bg_color[2]+10),  # Slightly lighter at bottom
            12  # Rounded corners
        )
        pygame.draw.rect(screen, border_color, input_rect, 2, border_radius=12)

        # Add a small indicator text below the input field when active
        if self.reason_input_active:
            indicator_font = pygame.font.SysFont("Arial", int(14 * min(scale_x, scale_y)))
            indicator_text = indicator_font.render("Input active - type a number", True, (200, 255, 200))
            indicator_rect = indicator_text.get_rect(
                centerx=input_rect.centerx,
                top=input_rect.bottom + 5
            )
            screen.blit(indicator_text, indicator_rect)

        # Input text with improved styling
        input_font = pygame.font.SysFont("Arial", int(26 * min(scale_x, scale_y)), bold=True)
        if self.reason_input_text:
            input_surf = input_font.render(self.reason_input_text, True, (255, 255, 255))
            input_text_rect = input_surf.get_rect(
                x=input_rect.x + 15,  # More padding
                centery=input_rect.centery
            )
            screen.blit(input_surf, input_text_rect)

            # Draw cursor if active with animation
            if self.reason_input_active and self.reason_cursor_visible:
                cursor_x = input_text_rect.right + 2
                # Use a more visible cursor with rounded ends
                cursor_width = int(3 * min(scale_x, scale_y))
                cursor_height = int(30 * min(scale_x, scale_y))
                cursor_rect = Rect(
                    cursor_x - cursor_width//2,
                    input_rect.centery - cursor_height//2,
                    cursor_width,
                    cursor_height
                )
                pygame.draw.rect(screen, (180, 220, 255), cursor_rect, border_radius=cursor_width)
        else:
            # Draw cursor at beginning if active with no text
            if self.reason_input_active and self.reason_cursor_visible:
                cursor_x = input_rect.x + 15
                # Use a more visible cursor with rounded ends
                cursor_width = int(3 * min(scale_x, scale_y))
                cursor_height = int(30 * min(scale_x, scale_y))
                cursor_rect = Rect(
                    cursor_x - cursor_width//2,
                    input_rect.centery - cursor_height//2,
                    cursor_width,
                    cursor_height
                )
                pygame.draw.rect(screen, (180, 220, 255), cursor_rect, border_radius=cursor_width)

        # Submit button with enhanced styling
        button_width = int(100 * min(scale_x, scale_y))
        button_height = int(36 * min(scale_x, scale_y))
        button_rect = Rect(
            box_x + box_width - button_width - int(30 * min(scale_x, scale_y)),
            input_rect.bottom + int(15 * min(scale_x, scale_y)),
            button_width,
            button_height
        )

        # Draw button with gradient and glow effect
        submit_glow_size = 4
        submit_glow_color = (80, 200, 120, 40)  # Green glow with alpha
        submit_glow_surface = pygame.Surface((button_rect.width + submit_glow_size*2, button_rect.height + submit_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(submit_glow_surface, submit_glow_color, (0, 0, button_rect.width + submit_glow_size*2, button_rect.height + submit_glow_size*2), border_radius=10)
        screen.blit(submit_glow_surface, (button_rect.x - submit_glow_size, button_rect.y - submit_glow_size))

        self.draw_gradient_rect(
            screen,
            button_rect,
            (40, 140, 80),  # Darker green
            (60, 180, 100),  # Lighter green
            10  # Rounded corners
        )

        button_font = pygame.font.SysFont("Arial", int(18 * min(scale_x, scale_y)), bold=True)
        button_text = button_font.render("Submit", True, (255, 255, 255))
        button_text_rect = button_text.get_rect(center=button_rect.center)

        # Add text shadow for depth
        shadow_text = button_font.render("Submit", True, (20, 60, 40))
        shadow_rect = shadow_text.get_rect(centerx=button_text_rect.centerx + 1, centery=button_text_rect.centery + 1)
        screen.blit(shadow_text, shadow_rect)
        screen.blit(button_text, button_text_rect)

        # Store button hit area
        self.modal_hit_areas["pause_reason_submit"] = button_rect
        # Store input field hit area
        self.modal_hit_areas["pause_reason_input"] = input_rect

        # Always add admin control buttons with enhanced styling
        # Button dimensions
        admin_button_width = int(180 * min(scale_x, scale_y))
        admin_button_height = int(50 * min(scale_x, scale_y))
        admin_button_spacing = int(20 * min(scale_x, scale_y))
        admin_buttons_y = box_y + box_height - admin_button_height - int(30 * min(scale_x, scale_y))

        # Calculate button positions with increased spacing to prevent accidental clicks
        admin_button_spacing = int(30 * min(scale_x, scale_y))  # Increased from 20 to 30

        # Resume button with enhanced styling - positioned more to the left
        resume_rect = Rect(
            box_x + (box_width - admin_button_width * 2 - admin_button_spacing) // 2 - int(10 * min(scale_x, scale_y)),
            admin_buttons_y,
            admin_button_width,
            admin_button_height
        )

        # Add glow effect for resume button
        resume_glow_size = 6
        resume_glow_color = (60, 180, 80, 40)  # Green glow with alpha
        resume_glow_surface = pygame.Surface((resume_rect.width + resume_glow_size*2, resume_rect.height + resume_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(resume_glow_surface, resume_glow_color, (0, 0, resume_rect.width + resume_glow_size*2, resume_rect.height + resume_glow_size*2), border_radius=15)
        screen.blit(resume_glow_surface, (resume_rect.x - resume_glow_size, resume_rect.y - resume_glow_size))

        self.draw_gradient_rect(
            screen,
            resume_rect,
            (40, 130, 40),  # Darker green
            (60, 170, 60),  # Lighter green
            12  # Rounded corners
        )

        admin_button_font = pygame.font.SysFont("Arial", int(22 * min(scale_x, scale_y)), bold=True)
        resume_text = admin_button_font.render("Resume Game", True, (255, 255, 255))
        resume_text_rect = resume_text.get_rect(center=resume_rect.center)

        # Add text shadow for depth
        shadow_text = admin_button_font.render("Resume Game", True, (20, 60, 20))
        shadow_rect = shadow_text.get_rect(centerx=resume_text_rect.centerx + 1, centery=resume_text_rect.centery + 1)
        screen.blit(shadow_text, shadow_rect)
        screen.blit(resume_text, resume_text_rect)

        # Store hit area with a unique key to prevent confusion
        self.modal_hit_areas["pause_resume"] = resume_rect

        # Reset button with enhanced styling - positioned more to the right
        reset_rect = Rect(
            resume_rect.right + admin_button_spacing + int(10 * min(scale_x, scale_y)),
            admin_buttons_y,
            admin_button_width,
            admin_button_height
        )

        # Add glow effect for reset button
        reset_glow_size = 6
        reset_glow_color = (180, 60, 60, 40)  # Red glow with alpha
        reset_glow_surface = pygame.Surface((reset_rect.width + reset_glow_size*2, reset_rect.height + reset_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(reset_glow_surface, reset_glow_color, (0, 0, reset_rect.width + reset_glow_size*2, reset_rect.height + reset_glow_size*2), border_radius=15)
        screen.blit(reset_glow_surface, (reset_rect.x - reset_glow_size, reset_rect.y - reset_glow_size))

        self.draw_gradient_rect(
            screen,
            reset_rect,
            (130, 40, 40),  # Darker red
            (170, 60, 60),  # Lighter red
            12  # Rounded corners
        )

        reset_text = admin_button_font.render("Reset Game", True, (255, 255, 255))
        reset_text_rect = reset_text.get_rect(center=reset_rect.center)

        # Add text shadow for depth
        shadow_text = admin_button_font.render("Reset Game", True, (60, 20, 20))
        shadow_rect = shadow_text.get_rect(centerx=reset_text_rect.centerx + 1, centery=reset_text_rect.centery + 1)
        screen.blit(shadow_text, shadow_rect)
        screen.blit(reset_text, reset_text_rect)

        self.modal_hit_areas["pause_reset"] = reset_rect

    def draw_admin_control(self, screen):
        """Draw the admin control overlay - now just returns immediately for backward compatibility"""
        # Return immediately - admin control functionality is now in the pause reason window
        return

    def draw_winner_validation(self, screen):
        """Draw the winner validation overlay"""
        if not hasattr(self.game, 'game_state') or not self.game.game_state.show_winner_validation:
            return

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create validation box
        box_width = int(screen_width * 0.7)
        box_height = int(screen_height * 0.5)
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # Draw box with gradient
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (40, 100, 180),  # Blue
            (60, 140, 220),  # Lighter blue
            20  # Border radius
        )

        # Title text
        title_font = pygame.font.SysFont("Arial", int(36 * min(scale_x, scale_y)), bold=True)
        title_text = title_font.render("Validating Player Claim", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=box_x + box_width // 2, y=box_y + 20)
        screen.blit(title_text, title_rect)

        # Player info
        player_font = pygame.font.SysFont("Arial", int(24 * min(scale_x, scale_y)))
        cartella_text = player_font.render(f"Cartella #{self.game.game_state.player_claim_cartella}", True, (255, 255, 255))
        cartella_rect = cartella_text.get_rect(centerx=box_x + box_width // 2, y=box_y + 80)
        screen.blit(cartella_text, cartella_rect)

        # Loading/processing indicator
        loading_text = player_font.render("Validating claim...", True, (255, 255, 255))
        loading_rect = loading_text.get_rect(centerx=box_x + box_width // 2, y=box_y + 120)
        screen.blit(loading_text, loading_rect)

        # Checks
        check_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)))
        checks = [
            "• Is cartella registered?",
            "• Does board match winning pattern?",
            "• Was claim made before next number was called?"
        ]

        for i, check in enumerate(checks):
            check_surf = check_font.render(check, True, (220, 220, 220))
            check_rect = check_surf.get_rect(
                x=box_x + 50,
                y=box_y + 160 + i * 30
            )
            screen.blit(check_surf, check_rect)

    def draw_player_board(self, screen, card_id=None, board_rect=None, called_numbers=None, show_claim_status=False, in_winner_display=False):
        """
        Draw the player's board with the given card_id

        Args:
            screen: The pygame screen to draw on
            card_id: The cartella number to draw
            board_rect: The rectangle to draw the board in
            called_numbers: List of called numbers
            show_claim_status: Whether to show the claim status text
            in_winner_display: Whether this board is being drawn in the winner display
        """
        # Store winning patterns cells for later use
        self.winning_patterns_cells = {}

        # If no board_rect is provided, return early
        if not board_rect:
            return

        # Get board position and size from the rect
        board_x, board_y = board_rect.x, board_rect.y
        board_width, board_height = board_rect.width, board_rect.height

        # ALWAYS use the full width for the board
        # Calculate cell dimensions to fill the entire width with improved spacing
        cell_width = board_width // 5  # Divide width by 5 columns

        # Use a smaller margin when space is limited
        if board_height < 300:
            cell_margin = 1  # Minimal margin for small boards
        else:
            cell_margin = max(2, min(cell_width, board_height // 5) // 25)  # Proportional margin

        # Calculate the total height needed for all rows including margins
        # We need 5 rows of numbers + 1 header row = 6 rows total
        # And we need 7 margins (6 between rows + 1 extra for symmetry)
        total_height_needed = board_height

        # Calculate the maximum cell height that will allow all rows to be visible
        # Reserve space for margins first
        available_height_for_cells = total_height_needed - (cell_margin * 7)
        max_cell_height = available_height_for_cells // 6  # Divide by total rows (5 + header)

        # Adjust cell width to account for margins while maintaining full width usage
        adjusted_cell_width = (board_width - (cell_margin * 6)) // 5  # 6 margins (5 between cells + 1 extra for symmetry)

        # Use the smaller of width or height to ensure cells fit
        cell_size = (adjusted_cell_width, min(adjusted_cell_width, max_cell_height))

        # Position the board to use the full width of the provided rectangle with margins
        # Add half a margin to the starting position for symmetry
        board_x = board_rect.x + cell_margin // 2

        # Calculate the total height needed for the board (5 number rows + 1 header row)
        total_board_rows_height = (cell_size[1] * 6) + (cell_margin * 5)

        # Ensure we leave enough space at the bottom for buttons
        # Position the board closer to the top to ensure bottom rows are visible
        vertical_offset = min(20, (board_rect.height - total_board_rows_height) // 3)
        board_y = board_rect.y + vertical_offset

        # Calculate the total board size including margins
        total_board_width = (cell_size[0] + cell_margin) * 5 + cell_margin
        total_board_height = (cell_size[1] + cell_margin) * 6 + cell_margin  # Account for BINGO header

        # Draw board background with rounded corners - solid black for clean look
        board_bg_rect = pygame.Rect(board_x - cell_margin, board_y - cell_margin,
                                   total_board_width, total_board_height)
        pygame.draw.rect(screen, (0, 0, 0), board_bg_rect, border_radius=8)

        # Draw column headers (B-I-N-G-O) - bright red background with white text
        header_font_size = int(min(cell_size[0], cell_size[1]) * 0.7)  # Larger font
        header_font = pygame.font.SysFont("Arial", header_font_size, bold=True)
        header_letters = ["B", "I", "N", "G", "O"]
        header_color = (180, 0, 0)  # Dark red background for headers (matching image)

        # Draw header row - this is the first row of the board
        for col, letter in enumerate(header_letters):
            # Draw header cell background with margins
            header_rect = pygame.Rect(
                board_x + col * (cell_size[0] + cell_margin),
                board_y,
                cell_size[0],
                cell_size[1]
            )
            # Draw header with rounded corners for a modern look
            pygame.draw.rect(screen, header_color, header_rect, border_radius=4)

            # Draw border lines with slightly thicker width for better visibility
            pygame.draw.rect(screen, (255, 255, 255), header_rect, 2, border_radius=4)

            # Draw letter with enhanced 3D effect
            # First draw shadow
            shadow_offset = 2
            shadow_color = (100, 0, 0)  # Darker red for shadow
            shadow_surf = header_font.render(letter, True, shadow_color)
            shadow_rect = shadow_surf.get_rect(center=(header_rect.centerx + shadow_offset, header_rect.centery + shadow_offset))
            screen.blit(shadow_surf, shadow_rect)

            # Then draw main letter
            letter_surf = header_font.render(letter, True, (255, 255, 255))
            letter_rect = letter_surf.get_rect(center=header_rect.center)
            screen.blit(letter_surf, letter_rect)

        # Adjust board_y to account for the header row we just drew
        # This ensures the number cells are drawn below the header row
        board_y += cell_size[1]

        # Try to get the board data - starting with the pre-generated boards
        board_data = None

        # First attempt: Try to get board from the bingo_boards.json for this cartella
        try:
            # Use the cartella number from the argument
            cartella_id = card_id
            if hasattr(self.game, 'bingo_boards_db') and cartella_id is not None and 1 <= cartella_id <= 1200:
                with open(self.game.bingo_boards_db, 'r') as file:
                    boards = json.load(file)

                    # Get board for current cartella number
                    cartella_key = str(cartella_id)
                    if cartella_key in boards:
                        board_data = boards[cartella_key]
                        # Found the board, we can use it
        except (FileNotFoundError, json.JSONDecodeError, Exception):
            # If loading fails, continue to fallback methods
            pass

        # Second attempt: Get from the current game's board if cartella is active
        if board_data is None and hasattr(self.game, 'cartella_number') and card_id == self.game.cartella_number:
            board_data = self.game.bingo_board

        # Third attempt: Legacy methods for backward compatibility
        if board_data is None:
            card = None

            # If card_id is a valid integer, try to get the card from the game
            if card_id is not None and isinstance(card_id, int) and hasattr(self.game, 'players'):
                try:
                    for player in self.game.players:
                        if hasattr(player, 'cards'):
                            for c in player.cards:
                                if hasattr(c, 'id') and c.id == card_id:
                                    card = c
                                    break
                except:
                    pass

            # If we found a card, try to get the board data from it
            if card:
                if hasattr(card, 'board'):
                    board_data = card.board
                elif hasattr(card, 'data'):
                    board_data = card.data
                elif hasattr(card, 'numbers'):
                    board_data = card.numbers
                elif hasattr(card, 'get_numbers'):
                    try:
                        board_data = card.get_numbers()
                    except:
                        pass

        # If we couldn't get the board data, create a default one that matches the BINGO layout
        if not board_data:
            # Create a default 5x5 board with correct number ranges for each column
            board_data = []
            for col in range(5):
                column = []
                start = col * 15 + 1  # B: 1-15, I: 16-30, N: 31-45, G: 46-60, O: 61-75
                end = start + 14

                # Generate 5 numbers for this column
                nums = []
                for i in range(5):
                    if col == 2 and i == 2:  # Free space in the middle
                        nums.append(0)
                    else:
                        # Distribute 5 numbers somewhat evenly in the column's range
                        num = start + i * 3
                        nums.append(num)

                board_data.append(nums)

        # Calculate the maximum safe font size to ensure all rows are visible
        # First, check if we have enough vertical space for all rows
        total_board_height_needed = (cell_size[1] * 5) + (cell_margin * 4)  # 5 rows with margins
        available_height = board_rect.height - cell_size[1]  # Subtract header height

        # If we don't have enough space, reduce the cell height
        if total_board_height_needed > available_height:
            # Recalculate cell size to fit all rows
            cell_size = (cell_size[0], int(available_height / 5.2))  # 5 rows with a little extra space

        # Now calculate font size based on adjusted cell size
        number_font_size = int(min(cell_size[0], cell_size[1]) * 0.8)  # Slightly reduced for safety
        number_font = pygame.font.SysFont("Arial", number_font_size, bold=True)

        # We'll draw cell borders individually with each cell

        # Track cells to highlight based on pattern checking
        winner_pattern = None
        highlighted_cells = []

        # Dictionary to store all possible winning patterns and their cells
        all_winning_patterns = {}

        # Track multiple winning patterns
        winning_patterns_cells = {}

        # Check if we're in the winner display context
        # Use the parameter if provided, otherwise check the context flags
        if not in_winner_display:
            if hasattr(self, '_in_winner_display_context') and self._in_winner_display_context:
                in_winner_display = True
            elif hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'show_winner_display'):
                in_winner_display = self.game.game_state.show_winner_display

        # If we're in the winner display, get all possible winning patterns
        if in_winner_display:
            all_winning_patterns = pattern_utils.get_all_winning_pattern_cells()

            # Check for multiple winning patterns on this board
            if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'claimed_patterns'):
                cartella_number = None
                if hasattr(self.game.game_state, 'player_claim_cartella'):
                    cartella_number = self.game.game_state.player_claim_cartella

                # If we have a valid cartella number and it has claimed patterns
                if cartella_number and cartella_number in self.game.game_state.claimed_patterns:
                    # For each claimed pattern, get its cells
                    for pattern_numbers in self.game.game_state.claimed_patterns[cartella_number]:
                        # Find which pattern this corresponds to
                        for pattern_name, pattern_cells in all_winning_patterns.items():
                            # Check if this pattern matches the claimed pattern
                            pattern_matches = True
                            for row, col in pattern_cells:
                                try:
                                    number = board_data[col][row]
                                    if number not in pattern_numbers and number != 0:  # Skip FREE space
                                        pattern_matches = False
                                        break
                                except:
                                    pattern_matches = False
                                    break

                            if pattern_matches:
                                # Add this pattern's cells to our winning patterns
                                winning_patterns_cells[pattern_name] = pattern_cells
                                # Add these cells to the highlighted cells list
                                for cell in pattern_cells:
                                    if cell not in highlighted_cells:
                                        highlighted_cells.append(cell)

        # Get all possible winning patterns
        all_winning_patterns_found = []

        # If we're in the winner display, check for ALL possible winning patterns
        if in_winner_display:
            # First check if we have winning patterns from the game state
            # This ensures we use the patterns that were actually detected during validation
            if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'winning_patterns') and self.game.game_state.winning_patterns:
                # For each pattern in the game state, get its cells and add them to our dictionaries
                for pattern_name in self.game.game_state.winning_patterns:
                    # Get the cells for this pattern
                    pattern_cells = pattern_utils.get_pattern_cells_by_name(pattern_name)
                    if pattern_cells:
                        # Add this pattern to our winning patterns dictionary
                        winning_patterns_cells[pattern_name] = pattern_cells
                        all_winning_patterns_found.append(pattern_name)

                        # Add these cells to the highlighted cells list
                        for cell in pattern_cells:
                            if cell not in highlighted_cells:
                                highlighted_cells.append(cell)

            # If we didn't get any patterns from the game state, fall back to detecting them ourselves
            if not all_winning_patterns_found:
                # Check for all possible winning patterns
                # 1. Check rows
                for row in range(5):
                    row_complete = True
                    for col in range(5):
                        try:
                            # Always access board in column-major format (board_data[col][row])
                            number = board_data[col][row]
                            if number != 0 and (not called_numbers or number not in called_numbers):
                                row_complete = False
                                break
                        except:
                            row_complete = False
                            break

                    if row_complete:
                        pattern_name = f"Row {row+1}"
                        all_winning_patterns_found.append(pattern_name)
                        # Add cells to highlighted cells
                        for col in range(5):
                            if (row, col) not in highlighted_cells:
                                highlighted_cells.append((row, col))

                        # Add this pattern to our winning patterns dictionary
                        pattern_cells = [(row, col) for col in range(5)]
                        winning_patterns_cells[pattern_name] = pattern_cells

                # 2. Check columns
                for col in range(5):
                    col_complete = True
                    for row in range(5):
                        try:
                            # Always access board in column-major format (board_data[col][row])
                            number = board_data[col][row]
                            if number != 0 and (not called_numbers or number not in called_numbers):
                                col_complete = False
                                break
                        except:
                            col_complete = False
                            break

                    if col_complete:
                        pattern_name = f"Column {col+1}"
                        all_winning_patterns_found.append(pattern_name)
                        # Add cells to highlighted cells
                        for row in range(5):
                            if (row, col) not in highlighted_cells:
                                highlighted_cells.append((row, col))

                        # Add this pattern to our winning patterns dictionary
                        pattern_cells = [(row, col) for row in range(5)]
                        winning_patterns_cells[pattern_name] = pattern_cells

                # 3. Check diagonal (top-left to bottom-right)
                diagonal1_complete = True
                for i in range(5):
                    try:
                        number = board_data[i][i]
                        if number != 0 and (not called_numbers or number not in called_numbers):
                            diagonal1_complete = False
                            break
                    except:
                        diagonal1_complete = False
                        break

                if diagonal1_complete:
                    pattern_name = "Diagonal (top-left to bottom-right)"
                    all_winning_patterns_found.append(pattern_name)
                    # Add cells to highlighted cells
                    for i in range(5):
                        if (i, i) not in highlighted_cells:
                            highlighted_cells.append((i, i))

                    # Add this pattern to our winning patterns dictionary
                    pattern_cells = [(i, i) for i in range(5)]
                    winning_patterns_cells[pattern_name] = pattern_cells

                # 4. Check diagonal (top-right to bottom-left)
                diagonal2_complete = True
                for i in range(5):
                    try:
                        number = board_data[i][4-i]
                        if number != 0 and (not called_numbers or number not in called_numbers):
                            diagonal2_complete = False
                            break
                    except:
                        diagonal2_complete = False
                        break

                if diagonal2_complete:
                    pattern_name = "Diagonal (top-right to bottom-left)"
                    all_winning_patterns_found.append(pattern_name)
                    # Add cells to highlighted cells
                    for i in range(5):
                        if (i, 4-i) not in highlighted_cells:
                            highlighted_cells.append((i, 4-i))

                    # Add this pattern to our winning patterns dictionary
                    pattern_cells = [(i, 4-i) for i in range(5)]
                    winning_patterns_cells[pattern_name] = pattern_cells

        # If not in winner display or no patterns found, fall back to the game state's winner pattern
        if (not in_winner_display or not all_winning_patterns_found) and hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'winner_pattern'):
            winner_pattern = self.game.game_state.winner_pattern

            # Determine which cells should be highlighted based on the pattern
            if winner_pattern == "Any Row":
                # Find a complete row
                for row in range(5):
                    row_complete = True
                    for col in range(5):
                        try:
                            # Always access board in column-major format (board_data[col][row])
                            number = board_data[col][row]

                            if number != 0 and (not called_numbers or number not in called_numbers):
                                row_complete = False
                                break
                        except:
                            row_complete = False
                            break

                    if row_complete:
                        for col in range(5):
                            highlighted_cells.append((row, col))
                        # Store the actual winning row pattern
                        winner_pattern = f"Row {row+1}"
                        break

            elif winner_pattern == "Any Column":
                # Find a complete column
                for col in range(5):
                    col_complete = True
                    for row in range(5):
                        try:
                            # Always access board in column-major format (board_data[col][row])
                            number = board_data[col][row]

                            if number != 0 and (not called_numbers or number not in called_numbers):
                                col_complete = False
                                break
                        except:
                            col_complete = False
                            break

                    if col_complete:
                        for row in range(5):
                            highlighted_cells.append((row, col))
                        # Store the actual winning column pattern
                        winner_pattern = f"Column {col+1}"
                        break

            elif winner_pattern == "Diagonal (top-left to bottom-right)" or winner_pattern == "Diagonal":
                # Highlight the main diagonal (top-left to bottom-right)
                for i in range(5):
                    highlighted_cells.append((i, i))

            elif winner_pattern == "Diagonal (top-right to bottom-left)":
                # Highlight the anti-diagonal (top-right to bottom-left)
                for i in range(5):
                    highlighted_cells.append((i, 4-i))

            # If we're in the winner display, also get the specific cells for the winning pattern
            if in_winner_display and winner_pattern:
                # Get the cells for the specific winning pattern
                pattern_cells = pattern_utils.get_pattern_cells_by_name(winner_pattern)
                if pattern_cells:
                    # Add this pattern to our winning patterns dictionary
                    winning_patterns_cells[winner_pattern] = pattern_cells

                    # Make sure all cells from this pattern are in highlighted_cells
                    for cell in pattern_cells:
                        if cell not in highlighted_cells:
                            highlighted_cells.append(cell)

        # Import time for pulsating effects
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1

        # Adjust board_y to account for the header row we just drew
        # This ensures the number cells are drawn below the header row
        # Use minimal margin between header and number cells to save vertical space
        board_y += cell_size[1] + (cell_margin // 2)

        # Check if this is an unregistered board
        is_unregistered_board = False
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'claim_type'):
            is_unregistered_board = self.game.game_state.claim_type == "not_registered"

        # Draw cells with numbers (or Amharic text for unregistered boards)
        for row in range(5):
            for col in range(5):
                # Cell position with margins
                cell_x = board_x + col * (cell_size[0] + cell_margin)
                cell_y = board_y + row * (cell_size[1] + cell_margin)
                cell_rect = pygame.Rect(cell_x, cell_y, cell_size[0], cell_size[1])

                # Get the number for this cell - always use column-major format
                try:
                    # The correct way to access board data: board_data[column][row]
                    number = board_data[col][row]
                except (IndexError, TypeError):
                    # Use a default number if we can't get the data
                    # Ensure it's in the proper range for this column
                    start = col * 15 + 1
                    number = start + row * 3
                    if col == 2 and row == 2:  # Free space in the middle
                        number = 0

                # Check if it's the free space (middle)
                is_free_space = (row == 2 and col == 2) or number == 0

                # Check if the number has been called
                is_called = called_numbers and number in called_numbers

                # Check if this is the most recently called number for blinking effect
                is_recent = False

                # CRITICAL FIX: ONLY the current number should blink, not previously skipped numbers
                # This ensures only the current number is blinking in claim windows
                if hasattr(self.game, 'current_number'):
                    is_recent = number == self.game.current_number

                # Check if this number is in any skipped pattern
                is_skipped_number = False
                if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'skipped_patterns'):
                    # Get the cartella number we're displaying
                    cartella_number = card_id
                    if cartella_number in self.game.game_state.skipped_patterns:
                        # CRITICAL FIX: Don't mark the current number as a skipped number
                        # Only previously skipped numbers should be highlighted with red squares
                        if hasattr(self.game, 'current_number') and number == self.game.current_number:
                            # Current number should never be marked as skipped
                            is_skipped_number = False
                        else:
                            # Check each skipped pattern for this cartella
                            for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                if number in skipped_pattern:
                                    is_skipped_number = True
                                    break

                # Check if this is the winning number (the number that completed the winning pattern)
                is_winning_number = False
                if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'winning_number'):
                    is_winning_number = number == self.game.game_state.winning_number

                # Check if this is the last winning chance number
                is_last_winning_chance = False
                if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'last_winning_chance_number'):
                    is_last_winning_chance = number == self.game.game_state.last_winning_chance_number

                # Check if this cell is part of the winning pattern
                is_winning_cell = (row, col) in highlighted_cells

                # Check if this cell is part of any winning pattern
                # This will be used to highlight all possible winning patterns in the winner display
                cell_patterns = []
                if in_winner_display:
                    # Check each pattern to see if this cell is part of it
                    for pattern_name, pattern_cells in all_winning_patterns.items():
                        if (row, col) in pattern_cells:
                            cell_patterns.append(pattern_name)

                    # Also check if this cell is part of any of our identified winning patterns
                    for pattern_name, pattern_cells in winning_patterns_cells.items():
                        if (row, col) in pattern_cells:
                            # Make sure this cell is marked as a winning cell
                            is_winning_cell = True

                # Black background for all cells (matching image)
                cell_bg_color = (0, 0, 0)  # Black background

                # Draw cell background with appropriate color
                if is_free_space:
                    cell_bg_color = (255, 0, 0)  # Bright red for FREE space
                elif is_recent:
                    # Blinking effect for recent number (current called number)
                    pulse = (math.sin(time.time() * 8) + 1) / 2  # Faster pulse
                    r = int(255 * pulse)  # Pulsing red
                    g = int(100 * pulse)  # Less green for more reddish color
                    cell_bg_color = (r, g, 0)  # Red-orange pulsing color

                    # Add a glowing effect around the current number
                    glow_size = 4
                    glow_alpha = int(150 + 105 * pulse)  # Pulsing alpha (150-255)
                    glow_rect = pygame.Rect(
                        cell_rect.x - glow_size,
                        cell_rect.y - glow_size,
                        cell_rect.width + glow_size * 2,
                        cell_rect.height + glow_size * 2
                    )
                    glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)
                    glow_color = (255, 50, 50, glow_alpha)  # Red glow with pulsating alpha
                    pygame.draw.rect(glow_surface, glow_color, (0, 0, glow_rect.width, glow_rect.height), border_radius=6)
                    screen.blit(glow_surface, glow_rect.topleft)
                elif is_skipped_number and is_called and not is_recent:
                    # Highlight skipped numbers with a red square
                    # CRITICAL FIX: Only apply red square to skipped numbers that are NOT the current number
                    cell_bg_color = (0, 100, 0)  # Keep the dark green background for called numbers

                    # Draw a red square around the skipped number
                    square_size = min(cell_rect.width, cell_rect.height)
                    square_rect = pygame.Rect(
                        cell_rect.x,
                        cell_rect.y,
                        square_size,
                        square_size
                    )

                    # Make the red square visible with a solid color (no pulsing)
                    square_color = (220, 50, 50)  # Bright red with no pulsing effect
                    pygame.draw.rect(screen, square_color, square_rect, 3, border_radius=4)  # 3px border with rounded corners
                elif is_winning_number:
                    # Winning number - use a bright green background with animation
                    pulse = (math.sin(time.time() * 6) + 1) / 2  # Medium pulse between 0 and 1
                    r = int(50 * pulse)  # Small red component
                    g = int(150 + 105 * pulse)  # Pulsing green (150-255)
                    cell_bg_color = (r, g, 0)  # Green pulsing color

                    # Add a glowing effect around the winning number
                    glow_size = 3
                    glow_alpha = int(120 + 135 * pulse)  # Pulsing alpha (120-255)
                    glow_rect = pygame.Rect(
                        cell_rect.x - glow_size,
                        cell_rect.y - glow_size,
                        cell_rect.width + glow_size * 2,
                        cell_rect.height + glow_size * 2
                    )
                    glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)
                    glow_color = (100, 255, 100, glow_alpha)  # Green glow with pulsating alpha
                    pygame.draw.rect(glow_surface, glow_color, (0, 0, glow_rect.width, glow_rect.height), border_radius=6)
                    screen.blit(glow_surface, glow_rect.topleft)
                elif is_last_winning_chance:
                    # Last winning chance number - use a bright yellow/gold background
                    pulse = (math.sin(time.time() * 4) + 1) / 2  # Slow pulse between 0 and 1
                    r = int(200 + 55 * pulse)  # Pulsing red (200-255)
                    g = int(180 + 75 * pulse)  # Pulsing green (180-255)
                    cell_bg_color = (r, g, 0)  # Gold pulsing color

                    # Add a subtle glowing effect
                    glow_size = 2
                    glow_alpha = int(100 + 155 * pulse)  # Pulsing alpha (100-255)
                    glow_rect = pygame.Rect(
                        cell_rect.x - glow_size,
                        cell_rect.y - glow_size,
                        cell_rect.width + glow_size * 2,
                        cell_rect.height + glow_size * 2
                    )
                    glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)
                    glow_color = (255, 255, 100, glow_alpha)  # Gold glow with pulsating alpha
                    pygame.draw.rect(glow_surface, glow_color, (0, 0, glow_rect.width, glow_rect.height), border_radius=6)
                    screen.blit(glow_surface, glow_rect.topleft)
                elif is_called:
                    cell_bg_color = (0, 100, 0)  # Dark green for called numbers
                elif is_winning_cell:
                    # ENHANCED VISUAL: Special background for winning pattern cells
                    if in_winner_display:
                        # Create a more vibrant pulsating background effect
                        pulse_intensity = (math.sin(time.time() * 4) + 1) / 2  # Value between 0 and 1

                        # Get the base color from pattern_utils
                        base_color = pattern_utils.get_pattern_color("")

                        # Create a brighter, more distinct background color
                        # Make it significantly brighter than the dark green of called numbers
                        bg_r = int(base_color[0] * (0.9 + 0.1 * pulse_intensity))  # Varies from 90% to 100% of base
                        bg_g = int(base_color[1] * (0.9 + 0.1 * pulse_intensity))  # Varies from 90% to 100% of base
                        bg_b = int(base_color[2] * (0.9 + 0.1 * pulse_intensity))  # Varies from 90% to 100% of base

                        cell_bg_color = (bg_r, bg_g, bg_b)  # Bright turquoise background for winning pattern
                    else:
                        cell_bg_color = (100, 100, 200)  # Blue-ish for winning pattern in other views
                # Remove special coloring for non-winning patterns
                # Keep original colors for all other cells

                # Draw cell background with rounded corners for modern look
                pygame.draw.rect(screen, cell_bg_color, cell_rect, border_radius=4)

                # Draw white border around each cell with slightly thicker width for better visibility
                border_color = (255, 255, 255)

                # Use brighter border for called numbers
                if is_called and not is_recent:
                    border_color = (200, 255, 200)  # Light green border for called numbers

                # ENHANCED VISUAL: Special glowing effect for winning pattern cells
                if is_winning_cell:
                    if in_winner_display:
                        # CRITICAL FIX: Create a more vibrant pulsating glow effect with red-yellow gradient for winning cells
                        # This matches the user's preference for red with yellow gradient to visually separate complete patterns
                        pulse_intensity = (math.sin(time.time() * 4) + 1) / 2  # Value between 0 and 1, faster pulse

                        # Create a gradient from red to yellow for the glow (as requested)
                        glow_r = int(255)  # Red component stays at max
                        glow_g = int(180 + (75 * pulse_intensity))  # Green varies from 180 to 255
                        glow_b = int(0 + (50 * pulse_intensity))  # Blue varies from 0 to 50

                        # Draw multiple concentric circles for glow effect
                        glow_radius = min(cell_size[0], cell_size[1]) // 2
                        glow_center = cell_rect.center

                        # Draw outer glow circles with decreasing alpha - make them larger and more intense
                        for i in range(4):  # Add an extra glow layer
                            # Increase radius for each outer circle - make them larger
                            outer_radius = glow_radius + (i * 4) + int(3 * pulse_intensity)  # Larger radius
                            # Decrease alpha for each outer circle - but keep it more visible
                            alpha = 180 - (i * 35)  # Higher alpha values for more visibility

                            # Create a surface for the glow
                            glow_surface = pygame.Surface((outer_radius * 2, outer_radius * 2), pygame.SRCALPHA)
                            pygame.draw.circle(glow_surface, (glow_r, glow_g, glow_b, alpha),
                                              (outer_radius, outer_radius), outer_radius)

                            # Blit the glow surface
                            glow_rect = glow_surface.get_rect(center=glow_center)
                            screen.blit(glow_surface, glow_rect)

                        # Use a bright border color that matches the glow
                        border_color = (255, 220, 50)  # Bright gold border

                        # Add an extra highlight border with thicker width for more emphasis
                        pygame.draw.rect(screen, (255, 255, 0), cell_rect, 3, border_radius=4)

                        # CRITICAL FIX: Add a second pulsating effect with a different frequency
                        # This creates a more dynamic and visually appealing animation
                        second_pulse = (math.sin(time.time() * 6 + 1) + 1) / 2  # Different phase and frequency

                        # Create a second glow with a slightly different color
                        second_glow_r = int(255)  # Red component stays at max
                        second_glow_g = int(150 + (105 * second_pulse))  # Green varies more widely
                        second_glow_b = int(0 + (30 * second_pulse))  # Less blue variation

                        # Create a smaller, more intense inner glow
                        inner_glow_radius = glow_radius - 2
                        inner_glow_surface = pygame.Surface((inner_glow_radius * 2, inner_glow_radius * 2), pygame.SRCALPHA)
                        inner_glow_alpha = int(200 - (50 * second_pulse))  # More opaque inner glow
                        pygame.draw.circle(inner_glow_surface, (second_glow_r, second_glow_g, second_glow_b, inner_glow_alpha),
                                          (inner_glow_radius, inner_glow_radius), inner_glow_radius)

                        # Blit the inner glow surface
                        inner_glow_rect = inner_glow_surface.get_rect(center=glow_center)
                        screen.blit(inner_glow_surface, inner_glow_rect)
                    else:
                        # For non-winner displays, use the original blue border
                        border_color = (200, 200, 255)  # Light blue border for winning pattern

                pygame.draw.rect(screen, border_color, cell_rect, 2, border_radius=4)

                # Add extra highlight for recent number
                if is_recent:
                    # Pulsating border with glow effect
                    pulse_width = int(2 + math.sin(time.time() * 8) * 2)  # 0-4 pixels
                    pulse_alpha = int(150 + math.sin(time.time() * 12) * 105)  # 45-255 alpha

                    # Create a slightly larger rect for the glow
                    glow_size = 4
                    glow_rect = pygame.Rect(
                        cell_rect.x - glow_size,
                        cell_rect.y - glow_size,
                        cell_rect.width + glow_size * 2,
                        cell_rect.height + glow_size * 2
                    )

                    # Create a surface with per-pixel alpha for the glow
                    glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)
                    glow_color = (255, 255, 0, pulse_alpha)  # Yellow with pulsating alpha
                    pygame.draw.rect(glow_surface, glow_color, (0, 0, glow_rect.width, glow_rect.height), border_radius=6)
                    screen.blit(glow_surface, glow_rect.topleft)

                    # Draw the main highlight border
                    pygame.draw.rect(screen, (255, 255, 0), cell_rect, pulse_width, border_radius=4)

                # Draw number, FREE text, or Amharic text for unregistered boards
                if is_unregistered_board and not is_free_space:
                    # For unregistered boards, don't display any numbers
                    # We'll display the Amharic text in a large overlay after drawing all cells
                    pass  # Empty block to satisfy Python syntax
                elif not is_free_space:
                    # Choose text color based on cell state
                    if is_recent:
                        text_color = (0, 0, 0)  # Black text on bright background for contrast
                    elif is_called:
                        text_color = (255, 255, 100)  # Yellow-ish text for called numbers
                    else:
                        text_color = (255, 255, 255)  # White text for uncalled numbers

                    # Make called numbers bold and larger
                    if is_called or is_recent:
                        # Use larger font size for called numbers
                        font_size = int(min(cell_size[0], cell_size[1]) * 0.95)  # Slightly larger
                        called_font = pygame.font.SysFont("Arial", font_size, bold=True)

                        # Add shadow for depth on called numbers
                        shadow_offset = 2
                        shadow_color = (0, 0, 0) if is_recent else (0, 50, 0)  # Black or dark green shadow
                        shadow_surf = called_font.render(str(number), True, shadow_color)
                        shadow_rect = shadow_surf.get_rect(center=(cell_rect.centerx + shadow_offset, cell_rect.centery + shadow_offset))
                        screen.blit(shadow_surf, shadow_rect)

                        # Main number
                        number_surf = called_font.render(str(number), True, text_color)
                    else:
                        # Regular numbers
                        number_surf = number_font.render(str(number), True, text_color)

                    # Center the number in the cell
                    number_rect = number_surf.get_rect(center=cell_rect.center)
                    screen.blit(number_surf, number_rect)
                else:
                    # Draw "FREE" text in white on red background with enhanced styling
                    free_font_size = int(min(cell_size[0], cell_size[1]) * 0.75)  # Increased font size
                    free_font = pygame.font.SysFont("Arial", free_font_size, bold=True)

                    # Add shadow for depth
                    shadow_offset = 2
                    shadow_color = (100, 0, 0)  # Dark red shadow
                    shadow_surf = free_font.render("FREE", True, shadow_color)
                    shadow_rect = shadow_surf.get_rect(center=(cell_rect.centerx + shadow_offset, cell_rect.centery + shadow_offset))
                    screen.blit(shadow_surf, shadow_rect)

                    # Main text with slight glow effect
                    free_surf = free_font.render("FREE", True, (255, 255, 255))
                    free_rect = free_surf.get_rect(center=cell_rect.center)
                    screen.blit(free_surf, free_rect)

        # If this is an unregistered board, display the Amharic text as an overlay with enhanced visuals
        if is_unregistered_board:
            # Create a semi-transparent overlay for the entire board area with a gradient effect
            board_area_width = (cell_size[0] + cell_margin) * 5
            board_area_height = (cell_size[1] + cell_margin) * 5
            overlay = pygame.Surface((board_area_width, board_area_height), pygame.SRCALPHA)

            # Create a darker gradient background for better contrast
            for y in range(board_area_height):
                # Calculate gradient alpha (more opaque in the center, more transparent at edges)
                gradient_factor = 1.0 - abs(y / board_area_height - 0.5) * 1.2
                alpha = int(200 * gradient_factor)  # Higher alpha for better contrast
                overlay.fill((0, 0, 0, alpha), (0, y, board_area_width, 1))

            # Calculate the position to blit the overlay
            overlay_x = board_x
            overlay_y = board_y
            screen.blit(overlay, (overlay_x, overlay_y))

            # Calculate the center of the board
            board_center_x = overlay_x + board_area_width // 2
            board_center_y = overlay_y + board_area_height // 2

            # Use a much larger font size for the Amharic text to fill more of the board
            # Increase from 15% to 25% of the board size
            amharic_font_size = int(min(board_area_width, board_area_height) * 0.25)

            # Get the Amharic font using AmharicSupport
            amharic_font = AmharicSupport.get_amharic_font(amharic_font_size)
            amharic_text = "ያልተመዘገበ ሰሌዳ!"

            # Create animation effect based on time
            current_time = time.time()
            pulse = (math.sin(current_time * 2) + 1) / 2  # Slower pulse for better readability

            # Add multiple shadow layers for a stronger 3D effect
            for i in range(3):
                shadow_offset = 3 + i
                # Vary the shadow color slightly for each layer
                shadow_alpha = int(180 - i * 40)
                shadow_color = (30, 30, 30, shadow_alpha)

                # Use AmharicSupport to render the text
                shadow_surf = AmharicSupport.render_text(amharic_text, amharic_font, shadow_color)
                shadow_rect = shadow_surf.get_rect(center=(board_center_x + shadow_offset, board_center_y + shadow_offset))
                screen.blit(shadow_surf, shadow_rect)

            # Create a vibrant, high-contrast color for the main text that pulses between two colors
            # Pulse between bright red and bright yellow for high visibility
            red = 255
            green = int(100 + 155 * pulse)  # Pulse between 100 and 255
            blue = int(50 * pulse)  # Pulse between 0 and 50
            text_color = (red, green, blue)

            # Main text with enhanced glow effect
            amharic_surf = AmharicSupport.render_text(amharic_text, amharic_font, text_color)
            amharic_rect = amharic_surf.get_rect(center=(board_center_x, board_center_y))

            # Add a glowing halo effect around the text
            glow_size = int(10 + 5 * pulse)  # Pulsating glow size
            glow_alpha = int(100 + 100 * pulse)  # Pulsating alpha
            glow_color = (255, 200, 50, glow_alpha)  # Golden glow

            # Create a larger surface for the glow
            glow_surf = pygame.Surface((amharic_rect.width + glow_size * 2, amharic_rect.height + glow_size * 2), pygame.SRCALPHA)

            # Draw a soft glow using multiple circles
            for i in range(glow_size, 0, -2):
                current_alpha = glow_alpha * (i / glow_size)
                current_color = (glow_color[0], glow_color[1], glow_color[2], int(current_alpha))
                pygame.draw.rect(glow_surf, current_color,
                                (glow_size - i, glow_size - i,
                                 amharic_rect.width + i * 2, amharic_rect.height + i * 2),
                                 border_radius=i*2)

            # Position and blit the glow
            glow_rect = glow_surf.get_rect(center=(board_center_x, board_center_y))
            screen.blit(glow_surf, glow_rect.topleft)

            # Finally, blit the main text on top
            screen.blit(amharic_surf, amharic_rect)

            # Add a subtle pulsing border around the entire board for extra emphasis
            border_width = int(3 + 2 * pulse)  # Pulsing border width
            border_color = (255, int(100 + 155 * pulse), 0)  # Pulsing color
            pygame.draw.rect(screen, border_color,
                            (overlay_x - border_width, overlay_y - border_width,
                             board_area_width + border_width * 2, board_area_height + border_width * 2),
                             border_width, border_radius=8)

        # If this is a claim display, we'll still check for winning patterns but won't display redundant text
        if show_claim_status:
            # Check if the board has any winning patterns with the current called numbers
            has_winning_pattern = False

            # Get the card for this player
            if card_id is not None:
                card = self.game.bingo_logic.get_card_for_player(card_id)
                if card and called_numbers:
                    # Check if the card has any winning patterns with the current called numbers
                    has_winning_pattern = card.check_winning_patterns(called_numbers)

            # Return a position just below the board for layout calculations
            return board_y + (cell_size[1] * 5) + 5
        return None

    def draw_winner_display(self, screen):
        """Draw the winner display overlay with cheering animation"""
        if not hasattr(self.game, 'game_state') or not self.game.game_state.show_winner_display:
            return

        # Set a flag to indicate we're in the winner display context
        # This will be used by the draw_player_board method to apply special visual effects
        self._in_winner_display_context = True

        # CRITICAL FIX: Check if this is a late claim by examining multiple indicators
        is_late_claim = False

        # Check if we're in the process of checking multiple claims
        checking_multiple_claims = False
        if hasattr(self.game, 'game_state'):
            if hasattr(self.game.game_state, 'checking_next_claim') and self.game.game_state.checking_next_claim:
                checking_multiple_claims = True
                print("EMERGENCY FIX: Detected we're checking multiple claims - skipping late claim checks")

        # Only perform these checks if we're not checking multiple claims
        if not checking_multiple_claims:
            # Check 1: Direct check for late_claim flag in game_state
            if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'late_claim'):
                if self.game.game_state.late_claim:
                    is_late_claim = True
                    print("EMERGENCY FIX: Detected late claim via late_claim flag")

            # Check 2: Check was_next_number_called flag in game_state
            if not is_late_claim and hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'was_next_number_called'):
                if self.game.game_state.was_next_number_called:
                    is_late_claim = True
                    print("EMERGENCY FIX: Detected late claim via was_next_number_called flag")

            # CRITICAL FIX: Remove the problematic pattern completion time check
            # This check was incorrectly marking valid claims as late claims
            # The previous code was checking if the current number was not the first number called,
            # which is almost always true and not a valid indicator of a late claim
            # We'll rely on the more accurate was_next_number_called and late_claim flags instead
            # Removed excessive debug logging that was spamming the console

        # CRITICAL FIX: Only override validation results if current number is NOT part of winning pattern
        # If the current number is part of the winning pattern, the validation logic is correct
        should_override_validation = False

        if is_late_claim and hasattr(self.game, 'game_state'):
            # Check if current number is part of any winning pattern
            is_current_number_in_pattern = False

            if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'player_claim_cartella'):
                current_number = self.game.current_number
                cartella_number = self.game.game_state.player_claim_cartella

                # Get the card for this player
                if hasattr(self.game, 'bingo_logic'):
                    card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    if card and hasattr(self.game.game_state, 'winner_pattern'):
                        # Get the winning pattern numbers
                        if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, self.game.game_state.winner_pattern)

                            if current_number in pattern_numbers:
                                is_current_number_in_pattern = True
                                print(f"UI OVERRIDE PREVENTION: Current number {current_number} is part of winning pattern")
                                print(f"Respecting validation logic decision - this is a valid winner")

            # Only override if current number is NOT part of the winning pattern
            if not is_current_number_in_pattern:
                should_override_validation = True
                print("UI OVERRIDE: Current number not in pattern - overriding to missed winner display")
            else:
                print("UI OVERRIDE PREVENTION: Current number in pattern - keeping validation result")

        if should_override_validation:
            # Force the missed winner display to show instead
            self.game.game_state.show_winner_display = False
            self.game.game_state.show_missed_winner_display = True
            self.game.game_state.claim_type = "missed_winner"
            self.game.game_state.validation_result = False
            self.game.game_state.invalid_claim_reason = "Claim made after next number was called"

            # Log the correction
            print("EMERGENCY FIX: Redirecting to missed winner display")

            # Call the missed winner display directly
            self.draw_missed_winner_display(screen)
            return

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Define consistent spacing
        min_spacing = int(15 * min(scale_x, scale_y))

        # Initialize particles for cheering animation if not already done
        if not hasattr(self, 'winner_particles'):
            self.winner_particles = []
            self.winner_animation_start_time = time.time()
            self.initialize_winner_particles(screen_width, screen_height)

        # Update and draw particles for cheering animation
        self.update_winner_particles(screen_width, screen_height)
        self.draw_winner_particles(screen)

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 150))  # Black with slightly less opacity (60%) for better particle visibility
        screen.blit(overlay, (0, 0))

        # Create winner box with improved dimensions - match invalid claim window style
        box_width = int(screen_width * 0.55)  # Match invalid claim window width
        box_height = int(screen_height * 0.8)  # Increased height from 0.7 to 0.8 to provide more space

        # Use stored position if we're dragging or have a saved position
        if self.popup_position and (self.dragging or not hasattr(self.game.game_state, 'just_opened_validation')):
            box_x, box_y = self.popup_position
        else:
            # Default centered position
            box_x = (screen_width - box_width) // 2
            box_y = (screen_height - box_height) // 2
            self.popup_position = (box_x, box_y)

            # Mark that we've just opened the validation window
            if hasattr(self.game, 'game_state'):
                self.game.game_state.just_opened_validation = True

        # Create a more visually appealing background with enhanced gradient
        # First, add a subtle outer glow effect
        outer_glow_size = 15
        outer_glow_color = (80, 80, 150, 30)  # Subtle blue glow with alpha
        outer_glow_surface = pygame.Surface((box_width + outer_glow_size*2, box_height + outer_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, box_width + outer_glow_size*2, box_height + outer_glow_size*2), border_radius=25)
        screen.blit(outer_glow_surface, (box_x - outer_glow_size, box_y - outer_glow_size))

        # Add a second inner glow for depth
        inner_glow_size = 8
        inner_glow_color = (100, 100, 180, 50)  # Brighter blue glow with alpha
        inner_glow_surface = pygame.Surface((box_width + inner_glow_size*2, box_height + inner_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, box_width + inner_glow_size*2, box_height + inner_glow_size*2), border_radius=22)
        screen.blit(inner_glow_surface, (box_x - inner_glow_size, box_y - inner_glow_size))

        # Draw main box with enhanced gradient background
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (20, 20, 35),  # Richer blue-tinted dark
            (40, 40, 60),  # Richer lighter blue-tinted dark
            20  # Border radius
        )

        # Add header bar with enhanced gradient and visual effects
        header_height = int(55 * min(scale_x, scale_y))  # Slightly taller header for better proportions
        banner_rect = Rect(box_x, box_y, box_width, header_height)

        # For valid claims, use vibrant green gradient
        header_color1 = (25, 90, 45)  # Rich dark green
        header_color2 = (45, 140, 70)  # Vibrant lighter green

        # Add subtle glow effect to header
        header_glow_size = 5
        header_glow_color = (header_color2[0], header_color2[1], header_color2[2], 40)  # Matching glow with alpha
        header_glow_surface = pygame.Surface((banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(header_glow_surface, header_glow_color, (0, 0, banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), border_radius=15)
        screen.blit(header_glow_surface, (banner_rect.x - header_glow_size, banner_rect.y - header_glow_size))

        # Draw the header with enhanced gradient
        self.draw_gradient_rect(
            screen,
            banner_rect,
            header_color1,
            header_color2,
            border_radius=10  # Rounded corners at top
        )

        # Store the header area for dragging
        self.modal_hit_areas["claim_header_drag"] = banner_rect

        # Get board number if available
        board_num = ""
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'player_claim_cartella'):
            board_num = f"Board #{self.game.game_state.player_claim_cartella} - "

        # Check if this is actually a valid claim or a late claim that was incorrectly marked as valid
        # This is a critical check to ensure we never show "VALID WINNER" for a late claim
        is_late_claim = False
        is_skipped_pattern = False
        is_current_number_mismatch = False

        # Check if we're in the process of checking multiple claims
        checking_multiple_claims = False
        if hasattr(self.game, 'game_state'):
            if hasattr(self.game.game_state, 'checking_next_claim') and self.game.game_state.checking_next_claim:
                checking_multiple_claims = True
                print("CRITICAL UI CORRECTION: Detected we're checking multiple claims - skipping late claim checks for title")

        # Only perform these checks if we're not checking multiple claims
        if not checking_multiple_claims:
            # CRITICAL FIX: Only use the late_claim flag, which is more reliable
            # The was_next_number_called flag can be misleading in some cases
            if hasattr(self.game, 'game_state'):
                if hasattr(self.game.game_state, 'late_claim') and self.game.game_state.late_claim:
                    is_late_claim = True
                    print("CRITICAL UI CORRECTION: Detected late claim via late_claim flag for title")
                # Removed the problematic was_next_number_called check that was causing false positives

                # CRITICAL FIX: First check if the current number is part of any winning pattern
                is_current_number_in_any_pattern = False

                if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'player_claim_cartella'):
                    current_number = self.game.current_number
                    cartella_number = self.game.game_state.player_claim_cartella

                    # Get the card for this player
                    card = None
                    if hasattr(self.game, 'bingo_logic'):
                        card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    # Get all winning patterns for this card
                    if card:
                        all_patterns = []
                        if hasattr(card, 'winning_patterns'):
                            all_patterns = card.winning_patterns
                        elif hasattr(card, 'winning_pattern') and card.winning_pattern:
                            all_patterns = [card.winning_pattern]

                        # Check if the current number is part of any winning pattern
                        for pattern_name in all_patterns:
                            if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                                pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, pattern_name)

                                if current_number in pattern_numbers:
                                    is_current_number_in_any_pattern = True
                                    break

                # Only check for skipped patterns if the current number is not part of any winning pattern
                if not is_current_number_in_any_pattern:
                    # Check if this is a skipped pattern
                    if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'skipped_patterns'):
                        cartella_number = self.game.game_state.player_claim_cartella
                        if cartella_number in self.game.game_state.skipped_patterns and self.game.game_state.skipped_patterns[cartella_number]:
                            is_skipped_pattern = True
                            print("CRITICAL UI CORRECTION: Detected skipped pattern for title")

                    # NEW CHECK: Verify if the current claim number matches the current called number
                    if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'winning_number'):
                        current_number = self.game.current_number
                        winning_number = self.game.game_state.winning_number

                        if current_number != winning_number:
                            # Only mark as a mismatch if the winning number is NOT the current number
                            # AND the winning number is in the called numbers (meaning it was called earlier)
                            if hasattr(self.game, 'called_numbers') and winning_number in self.game.called_numbers:
                                is_current_number_mismatch = True
                                is_skipped_pattern = True
                                # Reduced logging - only log once per session
                                if not hasattr(self, '_logged_mismatch') or self._logged_mismatch != f"{current_number}_{winning_number}":
                                    print(f"Current number ({current_number}) does not match winning number ({winning_number})")
                                    print(f"Winning number {winning_number} was previously called, marking as skipped pattern")
                                    self._logged_mismatch = f"{current_number}_{winning_number}"
                            else:
                                # If the winning number is not in called numbers, this is an error condition
                                # Don't mark as skipped pattern
                                print(f"WARNING: Winning number {winning_number} is not in called numbers and doesn't match current number {current_number}")
                        else:
                            # Current number matches winning number - this is a valid claim
                            print(f"VALID CLAIM: Current number ({current_number}) matches winning number ({winning_number})")
                elif is_current_number_in_any_pattern:
                    # If the current number is part of any winning pattern, this is a valid claim
                    # regardless of whether the pattern was previously skipped
                    is_skipped_pattern = False
                    is_current_number_mismatch = False
                    print(f"CRITICAL FIX: Current number is part of a winning pattern - this is a valid claim")
                elif is_skipped_pattern:
                    # If the current number is not part of a winning pattern, check if this specific pattern was skipped
                    is_pattern_skipped = False

                    # Only consider it skipped if we can verify the exact pattern was skipped
                    if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'skipped_patterns'):
                        cartella_number = self.game.game_state.player_claim_cartella

                        # If we have a winner pattern, check if this specific pattern was skipped
                        if hasattr(self.game.game_state, 'winner_pattern') and hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            card = self.game.bingo_logic.get_card_for_player(cartella_number)
                            if card:
                                winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                    card, self.game.game_state.winner_pattern)

                                # Check if this specific pattern was skipped
                                if cartella_number in self.game.game_state.skipped_patterns:
                                    for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                        if sorted(skipped_pattern) == sorted(winning_pattern_numbers):
                                            is_pattern_skipped = True
                                            break

                    # If this specific pattern wasn't skipped, it's a valid claim
                    if not is_pattern_skipped:
                        is_skipped_pattern = False
                        print("CRITICAL UI CORRECTION: This is a valid claim for the current number, not a skipped pattern")

        # CORRECTED: Title for winner - only valid winners should reach this display
        # All winners here are valid (current number completes pattern)
        if is_late_claim:
            # CRITICAL FIX: Check if current number is part of winning pattern before changing title
            is_current_number_in_pattern = False

            if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'player_claim_cartella'):
                current_number = self.game.current_number
                cartella_number = self.game.game_state.player_claim_cartella

                # Get the card for this player
                if hasattr(self.game, 'bingo_logic'):
                    card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    if card and hasattr(self.game.game_state, 'winner_pattern'):
                        # Get the winning pattern numbers
                        if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, self.game.game_state.winner_pattern)

                            if current_number in pattern_numbers:
                                is_current_number_in_pattern = True
                                print(f"TITLE FIX: Current number {current_number} is part of winning pattern - keeping VALID WINNER title")

            # Only change title if current number is NOT part of the winning pattern
            if not is_current_number_in_pattern:
                # This should be a late claim - show "MISSED WINNER" instead
                title = f"{board_num}MISSED WINNER!"
                title_color = (255, 180, 80)  # Amber/orange color for missed winners
                print("CRITICAL UI CORRECTION: Changed 'VALID WINNER' to 'MISSED WINNER' for late claim")

                # Force the correct display to be shown
                if hasattr(self.game, 'game_state'):
                    self.game.game_state.show_winner_display = False
                    self.game.game_state.show_missed_winner_display = True
                    self.game.game_state.claim_type = "missed_winner"
                    self.game.game_state.validation_result = False
                    self.game.game_state.invalid_claim_reason = "Claim made after next number was called"
            else:
                # Current number is part of pattern - keep as valid winner
                title = f"{board_num}VALID WINNER!"
                title_color = (255, 215, 0)  # Gold color for valid claims
                print("TITLE FIX: Keeping VALID WINNER title - current number is part of winning pattern")
        elif is_skipped_pattern or is_current_number_mismatch:
            # This is a skipped pattern - show "PREVIOUSLY SKIPPED" instead
            title = f"{board_num}PREVIOUSLY SKIPPED!"
            title_color = (255, 180, 80)  # Amber/orange color for skipped winners
            print("CRITICAL UI CORRECTION: Changed 'VALID WINNER' to 'PREVIOUSLY SKIPPED' for skipped pattern")

            # If this was detected via number mismatch, make sure it's properly marked as a skipped pattern
            if is_current_number_mismatch and hasattr(self.game, 'game_state'):
                # Ensure we have a skipped_patterns dictionary
                if not hasattr(self.game.game_state, 'skipped_patterns'):
                    self.game.game_state.skipped_patterns = {}

                # Get the cartella number
                if hasattr(self.game.game_state, 'player_claim_cartella'):
                    cartella_number = self.game.game_state.player_claim_cartella

                    # Initialize the list for this cartella if needed
                    if cartella_number not in self.game.game_state.skipped_patterns:
                        self.game.game_state.skipped_patterns[cartella_number] = []

                    # Get the winning pattern numbers if possible
                    if hasattr(self.game.game_state, 'get_winning_pattern_numbers') and hasattr(self.game.game_state, 'winner_pattern'):
                        card = self.game.bingo_logic.get_card_for_player(cartella_number)
                        if card:
                            winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                card, self.game.game_state.winner_pattern)

                            # Add to skipped patterns if not already there
                            if winning_pattern_numbers not in self.game.game_state.skipped_patterns[cartella_number]:
                                self.game.game_state.skipped_patterns[cartella_number].append(winning_pattern_numbers)
                                print(f"Added pattern to skipped patterns for cartella {cartella_number}")
        else:
            # This is a valid claim (default case)
            title = f"{board_num}VALID WINNER!"
            title_color = (255, 215, 0)  # Gold color for valid claims

        # Display the title in the header with enhanced styling
        title_font = pygame.font.SysFont("Arial", int(28 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color
        shadow_text = title_font.render(title, True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(banner_rect.centerx + shadow_offset, banner_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main title text
        title_text = title_font.render(title, True, title_color)
        title_rect = title_text.get_rect(center=(banner_rect.centerx, banner_rect.centery))
        screen.blit(title_text, title_rect)

        # Add subtle pulsing glow for valid claims
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Slow pulse between 0 and 1
        glow_alpha = int(50 + 30 * pulse)  # Pulsing alpha between 50 and 80
        glow_color = (180, 255, 180, glow_alpha)  # Green glow with pulsing alpha

        glow_surface = pygame.Surface((title_rect.width + 20, title_rect.height + 10), pygame.SRCALPHA)
        pygame.draw.rect(glow_surface, glow_color, (0, 0, title_rect.width + 20, title_rect.height + 10), border_radius=10)
        screen.blit(glow_surface, (title_rect.x - 10, title_rect.y - 5))

        # Add gold stars on either side of the title for emphasis
        star_size = int(30 * min(scale_x, scale_y))
        star_points = []
        for i in range(5):
            angle = math.pi/2 + i * 2*math.pi/5
            inner_angle = angle + math.pi/5

            # Outer point
            outer_x = int(math.cos(angle) * star_size)
            outer_y = int(math.sin(angle) * star_size)

            # Inner point
            inner_x = int(math.cos(inner_angle) * star_size * 0.4)
            inner_y = int(math.sin(inner_angle) * star_size * 0.4)

            star_points.append((outer_x, outer_y))
            star_points.append((inner_x, inner_y))

        # Left star
        left_star_x = title_rect.left - star_size - 10
        left_star_y = title_rect.centery
        left_star_points = [(x + left_star_x, y + left_star_y) for x, y in star_points]
        pygame.draw.polygon(screen, (255, 215, 0), left_star_points)

        # Right star
        right_star_x = title_rect.right + star_size + 10
        right_star_y = title_rect.centery
        right_star_points = [(x + right_star_x, y + right_star_y) for x, y in star_points]
        pygame.draw.polygon(screen, (255, 215, 0), right_star_points)

        # ------------------- Start sequential layout positioning -------------------

        # 1. Position pattern info in a modern info bar with enhanced styling
        current_y = box_y + header_height + min_spacing  # More spacing for cleaner look
        compact_header_height = int(45 * min(scale_x, scale_y))  # Slightly taller for better readability

        # Create a modern info bar with rounded corners
        reason_box = Rect(
            box_x + int(15 * min(scale_x, scale_y)),  # More inset from edges for modern look
            current_y,
            box_width - int(30 * min(scale_x, scale_y)),  # More inset from edges for modern look
            compact_header_height
        )

        # For valid claims, use vibrant green gradient
        reason_color1 = (30, 110, 50)  # Rich dark green
        reason_color2 = (50, 160, 70)  # Vibrant lighter green

        # Add subtle glow effect to info bar
        info_glow_size = 4
        info_glow_color = (reason_color2[0], reason_color2[1], reason_color2[2], 40)  # Matching glow with alpha
        info_glow_surface = pygame.Surface((reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(info_glow_surface, info_glow_color, (0, 0, reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), border_radius=12)
        screen.blit(info_glow_surface, (reason_box.x - info_glow_size, reason_box.y - info_glow_size))

        # Draw the info bar with enhanced gradient and rounded corners
        self.draw_gradient_rect(
            screen,
            reason_box,
            reason_color1,
            reason_color2,
            10  # More rounded corners for modern look
        )

        # Add text to the info bar with improved styling
        player_font = pygame.font.SysFont("Arial", int(22 * min(scale_x, scale_y)), bold=True)

        # Create a display text showing previously skipped claim numbers
        if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'winner_pattern'):
            cartella_number = self.game.game_state.player_claim_cartella
            card = self.game.bingo_logic.get_card_for_player(cartella_number)

            # Initialize display text
            display_text = ""

            # Check if this is a skipped pattern
            is_skipped_pattern = False
            skipped_numbers = []

            if hasattr(self.game.game_state, 'skipped_patterns'):
                if cartella_number in self.game.game_state.skipped_patterns:
                    # Get the winning pattern numbers
                    try:
                        if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                card, self.game.game_state.winner_pattern)
                            winning_pattern_numbers = sorted(winning_pattern_numbers)

                            # Check if this pattern matches any skipped pattern
                            for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                if sorted(skipped_pattern) == winning_pattern_numbers:
                                    is_skipped_pattern = True
                                    # Store the skipped numbers for display
                                    skipped_numbers = skipped_pattern
                                    break
                    except Exception:
                        # Default to not skipped if there's an error
                        is_skipped_pattern = False

            # Format the display text based on whether it's a skipped pattern
            if is_skipped_pattern and skipped_numbers:
                # Sort the skipped numbers for consistent display
                skipped_numbers = sorted(skipped_numbers)

                # Create the animated text for skipped numbers
                display_text = "Previously skipped numbers: "

                # Add the skipped numbers with dash separators and brackets
                for i, num in enumerate(skipped_numbers):
                    if i > 0:
                        display_text += " - "
                    display_text += f"[{num}]"
            else:
                # If not a skipped pattern, show the pattern name
                if hasattr(self.game.game_state, 'winning_patterns') and self.game.game_state.winning_patterns:
                    # Multiple patterns
                    display_text = "Patterns: "
                    for i, pattern in enumerate(self.game.game_state.winning_patterns[:2]):
                        if i > 0:
                            display_text += ", "
                        display_text += pattern

                    # If there are more patterns, indicate it
                    if len(self.game.game_state.winning_patterns) > 2:
                        display_text += f" +{len(self.game.game_state.winning_patterns) - 2} more"
                else:
                    # Single pattern
                    display_text = f"Pattern: {self.game.game_state.winner_pattern}"
        else:
            display_text = f"🏆 Winner!"

        # Set up context for drawing the board with winning patterns
        self._in_winner_display_context = True

        # Draw the board first to populate winning_patterns_cells
        # Use a valid rect with reasonable dimensions instead of a 1x1 rect
        # We need to ensure the rect has valid dimensions for any surfaces created during drawing
        temp_board_rect = Rect(0, 0, 300, 300)  # Larger temporary rect with valid dimensions, not used for actual drawing

        # Check if the cartella number is valid before trying to draw the board
        if hasattr(self.game.game_state, 'player_claim_cartella') and self.game.game_state.player_claim_cartella is not None:
            try:
                # Add error handling to prevent crashes
                self.draw_player_board(screen, self.game.game_state.player_claim_cartella, temp_board_rect, self.game.called_numbers, in_winner_display=True)
            except Exception as e:
                print(f"Error in draw_player_board during winning pattern detection: {e}")
                # Initialize winning_patterns_cells if it doesn't exist
                if not hasattr(self, 'winning_patterns_cells'):
                    self.winning_patterns_cells = {}

        # Clear the context flag
        self._in_winner_display_context = False

        # Render the info text with animation for skipped numbers
        if "Previously skipped numbers:" in display_text:
            # Split the text into prefix and numbers
            prefix = "Previously skipped numbers: "
            numbers_text = display_text[len(prefix):]

            # Render the prefix part
            prefix_text = player_font.render(prefix, True, (255, 255, 255))
            prefix_rect = prefix_text.get_rect(midleft=(reason_box.left + 20, reason_box.centery))

            # Add subtle text shadow for prefix
            shadow_prefix_text = player_font.render(prefix, True, (0, 0, 0, 128))
            shadow_prefix_rect = shadow_prefix_text.get_rect(midleft=(prefix_rect.left + 1, prefix_rect.top + 1))
            screen.blit(shadow_prefix_text, shadow_prefix_rect)
            screen.blit(prefix_text, prefix_rect)

            # Calculate animation parameters
            pulse = (math.sin(time.time() * 3) + 1) / 2  # Pulsing effect between 0 and 1

            # Split the numbers text by dashes
            number_parts = numbers_text.split(" - ")

            # Start position for rendering numbers
            current_x = prefix_rect.right + 5

            # Render each number with animation
            for i, number in enumerate(number_parts):
                # Calculate color based on pulse - use red with yellow gradient as requested
                r = 255  # Red always at max
                g = int(180 + 75 * pulse)  # Pulse between 180 and 255 for yellow component
                b = 0    # No blue component

                number_color = (r, g, b)

                # Render the number with brackets
                number_text = player_font.render(number, True, number_color)
                number_rect = number_text.get_rect(midleft=(current_x, reason_box.centery))

                # Add glow effect with red-yellow gradient
                glow_size = int(3 + 2 * pulse)  # Size varies with pulse
                glow_surface = pygame.Surface((number_rect.width + glow_size*2, number_rect.height + glow_size*2), pygame.SRCALPHA)
                glow_alpha = int(100 + 50 * pulse)  # Alpha varies with pulse
                glow_color = (255, 200, 0, glow_alpha)  # Red-yellow glow
                pygame.draw.rect(glow_surface, glow_color, (0, 0, number_rect.width + glow_size*2, number_rect.height + glow_size*2), border_radius=5)
                screen.blit(glow_surface, (number_rect.left - glow_size, number_rect.top - glow_size))

                # Add subtle text shadow
                shadow_number_text = player_font.render(number, True, (0, 0, 0, 128))
                shadow_number_rect = shadow_number_text.get_rect(midleft=(number_rect.left + 1, number_rect.top + 1))
                screen.blit(shadow_number_text, shadow_number_rect)

                # Render the number
                screen.blit(number_text, number_rect)

                # Update position for next number
                current_x = number_rect.right + 10

                # Add dash if not the last number
                if i < len(number_parts) - 1:
                    dash_text = player_font.render(" - ", True, (255, 255, 255))
                    dash_rect = dash_text.get_rect(midleft=(current_x, reason_box.centery))

                    # Add subtle text shadow for dash
                    shadow_dash_text = player_font.render(" - ", True, (0, 0, 0, 128))
                    shadow_dash_rect = shadow_dash_text.get_rect(midleft=(dash_rect.left + 1, dash_rect.top + 1))
                    screen.blit(shadow_dash_text, shadow_dash_rect)

                    screen.blit(dash_text, dash_rect)
                    current_x = dash_rect.right + 5
        else:
            # Regular rendering for non-skipped patterns
            info_text_color = (255, 255, 255)  # White text
            info_text = player_font.render(display_text, True, info_text_color)
            info_rect = info_text.get_rect(center=(reason_box.centerx, reason_box.centery))

            # Add subtle text shadow
            shadow_info_text = player_font.render(display_text, True, (0, 0, 0, 128))
            shadow_info_rect = shadow_info_text.get_rect(center=(info_rect.centerx + 1, info_rect.centery + 1))
            screen.blit(shadow_info_text, shadow_info_rect)
            screen.blit(info_text, info_rect)

        # Set reason_box_height for later calculations
        reason_box_height = compact_header_height

        # Calculate space needed for elements below the board - minimized for maximum board space
        # Button height used for layout calculations
        button_height = int(40 * min(scale_x, scale_y))  # Button height

        # Calculate footer height with absolute minimal spacing
        footer_height = button_height + 2  # Absolute minimum padding for footer

        # Calculate available space between header and footer
        glow_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for glow effects
        footer_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for footer separation

        # Prioritize board space by minimizing other spacing
        available_height = box_height - header_height - reason_box_height - footer_height * 2 - min_spacing * 3 - glow_spacing - footer_spacing

        # Use a width that maximizes the board size while maintaining proportions
        max_board_width = int(box_width * 0.95)  # 95% of box width to maximize board size

        # Calculate board size based on available space
        board_size = min(available_height, max_board_width - 20)

        # Calculate board size based on available space - match invalid claim window style
        # Reserve space for footer and buttons first
        footer_and_buttons_height = int(button_height * 1.5) + min_spacing * 8  # Ensure ample space for buttons

        # Calculate maximum available height for the board
        max_available_board_height = box_height - header_height - reason_box_height - footer_and_buttons_height

        # Use a conservative percentage of the available height
        board_height = int(max_available_board_height * 0.9)  # 90% of available height after reserving footer space

        # Ensure the board height is always a multiple of 5 (for 5 rows) plus a bit for the header
        # This prevents uneven cell sizes that can cause rendering issues
        cell_height = board_height / 6  # 5 number rows + 1 header row
        cell_height = int(cell_height)  # Round down to ensure it fits
        board_height = cell_height * 6  # Recalculate to ensure even division

        # Use calculated width for better number visibility
        board_width = max_board_width

        # Create board size as a tuple (width, height)
        board_size = (board_width, board_height)

        # Double-check that we have enough space for the footer
        remaining_space = box_height - header_height - reason_box_height - board_height - min_spacing * 4
        min_footer_space = button_height + min_spacing * 6

        # If we don't have enough space, reduce the board height
        if remaining_space < min_footer_space:
            height_reduction = min_footer_space - remaining_space
            board_height -= height_reduction
            # Ensure height is still a multiple of rows
            cell_height = board_height / 6
            cell_height = int(cell_height)
            board_height = cell_height * 6
            board_size = (board_width, board_height)

        board_rect = Rect(
            box_x + (box_width - board_size[0]) // 2,  # Center horizontally
            reason_box.bottom + min_spacing * 2,  # More spacing for cleaner look
            board_size[0],  # Width from tuple
            board_size[1]   # Height from tuple
        )

        # Add enhanced visual effects around the board with multiple layers for depth
        # First, add a pulsating outer glow effect with multiple layers
        pulse = (math.sin(time.time() * 2) + 1) / 2  # Slow pulse between 0 and 1
        pulse_fast = (math.sin(time.time() * 4) + 1) / 2  # Faster pulse for secondary effects

        # Outermost glow - subtle ambient effect
        ambient_glow_size = int(15 + pulse * 5)  # Size varies between 15 and 20
        ambient_glow_alpha = int(20 + pulse * 15)  # Alpha varies between 20 and 35
        ambient_glow_color = (100, 200, 100, ambient_glow_alpha)  # Soft green glow with pulsating alpha

        ambient_glow_surface = pygame.Surface((board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(ambient_glow_surface, ambient_glow_color, (0, 0, board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), border_radius=12)
        screen.blit(ambient_glow_surface, (board_rect.x - ambient_glow_size, board_rect.y - ambient_glow_size))

        # Middle glow - more vibrant
        outer_glow_size = int(10 + pulse_fast * 4)  # Size varies between 10 and 14
        outer_glow_alpha = int(40 + pulse * 25)  # Alpha varies between 40 and 65
        outer_glow_color = (130, 220, 140, outer_glow_alpha)  # Richer green glow with pulsating alpha

        outer_glow_surface = pygame.Surface((board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), border_radius=10)
        screen.blit(outer_glow_surface, (board_rect.x - outer_glow_size, board_rect.y - outer_glow_size))

        # Inner glow for more depth - brightest and most defined
        inner_glow_size = 5
        inner_glow_alpha = int(70 + pulse_fast * 30)  # Alpha varies between 70 and 100
        inner_glow_color = (160, 255, 180, inner_glow_alpha)  # Bright green glow with pulsating alpha

        inner_glow_surface = pygame.Surface((board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), border_radius=8)
        screen.blit(inner_glow_surface, (board_rect.x - inner_glow_size, board_rect.y - inner_glow_size))

        # Draw a more modern border with gradient effect
        border_width = 3  # Slightly thicker border for better visibility
        border_color = (220, 255, 220)  # Brighter, more vibrant border
        pygame.draw.rect(screen, border_color, board_rect, border_width, border_radius=6)  # Slightly more rounded corners

        # Draw the board without the redundant claim status text
        self.draw_player_board(
            screen,
            self.game.game_state.player_claim_cartella,
            board_rect,
            self.game.called_numbers,
            show_claim_status=False  # Don't show the redundant claim status text
        )

        # 6. Create a modern footer area for the button - position it at the bottom of the box
        footer_height = button_height + 8  # Add padding for footer

        # Position the footer at a fixed distance from the bottom of the box
        # This ensures consistent button placement regardless of board size
        footer_top = box_y + box_height - footer_height - min_spacing * 3

        # Ensure the board doesn't overlap with the footer by adjusting the board height if needed
        required_space_for_footer = box_height - (board_rect.y - box_y) - board_rect.height
        min_required_footer_space = footer_height + min_spacing * 5

        if required_space_for_footer < min_required_footer_space:
            # Reduce board height to make room for footer
            height_reduction = min_required_footer_space - required_space_for_footer
            board_rect.height -= height_reduction

            # Recalculate cell heights to ensure even distribution
            cell_height = board_rect.height / 6  # 5 number rows + 1 header row
            cell_height = int(cell_height)  # Round down to ensure it fits
            board_rect.height = cell_height * 6  # Recalculate to ensure even division

        footer_rect = Rect(
            box_x + int(10 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_top,
            box_width - int(20 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_height
        )

        # Draw the footer with a subtle gradient
        self.draw_gradient_rect(
            screen,
            footer_rect,
            (25, 25, 35),  # Slightly lighter than background
            (35, 35, 45),  # Slightly lighter gradient
            8  # Rounded corners for modern look
        )

        # 7. Position the buttons in the footer with improved styling and proper spacing
        button_height = int(30 * min(scale_x, scale_y))  # Standard height for buttons

        # Calculate available width for buttons
        available_button_width = footer_rect.width - int(60 * min(scale_x, scale_y))  # Leave margins on both sides

        # Calculate button widths based on available space
        check_next_width = int(min(120, available_button_width * 0.35) * min(scale_x, scale_y))
        continue_width = int(min(180, available_button_width * 0.55) * min(scale_x, scale_y))

        # Ensure minimum button spacing
        min_button_spacing = int(30 * min(scale_x, scale_y))

        # Position for the "Check Next" button (left)
        check_next_rect = Rect(
            footer_rect.left + int(25 * min(scale_x, scale_y)),  # Left aligned with padding
            footer_rect.centery - button_height // 2,  # Centered vertically in footer
            check_next_width,
            button_height
        )

        # Position for the "Continue Playing" button (right)
        continue_rect = Rect(
            footer_rect.right - continue_width - int(25 * min(scale_x, scale_y)),  # Right aligned with padding
            footer_rect.centery - button_height // 2,  # Centered vertically in footer
            continue_width,
            button_height
        )

        # Verify buttons don't overlap
        if check_next_rect.right + min_button_spacing > continue_rect.left:
            # Adjust positions if they would overlap
            total_width = check_next_width + continue_width + min_button_spacing
            check_next_rect.left = footer_rect.left + (footer_rect.width - total_width) // 2
            continue_rect.left = check_next_rect.right + min_button_spacing

        # Draw "Check Next" button with blue gradient
        check_next_color1 = (60, 80, 120)  # Blue
        check_next_color2 = (80, 100, 150)  # Lighter blue

        self.draw_gradient_rect(
            screen,
            check_next_rect,
            check_next_color1,
            check_next_color2,
            12  # Rounded corners
        )

        # Draw "Continue Playing" button with green gradient
        continue_color1 = (30, 120, 50)  # Dark green
        continue_color2 = (40, 150, 60)  # Lighter green

        self.draw_gradient_rect(
            screen,
            continue_rect,
            continue_color1,
            continue_color2,
            12  # Rounded corners
        )

        # Add shadow for depth
        shadow_size = 4
        shadow_color = (0, 0, 0, 60)  # Subtle black shadow with alpha

        # Shadow for Check Next button
        shadow_surface = pygame.Surface((check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), pygame.SRCALPHA)
        pygame.draw.rect(shadow_surface, shadow_color, (0, 0, check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), border_radius=15)
        screen.blit(shadow_surface, (check_next_rect.x - shadow_size//2, check_next_rect.y + shadow_size//2))

        # Shadow for Continue Playing button
        shadow_surface = pygame.Surface((continue_rect.width + shadow_size, continue_rect.height + shadow_size), pygame.SRCALPHA)
        pygame.draw.rect(shadow_surface, shadow_color, (0, 0, continue_rect.width + shadow_size, continue_rect.height + shadow_size), border_radius=15)
        screen.blit(shadow_surface, (continue_rect.x - shadow_size//2, continue_rect.y + shadow_size//2))

        # Button text
        button_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color

        # Check Next button text with shadow
        shadow_text = button_font.render("Check Next", True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(check_next_rect.centerx + shadow_offset, check_next_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main Check Next button text
        check_next_text = button_font.render("Check Next", True, (255, 255, 255))
        check_next_text_rect = check_next_text.get_rect(center=check_next_rect.center)
        screen.blit(check_next_text, check_next_text_rect)

        # Continue Playing button text with shadow
        shadow_text = button_font.render("Continue Playing", True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(continue_rect.centerx + shadow_offset, continue_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main Continue Playing button text
        continue_text = button_font.render("Continue Playing", True, (255, 255, 255))
        continue_text_rect = continue_text.get_rect(center=continue_rect.center)
        screen.blit(continue_text, continue_text_rect)

        # Store button hit areas
        self.modal_hit_areas["check_next_claim"] = check_next_rect
        self.modal_hit_areas["resume_game"] = continue_rect

        # Reset the winner display context flag when we're done
        self._in_winner_display_context = False

    def initialize_winner_particles(self, screen_width, screen_height):
        """Initialize particles for winner animation"""
        # Create a limited number of particles for better performance
        max_particles = 100  # Limit for performance

        # Define particle colors - festive colors for celebration
        particle_colors = [
            (255, 215, 0, 255),    # Gold
            (255, 255, 0, 255),    # Yellow
            (0, 255, 0, 255),      # Green
            (0, 200, 255, 255),    # Cyan
            (255, 50, 50, 255),    # Red
            (255, 150, 0, 255),    # Orange
            (200, 0, 255, 255),    # Purple
        ]

        # Create particles with optimized properties
        for _ in range(max_particles):
            # Randomize starting positions across the screen
            x = random.randint(0, screen_width)
            y = random.randint(0, screen_height)

            # Randomize size between 3 and 8 pixels
            size = random.randint(3, 8)

            # Select random color from our palette
            color = random.choice(particle_colors)

            # Set moderate speed for better performance
            speed = random.uniform(0.5, 2.0)

            # Random angle in radians
            angle = random.uniform(0, 2 * math.pi)

            # Add some spin for visual interest
            spin = random.uniform(-0.05, 0.05)

            # Add particle to list
            self.winner_particles.append({
                "x": x,
                "y": y,
                "size": size,
                "color": color,
                "speed": speed,
                "angle": angle,
                "spin": spin,
                "alpha": 255,  # Start fully opaque
                "fade_rate": random.uniform(0.3, 0.6),  # How quickly it fades
            })

    def update_winner_particles(self, screen_width, screen_height):
        """Update particle positions and properties"""
        # Calculate time-based values for animation
        current_time = time.time()
        elapsed = current_time - self.winner_animation_start_time

        # Periodically add new particles to maintain the effect
        if elapsed > 0.1 and len(self.winner_particles) < 150 and random.random() < 0.3:
            # Add 1-3 new particles
            for _ in range(random.randint(1, 3)):
                # Create particles at the bottom of the screen
                x = random.randint(0, screen_width)
                y = screen_height + random.randint(5, 15)  # Just below the screen

                # Randomize size between 3 and 8 pixels
                size = random.randint(3, 8)

                # Select random color
                color = (
                    random.randint(180, 255),  # R
                    random.randint(180, 255),  # G
                    random.randint(50, 255),   # B
                    255  # Alpha
                )

                # Set upward trajectory
                speed = random.uniform(1.0, 3.0)
                angle = random.uniform(math.pi * 0.7, math.pi * 1.3)  # Mostly upward

                # Add particle to list
                self.winner_particles.append({
                    "x": x,
                    "y": y,
                    "size": size,
                    "color": color,
                    "speed": speed,
                    "angle": angle,
                    "spin": random.uniform(-0.05, 0.05),
                    "alpha": 255,
                    "fade_rate": random.uniform(0.3, 0.6),
                })

        # Update existing particles
        for particle in self.winner_particles[:]:
            # Move particle
            particle["x"] += math.cos(particle["angle"]) * particle["speed"]
            particle["y"] += math.sin(particle["angle"]) * particle["speed"]

            # Apply spin to angle
            particle["angle"] += particle["spin"]

            # Fade out particle
            particle["alpha"] -= particle["fade_rate"]

            # Remove particles that are off-screen or fully transparent
            if (particle["y"] < -20 or
                particle["x"] < -20 or
                particle["x"] > screen_width + 20 or
                particle["alpha"] <= 0):
                self.winner_particles.remove(particle)

    def draw_winner_particles(self, screen):
        """Draw all particles to the screen"""
        # Use a single surface for all particles for better performance
        particle_surface = pygame.Surface(screen.get_size(), pygame.SRCALPHA)

        # Draw each particle
        for particle in self.winner_particles:
            # Skip particles with very low alpha for performance
            if particle["alpha"] < 10:
                continue

            # Calculate actual color with current alpha
            color = (
                particle["color"][0],
                particle["color"][1],
                particle["color"][2],
                int(particle["alpha"])
            )

            # Draw the particle as a circle
            pygame.draw.circle(
                particle_surface,
                color,
                (int(particle["x"]), int(particle["y"])),
                particle["size"]
            )

        # Blit the entire particle surface at once
        screen.blit(particle_surface, (0, 0))

    def draw_missed_winner_display(self, screen):
        """Draw the missed winner display overlay"""
        if not hasattr(self.game, 'game_state') or not self.game.game_state.show_missed_winner_display:
            return

        # AUDIO CONSISTENCY FIX: Play appropriate sound based on actual claim validation result
        # This ensures the audio always matches the window title text (Invalid claim vs Valid winner)
        if not hasattr(self, '_played_missed_winner_sound'):
            # Determine the actual claim status from game state validation result
            is_invalid_claim = False
            claim_reason = ""

            # Check the validation result and claim type from game state
            if hasattr(self.game, 'game_state'):
                if hasattr(self.game.game_state, 'validation_result') and not self.game.game_state.validation_result:
                    is_invalid_claim = True
                    if hasattr(self.game.game_state, 'invalid_claim_reason'):
                        claim_reason = self.game.game_state.invalid_claim_reason
                elif hasattr(self.game.game_state, 'claim_type'):
                    # Check specific claim types that indicate invalid claims
                    invalid_claim_types = ["missed_winner", "late", "not_registered", "already_claimed"]
                    if self.game.game_state.claim_type in invalid_claim_types:
                        is_invalid_claim = True
                        claim_reason = f"Claim type: {self.game.game_state.claim_type}"

            # CRITICAL AUDIO FIX: Only play warning sound for INVALID claims
            # Check the actual validation result to determine correct audio
            validation_result = getattr(self.game.game_state, 'validation_result', False)
            claim_type = getattr(self.game.game_state, 'claim_type', None)

            print("=" * 80)
            print("UI AUDIO DECISION POINT")
            print(f"Validation result: {validation_result}")
            print(f"Claim type: {claim_type}")
            print(f"Claim reason: {claim_reason}")
            print("=" * 80)

            # CRITICAL FIX: Only play warning sound when validation result is actually False
            # Do NOT play warning sound based on claim_type alone - validation_result is the authoritative source
            if validation_result == False:
                # This is an invalid claim - play warning sound
                if hasattr(self.game, 'play_warning_sound'):
                    # Check if warning sound was already played recently to avoid duplicates
                    current_time = pygame.time.get_ticks()
                    if not hasattr(self, '_last_ui_warning_time') or (current_time - self._last_ui_warning_time) > 1000:
                        self.game.play_warning_sound()
                        self._last_ui_warning_time = current_time
                        self._played_missed_winner_sound = True
                        print(f"UI AUDIO: Playing warning sound for invalid claim")
                        print(f"Validation result: {validation_result}, Claim type: {claim_type}")
                    else:
                        print(f"UI AUDIO: Skipping duplicate warning sound (last played {current_time - self._last_ui_warning_time}ms ago)")
                else:
                    # Fallback to non-winner late claim sound if warning sound is not available
                    if hasattr(self.game, 'play_nonwinner_late_claim_sound'):
                        self.game.play_nonwinner_late_claim_sound()
                        self._played_missed_winner_sound = True
                        print("UI AUDIO FALLBACK: Playing non-winner late claim sound (warning sound not available)")
            else:
                # This is a valid claim - do NOT play warning sound
                print(f"UI AUDIO: NOT playing warning sound - this is a valid claim")
                print(f"Validation result: {validation_result}, Claim type: {claim_type}")
                print(f"Winner sound should have been played by validation logic")
                print(f"CRITICAL FIX: Respecting validation_result = {validation_result} over claim_type = {claim_type}")

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Define consistent spacing
        min_spacing = int(15 * min(scale_x, scale_y))

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create missed winner box with improved dimensions
        box_width = int(screen_width * 0.55)  # Match invalid claim window width
        box_height = int(screen_height * 0.7)  # Match invalid claim window height

        # Use stored position if we're dragging or have a saved position
        if self.popup_position and (self.dragging or not hasattr(self.game.game_state, 'just_opened_validation')):
            box_x, box_y = self.popup_position
        else:
            # Default centered position
            box_x = (screen_width - box_width) // 2
            box_y = (screen_height - box_height) // 2
            self.popup_position = (box_x, box_y)

            # Mark that we've just opened the validation window
            if hasattr(self.game, 'game_state'):
                self.game.game_state.just_opened_validation = True

        # Create a more visually appealing background with enhanced gradient
        # First, add a subtle outer glow effect
        outer_glow_size = 15
        outer_glow_color = (150, 100, 50, 30)  # Subtle orange glow with alpha
        outer_glow_surface = pygame.Surface((box_width + outer_glow_size*2, box_height + outer_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, box_width + outer_glow_size*2, box_height + outer_glow_size*2), border_radius=25)
        screen.blit(outer_glow_surface, (box_x - outer_glow_size, box_y - outer_glow_size))

        # Add a second inner glow for depth
        inner_glow_size = 8
        inner_glow_color = (180, 120, 60, 50)  # Brighter orange glow with alpha
        inner_glow_surface = pygame.Surface((box_width + inner_glow_size*2, box_height + inner_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, box_width + inner_glow_size*2, box_height + inner_glow_size*2), border_radius=22)
        screen.blit(inner_glow_surface, (box_x - inner_glow_size, box_y - inner_glow_size))

        # Draw main box with enhanced gradient background
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (20, 20, 35),  # Richer blue-tinted dark
            (40, 40, 60),  # Richer lighter blue-tinted dark
            20  # Border radius
        )

        # Add header bar with enhanced gradient and visual effects
        header_height = int(55 * min(scale_x, scale_y))  # Slightly taller header for better proportions
        banner_rect = Rect(box_x, box_y, box_width, header_height)

        # Check if the board has any winning patterns with the current called numbers
        has_winning_pattern = False
        if hasattr(self.game.game_state, 'player_claim_cartella'):
            card = self.game.bingo_logic.get_card_for_player(self.game.game_state.player_claim_cartella)
            if card and hasattr(self.game, 'called_numbers'):
                has_winning_pattern = card.check_winning_patterns(self.game.called_numbers)

        # Initialize particles for cheering animation if this is a valid winner
        if has_winning_pattern and not hasattr(self, 'winner_particles'):
            screen_width, screen_height = screen.get_size()
            self.winner_particles = []
            self.winner_animation_start_time = time.time()
            self.initialize_winner_particles(screen_width, screen_height)

            # Check if this is a skipped pattern
            is_skipped_pattern = False
            is_current_number_mismatch = False

            # CRITICAL FIX: First check if the current number is part of any winning pattern
            is_current_number_in_winning_pattern = False

            if hasattr(self.game, 'current_number'):
                current_number = self.game.current_number

                # Check if the current number is part of any winning pattern
                if hasattr(self.game.game_state, 'player_claim_cartella'):
                    cartella_number = self.game.game_state.player_claim_cartella
                    card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    # Check all winning patterns
                    if card:
                        # Get all winning patterns for this card
                        all_patterns = []
                        if hasattr(self.game.game_state, 'winning_patterns') and self.game.game_state.winning_patterns:
                            all_patterns = self.game.game_state.winning_patterns
                        elif hasattr(self.game.game_state, 'winner_pattern') and self.game.game_state.winner_pattern:
                            all_patterns = [self.game.game_state.winner_pattern]

                        # Check if the current number is part of any winning pattern
                        for pattern_name in all_patterns:
                            if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                                pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, pattern_name)

                                if current_number in pattern_numbers:
                                    is_current_number_in_winning_pattern = True
                                    break

            # Only check for skipped patterns if the current number is NOT part of any winning pattern
            if not is_current_number_in_winning_pattern:
                # Check if this pattern is in the skipped patterns list
                if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'skipped_patterns'):
                    cartella_number = self.game.game_state.player_claim_cartella
                    if cartella_number in self.game.game_state.skipped_patterns and self.game.game_state.skipped_patterns[cartella_number]:
                        # Check if this specific pattern was skipped
                        if hasattr(self.game.game_state, 'winner_pattern') and hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            card = self.game.bingo_logic.get_card_for_player(cartella_number)
                            if card:
                                winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                    card, self.game.game_state.winner_pattern)

                                # Check if this specific pattern was skipped
                                for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                    if sorted(skipped_pattern) == sorted(winning_pattern_numbers):
                                        is_skipped_pattern = True
                                        break

                # Check if the current number matches the winning number
                if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'winning_number'):
                    current_number = self.game.current_number
                    winning_number = self.game.game_state.winning_number

                    if current_number != winning_number:
                        # Only mark as a mismatch if the winning number is in the called numbers
                        if hasattr(self.game, 'called_numbers') and winning_number in self.game.called_numbers:
                            is_current_number_mismatch = True
                    else:
                        # Current number matches winning number - this is a valid claim
                        # Even if it's in skipped_patterns, if the current number matches the winning number,
                        # it's a valid claim for the current number (not a skipped pattern)
                        if is_skipped_pattern:
                            # Check if this specific pattern was skipped but is now valid with current number
                            is_pattern_skipped_but_now_valid = False

                            # If the winning number is the current number, this is a valid claim
                            # regardless of whether the pattern was previously skipped
                            if hasattr(self.game.game_state, 'player_claim_cartella'):
                                cartella_number = self.game.game_state.player_claim_cartella
                                card = self.game.bingo_logic.get_card_for_player(cartella_number)
                                if card:
                                    # Check if the current number is part of the winning pattern
                                    if hasattr(card, 'has_number') and card.has_number(current_number):
                                        is_pattern_skipped_but_now_valid = True

                            # If this pattern is now valid with the current number, it's not a skipped pattern
                            if is_pattern_skipped_but_now_valid:
                                is_skipped_pattern = False
            else:
                # If the current number is part of any winning pattern, this is a valid claim
                # regardless of whether the pattern was previously skipped
                is_skipped_pattern = False
                is_current_number_mismatch = False
                print(f"CRITICAL FIX: Current number is part of a winning pattern - this is a valid claim")

            # CRITICAL FIX: Do NOT play winner sound in missed winner display
            # This method is specifically for missed/invalid claims, so audio should match the claim status
            # The audio is already handled at the beginning of this method (lines 2362-2399)
            # Playing winner sound here causes the audio inconsistency bug where "Invalid claim"
            # popups sometimes play winner audio instead of warning audio
            print("AUDIO FIX: Skipping winner sound in missed winner display - audio already handled based on claim validation result")

        # Update and draw particles for valid winners
        if has_winning_pattern and hasattr(self, 'winner_particles'):
            screen_width, screen_height = screen.get_size()
            self.update_winner_particles(screen_width, screen_height)
            self.draw_winner_particles(screen)

        # Colors for header - use gold gradient for valid winners, orange/amber for missed winners
        if has_winning_pattern:
            header_color1 = (120, 100, 20)  # Dark gold
            header_color2 = (180, 150, 30)  # Lighter gold
        else:
            header_color1 = (120, 60, 20)  # Dark amber
            header_color2 = (180, 90, 30)  # Lighter amber

        # Add subtle glow effect to header
        header_glow_size = 5
        header_glow_color = (header_color2[0], header_color2[1], header_color2[2], 40)  # Matching glow with alpha
        header_glow_surface = pygame.Surface((banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(header_glow_surface, header_glow_color, (0, 0, banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), border_radius=15)
        screen.blit(header_glow_surface, (banner_rect.x - header_glow_size, banner_rect.y - header_glow_size))

        # Draw the header with enhanced gradient
        self.draw_gradient_rect(
            screen,
            banner_rect,
            header_color1,
            header_color2,
            border_radius=10  # Rounded corners at top
        )

        # Store the header area for dragging
        self.modal_hit_areas["claim_header_drag"] = banner_rect

        # Get board number if available
        board_num = ""
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'player_claim_cartella'):
            board_num = f"Board #{self.game.game_state.player_claim_cartella} - "

        # Check if the board has any winning patterns with the current called numbers
        has_winning_pattern = False
        if hasattr(self.game.game_state, 'player_claim_cartella'):
            card = self.game.bingo_logic.get_card_for_player(self.game.game_state.player_claim_cartella)
            if card and hasattr(self.game, 'called_numbers'):
                has_winning_pattern = card.check_winning_patterns(self.game.called_numbers)

        # Title for the display - check if this is a skipped pattern
        is_skipped_pattern = False

        # CRITICAL FIX: First check if the current number is part of any winning pattern
        is_current_number_in_winning_pattern = False

        if hasattr(self.game, 'current_number'):
            current_number = self.game.current_number

            # Check if the current number is part of any winning pattern
            if hasattr(self.game.game_state, 'player_claim_cartella'):
                cartella_number = self.game.game_state.player_claim_cartella
                card = self.game.bingo_logic.get_card_for_player(cartella_number)

                # Check all winning patterns
                if card:
                    # Get all winning patterns for this card
                    all_patterns = []
                    if hasattr(self.game.game_state, 'winning_patterns') and self.game.game_state.winning_patterns:
                        all_patterns = self.game.game_state.winning_patterns
                    elif hasattr(self.game.game_state, 'winner_pattern') and self.game.game_state.winner_pattern:
                        all_patterns = [self.game.game_state.winner_pattern]

                    print(f"CRITICAL FIX: Checking if current number {current_number} is part of any winning pattern")
                    print(f"All winning patterns: {all_patterns}")

                    # Check if the current number is part of any winning pattern
                    for pattern_name in all_patterns:
                        if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, pattern_name)
                            print(f"CRITICAL FIX: Pattern {pattern_name} numbers: {pattern_numbers}")

                            if current_number in pattern_numbers:
                                is_current_number_in_winning_pattern = True
                                print(f"CRITICAL FIX: Current number {current_number} is part of winning pattern {pattern_name}")
                                print(f"This is a valid claim regardless of previously skipped patterns")
                                break

        # Only check for skipped patterns if the current number is NOT part of any winning pattern
        if not is_current_number_in_winning_pattern:
            if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'skipped_patterns'):
                cartella_number = self.game.game_state.player_claim_cartella
                if cartella_number in self.game.game_state.skipped_patterns and self.game.game_state.skipped_patterns[cartella_number]:
                    is_skipped_pattern = True

            # Check if the current number matches the winning number
            is_current_number_mismatch = False
            if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'winning_number'):
                current_number = self.game.current_number
                winning_number = self.game.game_state.winning_number

                if current_number != winning_number:
                    # Only mark as a mismatch if the winning number is NOT the current number
                    # AND the winning number is in the called numbers (meaning it was called earlier)
                    if hasattr(self.game, 'called_numbers') and winning_number in self.game.called_numbers:
                        is_current_number_mismatch = True
                        is_skipped_pattern = True
                        # Reduced logging - only log once per session
                        if not hasattr(self, '_logged_mismatch_missed') or self._logged_mismatch_missed != f"{current_number}_{winning_number}":
                            print(f"Current number ({current_number}) does not match winning number ({winning_number})")
                            print(f"Winning number {winning_number} was previously called, marking as skipped pattern")
                            self._logged_mismatch_missed = f"{current_number}_{winning_number}"
                    else:
                        # If the winning number is not in called numbers, this is an error condition
                        # Don't mark as skipped pattern
                        print(f"WARNING: Winning number {winning_number} is not in called numbers and doesn't match current number {current_number}")
        else:
            # If the current number is part of any winning pattern, this is a valid claim
            # regardless of whether the pattern was previously skipped
            is_skipped_pattern = False
            is_current_number_mismatch = False

            # AUDIO CONSISTENCY NOTE: Audio is handled at the beginning of this method (lines 2362-2398)
            # based on the actual claim validation result, ensuring it matches the popup title

        # TITLE DETERMINATION: Set appropriate title based on pattern type and validation result
        # This title must be consistent with the audio played at the beginning of the method
        if has_winning_pattern:
            # CRITICAL FIX: Check if this is an already claimed pattern or current number not in pattern
            is_already_claimed = False
            is_current_number_not_in_pattern = False

            if hasattr(self.game.game_state, 'invalid_claim_reason'):
                if "already claimed" in self.game.game_state.invalid_claim_reason.lower():
                    is_already_claimed = True
                elif "current number not in pattern" in self.game.game_state.invalid_claim_reason.lower():
                    is_current_number_not_in_pattern = True

            # CRITICAL FIX: If the current number is part of the winning pattern, this is a valid claim
            # regardless of whether the pattern was previously claimed or skipped
            if is_current_number_in_winning_pattern:
                title = f"{board_num}VALID WINNER!"
                title_color = (255, 215, 0)  # Gold color for valid claims
                print(f"CRITICAL FIX: Current number is part of winning pattern - showing VALID WINNER!")
            elif is_current_number_not_in_pattern:
                # The current number is not part of the winning pattern
                title = f"{board_num}INVALID CLAIM!"
                title_color = (255, 100, 80)  # Red-orange color for invalid claims
            elif is_already_claimed:
                # This is an already claimed pattern and the current number is not part of it
                title = f"{board_num}ALREADY CLAIMED!"
                title_color = (255, 180, 80)  # Amber/orange color for already claimed
            elif is_skipped_pattern or is_current_number_mismatch:
                title = f"{board_num}PREVIOUSLY SKIPPED!"
                title_color = (255, 180, 80)  # Amber/orange color for skipped winners
            else:
                title = f"{board_num}VALID WINNER!"
                title_color = (255, 215, 0)  # Gold color for valid claims
        else:
            title = f"{board_num}MISSED WINNER!"
            title_color = (255, 180, 80)  # Amber/orange color for missed winners

        # Display the title in the header with enhanced styling
        title_font = pygame.font.SysFont("Arial", int(28 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color
        shadow_text = title_font.render(title, True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(banner_rect.centerx + shadow_offset, banner_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main title text
        title_text = title_font.render(title, True, title_color)
        title_rect = title_text.get_rect(center=(banner_rect.centerx, banner_rect.centery))
        screen.blit(title_text, title_rect)

        # Add subtle pulsing glow for missed claims
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Slow pulse between 0 and 1
        glow_alpha = int(50 + 30 * pulse)  # Pulsing alpha between 50 and 80
        glow_color = (255, 180, 100, glow_alpha)  # Orange glow with pulsing alpha

        glow_surface = pygame.Surface((title_rect.width + 20, title_rect.height + 10), pygame.SRCALPHA)
        pygame.draw.rect(glow_surface, glow_color, (0, 0, title_rect.width + 20, title_rect.height + 10), border_radius=10)
        screen.blit(glow_surface, (title_rect.x - 10, title_rect.y - 5))

        # ------------------- Start sequential layout positioning -------------------

        # 1. Position pattern info in a modern info bar with enhanced styling
        current_y = box_y + header_height + min_spacing  # More spacing for cleaner look
        compact_header_height = int(45 * min(scale_x, scale_y))  # Slightly taller for better readability

        # Create a modern info bar with rounded corners
        reason_box = Rect(
            box_x + int(15 * min(scale_x, scale_y)),  # More inset from edges for modern look
            current_y,
            box_width - int(30 * min(scale_x, scale_y)),  # More inset from edges for modern look
            compact_header_height
        )

        # Match the info bar colors with the header colors
        if has_winning_pattern:
            # For valid winners, use gold gradient
            reason_color1 = (100, 90, 20)  # Rich dark gold
            reason_color2 = (150, 130, 30)  # Vibrant lighter gold
        else:
            # For missed winners, use amber/orange gradient
            reason_color1 = (100, 70, 30)  # Rich dark amber
            reason_color2 = (150, 100, 40)  # Vibrant lighter amber

        # Add subtle glow effect to info bar
        info_glow_size = 4
        info_glow_color = (reason_color2[0], reason_color2[1], reason_color2[2], 40)  # Matching glow with alpha
        info_glow_surface = pygame.Surface((reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(info_glow_surface, info_glow_color, (0, 0, reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), border_radius=12)
        screen.blit(info_glow_surface, (reason_box.x - info_glow_size, reason_box.y - info_glow_size))

        # Draw the info bar with enhanced gradient and rounded corners
        self.draw_gradient_rect(
            screen,
            reason_box,
            reason_color1,
            reason_color2,
            10  # More rounded corners for modern look
        )

        # Add text to the info bar with improved styling
        player_font = pygame.font.SysFont("Arial", int(22 * min(scale_x, scale_y)), bold=True)

        # Display pattern information
        display_text = ""
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'winner_pattern'):
            # Check if this is a pattern that was already claimed
            if hasattr(self.game.game_state, 'invalid_claim_reason') and "already claimed" in self.game.game_state.invalid_claim_reason.lower():
                # CRITICAL FIX: Check if the current number is part of the winning pattern
                is_current_number_in_winning_pattern = False
                if hasattr(self.game, 'current_number') and hasattr(self.game.game_state, 'player_claim_cartella'):
                    current_number = self.game.current_number
                    cartella_number = self.game.game_state.player_claim_cartella
                    card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    if card and hasattr(self.game.game_state, 'winner_pattern') and hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                        winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(card, self.game.game_state.winner_pattern)

                        if current_number in winning_pattern_numbers:
                            is_current_number_in_winning_pattern = True
                            print(f"CRITICAL FIX: Current number {current_number} is part of winning pattern {self.game.game_state.winner_pattern}")
                            print(f"This should be a valid claim regardless of whether the pattern was previously claimed")

                # Make the text clearer about why the pattern was already claimed
                if is_current_number_in_winning_pattern:
                    # This shouldn't happen with the updated validation logic, but just in case
                    display_text = f"⚠️ {self.game.game_state.winner_pattern} - Valid with current number {current_number}"
                else:
                    # The pattern was already claimed and the current number is not part of it
                    # Get the current number for display
                    current_num_display = ""
                    if hasattr(self.game, 'current_number'):
                        current_num_display = f" (current: {self.game.current_number})"

                    # Check if the invalid claim reason is specifically about the current number
                    if hasattr(self.game.game_state, 'invalid_claim_reason') and "Current number not in pattern" in self.game.game_state.invalid_claim_reason:
                        display_text = f"⚠️ {self.game.game_state.winner_pattern} - Current number {self.game.current_number} not in pattern"
                    else:
                        display_text = f"⚠️ {self.game.game_state.winner_pattern} - Already Claimed{current_num_display}"
            else:
                # Get the current number and the winning number if available
                current_number = None
                winning_number = None

                if hasattr(self.game, 'current_number'):
                    current_number = self.game.current_number

                if hasattr(self.game.game_state, 'winning_number'):
                    winning_number = self.game.game_state.winning_number

                # Create a display text showing previously skipped claim numbers
                if hasattr(self.game.game_state, 'player_claim_cartella') and hasattr(self.game.game_state, 'winner_pattern'):
                    cartella_number = self.game.game_state.player_claim_cartella
                    card = self.game.bingo_logic.get_card_for_player(cartella_number)

                    # Initialize display text
                    display_text = ""

                    # Check if this is a skipped pattern
                    is_skipped_pattern = False
                    skipped_numbers = []

                    if hasattr(self.game.game_state, 'skipped_patterns'):
                        if cartella_number in self.game.game_state.skipped_patterns:
                            # Get the winning pattern numbers
                            try:
                                if hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                                    winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                        card, self.game.game_state.winner_pattern)
                                    winning_pattern_numbers = sorted(winning_pattern_numbers)

                                    # Check if this pattern matches any skipped pattern
                                    for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                        if sorted(skipped_pattern) == winning_pattern_numbers:
                                            is_skipped_pattern = True
                                            # Store the skipped numbers for display
                                            skipped_numbers = skipped_pattern
                                            break
                            except Exception:
                                # Default to not skipped if there's an error
                                is_skipped_pattern = False

                    # Format the display text based on whether it's a skipped pattern
                    if is_skipped_pattern and skipped_numbers:
                        # Sort the skipped numbers for consistent display
                        skipped_numbers = sorted(skipped_numbers)

                        # Create the animated text for skipped numbers
                        display_text = "Previously skipped numbers: "

                        # Add the skipped numbers with dash separators
                        for i, num in enumerate(skipped_numbers):
                            if i > 0:
                                display_text += " - "
                            display_text += f"{num}"
                    else:
                        # If not a skipped pattern, show the pattern name
                        if hasattr(self.game.game_state, 'winning_patterns') and self.game.game_state.winning_patterns:
                            # Multiple patterns
                            display_text = "Patterns: "
                            for i, pattern in enumerate(self.game.game_state.winning_patterns[:2]):
                                if i > 0:
                                    display_text += ", "
                                display_text += pattern

                            # If there are more patterns, indicate it
                            if len(self.game.game_state.winning_patterns) > 2:
                                display_text += f" +{len(self.game.game_state.winning_patterns) - 2} more"
                        else:
                            # Single pattern
                            display_text = f"Pattern: {self.game.game_state.winner_pattern}"

                    # Add status text based on pattern type
                    if is_current_number_mismatch and not is_skipped_pattern:
                        display_text += " - Claimed Too Late"
                else:
                    display_text = "⏱️ Valid pattern but claimed after next number was called"
        else:
            display_text = "⏱️ Valid pattern but claimed after next number was called"

        # Render the info text with animation for skipped numbers
        if "Previously skipped numbers:" in display_text:
            # Split the text into prefix and numbers
            prefix = "Previously skipped numbers: "
            numbers_text = display_text[len(prefix):]

            # Render the prefix part
            prefix_text = player_font.render(prefix, True, (255, 255, 255))
            prefix_rect = prefix_text.get_rect(midleft=(reason_box.left + 20, reason_box.centery))

            # Add subtle text shadow for prefix
            shadow_prefix_text = player_font.render(prefix, True, (0, 0, 0, 128))
            shadow_prefix_rect = shadow_prefix_text.get_rect(midleft=(prefix_rect.left + 1, prefix_rect.top + 1))
            screen.blit(shadow_prefix_text, shadow_prefix_rect)
            screen.blit(prefix_text, prefix_rect)

            # Calculate animation parameters
            pulse = (math.sin(time.time() * 3) + 1) / 2  # Pulsing effect between 0 and 1

            # Split the numbers text by dashes
            number_parts = numbers_text.split(" - ")

            # Start position for rendering numbers
            current_x = prefix_rect.right + 5

            # Render each number with animation
            for i, number in enumerate(number_parts):
                # Calculate color based on pulse
                if i % 2 == 0:
                    # Even numbers pulse between yellow and red
                    r = 255
                    g = int(180 + 75 * pulse)  # Pulse between 180 and 255
                    b = 0
                else:
                    # Odd numbers pulse between red and yellow
                    r = 255
                    g = int(255 - 75 * pulse)  # Pulse between 180 and 255
                    b = 0

                number_color = (r, g, b)

                # Render the number
                number_text = player_font.render(number, True, number_color)
                number_rect = number_text.get_rect(midleft=(current_x, reason_box.centery))

                # Add glow effect
                glow_size = int(3 + 2 * pulse)  # Size varies with pulse
                glow_surface = pygame.Surface((number_rect.width + glow_size*2, number_rect.height + glow_size*2), pygame.SRCALPHA)
                glow_alpha = int(100 + 50 * pulse)  # Alpha varies with pulse
                glow_color = (255, 200, 0, glow_alpha)
                pygame.draw.rect(glow_surface, glow_color, (0, 0, number_rect.width + glow_size*2, number_rect.height + glow_size*2), border_radius=5)
                screen.blit(glow_surface, (number_rect.left - glow_size, number_rect.top - glow_size))

                # Add subtle text shadow
                shadow_number_text = player_font.render(number, True, (0, 0, 0, 128))
                shadow_number_rect = shadow_number_text.get_rect(midleft=(number_rect.left + 1, number_rect.top + 1))
                screen.blit(shadow_number_text, shadow_number_rect)

                # Render the number
                screen.blit(number_text, number_rect)

                # Update position for next number
                current_x = number_rect.right + 10

                # Add dash if not the last number
                if i < len(number_parts) - 1:
                    dash_text = player_font.render(" - ", True, (255, 255, 255))
                    dash_rect = dash_text.get_rect(midleft=(current_x, reason_box.centery))

                    # Add subtle text shadow for dash
                    shadow_dash_text = player_font.render(" - ", True, (0, 0, 0, 128))
                    shadow_dash_rect = shadow_dash_text.get_rect(midleft=(dash_rect.left + 1, dash_rect.top + 1))
                    screen.blit(shadow_dash_text, shadow_dash_rect)

                    screen.blit(dash_text, dash_rect)
                    current_x = dash_rect.right + 5
        else:
            # Regular rendering for non-skipped patterns
            info_text_color = (255, 255, 255)  # White text
            info_text = player_font.render(display_text, True, info_text_color)
            info_rect = info_text.get_rect(center=(reason_box.centerx, reason_box.centery))

            # Add subtle text shadow
            shadow_info_text = player_font.render(display_text, True, (0, 0, 0, 128))
            shadow_info_rect = shadow_info_text.get_rect(center=(info_rect.centerx + 1, info_rect.centery + 1))
            screen.blit(shadow_info_text, shadow_info_rect)
            screen.blit(info_text, info_rect)

        # Set reason_box_height for later calculations
        reason_box_height = compact_header_height

        # Calculate space needed for elements below the board - minimized for maximum board space
        button_width = int(200 * min(scale_x, scale_y))  # Button width
        button_height = int(30 * min(scale_x, scale_y))  # Minimized button height

        # 4. Calculate maximum available board size - MAXIMIZE horizontal and vertical space usage
        # Calculate footer height with absolute minimal spacing
        footer_height = button_height + 2  # Absolute minimum padding for footer

        # No need to reserve space for "BOARD NOT WON" text anymore
        board_not_won_space = 0  # Removed space reservation for redundant text

        # Calculate available space between red header and footer
        # Minimize footer spacing to maximize board height
        glow_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for glow effects
        footer_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for footer separation

        # Prioritize board space by minimizing other spacing
        available_height = box_height - header_height - reason_box_height - footer_height - board_not_won_space - min_spacing * 3 - glow_spacing - footer_spacing

        # Use a width that maximizes the board size while maintaining proportions
        max_board_width = int(box_width * 0.95)  # 95% of box width to maximize board size

        # Calculate height based on available vertical space - maximize board size
        # Use a slightly more conservative percentage to prevent overlap
        board_height = int(available_height * 0.92)  # Use 92% of available height for maximum board size

        # Ensure the board height is always a multiple of 5 (for 5 rows) plus a bit for the header
        # This prevents uneven cell sizes that can cause rendering issues
        cell_height = board_height / 6  # 5 number rows + 1 header row
        cell_height = int(cell_height)  # Round down to ensure it fits
        board_height = cell_height * 6  # Recalculate to ensure even division

        # Use calculated width for better number visibility
        board_width = max_board_width

        # Create board size as a tuple (width, height)
        board_size = (board_width, board_height)

        # 5. Position the board - centered horizontally with proper spacing from headers
        # Calculate the maximum safe height to ensure the board doesn't overlap with footer
        # Add extra spacing to prevent overlap with the Resume Game button
        max_safe_height = box_height - header_height - reason_box_height - footer_height - min_spacing * 10 - 15

        # If the calculated board height is too large, reduce it
        if board_size[1] > max_safe_height * 0.9:
            board_height = int(max_safe_height * 0.9)
            # Recalculate to ensure even division
            cell_height = board_height / 6  # 5 number rows + 1 header row
            cell_height = int(cell_height)  # Round down to ensure it fits
            board_height = cell_height * 6  # Recalculate to ensure even division
            board_size = (board_size[0], board_height)

        board_rect = Rect(
            box_x + (box_width - board_size[0]) // 2,  # Center horizontally
            reason_box.bottom + min_spacing * 2,  # More spacing for cleaner look
            board_size[0],  # Width from tuple
            board_size[1]   # Height from tuple
        )

        # Add enhanced visual effects around the board with multiple layers for depth
        # First, add a pulsating outer glow effect with multiple layers
        pulse = (math.sin(time.time() * 2) + 1) / 2  # Slow pulse between 0 and 1
        pulse_fast = (math.sin(time.time() * 4) + 1) / 2  # Faster pulse for secondary effects

        # Use different glow colors based on whether this is a valid winner
        if has_winning_pattern:
            # Gold/green glow for valid winners (similar to winner display)
            # Outermost glow - subtle ambient effect
            ambient_glow_size = int(15 + pulse * 5)  # Size varies between 15 and 20
            ambient_glow_alpha = int(20 + pulse * 15)  # Alpha varies between 20 and 35
            ambient_glow_color = (180, 200, 100, ambient_glow_alpha)  # Soft gold/green glow with pulsating alpha

            ambient_glow_surface = pygame.Surface((board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(ambient_glow_surface, ambient_glow_color, (0, 0, board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), border_radius=12)
            screen.blit(ambient_glow_surface, (board_rect.x - ambient_glow_size, board_rect.y - ambient_glow_size))

            # Middle glow - more vibrant
            outer_glow_size = int(10 + pulse_fast * 4)  # Size varies between 10 and 14
            outer_glow_alpha = int(40 + pulse * 25)  # Alpha varies between 40 and 65
            outer_glow_color = (220, 220, 100, outer_glow_alpha)  # Richer gold glow with pulsating alpha

            outer_glow_surface = pygame.Surface((board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), border_radius=10)
            screen.blit(outer_glow_surface, (board_rect.x - outer_glow_size, board_rect.y - outer_glow_size))

            # Inner glow for more depth - brightest and most defined
            inner_glow_size = 5
            inner_glow_alpha = int(70 + pulse_fast * 30)  # Alpha varies between 70 and 100
            inner_glow_color = (255, 230, 100, inner_glow_alpha)  # Bright gold glow with pulsating alpha

            inner_glow_surface = pygame.Surface((board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), border_radius=8)
            screen.blit(inner_glow_surface, (board_rect.x - inner_glow_size, board_rect.y - inner_glow_size))
        else:
            # Amber glow for missed winners
            # Outermost glow - subtle ambient effect
            ambient_glow_size = int(15 + pulse * 5)  # Size varies between 15 and 20
            ambient_glow_alpha = int(20 + pulse * 15)  # Alpha varies between 20 and 35
            ambient_glow_color = (200, 150, 100, ambient_glow_alpha)  # Soft amber glow with pulsating alpha

            ambient_glow_surface = pygame.Surface((board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(ambient_glow_surface, ambient_glow_color, (0, 0, board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), border_radius=12)
            screen.blit(ambient_glow_surface, (board_rect.x - ambient_glow_size, board_rect.y - ambient_glow_size))

            # Middle glow - more vibrant
            outer_glow_size = int(10 + pulse_fast * 4)  # Size varies between 10 and 14
            outer_glow_alpha = int(40 + pulse * 25)  # Alpha varies between 40 and 65
            outer_glow_color = (220, 160, 100, outer_glow_alpha)  # Richer amber glow with pulsating alpha

            outer_glow_surface = pygame.Surface((board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), border_radius=10)
            screen.blit(outer_glow_surface, (board_rect.x - outer_glow_size, board_rect.y - outer_glow_size))

            # Inner glow for more depth - brightest and most defined
            inner_glow_size = 5
            inner_glow_alpha = int(70 + pulse_fast * 30)  # Alpha varies between 70 and 100
            inner_glow_color = (255, 180, 100, inner_glow_alpha)  # Bright amber glow with pulsating alpha

            inner_glow_surface = pygame.Surface((board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), pygame.SRCALPHA)
            pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), border_radius=8)
            screen.blit(inner_glow_surface, (board_rect.x - inner_glow_size, board_rect.y - inner_glow_size))

        # Draw a more modern border with gradient effect
        border_width = 3  # Slightly thicker border for better visibility
        if has_winning_pattern:
            border_color = (255, 240, 120)  # Gold border for valid winners
        else:
            border_color = (255, 220, 180)  # Amber border for missed winners
        pygame.draw.rect(screen, border_color, board_rect, border_width, border_radius=6)  # Slightly more rounded corners

        # Draw the board with visual indicators for skipped numbers and current number
        # Check if the board has any winning patterns with the current called numbers
        has_winning_pattern = False
        if hasattr(self.game.game_state, 'player_claim_cartella'):
            card = self.game.bingo_logic.get_card_for_player(self.game.game_state.player_claim_cartella)
            if card and hasattr(self.game, 'called_numbers'):
                # Check if the card has any winning patterns with the current called numbers
                # Only log this once by using a flag
                if not hasattr(self, '_logged_winning_patterns'):
                    self._logged_winning_patterns = True
                    has_winning_pattern = card.check_winning_patterns(self.game.called_numbers)
                    if has_winning_pattern:
                        print(f"Board #{self.game.game_state.player_claim_cartella} has winning patterns: {card.winning_patterns}")

                        # Store the winning patterns in the game state for proper highlighting
                        if hasattr(card, 'winning_patterns') and card.winning_patterns:
                            if not hasattr(self.game.game_state, 'winning_patterns'):
                                self.game.game_state.winning_patterns = []
                            self.game.game_state.winning_patterns = card.winning_patterns
                            print(f"Stored winning patterns in game state: {self.game.game_state.winning_patterns}")
                else:
                    has_winning_pattern = card.check_winning_patterns(self.game.called_numbers)

        # Set up context for drawing the board with winning patterns
        self._in_winner_display_context = True

        # Draw the board first to populate winning_patterns_cells
        # Use a valid rect with reasonable dimensions instead of a 1x1 rect
        # We need to ensure the rect has valid dimensions for any surfaces created during drawing
        temp_board_rect = Rect(0, 0, 300, 300)  # Larger temporary rect with valid dimensions, not used for actual drawing

        # Check if the cartella number is valid before trying to draw the board
        if hasattr(self.game.game_state, 'player_claim_cartella') and self.game.game_state.player_claim_cartella is not None:
            try:
                # Add error handling to prevent crashes
                self.draw_player_board(screen, self.game.game_state.player_claim_cartella, temp_board_rect, self.game.called_numbers, in_winner_display=True)
            except Exception as e:
                print(f"Error in draw_player_board during winning pattern detection: {e}")
                # Initialize winning_patterns_cells if it doesn't exist
                if not hasattr(self, 'winning_patterns_cells'):
                    self.winning_patterns_cells = {}

        # Now draw the actual board with winning pattern animations but without redundant text
        message_bottom = self.draw_player_board(
            screen,
            self.game.game_state.player_claim_cartella,
            board_rect,
            self.game.called_numbers,
            show_claim_status=False,  # Don't show redundant text at the bottom
            in_winner_display=True   # Enable winning pattern animations
        )

        # 6. Create a modern footer area for the button - position it below the message
        footer_height = button_height + 2  # Absolute minimum padding for footer

        # If we have a message_bottom position, use it to position the footer
        if message_bottom:
            footer_top = message_bottom + min_spacing * 3  # Increased spacing to fix overlap
        else:
            # Calculate the bottom of the board including glow effects
            # Use full glow consideration to prevent overlap
            board_bottom_with_glow = board_rect.bottom + ambient_glow_size  # Full glow consideration

            # Add extra spacing to ensure no overlap with the button (no need for board_not_won_space anymore)
            footer_top = board_bottom_with_glow + min_spacing * 3

        # Ensure footer doesn't go beyond the box bottom
        footer_top = min(footer_top, box_y + box_height - footer_height - min_spacing)

        footer_rect = Rect(
            box_x + int(10 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_top,
            box_width - int(20 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_height
        )

        # Draw the footer with a subtle gradient
        self.draw_gradient_rect(
            screen,
            footer_rect,
            (25, 25, 35),  # Slightly lighter than background
            (35, 35, 45),  # Slightly lighter gradient
            8  # Rounded corners for modern look
        )

        # 7. Position the buttons in the footer with improved styling
        button_width = int(180 * min(scale_x, scale_y))  # Slightly narrower to fit multiple buttons
        button_height = int(30 * min(scale_x, scale_y))  # Minimized height to match footer
        button_spacing = int(20 * min(scale_x, scale_y))  # Space between buttons

        # Determine which buttons to show
        show_check_next = True
        show_continue_playing = True
        show_penalise_button = False  # Default to not showing penalize button

        # Only show penalize button if this is not a skipped pattern
        # (player intentionally didn't claim when they could have)
        if hasattr(self.game, 'game_state'):
            # Check if the pattern is in skipped_patterns
            cartella_number = None
            if hasattr(self.game.game_state, 'player_claim_cartella'):
                cartella_number = self.game.game_state.player_claim_cartella

            # CRITICAL FIX: Improved logic for showing penalize button
            # Only show penalize button for genuine late claims
            if cartella_number and hasattr(self.game.game_state, 'skipped_patterns'):
                # First check if the cartella is in skipped_patterns
                if cartella_number not in self.game.game_state.skipped_patterns:
                    # Not in skipped patterns, check if this is a genuine late claim
                    genuine_late_claim = False

                    # Check if we have previous number information
                    if hasattr(self.game.game_state, 'previous_number') and self.game.game_state.previous_number is not None:
                        # Get the card for this player
                        card = self.game.bingo_logic.get_card_for_player(cartella_number)

                        # Create a copy of called numbers without the current number
                        if hasattr(self.game, 'called_numbers') and hasattr(self.game, 'current_number'):
                            current_number = self.game.current_number
                            if current_number in self.game.called_numbers:
                                # Create a copy of called numbers without the current number
                                previous_called = [n for n in self.game.called_numbers if n != current_number]

                                # Check if the card had a winning pattern with just the previous numbers
                                genuine_late_claim = card.check_winning_patterns(previous_called)

                                # Debug info - only print once during initialization
                                if not hasattr(self, '_logged_late_claim_status'):
                                    self._logged_late_claim_status = True
                                    if genuine_late_claim:
                                        print(f"Showing penalize button for genuine late claim")
                                    else:
                                        print(f"Not showing penalize button for false late claim")

                    # Only show penalize button for genuine late claims
                    show_penalise_button = genuine_late_claim
                else:
                    # In skipped patterns, check if the current pattern matches any skipped pattern
                    if hasattr(self.game.game_state, 'winner_pattern') and self.game.game_state.winner_pattern:
                        # Get the winning pattern numbers
                        card = self.game.bingo_logic.get_card_for_player(cartella_number)
                        if card and hasattr(self.game.game_state, 'get_winning_pattern_numbers'):
                            winning_pattern_numbers = self.game.game_state.get_winning_pattern_numbers(
                                card, self.game.game_state.winner_pattern)
                            winning_pattern_numbers = sorted(winning_pattern_numbers)

                            # Check if this pattern matches any skipped pattern
                            pattern_was_skipped = False
                            for skipped_pattern in self.game.game_state.skipped_patterns[cartella_number]:
                                if sorted(skipped_pattern) == winning_pattern_numbers:
                                    pattern_was_skipped = True
                                    break

                            # Check if this is a genuine skipped pattern
                            genuine_skipped_pattern = False
                            if pattern_was_skipped and hasattr(self.game.game_state, 'previous_number') and self.game.game_state.previous_number is not None:
                                # Create a copy of called numbers without the current number
                                if hasattr(self.game, 'called_numbers') and hasattr(self.game, 'current_number'):
                                    current_number = self.game.current_number
                                    if current_number in self.game.called_numbers:
                                        # Create a copy of called numbers without the current number
                                        previous_called = [n for n in self.game.called_numbers if n != current_number]

                                        # Check if the card had a winning pattern with just the previous numbers
                                        genuine_skipped_pattern = card.check_winning_patterns(previous_called)

                                        # Debug info - only print once during initialization
                                        if not hasattr(self, '_logged_penalize_status'):
                                            self._logged_penalize_status = True
                                            if genuine_skipped_pattern:
                                                print(f"Not showing penalize button for genuine skipped pattern")
                                            else:
                                                print(f"Showing penalize button for false skipped pattern")

                            # Only hide penalize button if pattern was genuinely skipped
                            show_penalise_button = not (pattern_was_skipped and genuine_skipped_pattern)
                    else:
                        # No winner pattern info, default to not showing penalize button for safety
                        show_penalise_button = False
            else:
                # No cartella number or no skipped_patterns attribute, default to not showing penalize button for safety
                show_penalise_button = False

        # Calculate button positions - similar to invalid claim window
        buttons_to_show = sum([show_check_next, show_continue_playing, show_penalise_button])
        total_width = buttons_to_show * button_width + (buttons_to_show - 1) * button_spacing

        # Center the buttons in the footer
        button_start_x = footer_rect.x + (footer_rect.width - total_width) // 2
        current_x = button_start_x

        # Position for the "Check Next" button (left)
        check_next_rect = Rect(
            current_x,
            footer_rect.centery - button_height // 2,
            button_width,
            button_height
        )
        current_x += button_width + button_spacing

        # Position for the "Penalise Player" button (middle)
        penalise_rect = Rect(
            current_x,
            footer_rect.centery - button_height // 2,
            button_width,
            button_height
        )
        current_x += button_width + button_spacing

        # Position for the "Continue Playing" button (right)
        continue_rect = Rect(
            current_x,
            footer_rect.centery - button_height // 2,
            button_width,
            button_height
        )

        # Draw "Check Next" button with blue gradient
        check_next_color1 = (60, 80, 120)  # Blue
        check_next_color2 = (80, 100, 150)  # Lighter blue

        self.draw_gradient_rect(
            screen,
            check_next_rect,
            check_next_color1,
            check_next_color2,
            12  # Rounded corners
        )

        # Draw "Continue Playing" button with amber gradient
        continue_color1 = (100, 70, 30)  # Dark amber
        continue_color2 = (150, 100, 40)  # Lighter amber

        self.draw_gradient_rect(
            screen,
            continue_rect,
            continue_color1,
            continue_color2,
            12  # Rounded corners
        )

        # Add shadow for depth
        shadow_size = 4
        shadow_color = (0, 0, 0, 60)  # Subtle black shadow with alpha

        # Shadow for Check Next button
        shadow_surface = pygame.Surface((check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), pygame.SRCALPHA)
        pygame.draw.rect(shadow_surface, shadow_color, (0, 0, check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), border_radius=15)
        screen.blit(shadow_surface, (check_next_rect.x - shadow_size//2, check_next_rect.y + shadow_size//2))

        # Shadow for Continue Playing button
        shadow_surface = pygame.Surface((continue_rect.width + shadow_size, continue_rect.height + shadow_size), pygame.SRCALPHA)
        pygame.draw.rect(shadow_surface, shadow_color, (0, 0, continue_rect.width + shadow_size, continue_rect.height + shadow_size), border_radius=15)
        screen.blit(shadow_surface, (continue_rect.x - shadow_size//2, continue_rect.y + shadow_size//2))

        # Button text
        button_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color

        # Check Next button text with shadow
        shadow_text = button_font.render("Check Next", True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(check_next_rect.centerx + shadow_offset, check_next_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main Check Next button text
        check_next_text = button_font.render("Check Next", True, (255, 255, 255))
        check_next_text_rect = check_next_text.get_rect(center=check_next_rect.center)
        screen.blit(check_next_text, check_next_text_rect)

        # Continue Playing button text with shadow
        shadow_text = button_font.render("Continue Playing", True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(continue_rect.centerx + shadow_offset, continue_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main Continue Playing button text
        continue_text = button_font.render("Continue Playing", True, (255, 255, 255))
        continue_text_rect = continue_text.get_rect(center=continue_rect.center)
        screen.blit(continue_text, continue_text_rect)

        # Only draw the Penalise Player button if show_penalise_button is True
        if show_penalise_button:
            # Draw "Penalise Player" button with red gradient
            penalise_color1 = (150, 50, 50)  # Dark red
            penalise_color2 = (180, 70, 70)  # Lighter red

            self.draw_gradient_rect(
                screen,
                penalise_rect,
                penalise_color1,
                penalise_color2,
                12  # Rounded corners
            )

            # Add shadow for penalise button
            shadow_surface = pygame.Surface((penalise_rect.width + shadow_size, penalise_rect.height + shadow_size), pygame.SRCALPHA)
            pygame.draw.rect(shadow_surface, shadow_color, (0, 0, penalise_rect.width + shadow_size, penalise_rect.height + shadow_size), border_radius=15)
            screen.blit(shadow_surface, (penalise_rect.x - shadow_size//2, penalise_rect.y + shadow_size//2))

            # Penalise button text with shadow
            shadow_text = button_font.render("⛔ Penalise Player", True, shadow_color)
            shadow_rect = shadow_text.get_rect(center=(penalise_rect.centerx + shadow_offset, penalise_rect.centery + shadow_offset))
            screen.blit(shadow_text, shadow_rect)

            # Main Penalise button text
            penalise_text = button_font.render("⛔ Penalise Player", True, (255, 220, 220))
            penalise_text_rect = penalise_text.get_rect(center=penalise_rect.center)
            screen.blit(penalise_text, penalise_text_rect)

            # Only add the hit area if we're actually showing the button
            self.modal_hit_areas["penalise_player"] = penalise_rect

        # Store button hit areas
        self.modal_hit_areas["check_next_claim"] = check_next_rect
        # The penalise_player hit area is only added if the button is shown (see above)
        self.modal_hit_areas["resume_game"] = continue_rect

    def draw_invalid_claim_display(self, screen):
        """Draw the invalid claim display overlay"""
        if not hasattr(self.game, 'game_state') or not self.game.game_state.show_invalid_claim_display:
            return

        # FIXED: Play the appropriate sound based on the claim type
        # Check if this is an unregistered board claim
        is_unregistered_board = False
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'claim_type'):
            is_unregistered_board = self.game.game_state.claim_type == "not_registered"

        # CRITICAL AUDIO FIX: Only play sound for truly invalid claims
        # Check the actual validation result to avoid overriding winner sounds
        if not hasattr(self, '_played_invalid_claim_sound'):
            validation_result = getattr(self.game.game_state, 'validation_result', False)
            claim_type = getattr(self.game.game_state, 'claim_type', None)

            print("=" * 80)
            print("INVALID CLAIM UI AUDIO DECISION POINT")
            print(f"Validation result: {validation_result}")
            print(f"Claim type: {claim_type}")
            print(f"Is unregistered board: {is_unregistered_board}")
            print("=" * 80)

            # Only play audio for truly invalid claims
            if validation_result == False:
                if is_unregistered_board and hasattr(self.game, 'play_warning_sound'):
                    # Check if warning sound was already played recently to avoid duplicates
                    current_time = pygame.time.get_ticks()
                    if not hasattr(self, '_last_invalid_ui_warning_time') or (current_time - self._last_invalid_ui_warning_time) > 1000:
                        self.game.play_warning_sound()
                        self._last_invalid_ui_warning_time = current_time
                        self._played_invalid_claim_sound = True
                        print("INVALID CLAIM UI: Playing warning sound for unregistered board")
                    else:
                        print(f"INVALID CLAIM UI: Skipping duplicate warning sound (last played {current_time - self._last_invalid_ui_warning_time}ms ago)")
                elif hasattr(self.game, 'play_nonwinner_late_claim_sound'):
                    # Play non-winner late claim sound for other invalid claims
                    self.game.play_nonwinner_late_claim_sound()
                    self._played_invalid_claim_sound = True
                    print("INVALID CLAIM UI: Playing non-winner late claim sound for invalid claim")
            else:
                # This is a valid claim - do NOT play any warning sounds
                print("INVALID CLAIM UI: NOT playing warning sound - this is a valid claim")
                print("Winner sound should have been played by validation logic")

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Define consistent spacing
        min_spacing = int(15 * min(scale_x, scale_y))

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create invalid claim box with improved dimensions
        box_width = int(screen_width * 0.55)  # Slightly wider for better proportions
        box_height = int(screen_height * 0.7)  # Maintained height

        # Use stored position if we're dragging or have a saved position
        if self.popup_position and (self.dragging or not hasattr(self.game.game_state, 'just_opened_validation')):
            box_x, box_y = self.popup_position
        else:
            # Default centered position
            box_x = (screen_width - box_width) // 2
            box_y = (screen_height - box_height) // 2
            self.popup_position = (box_x, box_y)

            # Mark that we've just opened the validation window
            if hasattr(self.game, 'game_state'):
                self.game.game_state.just_opened_validation = True

        # Create a more visually appealing background with enhanced gradient
        # First, add a subtle outer glow effect
        outer_glow_size = 15
        outer_glow_color = (80, 80, 150, 30)  # Subtle blue glow with alpha
        outer_glow_surface = pygame.Surface((box_width + outer_glow_size*2, box_height + outer_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, box_width + outer_glow_size*2, box_height + outer_glow_size*2), border_radius=25)
        screen.blit(outer_glow_surface, (box_x - outer_glow_size, box_y - outer_glow_size))

        # Add a second inner glow for depth
        inner_glow_size = 8
        inner_glow_color = (100, 100, 180, 50)  # Brighter blue glow with alpha
        inner_glow_surface = pygame.Surface((box_width + inner_glow_size*2, box_height + inner_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, box_width + inner_glow_size*2, box_height + inner_glow_size*2), border_radius=22)
        screen.blit(inner_glow_surface, (box_x - inner_glow_size, box_y - inner_glow_size))

        # Draw main box with enhanced gradient background
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (20, 20, 35),  # Richer blue-tinted dark
            (40, 40, 60),  # Richer lighter blue-tinted dark
            20  # Border radius
        )

        # Add header bar with enhanced gradient and visual effects
        header_height = int(55 * min(scale_x, scale_y))  # Slightly taller header for better proportions
        banner_rect = Rect(box_x, box_y, box_width, header_height)

        # Determine header colors based on claim type
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'validation_result'):
            if self.game.game_state.validation_result:
                # For valid claims, use vibrant green gradient
                header_color1 = (25, 90, 45)  # Rich dark green
                header_color2 = (45, 140, 70)  # Vibrant lighter green
            else:
                # For specific invalid claim types
                if hasattr(self.game.game_state, 'invalid_claim_reason'):
                    reason = self.game.game_state.invalid_claim_reason.lower()
                    if "no winning pattern" in reason:
                        # For no winning pattern, use orange-red gradient
                        header_color1 = (90, 40, 40)  # Dark orange-red
                        header_color2 = (140, 60, 60)  # Lighter orange-red
                    elif "not registered" in reason:
                        # For unregistered board, use purple gradient
                        header_color1 = (60, 30, 80)  # Dark purple
                        header_color2 = (90, 50, 120)  # Lighter purple
                    elif "too late" in reason or "after next number" in reason:
                        # For late claim, use blue gradient
                        header_color1 = (30, 50, 90)  # Dark blue
                        header_color2 = (50, 80, 140)  # Lighter blue
                    else:
                        # Default for other invalid claims
                        header_color1 = (60, 60, 90)  # Rich dark blue-purple
                        header_color2 = (80, 80, 120)  # Rich lighter blue-purple
                else:
                    # Default for invalid claims
                    header_color1 = (60, 60, 90)  # Rich dark blue-purple
                    header_color2 = (80, 80, 120)  # Rich lighter blue-purple
        else:
            # Default colors
            header_color1 = (60, 60, 90)  # Rich dark blue-purple
            header_color2 = (80, 80, 120)  # Rich lighter blue-purple

        # Add subtle glow effect to header
        header_glow_size = 5
        header_glow_color = (header_color2[0], header_color2[1], header_color2[2], 40)  # Matching glow with alpha
        header_glow_surface = pygame.Surface((banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(header_glow_surface, header_glow_color, (0, 0, banner_rect.width + header_glow_size*2, banner_rect.height + header_glow_size*2), border_radius=15)
        screen.blit(header_glow_surface, (banner_rect.x - header_glow_size, banner_rect.y - header_glow_size))

        # Draw the header with enhanced gradient
        self.draw_gradient_rect(
            screen,
            banner_rect,
            header_color1,
            header_color2,
            border_radius=10  # Rounded corners at top
        )

        # Store the header area for dragging
        self.modal_hit_areas["claim_header_drag"] = banner_rect

        # Determine the title based on validation result and reason
        # Include board number in the title to avoid redundancy in the info bar
        title = "Invalid Claim"
        title_color = (255, 130, 130)  # Softer red text for invalid claims

        # Get board number if available
        board_num = ""
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'player_claim_cartella'):
            board_num = f"Board #{self.game.game_state.player_claim_cartella} - "

        # Check if we have a validation result
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'validation_result'):
            if self.game.game_state.validation_result:
                title = f"{board_num}BINGO! Valid Claim"
                title_color = (130, 255, 130)  # Softer green for valid claims
            else:
                # More specific invalid claim titles
                if hasattr(self.game.game_state, 'invalid_claim_reason'):
                    reason = self.game.game_state.invalid_claim_reason.lower()
                    if "no winning pattern" in reason:
                        title = f"{board_num}No Winning Pattern"
                    elif "not registered" in reason:
                        title = f"{board_num}Unregistered Board"
                    elif "too late" in reason or "after next number" in reason:
                        title = f"{board_num}Late Claim"
                    else:
                        title = f"{board_num}Invalid Claim"

        # Display the title in the header with enhanced styling
        title_font = pygame.font.SysFont("Arial", int(28 * min(scale_x, scale_y)), bold=True)  # Slightly larger font

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color
        shadow_text = title_font.render(title, True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(banner_rect.centerx + shadow_offset, banner_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main title text
        title_text = title_font.render(title, True, title_color)
        title_rect = title_text.get_rect(center=(banner_rect.centerx, banner_rect.centery))
        screen.blit(title_text, title_rect)

        # Add subtle highlight effect on top of text for a modern look
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'validation_result'):
            if self.game.game_state.validation_result:
                # For valid claims, add a subtle pulsing glow
                pulse = (math.sin(time.time() * 3) + 1) / 2  # Slow pulse between 0 and 1
                glow_alpha = int(50 + 30 * pulse)  # Pulsing alpha between 50 and 80
                glow_color = (180, 255, 180, glow_alpha)  # Green glow with pulsing alpha

                glow_surface = pygame.Surface((title_rect.width + 20, title_rect.height + 10), pygame.SRCALPHA)
                pygame.draw.rect(glow_surface, glow_color, (0, 0, title_rect.width + 20, title_rect.height + 10), border_radius=10)
                screen.blit(glow_surface, (title_rect.x - 10, title_rect.y - 5))

        # No warning triangles in the image

        # ------------------- Start sequential layout positioning -------------------

        # 1. Position cartella text and reason in a modern info bar with enhanced styling
        current_y = box_y + header_height + min_spacing  # More spacing for cleaner look
        compact_header_height = int(45 * min(scale_x, scale_y))  # Slightly taller for better readability

        # Create a modern info bar with rounded corners
        reason_box = Rect(
            box_x + int(15 * min(scale_x, scale_y)),  # More inset from edges for modern look
            current_y,
            box_width - int(30 * min(scale_x, scale_y)),  # More inset from edges for modern look
            compact_header_height
        )

        # Determine colors based on claim type with enhanced gradients
        if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'validation_result'):
            if self.game.game_state.validation_result:
                # For valid claims, use vibrant green gradient
                reason_color1 = (30, 110, 50)  # Rich dark green
                reason_color2 = (50, 160, 70)  # Vibrant lighter green
            else:
                # For specific invalid claim types
                if hasattr(self.game.game_state, 'invalid_claim_reason'):
                    reason = self.game.game_state.invalid_claim_reason.lower()
                    if "no winning pattern" in reason:
                        # For no winning pattern, use orange-red gradient
                        reason_color1 = (130, 60, 30)  # Dark orange-red
                        reason_color2 = (170, 80, 40)  # Lighter orange-red
                    elif "not registered" in reason:
                        # For unregistered board, use purple gradient
                        reason_color1 = (80, 40, 100)  # Dark purple
                        reason_color2 = (110, 60, 140)  # Lighter purple
                    elif "too late" in reason or "after next number" in reason:
                        # For late claim, use blue gradient
                        reason_color1 = (40, 60, 120)  # Dark blue
                        reason_color2 = (60, 90, 160)  # Lighter blue
                    else:
                        # Default for other invalid claims
                        reason_color1 = (120, 40, 40)  # Rich dark red
                        reason_color2 = (160, 60, 60)  # Rich lighter red
                else:
                    # Default for invalid claims
                    reason_color1 = (120, 40, 40)  # Rich dark red
                    reason_color2 = (160, 60, 60)  # Rich lighter red
        else:
            # Default colors
            reason_color1 = (120, 40, 40)  # Rich dark red
            reason_color2 = (160, 60, 60)  # Rich lighter red

        # Add subtle glow effect to info bar
        info_glow_size = 4
        info_glow_color = (reason_color2[0], reason_color2[1], reason_color2[2], 40)  # Matching glow with alpha
        info_glow_surface = pygame.Surface((reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(info_glow_surface, info_glow_color, (0, 0, reason_box.width + info_glow_size*2, reason_box.height + info_glow_size*2), border_radius=12)
        screen.blit(info_glow_surface, (reason_box.x - info_glow_size, reason_box.y - info_glow_size))

        # Draw the info bar with enhanced gradient and rounded corners
        self.draw_gradient_rect(
            screen,
            reason_box,
            reason_color1,
            reason_color2,
            10  # More rounded corners for modern look
        )

        # Add text to the info bar with improved styling
        player_font = pygame.font.SysFont("Arial", int(22 * min(scale_x, scale_y)), bold=True)  # Slightly larger font

        # Create a combined info display to avoid redundancy
        # For the info bar, we'll show only additional information that's not already in the header
        # This eliminates redundancy while keeping all necessary information visible

        # Determine what information to show based on claim type
        display_text = ""

        if hasattr(self.game, 'game_state'):
            # For valid claims, show pattern information
            if hasattr(self.game.game_state, 'validation_result') and self.game.game_state.validation_result:
                if hasattr(self.game.game_state, 'winner_pattern') and self.game.game_state.winner_pattern:
                    display_text = f"🏆 Pattern: {self.game.game_state.winner_pattern}"
                else:
                    display_text = "🏆 Valid winning pattern"
            else:
                # For invalid claims, show additional details if available
                if hasattr(self.game.game_state, 'invalid_claim_reason'):
                    reason = self.game.game_state.invalid_claim_reason.lower()
                    if "too late" in reason:
                        # Extract the time from the reason if available
                        import re
                        time_match = re.search(r'\((\d+\.\d+)s', reason)
                        if time_match:
                            delay_time = time_match.group(1)
                            display_text = f"⏱️ Claim delayed by {delay_time}s"
                        else:
                            display_text = "⏱️ Claim made after next number was called"
                    elif "not registered" in reason:
                        display_text = "❌ Board not in player registry"
                    elif "no winning pattern" in reason:
                        # Show specific missing numbers or pattern details
                        display_text = "🔍 Check marked numbers carefully"
                    else:
                        display_text = "ℹ️ Click Resume to continue playing"

        # Only render and display text if we have something to show
        if display_text:
            # Render the info text
            info_text_color = (255, 255, 255)  # White text
            info_text = player_font.render(display_text, True, info_text_color)
            info_rect = info_text.get_rect(center=(reason_box.centerx, reason_box.centery))

            # Add subtle text shadow
            shadow_info_text = player_font.render(display_text, True, (0, 0, 0, 128))
            shadow_info_rect = shadow_info_text.get_rect(center=(info_rect.centerx + 1, info_rect.centery + 1))
            screen.blit(shadow_info_text, shadow_info_rect)
            screen.blit(info_text, info_rect)

        # We've already displayed all necessary information in the centered text
        # No need for additional text on the right side - this eliminates redundancy

        # Set reason_box_height for later calculations
        reason_box_height = compact_header_height

        # Calculate space needed for elements below the board - minimized for maximum board space
        button_width = int(200 * min(scale_x, scale_y))  # Button width
        button_height = int(30 * min(scale_x, scale_y))  # Minimized button height

        # 4. Calculate maximum available board size - MAXIMIZE horizontal and vertical space usage
        # Calculate footer height with absolute minimal spacing
        footer_height = button_height + 2  # Absolute minimum padding for footer

        # Reserve space for "BOARD NOT WON" text
        board_not_won_space = int(50 * min(scale_x, scale_y))  # Space for the text and shadow

        # Calculate available space between red header and footer
        # Minimize footer spacing to maximize board height
        glow_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for glow effects
        footer_spacing = int(15 * min(scale_x, scale_y))  # Minimal space for footer separation

        # Prioritize board space by minimizing other spacing
        available_height = box_height - header_height - reason_box_height - footer_height - board_not_won_space - min_spacing * 3 - glow_spacing - footer_spacing

        # Use a width that maximizes the board size while maintaining proportions
        max_board_width = int(box_width * 0.95)  # 95% of box width to maximize board size

        # Calculate height based on available vertical space - maximize board size
        # Use a slightly more conservative percentage to prevent overlap
        board_height = int(available_height * 0.92)  # Use 92% of available height for maximum board size

        # Ensure the board height is always a multiple of 5 (for 5 rows) plus a bit for the header
        # This prevents uneven cell sizes that can cause rendering issues
        cell_height = board_height / 6  # 5 number rows + 1 header row
        cell_height = int(cell_height)  # Round down to ensure it fits
        board_height = cell_height * 6  # Recalculate to ensure even division

        # Use calculated width for better number visibility
        board_width = max_board_width

        # Create board size as a tuple (width, height)
        board_size = (board_width, board_height)

        # 5. Position the board - centered horizontally with proper spacing from headers
        # Calculate the maximum safe height to ensure the board doesn't overlap with footer
        # Add extra spacing to prevent overlap with the Resume Game button
        max_safe_height = box_height - header_height - reason_box_height - footer_height - min_spacing * 10 - 15

        # If the calculated board height is too large, reduce it
        if board_size[1] > max_safe_height * 0.9:
            board_height = int(max_safe_height * 0.9)
            # Recalculate to ensure even division
            cell_height = board_height / 6  # 5 number rows + 1 header row
            cell_height = int(cell_height)  # Round down to ensure it fits
            board_height = cell_height * 6  # Recalculate to ensure even division
            board_size = (board_size[0], board_height)

        board_rect = Rect(
            box_x + (box_width - board_size[0]) // 2,  # Center horizontally
            reason_box.bottom + min_spacing * 2,  # More spacing for cleaner look
            board_size[0],  # Width from tuple
            board_size[1]   # Height from tuple
        )

        # Add enhanced visual effects around the board with multiple layers for depth
        # First, add a pulsating outer glow effect with multiple layers
        pulse = (math.sin(time.time() * 2) + 1) / 2  # Slow pulse between 0 and 1
        pulse_fast = (math.sin(time.time() * 4) + 1) / 2  # Faster pulse for secondary effects

        # Outermost glow - subtle ambient effect
        ambient_glow_size = int(15 + pulse * 5)  # Size varies between 15 and 20
        ambient_glow_alpha = int(20 + pulse * 15)  # Alpha varies between 20 and 35
        ambient_glow_color = (100, 120, 200, ambient_glow_alpha)  # Soft blue glow with pulsating alpha

        ambient_glow_surface = pygame.Surface((board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(ambient_glow_surface, ambient_glow_color, (0, 0, board_rect.width + ambient_glow_size * 2, board_rect.height + ambient_glow_size * 2), border_radius=12)
        screen.blit(ambient_glow_surface, (board_rect.x - ambient_glow_size, board_rect.y - ambient_glow_size))

        # Middle glow - more vibrant
        outer_glow_size = int(10 + pulse_fast * 4)  # Size varies between 10 and 14
        outer_glow_alpha = int(40 + pulse * 25)  # Alpha varies between 40 and 65
        outer_glow_color = (130, 140, 220, outer_glow_alpha)  # Richer blue glow with pulsating alpha

        outer_glow_surface = pygame.Surface((board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, board_rect.width + outer_glow_size * 2, board_rect.height + outer_glow_size * 2), border_radius=10)
        screen.blit(outer_glow_surface, (board_rect.x - outer_glow_size, board_rect.y - outer_glow_size))

        # Inner glow for more depth - brightest and most defined
        inner_glow_size = 5
        inner_glow_alpha = int(70 + pulse_fast * 30)  # Alpha varies between 70 and 100
        inner_glow_color = (160, 180, 255, inner_glow_alpha)  # Bright blue glow with pulsating alpha

        inner_glow_surface = pygame.Surface((board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, board_rect.width + inner_glow_size * 2, board_rect.height + inner_glow_size * 2), border_radius=8)
        screen.blit(inner_glow_surface, (board_rect.x - inner_glow_size, board_rect.y - inner_glow_size))

        # Draw a more modern border with gradient effect
        border_width = 3  # Slightly thicker border for better visibility
        border_color = (220, 220, 255)  # Brighter, more vibrant border
        pygame.draw.rect(screen, border_color, board_rect, border_width, border_radius=6)  # Slightly more rounded corners

        # Draw the board with visual indicators for skipped numbers and current number
        message_bottom = self.draw_player_board(
            screen,
            self.game.game_state.player_claim_cartella,
            board_rect,
            self.game.called_numbers,
            show_claim_status=True  # Enable visual indicators for skipped numbers and current number
        )

        # 6. Create a modern footer area for the button - position it below the message
        footer_height = button_height + 2  # Absolute minimum padding for footer

        # If we have a message_bottom position, use it to position the footer
        if message_bottom:
            footer_top = message_bottom + min_spacing * 3  # Increased spacing to fix overlap
        else:
            # Calculate the bottom of the board including glow effects
            # Use full glow consideration to prevent overlap
            board_bottom_with_glow = board_rect.bottom + ambient_glow_size  # Full glow consideration

            # Add extra spacing to ensure no overlap with the button (no need for board_not_won_space anymore)
            footer_top = board_bottom_with_glow + min_spacing * 3

        # Ensure footer doesn't go beyond the box bottom
        footer_top = min(footer_top, box_y + box_height - footer_height - min_spacing)

        footer_rect = Rect(
            box_x + int(10 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_top,
            box_width - int(20 * min(scale_x, scale_y)),  # Inset from edges for modern look
            footer_height
        )

        # Draw the footer with a subtle gradient
        self.draw_gradient_rect(
            screen,
            footer_rect,
            (25, 25, 35),  # Slightly lighter than background
            (35, 35, 45),  # Slightly lighter gradient
            8  # Rounded corners for modern look
        )

        # 7. Position the buttons in the footer with improved styling
        button_width = int(180 * min(scale_x, scale_y))  # Slightly narrower to fit multiple buttons
        button_height = int(30 * min(scale_x, scale_y))  # Minimized height to match footer
        button_spacing = int(20 * min(scale_x, scale_y))  # Space between buttons

        # Determine which buttons to show based on claim type
        show_confirm_button = False
        show_penalise_button = False
        show_resume_button = False

        if hasattr(self.game, 'game_state'):
            if hasattr(self.game.game_state, 'claim_type'):
                claim_type = self.game.game_state.claim_type
                if claim_type == "valid":
                    show_confirm_button = True
                elif claim_type == "not_registered":
                    # Don't show penalize button for unregistered boards
                    show_resume_button = True
                elif claim_type in ["late", "no_pattern"]:
                    show_penalise_button = True
                    show_resume_button = True
                else:
                    show_resume_button = True
            elif hasattr(self.game.game_state, 'validation_result'):
                if self.game.game_state.validation_result:
                    show_confirm_button = True
                else:
                    show_resume_button = True
                    # Check for specific invalid claim reasons
                    if hasattr(self.game.game_state, 'invalid_claim_reason'):
                        reason = self.game.game_state.invalid_claim_reason.lower()
                        # Don't show penalize button for unregistered boards
                        if "not registered" in reason:
                            pass  # Just show the resume button
                        # Show penalize button for other invalid claims
                        elif "no winning pattern" in reason or "too late" in reason or "after next number" in reason:
                            show_penalise_button = True
            else:
                show_resume_button = True
        else:
            show_resume_button = True

        # Calculate button positions based on which buttons are shown
        buttons_to_show = sum([show_confirm_button, show_penalise_button, show_resume_button])
        total_width = buttons_to_show * button_width + (buttons_to_show - 1) * button_spacing if buttons_to_show > 0 else 0

        # Start position for the first button
        button_start_x = footer_rect.centerx - total_width // 2

        # Position for the "Check Next" button (always shown)
        check_next_width = int(120 * min(scale_x, scale_y))
        check_next_rect = Rect(
            footer_rect.right - check_next_width - int(10 * min(scale_x, scale_y)),
            footer_rect.centery - button_height // 2 + 6,
            check_next_width,
            button_height
        )

        # Draw "Check Next" button
        check_next_color1 = (60, 80, 120)  # Blue
        check_next_color2 = (80, 100, 150)  # Lighter blue

        self.draw_gradient_rect(
            screen,
            check_next_rect,
            check_next_color1,
            check_next_color2,
            12  # Rounded corners
        )

        # Add shadow for depth
        shadow_size = 4
        shadow_color = (0, 0, 0, 60)  # Subtle black shadow with alpha
        shadow_surface = pygame.Surface((check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), pygame.SRCALPHA)
        pygame.draw.rect(shadow_surface, shadow_color, (0, 0, check_next_rect.width + shadow_size, check_next_rect.height + shadow_size), border_radius=15)
        screen.blit(shadow_surface, (check_next_rect.x - shadow_size//2, check_next_rect.y + shadow_size//2))

        # Button text
        button_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (20, 20, 30)  # Dark shadow color
        shadow_text = button_font.render("Check Next", True, shadow_color)
        shadow_rect = shadow_text.get_rect(center=(check_next_rect.centerx + shadow_offset, check_next_rect.centery + shadow_offset))
        screen.blit(shadow_text, shadow_rect)

        # Main button text
        check_next_text = button_font.render("Check Next", True, (255, 255, 255))
        check_next_text_rect = check_next_text.get_rect(center=check_next_rect.center)
        screen.blit(check_next_text, check_next_text_rect)

        # Store hit area for the "Check Next" button
        self.modal_hit_areas["check_next_claim"] = check_next_rect

        # Current x position for buttons
        current_x = button_start_x

        # Draw Confirm button if needed
        if show_confirm_button:
            confirm_rect = Rect(
                current_x,
                footer_rect.centery - button_height // 2 + 6,
                button_width,
                button_height
            )

            # Green gradient for confirm button
            confirm_color1 = (30, 120, 50)  # Dark green
            confirm_color2 = (40, 150, 60)  # Lighter green

            self.draw_gradient_rect(
                screen,
                confirm_rect,
                confirm_color1,
                confirm_color2,
                12  # Rounded corners
            )

            # Add shadow for depth
            shadow_surface = pygame.Surface((confirm_rect.width + shadow_size, confirm_rect.height + shadow_size), pygame.SRCALPHA)
            pygame.draw.rect(shadow_surface, shadow_color, (0, 0, confirm_rect.width + shadow_size, confirm_rect.height + shadow_size), border_radius=15)
            screen.blit(shadow_surface, (confirm_rect.x - shadow_size//2, confirm_rect.y + shadow_size//2))

            # Button text
            shadow_text = button_font.render("🎉 Confirm Winner", True, shadow_color)
            shadow_rect = shadow_text.get_rect(center=(confirm_rect.centerx + shadow_offset, confirm_rect.centery + shadow_offset))
            screen.blit(shadow_text, shadow_rect)

            confirm_text = button_font.render("🎉 Confirm Winner", True, (220, 255, 220))
            confirm_text_rect = confirm_text.get_rect(center=confirm_rect.center)
            screen.blit(confirm_text, confirm_text_rect)

            # Store hit area
            self.modal_hit_areas["confirm_winner"] = confirm_rect

            # Update current x position
            current_x += button_width + button_spacing

        # Draw Penalise button if needed
        if show_penalise_button:
            penalise_rect = Rect(
                current_x,
                footer_rect.centery - button_height // 2 + 6,
                button_width,
                button_height
            )

            # Red gradient for penalise button
            penalise_color1 = (150, 50, 50)  # Dark red
            penalise_color2 = (180, 70, 70)  # Lighter red

            self.draw_gradient_rect(
                screen,
                penalise_rect,
                penalise_color1,
                penalise_color2,
                12  # Rounded corners
            )

            # Add shadow for depth
            shadow_surface = pygame.Surface((penalise_rect.width + shadow_size, penalise_rect.height + shadow_size), pygame.SRCALPHA)
            pygame.draw.rect(shadow_surface, shadow_color, (0, 0, penalise_rect.width + shadow_size, penalise_rect.height + shadow_size), border_radius=15)
            screen.blit(shadow_surface, (penalise_rect.x - shadow_size//2, penalise_rect.y + shadow_size//2))

            # Button text
            shadow_text = button_font.render("⛔ Penalise Player", True, shadow_color)
            shadow_rect = shadow_text.get_rect(center=(penalise_rect.centerx + shadow_offset, penalise_rect.centery + shadow_offset))
            screen.blit(shadow_text, shadow_rect)

            penalise_text = button_font.render("⛔ Penalise Player", True, (255, 220, 220))
            penalise_text_rect = penalise_text.get_rect(center=penalise_rect.center)
            screen.blit(penalise_text, penalise_text_rect)

            # Store hit area
            self.modal_hit_areas["penalise_player"] = penalise_rect

            # Update current x position
            current_x += button_width + button_spacing

        # Draw Resume button if needed
        if show_resume_button:
            resume_rect = Rect(
                current_x,
                footer_rect.centery - button_height // 2 + 6,
                button_width,
                button_height
            )

            # Blue gradient for resume button
            resume_color1 = (60, 60, 100)  # Blue-purple
            resume_color2 = (80, 80, 130)  # Lighter blue-purple

            self.draw_gradient_rect(
                screen,
                resume_rect,
                resume_color1,
                resume_color2,
                12  # Rounded corners
            )

            # Add shadow for depth
            shadow_surface = pygame.Surface((resume_rect.width + shadow_size, resume_rect.height + shadow_size), pygame.SRCALPHA)
            pygame.draw.rect(shadow_surface, shadow_color, (0, 0, resume_rect.width + shadow_size, resume_rect.height + shadow_size), border_radius=15)
            screen.blit(shadow_surface, (resume_rect.x - shadow_size//2, resume_rect.y + shadow_size//2))

            # Button text
            button_text = "Resume Game"
            button_icon = "▶️"

            # Customize button text based on claim type
            if hasattr(self.game, 'game_state') and hasattr(self.game.game_state, 'claim_type'):
                claim_type = self.game.game_state.claim_type
                if claim_type == "no_pattern":
                    button_text = "Resume Game"
                    button_icon = "▶️"
                elif claim_type == "not_registered":
                    button_text = "Return to Game"
                    button_icon = "↩️"
                elif claim_type == "late":
                    button_text = "Continue Playing"
                    button_icon = "▶️"

            shadow_text = button_font.render(f"{button_icon} {button_text}", True, shadow_color)
            shadow_rect = shadow_text.get_rect(center=(resume_rect.centerx + shadow_offset, resume_rect.centery + shadow_offset))
            screen.blit(shadow_text, shadow_rect)

            resume_text = button_font.render(f"{button_icon} {button_text}", True, (255, 255, 255))
            resume_text_rect = resume_text.get_rect(center=resume_rect.center)
            screen.blit(resume_text, resume_text_rect)

            # Store hit area
            self.modal_hit_areas["resume_game"] = resume_rect

        # Add subtle hover effect if mouse is over the button
        mouse_pos = pygame.mouse.get_pos()
        if resume_rect.collidepoint(mouse_pos):
            # Draw a subtle highlight border
            highlight_color = (255, 255, 255, 80)  # White with alpha
            highlight_surface = pygame.Surface((resume_rect.width + 4, resume_rect.height + 4), pygame.SRCALPHA)
            pygame.draw.rect(highlight_surface, highlight_color, (0, 0, resume_rect.width + 4, resume_rect.height + 4), 2, border_radius=14)
            screen.blit(highlight_surface, (resume_rect.x - 2, resume_rect.y - 2))

        self.modal_hit_areas["invalid_claim_resume"] = resume_rect

    def draw_reset_confirmation(self, screen):
        """Draw the reset confirmation overlay"""
        if not self.show_reset_confirmation:
            # CRITICAL FIX: Ensure reset confirmation buttons are removed from modal_hit_areas
            # This prevents accidental clicks on invisible buttons
            if "reset_confirm" in self.modal_hit_areas:
                del self.modal_hit_areas["reset_confirm"]
                print("CRITICAL FIX: Removed reset_confirm button from hit areas in draw_reset_confirmation")

            if "reset_cancel" in self.modal_hit_areas:
                del self.modal_hit_areas["reset_cancel"]
                print("CRITICAL FIX: Removed reset_cancel button from hit areas in draw_reset_confirmation")

            return

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create confirmation box
        box_width = int(screen_width * 0.4)
        box_height = int(screen_height * 0.35)  # Increase height for more text
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # CRITICAL FIX: Print the box dimensions for debugging
        print(f"CRITICAL FIX: Reset confirmation box dimensions: x={box_x}, y={box_y}, width={box_width}, height={box_height}")

        # Draw box with gradient - using consistent colors with main.py and Board_selection_fixed.py
        self.draw_gradient_rect(
            screen,
            Rect(box_x, box_y, box_width, box_height),
            (150, 50, 50),  # Red
            (170, 70, 70),  # Lighter red
            15  # Border radius
        )

        # Title text
        title_font = pygame.font.SysFont("Arial", int(30 * min(scale_x, scale_y)), bold=True)
        title_text = title_font.render("Reset Game?", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=box_x + box_width // 2, y=box_y + 20)
        screen.blit(title_text, title_rect)

        # Warning icon (exclamation mark)
        icon_size = int(40 * min(scale_x, scale_y))
        icon_x = box_x + (box_width // 2) - (icon_size // 2)
        icon_y = box_y + 60

        # Draw warning triangle
        warning_color = (255, 220, 50)  # Yellow
        pygame.draw.polygon(screen, warning_color, [
            (icon_x + icon_size//2, icon_y),
            (icon_x, icon_y + icon_size),
            (icon_x + icon_size, icon_y + icon_size)
        ])

        # Draw exclamation mark
        pygame.draw.rect(screen, (0, 0, 0),
                       (icon_x + icon_size//2 - 3, icon_y + 10,
                        6, icon_size//2), 0)
        pygame.draw.rect(screen, (0, 0, 0),
                       (icon_x + icon_size//2 - 3, icon_y + icon_size//2 + 15,
                        6, 6), 0)

        # Confirmation text - multiple lines with warning about data
        confirm_font = pygame.font.SysFont("Arial", int(18 * min(scale_x, scale_y)))
        warning_text = [
            "Warning: This will reset the game and DELETE ALL DATA:",
            "• All player information will be deleted",
            "• Current session data will be erased",
            "• Bingo boards will be regenerated",
            "This action cannot be undone!"
        ]

        # Draw each line of text
        for i, line in enumerate(warning_text):
            line_color = (255, 255, 255)
            if i == 0 or i == len(warning_text) - 1:
                # Highlight first and last lines
                line_font = pygame.font.SysFont("Arial", int(18 * min(scale_x, scale_y)), bold=True)
                line_color = (255, 220, 100)  # Yellowish
                text_surf = line_font.render(line, True, line_color)
            else:
                text_surf = confirm_font.render(line, True, line_color)

            text_rect = text_surf.get_rect(centerx=box_x + box_width // 2, y=box_y + 120 + i * 25)
            screen.blit(text_surf, text_rect)

        # Calculate button dimensions and positions
        button_width = int(box_width * 0.35)
        button_height = int(40 * min(scale_x, scale_y))
        button_spacing = int(20 * min(scale_x, scale_y))
        buttons_y = box_y + box_height - button_height - 20

        # CRITICAL FIX: Print button dimensions for debugging
        print(f"CRITICAL FIX: Reset confirmation button dimensions: width={button_width}, height={button_height}, spacing={button_spacing}, y={buttons_y}")

        # Confirm button
        confirm_button_rect = Rect(
            box_x + (box_width // 2) - button_width - (button_spacing // 2),
            buttons_y,
            button_width,
            button_height
        )

        # CRITICAL FIX: Print confirm button rect for debugging
        print(f"CRITICAL FIX: Confirm button rect: {confirm_button_rect}")

        # CRITICAL FIX: Draw a border around the confirm button to make it more visible
        pygame.draw.rect(screen, (255, 255, 255), confirm_button_rect, 2, border_radius=8)

        self.draw_gradient_rect(
            screen,
            confirm_button_rect,
            (150, 50, 50),  # Red - consistent with Board_selection_fixed.py
            (170, 70, 70),  # Lighter red - consistent with Board_selection_fixed.py
            8  # Border radius
        )
        button_font = pygame.font.SysFont("Arial", int(22 * min(scale_x, scale_y)), bold=True)
        confirm_button_text = button_font.render("Confirm", True, (255, 255, 255))
        confirm_button_text_rect = confirm_button_text.get_rect(center=confirm_button_rect.center)
        screen.blit(confirm_button_text, confirm_button_text_rect)

        # CRITICAL FIX: Store the confirm button rect in modal_hit_areas
        # Make sure to remove any existing entry first
        if "reset_confirm" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_confirm"]
        self.modal_hit_areas["reset_confirm"] = confirm_button_rect
        print(f"CRITICAL FIX: Added reset_confirm button to hit areas: {confirm_button_rect}")

        # Cancel button
        cancel_button_rect = Rect(
            box_x + (box_width // 2) + (button_spacing // 2),
            buttons_y,
            button_width,
            button_height
        )

        # CRITICAL FIX: Print cancel button rect for debugging
        print(f"CRITICAL FIX: Cancel button rect: {cancel_button_rect}")

        # CRITICAL FIX: Draw a border around the cancel button to make it more visible
        pygame.draw.rect(screen, (255, 255, 255), cancel_button_rect, 2, border_radius=8)

        self.draw_gradient_rect(
            screen,
            cancel_button_rect,
            (70, 70, 80),  # Dark gray - consistent with Board_selection_fixed.py
            (100, 100, 110),  # Lighter gray - consistent with Board_selection_fixed.py
            8  # Border radius
        )
        cancel_button_text = button_font.render("Cancel", True, (255, 255, 255))
        cancel_button_text_rect = cancel_button_text.get_rect(center=cancel_button_rect.center)
        screen.blit(cancel_button_text, cancel_button_text_rect)

        # CRITICAL FIX: Store the cancel button rect in modal_hit_areas
        # Make sure to remove any existing entry first
        if "reset_cancel" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_cancel"]
        self.modal_hit_areas["reset_cancel"] = cancel_button_rect
        print(f"CRITICAL FIX: Added reset_cancel button to hit areas: {cancel_button_rect}")

        # CRITICAL FIX: Print all modal hit areas for debugging
        print(f"CRITICAL FIX: All modal hit areas: {self.modal_hit_areas.keys()}")

    def draw_gradient_rect(self, surface, rect, color1, color2, border_radius=0):
        """
        Draw a rectangle with a vertical gradient from color1 to color2

        Args:
            surface: pygame surface to draw on
            rect: pygame Rect defining the rectangle
            color1: RGB tuple for top color
            color2: RGB tuple for bottom color
            border_radius: radius for rounded corners
        """
        # Create a surface with per-pixel alpha
        width, height = rect.width, rect.height
        surface_rect = pygame.Surface((width, height), pygame.SRCALPHA)

        # Draw gradient
        for y in range(height):
            # Calculate color for this row by linear interpolation
            ratio = y / height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            pygame.draw.line(surface_rect, (r, g, b), (0, y), (width, y))

        # Create a rounded rect mask if border_radius > 0
        if border_radius > 0:
            mask = pygame.Surface((width, height), pygame.SRCALPHA)
            mask.fill((0, 0, 0, 0))
            pygame.draw.rect(mask, (255, 255, 255, 255), (0, 0, width, height), border_radius=border_radius)
            surface_rect.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Blit to the target surface
        surface.blit(surface_rect, rect.topleft)

    def handle_input(self, event):
        """Handle keyboard input for the pause reason prompt"""
        if not self.reason_input_active:
            return False

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                # Submit the reason
                print(f"Enter key pressed - submitting reason: {self.reason_input_text}")
                if hasattr(self.game, 'game_state'):
                    # Play click sound for better feedback
                    if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                        self.game.button_click_sound.play()

                    # Handle the pause reason
                    self.game.game_state.handle_pause_reason(self.reason_input_text)
                    self.reason_input_text = ""
                    self.reason_input_active = False
                    # Reset the pause prompt flag so it will auto-focus next time
                    if hasattr(self, 'pause_prompt_just_opened'):
                        self.pause_prompt_just_opened = False
                    return True
            elif event.key == pygame.K_BACKSPACE:
                # Remove the last character
                self.reason_input_text = self.reason_input_text[:-1]
                return True
            elif event.unicode.isdigit() and len(self.reason_input_text) < 4:
                # Only allow digits up to 4 characters (max 1200)
                self.reason_input_text += event.unicode
                return True

        return False

    def check_modal_button_click(self, pos):
        """Check if any modal button was clicked and handle the action"""
        button_clicked = False

        # CRITICAL FIX: Add debug logging for fullscreen mode
        is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
        if is_fullscreen:
            print(f"FULLSCREEN MODE: Processing click at position {pos}")
            print(f"Screen dimensions: {pygame.display.get_surface().get_size()}")

        # CRITICAL FIX: Check if reset confirmation dialog is showing
        if self.show_reset_confirmation:
            print(f"CRITICAL FIX: Reset confirmation dialog is showing")
            print(f"CRITICAL FIX: Checking if click at {pos} is on reset confirmation buttons")
            print(f"CRITICAL FIX: All modal hit areas: {self.modal_hit_areas.keys()}")

            # CRITICAL FIX: Get screen dimensions
            screen_width, screen_height = pygame.display.get_surface().get_size()

            # CRITICAL FIX: Calculate confirmation box dimensions
            box_width = int(screen_width * 0.4)
            box_height = int(screen_height * 0.35)
            box_x = (screen_width - box_width) // 2
            box_y = (screen_height - box_height) // 2

            # CRITICAL FIX: Get scale factors using the get_scale_factors method
            scale_x, scale_y = self.get_scale_factors()
            print(f"CRITICAL FIX: Using scale factors: scale_x={scale_x}, scale_y={scale_y}")

            # CRITICAL FIX: Calculate button dimensions
            button_width = int(box_width * 0.35)
            button_height = int(40 * min(scale_x, scale_y))
            button_spacing = int(20 * min(scale_x, scale_y))
            buttons_y = box_y + box_height - button_height - 20

            # CRITICAL FIX: Calculate confirm button rect
            confirm_button_rect = pygame.Rect(
                box_x + (box_width // 2) - button_width - (button_spacing // 2),
                buttons_y,
                button_width,
                button_height
            )

            # CRITICAL FIX: Calculate cancel button rect
            cancel_button_rect = pygame.Rect(
                box_x + (box_width // 2) + (button_spacing // 2),
                buttons_y,
                button_width,
                button_height
            )

            # CRITICAL FIX: Print button rects for debugging
            print(f"CRITICAL FIX: Confirm button rect: {confirm_button_rect}")
            print(f"CRITICAL FIX: Cancel button rect: {cancel_button_rect}")

            # CRITICAL FIX: Check if the click is on the confirm button
            if confirm_button_rect.collidepoint(pos):
                print(f"CRITICAL FIX: Click detected on reset_confirm button!")
                # Play click sound
                if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                    self.game.button_click_sound.play()

                # Handle reset confirmation
                self._handle_reset_confirm_click(pos, confirm_button_rect)
                return True

            # CRITICAL FIX: Check if the click is on the cancel button
            if cancel_button_rect.collidepoint(pos):
                print(f"CRITICAL FIX: Click detected on reset_cancel button!")
                # Play click sound
                if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                    self.game.button_click_sound.play()

                # Handle reset cancellation
                self._handle_reset_cancel_click(pos, cancel_button_rect)
                return True

            # CRITICAL FIX: Also check the modal_hit_areas dictionary for backward compatibility
            # Check if the click is on the confirm button
            if "reset_confirm" in self.modal_hit_areas:
                confirm_rect = self.modal_hit_areas["reset_confirm"]
                print(f"CRITICAL FIX: Checking reset_confirm button from modal_hit_areas at {confirm_rect}")
                if confirm_rect.collidepoint(pos):
                    print(f"CRITICAL FIX: Click detected on reset_confirm button from modal_hit_areas!")
                    # Play click sound
                    if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                        self.game.button_click_sound.play()

                    # Handle reset confirmation
                    self._handle_reset_confirm_click(pos, confirm_rect)
                    return True

            # Check if the click is on the cancel button
            if "reset_cancel" in self.modal_hit_areas:
                cancel_rect = self.modal_hit_areas["reset_cancel"]
                print(f"CRITICAL FIX: Checking reset_cancel button from modal_hit_areas at {cancel_rect}")
                if cancel_rect.collidepoint(pos):
                    print(f"CRITICAL FIX: Click detected on reset_cancel button from modal_hit_areas!")
                    # Play click sound
                    if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                        self.game.button_click_sound.play()

                    # Handle reset cancellation
                    self._handle_reset_cancel_click(pos, cancel_rect)
                    return True

        # Check for exit confirmation dialog buttons
        if self.show_exit_confirmation:
            # Check if the click is on the confirm button
            if "exit_confirm" in self.modal_hit_areas:
                confirm_rect = self.modal_hit_areas["exit_confirm"]
                if confirm_rect.collidepoint(pos):
                    print("Click detected on exit_confirm button!")
                    # Play click sound
                    if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                        self.game.button_click_sound.play()

                    # Handle exit confirmation
                    self._handle_exit_confirm_click()
                    return True

            # Check if the click is on the cancel button
            if "exit_cancel" in self.modal_hit_areas:
                cancel_rect = self.modal_hit_areas["exit_cancel"]
                if cancel_rect.collidepoint(pos):
                    print("Click detected on exit_cancel button!")
                    # Play click sound
                    if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                        self.game.button_click_sound.play()

                    # Handle exit cancellation
                    self._handle_exit_cancel_click()
                    return True

        # Special case for pause reason prompt: first check if the input field was clicked
        if hasattr(self.game, 'game_state') and self.game.game_state.show_pause_reason_prompt:
            input_rect = self.modal_hit_areas.get("pause_reason_input")
            if input_rect and input_rect.collidepoint(pos):
                self.reason_input_active = True
                # Reset cursor blink
                self.reason_cursor_visible = True
                self.reason_cursor_timer = 0
                button_clicked = True
                if is_fullscreen:
                    print(f"FULLSCREEN MODE: Input field clicked at {pos}, rect: {input_rect}")

        # CRITICAL FIX: Create a copy of the modal_hit_areas dictionary to prevent
        # "dictionary changed size during iteration" error
        modal_hit_areas_copy = dict(self.modal_hit_areas)

        # Process other buttons
        for key, rect in modal_hit_areas_copy.items():
            # CRITICAL FIX: Add extra debug logging for fullscreen mode
            if is_fullscreen and key in ["pause_resume", "pause_reset", "resume_game", "reset_confirm", "reset_cancel"]:
                print(f"FULLSCREEN MODE: Checking button '{key}' at rect {rect} against click at {pos}")
                print(f"FULLSCREEN MODE: Collision result: {rect.collidepoint(pos)}")

            if rect.collidepoint(pos):
                # Play click sound first before any action
                if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
                    self.game.button_click_sound.play()

                # Pause reason input field
                if key == "pause_reason_input":
                    self.reason_input_active = True
                    # Reset cursor blink
                    self.reason_cursor_visible = True
                    self.reason_cursor_timer = 0
                    button_clicked = True

                # Pause reason submit button
                elif key == "pause_reason_submit" and hasattr(self.game, 'game_state'):
                    self.game.game_state.handle_pause_reason(self.reason_input_text)
                    self.reason_input_text = ""
                    self.reason_input_active = False
                    # Reset the pause prompt flag so it will auto-focus next time
                    if hasattr(self, 'pause_prompt_just_opened'):
                        self.pause_prompt_just_opened = False
                    button_clicked = True

                # Admin control buttons (legacy)
                elif key == "admin_resume" and hasattr(self.game, 'game_state'):
                    self.game.game_state.show_admin_control = False
                    # Use the new resume method with audio
                    if hasattr(self.game, 'resume_game_with_audio'):
                        self.game.resume_game_with_audio()
                    else:
                        # Fallback to old method if new one doesn't exist
                        self.game.game_state.resume_game()
                    button_clicked = True

                elif key == "admin_reset" and hasattr(self.game, 'game_state'):
                    # Show reset confirmation dialog instead of immediately resetting
                    self.show_reset_confirmation = True
                    button_clicked = True

                elif key == "admin_close" and hasattr(self.game, 'game_state'):
                    self.game.game_state.close_admin_control()
                    button_clicked = True

                # Pause reason window admin buttons
                elif key == "pause_resume" and hasattr(self.game, 'game_state'):
                    print("RESUME BUTTON CLICKED - This should ONLY resume the game, never reset it")
                    print(f"Button rect: {rect}, Click position: {pos}")
                    print(f"Is fullscreen: {bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)}")

                    # CRITICAL FIX: Check if buttons are locked
                    reset_locked, resume_locked = self.check_button_locks()
                    if resume_locked:
                        print("CRITICAL FIX: Resume button is locked, ignoring click")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Lock the reset button to prevent conflicts
                    self.reset_button_locked = True
                    self.button_lock_time = pygame.time.get_ticks()
                    print("CRITICAL FIX: Locked reset button to prevent conflicts")

                    # CRITICAL FIX: Check if we're in a post-reset-cancel state
                    # If so, we need to take special precautions to prevent resetting the game
                    if self.check_post_reset_cancel_state():
                        print("CRITICAL FIX: We're in a post-reset-cancel state")
                        print("CRITICAL FIX: Ignoring resume button click to prevent accidental reset")
                        print("Please wait a few seconds before trying to resume the game")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Check if reset was recently canceled
                    # We still allow the button to be clicked, but we'll take extra precautions
                    if self.check_reset_recently_canceled():
                        print("CRITICAL FIX: Reset was recently canceled, but allowing resume functionality")
                        print("This prevents accidental game reset while still allowing resume")
                        # Force the game_started flag to true to prevent accidental reset
                        if hasattr(self.game, 'game_started'):
                            self.game.game_started = True

                    # CRITICAL FIX: Verify that the game is actually started and paused before resuming
                    # This prevents accidentally resetting the game when it's not actually running
                    if not hasattr(self.game, 'game_started') or not self.game.game_started:
                        print("CRITICAL ERROR: Attempted to resume a game that hasn't been started!")
                        print("This could cause the game to reset instead of resume.")
                        # Don't proceed with resume if the game isn't started
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Verify that the game is actually paused before resuming
                    if not hasattr(self.game.game_state, 'is_paused') or not self.game.game_state.is_paused:
                        print("CRITICAL ERROR: Attempted to resume a game that isn't paused!")
                        print("This could cause the game to reset instead of resume.")
                        # Don't proceed with resume if the game isn't paused
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Add additional safety check for fullscreen mode
                    # In fullscreen mode, we need to be extra careful about button clicks
                    is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
                    if is_fullscreen:
                        # Double-check that we're actually clicking the button
                        # This helps prevent accidental clicks in fullscreen mode
                        if not rect.collidepoint(pos):
                            print("CRITICAL ERROR: Button click position doesn't match button rect in fullscreen mode!")
                            print(f"Button rect: {rect}, Click position: {pos}")
                            button_clicked = True
                            return button_clicked

                    # CRITICAL FIX: Check if reset confirmation is showing and cancel it
                    if self.show_reset_confirmation:
                        print("CRITICAL FIX: Reset confirmation dialog was open when resume was clicked")
                        print("Closing reset confirmation dialog without resetting the game")
                        self.show_reset_confirmation = False

                        # CRITICAL FIX: Remove reset confirmation buttons from modal_hit_areas
                        # This prevents accidental clicks on invisible buttons
                        if "reset_confirm" in self.modal_hit_areas:
                            del self.modal_hit_areas["reset_confirm"]
                            print("CRITICAL FIX: Removed reset_confirm button from hit areas")

                        if "reset_cancel" in self.modal_hit_areas:
                            del self.modal_hit_areas["reset_cancel"]
                            print("CRITICAL FIX: Removed reset_cancel button from hit areas")

                        # Set the reset_recently_canceled flag to prevent accidental resets
                        self.reset_recently_canceled = True
                        self.reset_cancel_time = pygame.time.get_ticks()
                        print("CRITICAL FIX: Set reset_recently_canceled flag to prevent accidental resets")

                        # Don't proceed with resume if we just closed the reset confirmation
                        button_clicked = True
                        return button_clicked

                    # First hide the pause reason window immediately
                    self.game.game_state.show_pause_reason_prompt = False

                    # IMPORTANT: Make sure we're not accidentally resetting the game
                    # We should ONLY be resuming here, never resetting

                    # CRITICAL FIX: Add additional safety check before resuming
                    try:
                        # Then start the audio and resume process in the background
                        if hasattr(self.game, 'resume_game_with_audio'):
                            # Play the audio but don't wait for it
                            if hasattr(self.game, 'game_started_sound') and self.game.game_started_sound and hasattr(self.game, 'announcement_channel'):
                                # Stop any currently playing announcements
                                self.game.announcement_channel.stop()
                                # Play the game started announcement on the dedicated channel
                                self.game.announcement_channel.play(self.game.game_started_sound)

                            # Resume the game immediately without waiting for audio
                            print("Calling resume_game_with_audio() - this should ONLY resume the game")
                            self.game.game_state.resume_game()
                        else:
                            # Fallback to old method if new one doesn't exist
                            print("Calling resume_game() - this should ONLY resume the game")
                            self.game.game_state.resume_game()
                    except Exception as e:
                        print(f"ERROR resuming game: {e}")
                        print("This might indicate a deeper issue with the game state.")
                        # Don't let exceptions propagate - we want to keep the game running

                    button_clicked = True

                elif key == "pause_reset" and hasattr(self.game, 'game_state'):
                    print("RESET BUTTON CLICKED - Showing reset confirmation dialog")
                    print(f"Button rect: {rect}, Click position: {pos}")
                    print(f"Is fullscreen: {bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)}")

                    # CRITICAL FIX: Check if buttons are locked
                    reset_locked, resume_locked = self.check_button_locks()
                    if reset_locked:
                        print("CRITICAL FIX: Reset button is locked, ignoring click")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Lock the resume button to prevent conflicts
                    self.resume_button_locked = True
                    self.button_lock_time = pygame.time.get_ticks()
                    print("CRITICAL FIX: Locked resume button to prevent conflicts")

                    # CRITICAL FIX: Add additional safety check for fullscreen mode
                    # In fullscreen mode, we need to be extra careful about button clicks
                    is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
                    if is_fullscreen:
                        # Double-check that we're actually clicking the button
                        # This helps prevent accidental clicks in fullscreen mode
                        if not rect.collidepoint(pos):
                            print("CRITICAL ERROR: Button click position doesn't match button rect in fullscreen mode!")
                            print(f"Button rect: {rect}, Click position: {pos}")
                            button_clicked = True
                            return button_clicked

                    # Show reset confirmation dialog instead of immediately resetting
                    self.show_reset_confirmation = True
                    button_clicked = True

                # Reset confirmation dialog buttons
                elif key == "reset_confirm" and hasattr(self.game, 'game_state'):
                    # CRITICAL FIX: Ensure the reset confirmation dialog is actually showing
                    # This prevents accidental clicks on invisible buttons
                    if not self.show_reset_confirmation:
                        print("CRITICAL ERROR: Reset confirm button clicked but dialog is not showing!")
                        print("Ignoring this click to prevent accidental reset")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Add additional safety check for fullscreen mode
                    # In fullscreen mode, we need to be extra careful about button clicks
                    is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
                    if is_fullscreen:
                        # Double-check that we're actually clicking the button
                        # This helps prevent accidental clicks in fullscreen mode
                        if not rect.collidepoint(pos):
                            print("CRITICAL ERROR: Button click position doesn't match button rect in fullscreen mode!")
                            print(f"Button rect: {rect}, Click position: {pos}")
                            button_clicked = True
                            return button_clicked

                    # Call the dedicated handler method
                    self._handle_reset_confirm_click(pos, rect)
                    button_clicked = True

                elif key == "reset_cancel" and hasattr(self.game, 'game_state'):
                    # CRITICAL FIX: Ensure the reset confirmation dialog is actually showing
                    # This prevents accidental clicks on invisible buttons
                    if not self.show_reset_confirmation:
                        print("CRITICAL ERROR: Reset cancel button clicked but dialog is not showing!")
                        print("Ignoring this click to prevent accidental actions")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Add additional safety check for fullscreen mode
                    is_fullscreen = bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)
                    if is_fullscreen:
                        # Double-check that we're actually clicking the button
                        if not rect.collidepoint(pos):
                            print("CRITICAL ERROR: Button click position doesn't match button rect in fullscreen mode!")
                            print(f"Button rect: {rect}, Click position: {pos}")
                            button_clicked = True
                            return button_clicked

                    # Call the dedicated handler method
                    self._handle_reset_cancel_click(pos, rect)
                    button_clicked = True

                # Winner/invalid claim buttons
                elif key == "winner_close" and hasattr(self.game, 'game_state'):
                    self.game.game_state.close_validation_display()
                    button_clicked = True

                elif key == "resume_game" and hasattr(self.game, 'game_state'):
                    # Reset the logging flag when closing the missed winner display
                    if hasattr(self, '_logged_winning_patterns'):
                        delattr(self, '_logged_winning_patterns')

                    # CRITICAL FIX: Check if we're in a post-reset-cancel state
                    # If so, we need to take special precautions to prevent resetting the game
                    if self.check_post_reset_cancel_state():
                        print("CRITICAL FIX: We're in a post-reset-cancel state")
                        print("CRITICAL FIX: Ignoring resume_game button click to prevent accidental reset")
                        print("Please wait a few seconds before trying to resume the game")
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Check if reset was recently canceled
                    # We still allow the button to be clicked, but we'll take extra precautions
                    if self.check_reset_recently_canceled():
                        print("CRITICAL FIX: Reset was recently canceled, but allowing resume functionality")
                        print("This prevents accidental game reset while still allowing resume")
                        # Force the game_started flag to true to prevent accidental reset
                        if hasattr(self.game, 'game_started'):
                            self.game.game_started = True

                    # CRITICAL FIX: Check if reset confirmation is showing and cancel it
                    if self.show_reset_confirmation:
                        print("CRITICAL FIX: Reset confirmation dialog was open when resume_game was clicked")
                        print("Closing reset confirmation dialog without resetting the game")
                        self.show_reset_confirmation = False

                        # Set the reset_recently_canceled flag to prevent accidental resets
                        self.reset_recently_canceled = True
                        self.reset_cancel_time = pygame.time.get_ticks()
                        print("CRITICAL FIX: Set reset_recently_canceled flag to prevent accidental resets")

                        # Don't proceed with resume if we just closed the reset confirmation
                        button_clicked = True
                        return button_clicked

                    # CRITICAL FIX: Add additional safety check before closing validation display
                    try:
                        print("Calling close_validation_display() - This should safely close the validation window")
                        self.game.game_state.close_validation_display()
                    except Exception as e:
                        print(f"ERROR closing validation display: {e}")
                        print("This might indicate a deeper issue with the game state.")
                        # Don't let exceptions propagate - we want to keep the game running

                    button_clicked = True

                elif key == "confirm_winner" and hasattr(self.game, 'game_state'):
                    self.game.game_state.close_validation_display()
                    button_clicked = True

                elif key == "penalise_player" and hasattr(self.game, 'game_state'):
                    # Check if this is a valid claim type for penalization (not unregistered)
                    valid_for_penalization = True

                    # Check claim_type attribute first
                    if hasattr(self.game.game_state, 'claim_type'):
                        if self.game.game_state.claim_type == "not_registered":
                            valid_for_penalization = False

                    # Also check invalid_claim_reason as a backup
                    elif hasattr(self.game.game_state, 'invalid_claim_reason'):
                        if "not registered" in self.game.game_state.invalid_claim_reason.lower():
                            valid_for_penalization = False

                    # Play the penalization sound for valid claim types
                    if valid_for_penalization and hasattr(self.game, 'play_penalize_sound'):
                        self.game.play_penalize_sound()
                        print("Playing penalization sound for valid claim type")

                    # Penalise the player by removing them from the current game session
                    if hasattr(self.game.game_state, 'player_claim_cartella'):
                        # CRITICAL FIX: Call the penalise_player method which now handles all the cleanup
                        # This ensures the game is properly resumed after penalization
                        print("CRITICAL FIX: Calling penalise_player with comprehensive cleanup")
                        self.game.game_state.penalise_player(self.game.game_state.player_claim_cartella)

                        # CRITICAL FIX: Force a redraw of the screen to ensure the game is properly resumed
                        if hasattr(self.game, 'force_redraw'):
                            self.game.force_redraw = True
                            print("CRITICAL FIX: Forced redraw after penalization")

                    button_clicked = True

                elif key == "check_next_claim" and hasattr(self.game, 'game_state'):
                    # Reset the logging flag when closing the missed winner display
                    if hasattr(self, '_logged_winning_patterns'):
                        delattr(self, '_logged_winning_patterns')
                    # Close the current claim window and return to the pause reason prompt
                    self.game.game_state.check_next_claim()
                    button_clicked = True

        return button_clicked

    def update_cursor_blink(self, time_passed):
        """Update the cursor blink state for input fields"""
        if self.reason_input_active:
            self.reason_cursor_timer += time_passed
            if self.reason_cursor_timer >= 500:  # Blink every 500ms
                self.reason_cursor_visible = not self.reason_cursor_visible
                self.reason_cursor_timer = 0
                return True

        return False

    def check_reset_recently_canceled(self):
        """
        Check if reset was recently canceled and clear the flag after a timeout

        Returns:
            bool: True if reset was recently canceled and the timeout hasn't expired
        """
        if not self.reset_recently_canceled:
            return False

        # CRITICAL FIX: If the reset_cancel_time is 0, immediately clear the flag
        # This handles cases where the timer wasn't properly initialized
        if self.reset_cancel_time == 0:
            if self.reset_recently_canceled:
                self.reset_recently_canceled = False
                print("CRITICAL FIX: Cleared reset_recently_canceled flag with zero timer")
            return False

        # Check if enough time has passed since the reset was canceled
        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.reset_cancel_time

        # If more than 500ms have passed, clear the flag
        # Reduced from 5 seconds to 500ms to minimize button unresponsiveness
        if elapsed > 500:  # 500ms timeout
            self.reset_recently_canceled = False
            print("CRITICAL FIX: Cleared reset_recently_canceled flag after timeout")
            return False

        # CRITICAL FIX: Log the remaining time for debugging
        remaining = 500 - elapsed
        if remaining > 0 and remaining % 100 < 10:  # Log approximately every 100ms
            print(f"CRITICAL FIX: Reset recently canceled state active, {remaining}ms remaining")

        # Reset was recently canceled and timeout hasn't expired
        # We still return True to indicate caution, but the button handlers
        # have been updated to allow functionality while taking precautions
        return True

    def check_button_locks(self):
        """
        Check if buttons are locked and unlock them after a timeout

        Returns:
            tuple: (reset_locked, resume_locked) indicating if each button is locked
        """
        if not self.reset_button_locked and not self.resume_button_locked:
            return False, False

        # Check if enough time has passed since the buttons were locked
        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.button_lock_time

        # CRITICAL FIX: If the button_lock_time is 0, immediately unlock both buttons
        # This handles cases where the lock timer wasn't properly initialized
        if self.button_lock_time == 0:
            was_locked = False
            if self.reset_button_locked:
                self.reset_button_locked = False
                was_locked = True
                print("CRITICAL FIX: Unlocked reset button with zero lock time")

            if self.resume_button_locked:
                self.resume_button_locked = False
                was_locked = True
                print("CRITICAL FIX: Unlocked resume button with zero lock time")

            if was_locked:
                # Set a valid lock time to prevent immediate re-locking
                self.button_lock_time = current_time - self.button_lock_duration - 1

            return False, False

        # If more than the lock duration has passed, clear the locks
        if elapsed > self.button_lock_duration:
            if self.reset_button_locked:
                self.reset_button_locked = False
                print("CRITICAL FIX: Unlocked reset button after timeout")

            if self.resume_button_locked:
                self.resume_button_locked = False
                print("CRITICAL FIX: Unlocked resume button after timeout")

        # Return the current lock states
        return self.reset_button_locked, self.resume_button_locked

    def _handle_reset_confirm_click(self, pos, rect):
        """Handle a click on the reset confirm button"""
        print("RESET CONFIRMED - This should reset the game and return to board selection")
        print(f"Button rect: {rect}, Click position: {pos}")
        print(f"Is fullscreen: {bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)}")

        # CRITICAL FIX: Play click sound to provide feedback
        if hasattr(self.game, 'button_click_sound') and self.game.button_click_sound:
            self.game.button_click_sound.play()

        # CRITICAL FIX: Play clear announce sound for game reset
        if hasattr(self.game, 'clear_announce_sound') and self.game.clear_announce_sound:
            self.game.clear_announce_sound.play()
            print("CRITICAL FIX: Playing clear announce sound for game reset")

        # CRITICAL FIX: Store the reset confirmation state before closing the dialog
        # This ensures we can properly handle the reset process
        reset_confirmation_was_showing = self.show_reset_confirmation
        print(f"CRITICAL FIX: Reset confirmation was showing: {reset_confirmation_was_showing}")

        # Close the confirmation dialog first
        self.show_reset_confirmation = False
        if hasattr(self.game, 'game_state'):
            self.game.game_state.show_admin_control = False

            # Also close the pause reason prompt if it's open
            self.game.game_state.show_pause_reason_prompt = False

        # CRITICAL FIX: Remove reset confirmation buttons from modal_hit_areas
        # This prevents accidental clicks on invisible buttons
        if "reset_confirm" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_confirm"]
            print("CRITICAL FIX: Removed reset_confirm button from hit areas")

        if "reset_cancel" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_cancel"]
            print("CRITICAL FIX: Removed reset_cancel button from hit areas")

        # CRITICAL FIX: This section is now handled in the _handle_reset_confirm_click method
        # to ensure the sound is played before any other actions are taken

        # First, if the game has a usage_tracker, end the game tracking
        if hasattr(self.game, 'usage_tracker'):
            try:
                # Check if game is started and active
                game_started = hasattr(self.game, 'game_started') and self.game.game_started
                usage_tracker_active = self.game.usage_tracker.active
                print(f"Game started: {game_started}, Usage tracker active: {usage_tracker_active}")

                # CRITICAL FIX: Ensure usage tracker is active before resetting
                if hasattr(self.game, 'ensure_usage_tracker_active'):
                    print("CRITICAL FIX: Calling ensure_usage_tracker_active method before reset")
                    self.game.ensure_usage_tracker_active()
                elif game_started and not usage_tracker_active:
                    print("CRITICAL FIX: Game is started but usage tracker is not active. Activating it now.")
                    self.game.usage_tracker.active = True
                    self.game.usage_tracker.game_start_time = time.time() - 60  # Assume game has been running for at least 1 minute
                    self.game.usage_tracker.current_game_id = f"game_{int(time.time())}"
                    print(f"Usage tracker activated: active={self.game.usage_tracker.active}, game_id={self.game.usage_tracker.current_game_id}")

                # Get the most recent voucher's share percentage
                voucher_history = self.game.usage_tracker.voucher_manager.get_voucher_history(1)
                share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

                # Get commission percentage from game settings
                if hasattr(self.game, 'commission_percentage'):
                    commission_percentage = self.game.commission_percentage
                elif hasattr(self.game, 'settings') and isinstance(self.game.settings, dict):
                    commission_percentage = self.game.settings.get('game', {}).get('commission_percentage', 20)
                else:
                    commission_percentage = 20  # Default commission percentage

                # Get total bets from game
                total_bets = 0
                if hasattr(self.game, 'bet_amount') and hasattr(self.game, 'players'):
                    total_bets = self.game.bet_amount * len(self.game.players)

                # CRITICAL FIX: Set a minimum total bets amount to ensure credits are deducted
                # This is needed because the usage tracker calculates credits based on total bets
                min_total_bets = 100

                # Use the maximum of current total bets and minimum total bets
                forced_total_bets = max(total_bets, min_total_bets)
                print(f"CRITICAL FIX: Using total bets amount: {forced_total_bets} (current: {total_bets}, minimum: {min_total_bets})")

                # End tracking with the new parameters
                result = self.game.usage_tracker.end_game(
                    share_percentage=share_percentage,
                    total_bets=forced_total_bets,
                    commission_percentage=commission_percentage
                )

                # Show detailed message if tracking was active
                if result.get('success', False):
                    if result['credits_used'] > 0:
                        # Create a detailed message showing the calculation
                        message = (
                            f"Game completed. Commission: {result['referee_commission']:.2f} ETB ({result['commission_percentage']}%) | "
                            f"Share: {result['share_percentage']}% | "
                            f"Deducted: {result['credits_used']} credits | "
                            f"Balance: {result['new_balance']} credits"
                        )
                    else:
                        message = f"Game completed. No credits deducted. Balance: {result['new_balance']} credits"

                    self.game.message = message
                    self.game.message_type = "info"
                    self.game.message_timer = 300  # 5 seconds at 60 FPS

                    # Print to console for debugging
                    print(f"Credit deduction completed: {message}")
            except Exception as e:
                print(f"Error ending game tracking: {e}")
                import traceback
                traceback.print_exc()

        # CRITICAL FIX: Ensure the game is properly reset
        # First, make sure the game is in a state that can be reset
        print("CRITICAL FIX: Ensuring game is in a proper state for reset")

        # Force the game_started flag to true to ensure reset_game() works properly
        if hasattr(self.game, 'game_started'):
            was_game_started = self.game.game_started
            print(f"CRITICAL FIX: Current game_started state: {was_game_started}")
            # Always set game_started to True before resetting to ensure proper reset
            self.game.game_started = True
            print("CRITICAL FIX: Setting game_started to True to ensure proper reset")

        # CRITICAL FIX: Make sure the bingo caller is initialized
        if not hasattr(self.game, 'bingo_caller') or self.game.bingo_caller is None:
            print("CRITICAL FIX: Initializing bingo caller before reset")
            if hasattr(self.game.game_state, 'setup_bingo_caller'):
                try:
                    self.game.game_state.setup_bingo_caller()
                    print("Successfully initialized bingo caller")
                except Exception as e:
                    print(f"Error initializing bingo caller: {e}")
                    # Continue with reset even if initialization fails

        # CRITICAL FIX: Stop the bingo caller if it's active
        if hasattr(self.game, 'bingo_caller') and self.game.bingo_caller:
            try:
                if hasattr(self.game.bingo_caller, 'active') and self.game.bingo_caller.active:
                    self.game.bingo_caller.stop_calling()
                    print("CRITICAL FIX: Stopped bingo caller before resetting game state")
            except Exception as e:
                print(f"Error stopping bingo caller: {e}")

        # CRITICAL FIX: Directly reset game state variables
        print("CRITICAL FIX: Directly resetting game state variables")
        self.game.called_numbers = []
        self.game.current_number = None
        self.game.game_started = False

        # Reset the Bingo Favor Mode if it's active
        if hasattr(self.game, 'favor_mode'):
            self.game.favor_mode.reset()
            print("CRITICAL FIX: Reset Bingo Favor Mode")

        # Call reset_game - it will always return True now
        print("Calling game_state.reset_game() - This should reset the game state")
        should_return_to_board_selection = self.game.game_state.reset_game()
        print(f"Reset game returned: {should_return_to_board_selection} (should always be True)")

        # CRITICAL: Make sure we're not in demo mode
        if hasattr(self.game, 'is_demo_mode'):
            self.game.is_demo_mode = False
            print("Explicitly set is_demo_mode to False to prevent demo mode after reset")

        # CRITICAL FIX: Force a redraw of the screen to ensure UI is updated
        if hasattr(self.game, 'force_redraw'):
            self.game.force_redraw = True
            print("CRITICAL FIX: Forced redraw after reset")

        # CRITICAL FIX: Clear all JSON data before returning to board selection
        print("CRITICAL FIX: Clearing all JSON data before returning to board selection")
        try:
            from main import clear_all_json_data
            clear_result = clear_all_json_data()
            print(f"CRITICAL FIX: JSON data cleared: {clear_result}")
        except Exception as e:
            print(f"Error clearing JSON data: {str(e)}")
            import traceback
            traceback.print_exc()

        # Always attempt to return to board selection after reset
        print("Attempting to return to board selection after reset...")
        try:
            # Import the board selection module
            from Board_selection_fixed import show_board_selection
            print("Successfully imported Board_selection_fixed module")

            # Get the current screen
            screen = pygame.display.get_surface()
            print(f"Got current screen: {screen.get_size()}")

            # Use the global screen mode manager to get current screen mode
            try:
                from screen_mode_manager import get_screen_mode_manager
                screen_mode_manager = get_screen_mode_manager()
                is_fullscreen = screen_mode_manager.get_current_screen_mode()
                print(f"Current screen mode from settings: {'Fullscreen' if is_fullscreen else 'Windowed'}")
            except Exception as e:
                print(f"Error getting screen mode from manager: {e}")
                # Fallback to manual detection
                is_fullscreen = bool(screen.get_flags() & pygame.FULLSCREEN)
                print(f"Fallback screen mode detection: {'Fullscreen' if is_fullscreen else 'Windowed'}")

            # CRITICAL FIX: Force a redraw of the screen before showing board selection
            pygame.display.flip()
            print("CRITICAL FIX: Forced screen update before showing board selection")

            # CRITICAL FIX: Add a small delay to ensure the screen is updated
            import time
            time.sleep(0.1)
            print("CRITICAL FIX: Added small delay before showing board selection")

            # Show the board selection window with animation since we're coming from a reset
            # The board selection will use the global screen mode manager to ensure consistency
            print("Calling show_board_selection with from_reset=True...")
            selected_cartellas = show_board_selection(screen, from_reset=True, maintain_fullscreen=is_fullscreen)
            print(f"Board selection returned with selected cartellas: {selected_cartellas}")

            # Update game with selected cartella numbers if any were selected
            if selected_cartellas:
                # Update player list with selected cartella numbers
                from player_storage import load_players_from_json
                self.game.players = load_players_from_json()
                print(f"Loaded {len(self.game.players)} players from JSON")

                # Calculate prize pool based on players
                self.game.calculate_prize_pool()
                print(f"Recalculated prize pool: {self.game.prize_pool}")
        except Exception as e:
            print(f"Error returning to board selection: {str(e)}")
            import traceback
            traceback.print_exc()

            # Show error message
            if hasattr(self.game, 'message'):
                self.game.message = f"Error returning to board selection: {str(e)}"
                self.game.message_type = "error"
                self.game.message_timer = 180

    def _handle_reset_cancel_click(self, pos, rect):
        """Handle a click on the reset cancel button"""
        print("RESET CANCELLED - This should ONLY close the reset confirmation dialog")
        print(f"Button rect: {rect}, Click position: {pos}")
        print(f"Is fullscreen: {bool(pygame.display.get_surface().get_flags() & pygame.FULLSCREEN)}")

        # Close the confirmation dialog without resetting the game
        self.show_reset_confirmation = False

        # CRITICAL FIX: Remove reset confirmation buttons from modal_hit_areas
        # This prevents accidental clicks on invisible buttons
        if "reset_confirm" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_confirm"]
            print("CRITICAL FIX: Removed reset_confirm button from hit areas")

        if "reset_cancel" in self.modal_hit_areas:
            del self.modal_hit_areas["reset_cancel"]
            print("CRITICAL FIX: Removed reset_cancel button from hit areas")

        # CRITICAL FIX: Set the reset_recently_canceled flag to prevent accidental resets
        self.reset_recently_canceled = True
        self.reset_cancel_time = pygame.time.get_ticks()
        print("CRITICAL FIX: Set reset_recently_canceled flag to prevent accidental resets")

        # CRITICAL FIX: Set the post-reset-cancel state to prevent resume from resetting
        # Using a shorter duration (500ms) to minimize button unresponsiveness
        self.post_reset_cancel_state = True
        self.post_reset_cancel_time = pygame.time.get_ticks()
        print("CRITICAL FIX: Set post-reset-cancel state to prevent resume from resetting")

        # CRITICAL FIX: Immediately unlock both buttons when reset is canceled
        # This ensures buttons are responsive after canceling a reset
        self.reset_button_locked = False
        self.resume_button_locked = False
        self.button_lock_time = 0  # Reset the lock timer
        print("CRITICAL FIX: Immediately unlocked both buttons after reset cancellation")

        # CRITICAL FIX: Make sure we're not accidentally resetting the game
        # We should ONLY be closing the dialog here, never resetting
        print("Reset confirmation dialog closed without resetting the game")

    def check_post_reset_cancel_state(self):
        """
        Check if we're in a post-reset-cancel state and clear the flag after a timeout

        Returns:
            bool: True if we're in a post-reset-cancel state and the timeout hasn't expired
        """
        if not self.post_reset_cancel_state:
            return False

        # CRITICAL FIX: If the post_reset_cancel_time is 0, immediately clear the state
        # This handles cases where the timer wasn't properly initialized
        if self.post_reset_cancel_time == 0:
            if self.post_reset_cancel_state:
                self.post_reset_cancel_state = False
                print("CRITICAL FIX: Cleared post-reset-cancel state with zero timer")
            return False

        # Check if enough time has passed since entering the post-reset-cancel state
        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.post_reset_cancel_time

        # If more than the post-reset-cancel duration has passed, clear the flag
        if elapsed > self.post_reset_cancel_duration:
            self.post_reset_cancel_state = False
            print("CRITICAL FIX: Cleared post-reset-cancel state after timeout")
            return False

        # CRITICAL FIX: Log the remaining time for debugging
        remaining = self.post_reset_cancel_duration - elapsed
        if remaining > 0 and remaining % 100 < 10:  # Log approximately every 100ms
            print(f"CRITICAL FIX: Post-reset-cancel state active, {remaining}ms remaining")

        # We're still in the post-reset-cancel state
        return True

    def handle_mouse_event(self, event):
        """Handle mouse events for dragging the popup window"""
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left mouse button
            # Check if we clicked on the header drag area
            if "claim_header_drag" in self.modal_hit_areas and self.modal_hit_areas["claim_header_drag"].collidepoint(event.pos):
                self.dragging = True
                mouse_x, mouse_y = event.pos
                if self.popup_position:
                    self.drag_offset_x = mouse_x - self.popup_position[0]
                    self.drag_offset_y = mouse_y - self.popup_position[1]

        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:  # Left mouse button released
            self.dragging = False

        elif event.type == pygame.MOUSEMOTION and self.dragging:
            # Update the window position while dragging
            mouse_x, mouse_y = event.pos
            self.popup_position = (mouse_x - self.drag_offset_x, mouse_y - self.drag_offset_y)

    def draw_exit_confirmation(self, screen):
        """Draw the exit confirmation dialog with account deduction warning"""
        if not self.show_exit_confirmation:
            return

        # Get credit usage information if available
        credits_info = None
        if hasattr(self.game, 'usage_tracker'):
            # Get total bets from game
            total_bets = 0
            if hasattr(self.game, 'bet_amount') and hasattr(self.game, 'players'):
                total_bets = self.game.bet_amount * len(self.game.players)

            # Get commission percentage from game settings
            if hasattr(self.game, 'commission_percentage'):
                commission_percentage = self.game.commission_percentage
            elif hasattr(self.game, 'settings') and isinstance(self.game.settings, dict):
                commission_percentage = self.game.settings.get('game', {}).get('commission_percentage', 20)
            else:
                commission_percentage = 20  # Default commission percentage

            # Get usage info with the game parameters
            credits_info = self.game.usage_tracker.get_estimated_usage(
                total_bets=total_bets,
                commission_percentage=commission_percentage
            )

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Get current scale factors
        scale_x, scale_y = self.get_scale_factors()

        # Draw semi-transparent overlay with blur effect
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Black with 70% opacity
        screen.blit(overlay, (0, 0))

        # Create confirmation box
        box_width = int(screen_width * 0.4)
        box_height = int(screen_height * 0.3)
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # Create a visually appealing background with gradient and glow effects
        # First, add a subtle outer glow effect
        outer_glow_size = 15
        outer_glow_color = (150, 50, 50, 30)  # Subtle red glow with alpha
        outer_glow_surface = pygame.Surface((box_width + outer_glow_size*2, box_height + outer_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(outer_glow_surface, outer_glow_color, (0, 0, box_width + outer_glow_size*2, box_height + outer_glow_size*2), border_radius=25)
        screen.blit(outer_glow_surface, (box_x - outer_glow_size, box_y - outer_glow_size))

        # Add a second inner glow for depth
        inner_glow_size = 8
        inner_glow_color = (180, 60, 60, 50)  # Brighter red glow with alpha
        inner_glow_surface = pygame.Surface((box_width + inner_glow_size*2, box_height + inner_glow_size*2), pygame.SRCALPHA)
        pygame.draw.rect(inner_glow_surface, inner_glow_color, (0, 0, box_width + inner_glow_size*2, box_height + inner_glow_size*2), border_radius=22)
        screen.blit(inner_glow_surface, (box_x - inner_glow_size, box_y - inner_glow_size))

        # Draw main box with enhanced gradient background
        self.draw_gradient_rect(
            screen,
            pygame.Rect(box_x, box_y, box_width, box_height),
            (70, 20, 20),  # Richer dark red
            (100, 30, 30),  # Richer medium red
            20  # Border radius
        )

        # Add header bar with enhanced gradient and visual effects
        header_height = int(55 * min(scale_x, scale_y))
        header_rect = pygame.Rect(box_x, box_y, box_width, header_height)

        # Draw the header with enhanced gradient
        self.draw_gradient_rect(
            screen,
            header_rect,
            (100, 30, 30),  # Dark red
            (150, 40, 40),  # Lighter red
            border_radius=10  # Rounded corners at top
        )

        # Title text with shadow for depth
        title_font = pygame.font.SysFont("Arial", int(36 * min(scale_x, scale_y)), bold=True)

        # Add text shadow for depth
        shadow_offset = int(2 * min(scale_x, scale_y))
        shadow_color = (30, 10, 10)  # Dark shadow color
        shadow_text = title_font.render("Exit Confirmation", True, shadow_color)
        shadow_rect = shadow_text.get_rect(centerx=header_rect.centerx + shadow_offset, centery=header_rect.centery + shadow_offset)
        screen.blit(shadow_text, shadow_rect)

        # Main title text
        title_text = title_font.render("Exit Confirmation", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=header_rect.centerx, centery=header_rect.centery)
        screen.blit(title_text, title_rect)

        # Warning icon
        warning_font = pygame.font.SysFont("Arial", int(48 * min(scale_x, scale_y)), bold=True)
        warning_text = warning_font.render("⚠️", True, (255, 220, 0))
        warning_rect = warning_text.get_rect(centerx=box_x + box_width // 2, y=box_y + header_height + int(20 * min(scale_x, scale_y)))
        screen.blit(warning_text, warning_rect)

        # Description text
        desc_font = pygame.font.SysFont("Arial", int(20 * min(scale_x, scale_y)))

        # Basic description text
        desc_text = [
            "Are you sure you want to exit?",
            "This will deduct credits from your account",
            "same as clearing the game session."
        ]

        # Add credit information if available
        if credits_info and credits_info.get("active", False):
            # Get credit usage details
            credits_used = credits_info.get("credits_used", 0)
            remaining_credits = credits_info.get("remaining_credits", 0)
            commission = credits_info.get("referee_commission", 0)
            commission_percentage = credits_info.get("commission_percentage", 20)
            share_percentage = credits_info.get("share_percentage", 30)

            # Only show detailed credit info if credits will be deducted
            if credits_used > 0:
                desc_text = [
                    "Are you sure you want to exit?",
                    f"Credits to deduct: {credits_used}",
                    f"Commission: {commission:.2f} ETB ({commission_percentage}%)",
                    f"Share: {share_percentage}%",
                    f"Remaining balance: {remaining_credits} credits"
                ]
            else:
                desc_text = [
                    "Are you sure you want to exit?",
                    "No credits will be deducted.",
                    f"Current balance: {remaining_credits} credits"
                ]

        # Draw description text
        for i, line in enumerate(desc_text):
            text_surf = desc_font.render(line, True, (255, 255, 255))
            text_rect = text_surf.get_rect(
                centerx=box_x + box_width // 2,
                y=warning_rect.bottom + int(20 * min(scale_x, scale_y)) + i * int(30 * min(scale_x, scale_y))
            )
            screen.blit(text_surf, text_rect)

        # Buttons
        button_width = int(120 * min(scale_x, scale_y))
        button_height = int(40 * min(scale_x, scale_y))
        button_spacing = int(20 * min(scale_x, scale_y))

        # Calculate total width needed for both buttons with spacing
        total_buttons_width = button_width * 2 + button_spacing

        # Calculate starting x position to center both buttons
        buttons_start_x = box_x + (box_width - total_buttons_width) // 2

        # Y position for buttons - at the bottom of the box with some padding
        buttons_y = box_y + box_height - button_height - int(20 * min(scale_x, scale_y))

        # Cancel button (left)
        cancel_rect = pygame.Rect(
            buttons_start_x,
            buttons_y,
            button_width,
            button_height
        )

        # Draw cancel button with gradient
        self.draw_gradient_rect(
            screen,
            cancel_rect,
            (60, 60, 70),  # Dark gray
            (80, 80, 90),  # Lighter gray
            10  # Rounded corners
        )

        # Cancel button text
        button_font = pygame.font.SysFont("Arial", int(18 * min(scale_x, scale_y)), bold=True)
        cancel_text = button_font.render("Cancel", True, (255, 255, 255))
        cancel_text_rect = cancel_text.get_rect(center=cancel_rect.center)
        screen.blit(cancel_text, cancel_text_rect)

        # Store hit area for cancel button
        self.modal_hit_areas["exit_cancel"] = cancel_rect

        # Confirm button (right)
        confirm_rect = pygame.Rect(
            buttons_start_x + button_width + button_spacing,
            buttons_y,
            button_width,
            button_height
        )

        # Draw confirm button with gradient
        self.draw_gradient_rect(
            screen,
            confirm_rect,
            (150, 30, 30),  # Dark red
            (180, 50, 50),  # Lighter red
            10  # Rounded corners
        )

        # Confirm button text
        confirm_text = button_font.render("Exit", True, (255, 255, 255))
        confirm_text_rect = confirm_text.get_rect(center=confirm_rect.center)
        screen.blit(confirm_text, confirm_text_rect)

        # Store hit area for confirm button
        self.modal_hit_areas["exit_confirm"] = confirm_rect

    def _handle_exit_confirm_click(self):
        """Handle a click on the exit confirm button"""
        print("EXIT CONFIRMED - This will exit the application with account deduction")

        # Save the number_call_delay to player_storage before exiting
        try:
            if hasattr(self.game, 'number_call_delay'):
                from player_storage import save_game_settings, load_game_settings
                # Get current settings first
                settings = load_game_settings()
                # Update with current number_call_delay
                settings['number_call_delay'] = self.game.number_call_delay
                # Save back to player_storage
                save_game_settings(settings)
                print(f"Saved number_call_delay={self.game.number_call_delay}s to player_storage before exit")
        except Exception as e:
            print(f"Error saving number_call_delay to player_storage: {e}")

        # Check if game is started and active
        game_started = hasattr(self.game, 'game_started') and self.game.game_started
        print(f"Game started: {game_started}")

        # Check if usage tracker is available and active
        has_usage_tracker = hasattr(self.game, 'usage_tracker')
        usage_tracker_active = has_usage_tracker and self.game.usage_tracker.active
        print(f"Has usage tracker: {has_usage_tracker}")
        if has_usage_tracker:
            print(f"Usage tracker state: active={self.game.usage_tracker.active}, game_id={self.game.usage_tracker.current_game_id}")

        # Deduct credits from account (same as clear game session)
        try:
            # Check if payment system is available
            if hasattr(self.game, 'voucher_manager'):
                print(f"Voucher manager available: {self.game.voucher_manager}")
                print(f"Current credits: {self.game.voucher_manager.credits}")

                # CRITICAL FIX: Ensure usage tracker is active before exiting
                if hasattr(self.game, 'ensure_usage_tracker_active'):
                    print("CRITICAL FIX: Calling ensure_usage_tracker_active method")
                    self.game.ensure_usage_tracker_active()
                elif has_usage_tracker and not usage_tracker_active and game_started:
                    print("CRITICAL FIX: Game is started but usage tracker is not active. Activating it now.")
                    self.game.usage_tracker.active = True
                    self.game.usage_tracker.game_start_time = time.time() - 60  # Assume game has been running for at least 1 minute
                    self.game.usage_tracker.current_game_id = f"game_{int(time.time())}"
                    print(f"Usage tracker activated: active={self.game.usage_tracker.active}, game_id={self.game.usage_tracker.current_game_id}")

                # CRITICAL FIX: Set a minimum total bets amount to ensure credits are deducted
                # This is needed because the usage tracker calculates credits based on total bets
                if hasattr(self.game, 'usage_tracker') and self.game.usage_tracker.active:
                    # Force a minimum total bets amount of 100 to ensure at least 1 credit is used
                    print("CRITICAL FIX: Setting minimum total bets amount to ensure credits are deducted")
                    min_total_bets = 100

                    # Get the current total bets
                    current_total_bets = 0
                    if hasattr(self.game, 'bet_amount') and hasattr(self.game, 'players'):
                        current_total_bets = self.game.bet_amount * len(self.game.players)

                    # Use the maximum of current total bets and minimum total bets
                    total_bets = max(current_total_bets, min_total_bets)
                    print(f"CRITICAL FIX: Using total bets amount: {total_bets} (current: {current_total_bets}, minimum: {min_total_bets})")

                    # End the game with the forced total bets amount
                    result = self.game.usage_tracker.end_game(total_bets=total_bets)
                    print(f"CRITICAL FIX: Forced end_game result: {result}")

                    # If end_game was successful, clear the game session without deducting more credits
                    if result.get('success', False):
                        # Clear the game session without deducting more credits
                        # We've already deducted credits with end_game
                        print("CRITICAL FIX: Game ended successfully, clearing game session without deducting more credits")
                        self.game.usage_tracker.active = False
                        result = {
                            'success': True,
                            'message': 'Game session cleared and credits deducted.',
                            'credits_used': result.get('credits_used', 0),
                            'new_balance': result.get('new_balance', self.game.voucher_manager.credits)
                        }
                    else:
                        # If end_game failed, try to clear the game session normally
                        print("CRITICAL FIX: Forced end_game failed, trying normal clear_game_session")
                        result = self.game.voucher_manager.clear_game_session()
                else:
                    # If usage tracker is not available or not active, use normal clear_game_session
                    result = self.game.voucher_manager.clear_game_session()

                print(f"Clear game session result: {result}")

                # Log the result
                if result.get("success", False):
                    credits_used = result.get("credits_used", 0)
                    new_balance = result.get("new_balance", 0)

                    if credits_used > 0:
                        print(f"Credits deducted from account: {credits_used}. New balance: {new_balance}")
                    else:
                        print(f"No credits deducted. Current balance: {new_balance}")
                else:
                    print(f"Error clearing game session: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"Error deducting credits: {e}")
            import traceback
            traceback.print_exc()

        # Close the confirmation dialog
        self.show_exit_confirmation = False

        # Exit the application
        pygame.quit()
        import sys
        sys.exit()

    def _handle_exit_cancel_click(self):
        """Handle a click on the exit cancel button"""
        print("EXIT CANCELLED - This will close the exit confirmation dialog")

        # Close the confirmation dialog
        self.show_exit_confirmation = False