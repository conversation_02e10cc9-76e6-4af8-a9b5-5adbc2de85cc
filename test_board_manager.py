import json
import os
import random
import time

def test_board_persistence():
    """Test that boards persist after being saved and loaded multiple times"""
    # Path to the bingo boards file
    boards_file = os.path.join('data', 'bingo_boards.json')
    
    # Ensure the data directory exists
    os.makedirs('data', exist_ok=True)
    
    # First, check if the file exists and has content
    if os.path.exists(boards_file):
        with open(boards_file, 'r') as file:
            file_content = file.read().strip()
            if file_content:
                try:
                    boards = json.loads(file_content)
                    print(f"Initial state: Found {len(boards)} boards in {boards_file}")
                except json.JSONDecodeError:
                    print(f"Initial state: File exists but contains invalid JSON")
                    boards = {}
            else:
                print(f"Initial state: File exists but is empty")
                boards = {}
    else:
        print(f"Initial state: File does not exist")
        boards = {}
    
    # Add a test board
    test_board = create_test_board()
    boards["test_board"] = test_board
    
    # Save the boards to the file
    print(f"Saving {len(boards)} boards to {boards_file}...")
    with open(boards_file, 'w') as file:
        json.dump(boards, file, indent=2)
    
    # Wait a moment to ensure the file is written
    time.sleep(1)
    
    # Load the boards back from the file
    print("Loading boards from file...")
    with open(boards_file, 'r') as file:
        loaded_boards = json.load(file)
    
    # Check if the test board was saved and loaded correctly
    if "test_board" in loaded_boards:
        print("SUCCESS: Test board was saved and loaded correctly")
    else:
        print("ERROR: Test board was not found in loaded boards")
    
    # Now simulate what happens in the game
    print("\nSimulating game behavior...")
    
    # Create a new board for cartella 999
    cartella_key = "999"
    new_board = create_test_board()
    
    # Add the new board to the loaded boards
    loaded_boards[cartella_key] = new_board
    
    # Save the updated boards to the file
    print(f"Saving updated boards ({len(loaded_boards)} boards) to {boards_file}...")
    with open(boards_file, 'w') as file:
        json.dump(loaded_boards, file, indent=2)
    
    # Wait a moment to ensure the file is written
    time.sleep(1)
    
    # Load the boards back from the file again
    print("Loading boards from file again...")
    with open(boards_file, 'r') as file:
        final_boards = json.load(file)
    
    # Check if both the test board and the new board are in the final loaded boards
    if "test_board" in final_boards and cartella_key in final_boards:
        print(f"SUCCESS: Both test board and cartella {cartella_key} were found in final loaded boards")
        print(f"Final board count: {len(final_boards)}")
    else:
        print(f"ERROR: Not all boards were found in final loaded boards")
        if "test_board" not in final_boards:
            print("  - test_board is missing")
        if cartella_key not in final_boards:
            print(f"  - cartella {cartella_key} is missing")
    
    return True

def create_test_board():
    """Create a test bingo board"""
    board = []
    
    # For each column (B, I, N, G, O)
    for col in range(5):
        column = []
        # Range for this column: col*15+1 to col*15+15
        min_val = col * 15 + 1
        max_val = min_val + 14
        
        # Generate 5 random numbers for this column
        nums = random.sample(range(min_val, max_val + 1), 5)
        column.extend(nums)
        
        # Set the middle square (free space) to 0
        if col == 2:
            column[2] = 0
            
        board.append(column)
        
    return board

if __name__ == "__main__":
    test_board_persistence()
