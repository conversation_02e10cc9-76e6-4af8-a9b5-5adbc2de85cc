#!/usr/bin/env python3
"""
Test script to simulate game completion events and verify stats recording
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_game_completion_recording():
    """Test game completion recording with various scenarios"""
    
    print("=" * 80)
    print("TESTING GAME COMPLETION RECORDING")
    print("=" * 80)
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Valid Winner - Full House",
            "data": {
                "winner_name": "Cartella #15",
                "winner_cartella": 15,
                "claim_type": "Full House",
                "game_duration": 180.5,
                "player_count": 6,
                "prize_amount": 240.0,
                "commission_percentage": 20,
                "called_numbers": [1, 5, 12, 18, 23, 34, 45, 56, 67, 78],
                "is_demo_mode": False,
                "date_time": (datetime.now() - timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S'),
                "stake": 50,
                "bet_amount": 50,
                "completion_type": "valid_winner"
            }
        },
        {
            "name": "Valid Winner - First Line",
            "data": {
                "winner_name": "Cartella #23",
                "winner_cartella": 23,
                "claim_type": "First Line",
                "game_duration": 95.2,
                "player_count": 4,
                "prize_amount": 160.0,
                "commission_percentage": 20,
                "called_numbers": [3, 7, 14, 21, 28, 35, 42],
                "is_demo_mode": False,
                "date_time": (datetime.now() - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
                "stake": 50,
                "bet_amount": 50,
                "completion_type": "valid_winner"
            }
        },
        {
            "name": "Auto-Detected Winner",
            "data": {
                "winner_name": "Cartella #8",
                "winner_cartella": 8,
                "claim_type": "Auto-Detected",
                "game_duration": 220.8,
                "player_count": 8,
                "prize_amount": 320.0,
                "commission_percentage": 20,
                "called_numbers": [2, 9, 16, 24, 31, 38, 45, 52, 69, 75, 81],
                "is_demo_mode": False,
                "date_time": (datetime.now() - timedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S'),
                "stake": 50,
                "bet_amount": 50,
                "completion_type": "auto_detected_winner"
            }
        },
        {
            "name": "Game with No Winner",
            "data": {
                "winner_name": "No Winner",
                "winner_cartella": 0,
                "claim_type": "no_winner",
                "game_duration": 300.0,
                "player_count": 5,
                "prize_amount": 200.0,
                "commission_percentage": 20,
                "called_numbers": [4, 11, 19, 26, 33, 40, 47, 54, 61, 68, 75, 82, 89],
                "is_demo_mode": False,
                "date_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "stake": 50,
                "bet_amount": 50,
                "completion_type": "no_winner"
            }
        }
    ]
    
    # Test each scenario
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'-' * 60}")
        print(f"TEST {i}: {scenario['name']}")
        print(f"{'-' * 60}")
        
        try:
            # Try using thread_safe_db directly
            import thread_safe_db
            
            print(f"Recording game completion: {scenario['data']['winner_name']}")
            result = thread_safe_db.record_game_completed(scenario['data'])
            print(f"Result: {result}")
            
            if result:
                print(f"✅ Successfully recorded: {scenario['name']}")
            else:
                print(f"❌ Failed to record: {scenario['name']}")
                
        except Exception as e:
            print(f"❌ Error recording {scenario['name']}: {e}")
            import traceback
            traceback.print_exc()
        
        # Small delay between tests
        time.sleep(0.5)
    
    print(f"\n{'=' * 80}")
    print("TESTING COMPLETE - VERIFYING DATABASE")
    print(f"{'=' * 80}")
    
    # Verify the records were added
    try:
        import sqlite3
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        # Get total count
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f"Total game history records: {total_count}")
        
        # Get recent records
        cursor.execute('''
        SELECT id, date_time, username, claim_type, players, total_prize 
        FROM game_history 
        ORDER BY date_time DESC 
        LIMIT 10
        ''')
        
        records = cursor.fetchall()
        print(f"\nRecent game records:")
        print(f"{'ID':<4} {'Date/Time':<20} {'Winner':<15} {'Type':<15} {'Players':<8} {'Prize':<10}")
        print("-" * 80)
        
        for record in records:
            print(f"{record[0]:<4} {record[1]:<20} {record[2]:<15} {record[3]:<15} {record[4]:<8} {record[5]:<10.1f}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error verifying database: {e}")

if __name__ == "__main__":
    test_game_completion_recording()
