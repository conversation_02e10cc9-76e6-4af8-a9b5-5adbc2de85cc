import pygame
from settings_manager import SettingsManager # For consistent styling
from stats_models import SessionLocal, GameHistory, DailyStat, OverallSummary, GameStatus # Re-use models and session
from sqlalchemy import func, text
import datetime
import math
import os

# Matplotlib for charts (can be adapted or replaced with Pygame drawing)
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg # May need to adapt for Pygame

class PygameStatsView:
    def __init__(self, screen, game_instance):
        self.screen = screen
        self.game_instance = game_instance # To access shared resources like scaling, fonts, db session
        self.settings_manager = SettingsManager()
        self.db_session = SessionLocal()

        self.visible = False # Controls if the stats view is active
        self.needs_refresh = False # Flag to track if stats need refresh when page becomes active

        # UI elements and state for the stats page will be defined here
        # e.g., positions for summary cards, chart data, table rows, scroll state, etc.
        self.font_color = (255, 255, 255) # Default white, can be themed
        self.bg_color = (10, 30, 45)    # Default dark blue, can be themed

        self.current_history_page = 1
        self.total_history_pages = 1
        self.history_page_size = 15 # From original stats_page.py
        self.search_username_var_text = "" # Pygame equivalent for StringVar
        self.search_status_var_text = "All Statuses" # Pygame equivalent

        self.summary_data = {
            "total_earning": "0.0 ETB",
            "daily_games": "0",
            "daily_earning": "0.0 ETB",
            "wallet_balance": "0.0 ETB"
        }
        self.weekly_earnings_data = ([], []) # labels, earnings
        self.game_history_rows = []
        self.voucher_history_rows = []

        self.time_period_var = "Weekly" # For earnings chart

        # Hit areas for interactive elements
        self.hit_areas = {}
        self.scroll_y_history = 0
        self.scroll_y_vouchers = 0
        self.scroll_bar_history_rect = None
        self.scroll_bar_history_handle_rect = None
        self.dragging_history_scrollbar = False

        self.title_font = None
        self.header_font = None
        self.text_font = None
        self.small_font = None
        self.button_font = None # Added for buttons
        self.init_fonts()
        self.init_theme_colors()

        # Chart related (placeholder, might need specific Pygame integration)
        self.fig = Figure(figsize=(5, 2.5), dpi=100) # Adjusted size
        self.ax = self.fig.add_subplot(111)
        # self.canvas = FigureCanvasTkAgg(self.fig, master=None) # This needs Pygame adaptation

        self.load_all_data()

    def init_fonts(self):
        # Use scaled_font_size from game_instance or define locally
        gsfs = self.game_instance.scaled_font_size if hasattr(self.game_instance, 'scaled_font_size') else lambda x: int(x * 1.0)
        self.title_font = pygame.font.SysFont("Arial", gsfs(28), bold=True)
        self.header_font = pygame.font.SysFont("Arial", gsfs(22), bold=True)
        self.text_font = pygame.font.SysFont("Arial", gsfs(18))
        self.small_font = pygame.font.SysFont("Arial", gsfs(14))
        self.button_font = pygame.font.SysFont("Arial", gsfs(16), bold=True) # Added

    def init_theme_colors(self):
        # Align with settings_window.py approach
        current_theme = self.settings_manager.get_setting('display', 'ui_theme', 'dark')
        current_accent = self.settings_manager.get_setting('display', 'ui_accent_color', 'blue')

        # Use SettingsManager to get theme colors for consistency
        self.colors = self.settings_manager.get_theme_colors(current_theme, current_accent)

        self.bg_color = self.colors.get("app_bg", (10, 30, 45))
        self.panel_bg_color = self.colors.get("panel_bg", (20, 50, 70))
        self.header_bg_color = self.colors.get("panel_header", (30, 70, 90))
        self.font_color = self.colors.get("light_text", (220, 220, 220))
        self.accent_color = self.colors.get("accent", (0, 140, 240))
        self.control_border_color = self.colors.get("control_border", (40, 80, 110))
        self.button_bg_color = self.colors.get("accent", (0, 140, 240))
        self.button_hover_bg_color = self.colors.get("accent_hover", (0, 160, 255))
        self.button_text_color = self.colors.get("button_text", (255,255,255))


    def show(self):
        print("PygameStatsView: show() called")
        self.visible = True
        # Hide board selection if it exists
        if hasattr(self.game_instance, 'board_selection') and self.game_instance.board_selection:
            print("PygameStatsView: Hiding board selection")
            self.game_instance.board_selection.hide()
        # Don't change screen mode - use the existing screen
        # self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        print("PygameStatsView: Loading data...")
        self.load_all_data() # Refresh data when shown
        print("PygameStatsView: show() completed")

    def hide(self):
        self.visible = False
        # Restore previous window mode if board selection was visible
        if hasattr(self.game_instance, 'board_selection') and self.game_instance.board_selection:
            self.game_instance.board_selection.show()

    def load_all_data(self):
        if not self.visible and self.game_instance.active_nav != "stats": # Only load if visible or becoming visible
            return
        print("PygameStatsView: Loading all data...")
        self.load_summary_data()
        self.load_earnings_chart_data()
        self.load_game_history_data()
        # self.load_voucher_history_data() # Add later if needed

    def refresh_stats(self):
        """Force refresh all stats data - called when stats need to be updated"""
        print("PygameStatsView: Refreshing stats data...")
        try:
            # Reload all data from database
            self.load_all_data()
            print("PygameStatsView: Stats data refreshed successfully")
        except Exception as e:
            print(f"PygameStatsView: Error refreshing stats data: {e}")
            import traceback
            traceback.print_exc()

    def load_summary_data(self):
        try:
            summary = self.db_session.query(OverallSummary).order_by(OverallSummary.last_updated.desc()).first()
            if summary:
                self.summary_data["total_earning"] = f"{summary.total_earnings:,.1f} ETB"
                self.summary_data["daily_games"] = f"{summary.current_daily_games}"
                self.summary_data["daily_earning"] = f"{summary.current_daily_earnings:,.1f} ETB"
                self.summary_data["wallet_balance"] = f"{summary.wallet_balance:,.1f} ETB"
            else: # Fallback to zeros
                for key in self.summary_data: self.summary_data[key] = "0 ETB" if "ETB" in key else "0"
        except Exception as e:
            print(f"Error loading summary data for PygameStatsView: {e}")
            for key in self.summary_data: self.summary_data[key] = "Error"

    def load_earnings_chart_data(self):
        # This is a simplified version of the logic in stats_page.py
        # It prepares data; actual drawing will be in the draw() method
        try:
            today = datetime.date.today()
            if self.time_period_var == "Weekly":
                start_date = today - datetime.timedelta(days=6)
                date_format_str = "%a"
            elif self.time_period_var == "Monthly":
                start_date = today.replace(day=1)
                start_date = max(start_date, today - datetime.timedelta(days=29))
                date_format_str = "%d"
            # Add Yearly and All-time later if needed
            else: # Default to weekly
                start_date = today - datetime.timedelta(days=6)
                date_format_str = "%a"

            stats_data = self.db_session.query(DailyStat.date, DailyStat.earnings)\
                .filter(DailyStat.date >= start_date, DailyStat.date <= today)\
                .order_by(DailyStat.date).all()

            date_earnings_map = {stat_date: (earnings_val or 0.0) for stat_date, earnings_val in stats_data}

            dates_for_chart = []
            earnings_for_chart = []
            current_date_iter = start_date
            while current_date_iter <= today:
                dates_for_chart.append(current_date_iter.strftime(date_format_str))
                earnings_for_chart.append(date_earnings_map.get(current_date_iter, 0.0))
                current_date_iter += datetime.timedelta(days=1)

            self.weekly_earnings_data = (dates_for_chart, earnings_for_chart)
        except Exception as e:
            print(f"Error loading earnings chart data for PygameStatsView: {e}")
            self.weekly_earnings_data = (["Error"] * 7, [0] * 7)


    def load_game_history_data(self):
        try:
            # Simplified query for now, add search/filter later
            base_query = self.db_session.query(
                GameHistory.id, GameHistory.username, GameHistory.players,
                GameHistory.stake, GameHistory.total_prize, GameHistory.fee,
                GameHistory.status, GameHistory.date_time
            )

            # Apply filters (basic example)
            if self.search_username_var_text:
                base_query = base_query.filter(GameHistory.username.ilike(f"%{self.search_username_var_text}%"))
            if self.search_status_var_text and self.search_status_var_text != "All Statuses":
                try:
                    status_enum = GameStatus.from_string(self.search_status_var_text)
                    base_query = base_query.filter(GameHistory.status == status_enum)
                except ValueError:
                    pass # Invalid status string

            total_items = base_query.count()
            self.total_history_pages = math.ceil(total_items / self.history_page_size)
            if self.total_history_pages == 0: self.total_history_pages = 1
            if self.current_history_page > self.total_history_pages:
                self.current_history_page = self.total_history_pages

            offset = (self.current_history_page - 1) * self.history_page_size

            history_rows_db = base_query.order_by(GameHistory.date_time.desc())\
                                     .limit(self.history_page_size).offset(offset).all()

            self.game_history_rows = []
            for row in history_rows_db:
                status_display = row.status.value.title() if row.status else "N/A"
                date_display = row.date_time.strftime("%Y-%m-%d %H:%M") if row.date_time else "Unknown"
                self.game_history_rows.append({
                    "id": row.id, "username": row.username, "players": row.players or 0,
                    "stake": f"{row.stake or 0:,.1f} ETB", "prize": f"{row.total_prize or 0:,.1f} ETB",
                    "remaining": f"{row.fee or 0:,.1f} ETB", # Assuming fee is remaining credit as per original
                    "status": status_display, "date": date_display
                })
            if not self.game_history_rows:
                 self.game_history_rows.append({"id": "No records found", "username": "", "players": "", "stake": "", "prize": "", "remaining": "", "status": "", "date": ""})

        except Exception as e:
            print(f"Error loading game history for PygameStatsView: {e}")
            self.game_history_rows = [{"id": f"Error: {e}", "username": "", "players": "", "stake": "", "prize": "", "remaining": "", "status": "", "date": ""}]


    def draw_text(self, text, font, color, x, y, align="left", surface=None, shadow=False, shadow_color=(0,0,0,100), shadow_offset=(1,1)):
        if surface is None:
            surface = self.screen

        text_surface = font.render(str(text), True, color)
        text_rect = text_surface.get_rect()

        if align == "left":
            text_rect.topleft = (x, y)
        elif align == "center":
            text_rect.center = (x, y)
        elif align == "right":
            text_rect.topright = (x, y)

        if shadow:
            shadow_surface = font.render(str(text), True, shadow_color)
            surface.blit(shadow_surface, (text_rect.x + shadow_offset[0], text_rect.y + shadow_offset[1]))
        surface.blit(text_surface, text_rect)
        return text_rect

    def _darken_color(self, color, amount):
        try:
            return tuple(max(0, c - amount) for c in color[:3]) + (color[3:] if len(color) > 3 else ())
        except: return color # fallback

    def _lighten_color(self, color, amount):
        try:
            return tuple(min(255, c + amount) for c in color[:3]) + (color[3:] if len(color) > 3 else ())
        except: return color


    def draw_panel(self, rect, bg_color=None, border_radius=10, border_color=None, border_width=1, header_text=None, header_font=None, header_color=None, header_bg_color=None):
        """Draw a modern panel, similar to settings_window.py"""
        if bg_color is None: bg_color = self.panel_bg_color
        if header_font is None: header_font = self.header_font
        if header_color is None: header_color = self.font_color
        if header_bg_color is None: header_bg_color = self.header_bg_color

        scaled_radius = int(border_radius * min(self.game_instance.scale_x, self.game_instance.scale_y))

        # Main panel background
        pygame.draw.rect(self.screen, bg_color, rect, border_radius=scaled_radius)

        content_rect = pygame.Rect(rect)

        if header_text:
            header_height = int(header_font.get_height() * 1.8) # Slightly more padding for header
            panel_header_rect = pygame.Rect(rect.x, rect.y, rect.width, header_height)

            pygame.draw.rect(self.screen, header_bg_color, panel_header_rect,
                             border_top_left_radius=scaled_radius,
                             border_top_right_radius=scaled_radius)

            self.draw_text(header_text, header_font, header_color,
                           panel_header_rect.centerx, panel_header_rect.centery, align="center")

            # Line below header
            line_y = rect.y + header_height
            pygame.draw.line(self.screen, self.control_border_color or self._darken_color(header_bg_color, 20),
                             (rect.x, line_y -1), (rect.right, line_y -1 ), 1)

            content_rect.top = line_y
            content_rect.height = rect.height - header_height

        if border_color:
            pygame.draw.rect(self.screen, border_color, rect, width=border_width, border_radius=scaled_radius)

        return content_rect # Return the area available for content

    def draw_button(self, rect, text, button_id, bg_color=None, text_color=None, hover_bg_color=None, border_radius=5):
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = rect.collidepoint(mouse_pos)

        current_bg = bg_color or self.button_bg_color
        if is_hovered and (hover_bg_color or self.button_hover_bg_color):
            current_bg = hover_bg_color or self.button_hover_bg_color

        current_text_color = text_color or self.button_text_color

        pygame.draw.rect(self.screen, current_bg, rect, border_radius=int(border_radius * min(self.game_instance.scale_x, self.game_instance.scale_y)))
        self.draw_text(text, self.button_font, current_text_color, rect.centerx, rect.centery, align="center")
        self.hit_areas[button_id] = rect
        return is_hovered

    def draw(self):
        if not self.visible and self.game_instance.active_nav != "stats":
            return

        self.screen.fill(self.bg_color) # Background for the stats page

        # --- Layout constants ---
        padding = int(20 * self.game_instance.scale_x)
        screen_w, screen_h = self.screen.get_size()
        content_width = screen_w - 2 * padding

        current_y = padding

        # --- Title ---
        title_rect = self.draw_text("Game Statistics", self.title_font, self.font_color, screen_w / 2, current_y + self.title_font.get_height() / 2, align="center")
        current_y += title_rect.height + padding * 1.5

        # --- Summary Cards Section ---
        summary_card_width = (content_width - padding * 3) / 4 # 4 cards
        summary_card_height = int(80 * self.game_instance.scale_y)
        summary_keys = ["total_earning", "daily_games", "daily_earning", "wallet_balance"]
        summary_titles = ["Total Earnings", "Today's Games", "Today's Earnings", "Wallet Balance"]

        for i, key in enumerate(summary_keys):
            card_x = padding + i * (summary_card_width + padding)
            card_rect = pygame.Rect(card_x, current_y, summary_card_width, summary_card_height)
            self.draw_panel(card_rect, self.panel_bg_color, border_radius=8, border_color=self.control_border_color,
                              header_text=summary_titles[i], header_font=self.small_font, header_color=self.font_color, header_bg_color=self.header_bg_color)

            value_text = self.summary_data.get(key, "N/A")
            text_y_offset = self.small_font.get_height() * 1.5 # Adjust based on header
            self.draw_text(value_text, self.header_font, self.accent_color, card_rect.centerx, card_rect.y + text_y_offset + self.header_font.get_height()/2, align="center")
        current_y += summary_card_height + padding

        # --- Earnings Chart and Game History (Side by Side) ---
        left_column_width = int(content_width * 0.6) - padding / 2
        right_column_width = int(content_width * 0.4) - padding / 2

        column_height = screen_h - current_y - padding # Remaining height

        # Earnings Chart (Left Column)
        chart_panel_rect = pygame.Rect(padding, current_y, left_column_width, column_height)
        chart_content_rect = self.draw_panel(chart_panel_rect, self.panel_bg_color, border_radius=8, border_color=self.control_border_color,
                                             header_text=f"{self.time_period_var} Earnings", header_font=self.header_font, header_color=self.font_color, header_bg_color=self.header_bg_color)

        # Placeholder for chart drawing (actual Matplotlib integration is complex for Pygame direct draw)
        # For now, just display the data as text or simple bars
        if self.weekly_earnings_data and self.weekly_earnings_data[0]:
            labels, earnings = self.weekly_earnings_data
            bar_area_rect = pygame.Rect(chart_content_rect.x + padding, chart_content_rect.y + padding,
                                        chart_content_rect.width - 2 * padding, chart_content_rect.height - 2 * padding - int(30 * self.game_instance.scale_y)) # Space for total

            if earnings:
                max_earn = max(earnings) if any(e > 0 for e in earnings) else 1.0 # Avoid division by zero
                bar_width_total = bar_area_rect.width / len(labels)
                bar_spacing = int(bar_width_total * 0.2)
                bar_actual_width = int(bar_width_total * 0.8)

                for i, (label, earn) in enumerate(zip(labels, earnings)):
                    bar_height = (earn / max_earn) * (bar_area_rect.height - self.small_font.get_height() - 5) # leave space for label
                    bar_x = bar_area_rect.x + i * bar_width_total
                    bar_rect = pygame.Rect(bar_x + bar_spacing / 2, bar_area_rect.bottom - bar_height - self.small_font.get_height() - 5, bar_actual_width, bar_height)
                    pygame.draw.rect(self.screen, self.accent_color, bar_rect, border_radius=3)
                    self.draw_text(label, self.small_font, self.font_color, bar_rect.centerx, bar_area_rect.bottom - self.small_font.get_height()/2, align="center")

            total_chart_earnings = sum(earnings)
            self.draw_text(f"Total: {total_chart_earnings:,.1f} ETB", self.text_font, self.font_color,
                           chart_content_rect.centerx, chart_content_rect.bottom - padding/2 - self.text_font.get_height()/2, align="center")


        # Game History (Right Column)
        history_panel_rect = pygame.Rect(padding + left_column_width + padding, current_y, right_column_width, column_height)
        history_content_rect_full = self.draw_panel(history_panel_rect, self.panel_bg_color, border_radius=8, border_color=self.control_border_color,
                                               header_text="Game History", header_font=self.header_font, header_color=self.font_color, header_bg_color=self.header_bg_color)

        # Define area for table content, excluding header and pagination
        pagination_controls_height = int(40 * self.game_instance.scale_y)
        search_filter_controls_height = int(40 * self.game_instance.scale_y)
        table_header_draw_height = self.small_font.get_height() + padding # Visual height for the column headers row

        # Area for the actual rows of the table (scrollable part)
        # This is the visible 'window' for the rows.
        self.history_table_rows_render_area = pygame.Rect(
            history_content_rect_full.x + padding,
            history_content_rect_full.y + search_filter_controls_height + table_header_draw_height, # Start below search and headers
            history_content_rect_full.width - 2 * padding,
            history_content_rect_full.height - search_filter_controls_height - table_header_draw_height - pagination_controls_height - padding # available height for rows
        )

        # Search/Filter for Game History (simplified) - Position within history_content_rect_full
        filter_controls_y = history_content_rect_full.y + padding / 2
        self.draw_text("Search:", self.small_font, self.font_color, history_content_rect_full.x + padding, filter_controls_y + self.small_font.get_height()/2, align="left")
        # Placeholder for search input drawing

        # Table Headers
        headers_y_pos = history_content_rect_full.y + search_filter_controls_height + padding / 2 # Y position for drawing headers
        history_headers_spec = [("ID", 0.1), ("User", 0.3), ("Prize", 0.2), ("Date", 0.25), ("Status", 0.15)]

        current_col_x = self.history_table_rows_render_area.x # Headers align with the row render area
        header_render_width = self.history_table_rows_render_area.width

        for i, (header_text, width_percent) in enumerate(history_headers_spec):
            col_width = header_render_width * width_percent
            self.draw_text(header_text, self.small_font, self.font_color,
                           current_col_x + col_width / 2,
                           headers_y_pos + self.small_font.get_height() / 2, align="center")
            if i < len(history_headers_spec) - 1:
                 pygame.draw.line(self.screen, self.control_border_color,
                                 (current_col_x + col_width -1, headers_y_pos),
                                 (current_col_x + col_width -1, self.history_table_rows_render_area.bottom), 1)
            current_col_x += col_width

        pygame.draw.line(self.screen, self.control_border_color,
                         (self.history_table_rows_render_area.x, headers_y_pos + self.small_font.get_height() + padding/4),
                         (self.history_table_rows_render_area.right, headers_y_pos + self.small_font.get_height() + padding/4), 1)

        row_height = self.text_font.get_height() + padding / 2
        total_rows_content_height = len(self.game_history_rows) * row_height

        # Table Rows - draw onto a separate surface then blit, allows clipping/scrolling
        table_rows_surface = pygame.Surface((self.history_table_rows_render_area.width, total_rows_content_height), pygame.SRCALPHA)
        table_rows_surface.fill((0,0,0,0))

        for i, row_data in enumerate(self.game_history_rows):
            row_y_on_surface = i * row_height # Y position on the table_rows_surface
            row_bg_color = self.panel_bg_color if i % 2 == 0 else self._darken_color(self.panel_bg_color, 10)
            pygame.draw.rect(table_rows_surface, row_bg_color, (0, row_y_on_surface, self.history_table_rows_render_area.width, row_height))

            display_data_ordered = [
                row_data.get("id","N/A"), row_data.get("username","N/A"), row_data.get("prize","N/A"),
                row_data.get("date","N/A"), row_data.get("status","N/A")
            ]
            current_cell_x = 0
            for j, cell_text in enumerate(display_data_ordered):
                cell_width = self.history_table_rows_render_area.width * history_headers_spec[j][1]
                self.draw_text(str(cell_text), self.small_font, self.font_color,
                               current_cell_x + padding/2,
                               row_y_on_surface + row_height/2 - self.small_font.get_height()/2,
                               align="left", surface=table_rows_surface)
                current_cell_x += cell_width

        # Blit the scrollable part of the table_rows_surface to the screen
        self.screen.blit(table_rows_surface, self.history_table_rows_render_area.topleft,
                         (0, self.scroll_y_history, self.history_table_rows_render_area.width, self.history_table_rows_render_area.height))

        # Scrollbar for Game History
        max_scroll_y = max(0, total_rows_content_height - self.history_table_rows_render_area.height)
        if max_scroll_y > 0 :
            scrollbar_track_w = int(10 * self.game_instance.scale_x)
            self.scroll_bar_history_rect = pygame.Rect(
                self.history_table_rows_render_area.right + 2,
                self.history_table_rows_render_area.y, # Align with the row rendering area
                scrollbar_track_w,
                self.history_table_rows_render_area.height
            )
            pygame.draw.rect(self.screen, self._darken_color(self.control_border_color, 20), self.scroll_bar_history_rect, border_radius=5)

            handle_h = max(int(20 * self.game_instance.scale_y), self.scroll_bar_history_rect.height * (self.history_table_rows_render_area.height / total_rows_content_height if total_rows_content_height > 0 else 1))
            scroll_perc = self.scroll_y_history / max_scroll_y if max_scroll_y > 0 else 0
            handle_y_pos = self.scroll_bar_history_rect.y + (self.scroll_bar_history_rect.height - handle_h) * scroll_perc

            self.scroll_bar_history_handle_rect = pygame.Rect(self.scroll_bar_history_rect.x, handle_y_pos, scrollbar_track_w, handle_h)
            pygame.draw.rect(self.screen, self.accent_color, self.scroll_bar_history_handle_rect, border_radius=5)
        else:
            self.scroll_bar_history_rect = None
            self.scroll_bar_history_handle_rect = None
            if self.scroll_y_history != 0: self.scroll_y_history = 0

        # Pagination Controls
        pagination_area_y = history_content_rect_full.bottom - pagination_controls_height
        button_width = int(80 * self.game_instance.scale_x)
        button_height = int(30 * self.game_instance.scale_y)

        page_info_text = f"Page {self.current_history_page} of {self.total_history_pages}"
        page_info_width = self.text_font.size(page_info_text)[0]

        total_pagination_width = button_width * 2 + page_info_width + padding * 2
        start_x_pagination = history_content_rect_full.centerx - total_pagination_width / 2

        prev_button_rect = pygame.Rect(start_x_pagination, pagination_area_y, button_width, button_height)
        self.draw_button(prev_button_rect, "Prev", "hist_prev")

        text_rect_page_info = self.draw_text(page_info_text, self.text_font, self.font_color,
                                             start_x_pagination + button_width + padding + page_info_width/2,
                                             pagination_area_y + button_height/2, align="center")

        next_button_rect = pygame.Rect(text_rect_page_info.right + padding, pagination_area_y, button_width, button_height)
        self.draw_button(next_button_rect, "Next", "hist_next")


    def handle_event(self, event):
        consumed = False
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1: # Left click
                mouse_pos = event.pos
                if self.hit_areas.get("hist_prev") and self.hit_areas["hist_prev"].collidepoint(mouse_pos):
                    if self.current_history_page > 1:
                        self.current_history_page -= 1
                        self.load_game_history_data()
                    consumed = True
                elif self.hit_areas.get("hist_next") and self.hit_areas["hist_next"].collidepoint(mouse_pos):
                    if self.current_history_page < self.total_history_pages:
                        self.current_history_page += 1
                        self.load_game_history_data()
                    consumed = True
                # Add checks for other interactive elements (filters, chart period) here
        elif event.type == pygame.KEYDOWN:
            # Handle keyboard input for search fields if active
            pass
        elif event.type == pygame.MOUSEWHEEL:
            # Handle scrolling for tables - conceptual
            # mouse_pos = pygame.mouse.get_pos()
            # if history_panel_rect.collidepoint(mouse_pos):
            #    self.scroll_y_history -= event.y * 20 # Adjust scroll speed
            #    # Add clamping for scroll_y_history based on content height
            #    consumed = True
            pass
        return consumed

    def event_consumed_by_ui(self, event):
        """Check if an event should be consumed by the stats view UI"""
        if not self.visible:
            return False

        if event.type == pygame.MOUSEBUTTONDOWN:
            # Check if the click is within any of our UI elements
            mouse_pos = event.pos
            for _, rect in self.hit_areas.items():
                if rect.collidepoint(mouse_pos):
                    return True

            # If we have a scrollbar and it's being dragged
            if hasattr(self, 'dragging_history_scrollbar') and self.dragging_history_scrollbar:
                return True

        return False