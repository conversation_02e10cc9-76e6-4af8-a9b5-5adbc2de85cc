@echo off
echo ========================================================
echo     WOW Games RethinkDB Dashboard
echo ========================================================
echo.

echo Step 1: Installing required packages...
python -m pip install flask rethinkdb==2.4.9
if %ERRORLEVEL% NEQ 0 (
    echo Error installing packages. Please try again.
    pause
    exit /b 1
)
echo Packages installed successfully.
echo.

echo Step 2: Starting the dashboard...
echo.
echo ========================================================
echo     Dashboard will be available at:
echo     http://localhost:5000
echo.
echo     You can access it from other devices using:
echo     http://YOUR_IP_ADDRESS:5000
echo.
echo     Press Ctrl+C to stop the dashboard
echo ========================================================
echo.

python rethink_dashboard.py