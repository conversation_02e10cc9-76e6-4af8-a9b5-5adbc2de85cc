import pygame
import math
import sys
import os
import json
import subprocess  # For clipboard operations
from common_header import draw_wow_bingo_header
import time
import random
from settings_manager import SettingsManager
from screen_mode_manager import get_screen_mode_manager
from player_storage import load_players_from_json
from amharic_support import AmharicSupport  # Import AmharicSupport for Amharic text rendering

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (50, 50, 50)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
LIGHT_BLUE = (100, 150, 255)
GOLD = (255, 215, 0)
ORANGE = (255, 165, 0)
NAV_BAR_BG = (30, 40, 50)

def show_settings_page(screen):
    """
    Show the settings page and handle interactions

    Args:
        screen: Pygame screen surface

    Returns:
        None
    """
    settings_page = SettingsPage(screen)
    settings_page.run()

class SettingsPage:
    def __init__(self, screen):
        self.screen = screen
        self.running = True
        self.scale_x = screen.get_width() / 1024
        self.scale_y = screen.get_height() / 768

        # Load settings
        try:
            self.settings_manager = SettingsManager()
            self.settings = self.settings_manager.load_settings()
            if not isinstance(self.settings, dict):
                self.settings = {}
        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            self.settings = {}

        # Initialize screen mode manager
        self.screen_mode_manager = get_screen_mode_manager()
        self.screen_mode_manager.set_screen_reference(screen)

        # Initialize UI elements
        self.hit_areas = {}
        self.button_states = {}
        self.init_button_animations()

        # Message display
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"

        # Active navigation section
        self.active_nav = "settings"

        # Active settings tab
        self.active_tab = "game"  # Default active tab

        # Scrolling variables
        self.scroll_y = 0  # Current scroll position
        self.max_scroll = 0  # Maximum scroll value
        self.content_height = 0  # Total content height
        self.visible_content_height = 0  # Visible content height
        self.scroll_speed = 40  # Pixels per scroll event

        # Dragging state for sliders
        self.dragging_slider = None
        self.dragging_offset = 0

        # Text input state
        self.active_text_input = None
        self.text_input_value = ""
        self.text_input_cursor_visible = True
        self.text_input_cursor_timer = 0

        # Load sound effects if enabled
        self.load_sound_effects()

        # Initialize theme colors
        self.init_theme_colors()

        # Initialize settings definitions
        self.init_settings_definitions()

    def init_button_animations(self):
        """Initialize animation states for buttons"""
        buttons = ["nav_play", "nav_stats", "nav_settings", "nav_help"]

        for button in buttons:
            self.button_states[button] = {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            }

    def init_theme_colors(self):
        """Initialize theme colors with modern, high-contrast palette"""
        # Get current theme and accent color from settings
        current_theme = self.settings_manager.get_setting('display', 'ui_theme', 'dark')
        current_accent = self.settings_manager.get_setting('display', 'ui_accent_color', 'blue')

        # Define accent colors for different tabs with improved visibility
        self.accent_colors = {
            "blue": (0, 140, 240),  # Brighter blue
            "teal": (0, 200, 200),  # Brighter teal
            "purple": (140, 100, 220),  # Brighter purple
            "orange": (255, 140, 40),  # Brighter orange
            "green": (50, 200, 100),  # Brighter green
            "red": (255, 70, 70),  # Brighter red
            "gold": (255, 215, 0)  # Brighter gold
        }

        # Set accent color based on settings
        self.ACCENT_COLOR = self.accent_colors.get(current_accent, self.accent_colors["blue"])
        self.ACCENT_TEAL = self.accent_colors["teal"]
        self.ACCENT_PURPLE = self.accent_colors["purple"]
        self.ACCENT_ORANGE = self.accent_colors["orange"]
        self.SUCCESS_GREEN = self.accent_colors["green"]

    def init_settings_definitions(self):
        """Initialize settings definitions for all tabs"""
        # Define standard item height and spacing
        self.item_height = int(80 * min(self.scale_x, self.scale_y))
        self.item_spacing = int(15 * min(self.scale_x, self.scale_y))

        # Define component types
        self.COMPONENT_TOGGLE = "toggle"
        self.COMPONENT_SLIDER = "slider"
        self.COMPONENT_DROPDOWN = "dropdown"
        self.COMPONENT_TEXT_INPUT = "text_input"
        self.COMPONENT_COLOR_PICKER = "color_picker"
        self.COMPONENT_BUTTON = "button"

        # Define settings for each tab
        self.settings_definitions = {
            "notifications": [
                {
                    "key": "enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Notifications",
                        "description": "Enable or disable the notification system for credit and voucher warnings",
                        "default_value": True
                    }
                },
                {
                    "key": "show_low_credit_warning",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Low Credit Warnings",
                        "description": "Show warnings when credit balance is below threshold",
                        "default_value": True
                    }
                },
                {
                    "key": "low_credit_threshold",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Low Credit Threshold",
                        "description": "Credit balance threshold for low balance warnings",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 500,
                        "value_format": "{:.0f} credits"
                    }
                },
                {
                    "key": "show_expiry_warning",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Voucher Expiry Warnings",
                        "description": "Show warnings when vouchers are about to expire",
                        "default_value": True
                    }
                },
                {
                    "key": "expiry_warning_days",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Expiry Warning Days",
                        "description": "Number of days before expiry to show warning",
                        "min_value": 1,
                        "max_value": 30,
                        "default_value": 5,
                        "value_format": "{:.0f} days"
                    }
                }
            ],
            "game": [
                {
                    "key": "number_call_delay",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Number Call Delay",
                        "description": "Time between automatic number calls in seconds",
                        "min_value": 1.0,
                        "max_value": 10.0,
                        "default_value": 3.0,
                        "value_format": "{:.1f}s"
                    }
                },
                {
                    "key": "strict_claim_timing",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Strict Claim Timing",
                        "description": "Enforce rule: claims must be made before next number is called",
                        "default_value": True
                    }
                },
                {
                    "key": "show_game_info",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Show Game Info",
                        "description": "Show or hide game information footer (credits, commission, share)",
                        "default_value": True
                    }
                },

                {
                    "key": "shuffle_duration",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Shuffle Duration",
                        "description": "Duration of shuffle animation",
                        "min_value": 0.5,
                        "max_value": 5.0,
                        "default_value": 3.0,
                        "value_format": "{:.1f}s"
                    }
                },
                {
                    "key": "commission_percentage",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Commission Percentage",
                        "description": "Percentage deducted from total bets for prize pool calculation",
                        "min_value": 0.0,
                        "max_value": 50.0,
                        "default_value": 20.0,
                        "value_format": "{:.1f}%"
                    }
                },
                {
                    "key": "show_total_selected",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Show Total Selected",
                        "description": "Show or hide the Total selected section in board selection",
                        "default_value": True
                    }
                }
            ],
            "display": [
                {
                    "key": "fullscreen",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Fullscreen Mode",
                        "description": "Run the game in fullscreen mode",
                        "default_value": False
                    }
                },
                {
                    "key": "animations_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Animations",
                        "description": "Show animations for visual effects",
                        "default_value": True
                    }
                },
                {
                    "key": "show_recent_calls",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Show Recent Calls",
                        "description": "Display recently called numbers",
                        "default_value": True
                    }
                },
                {
                    "key": "recent_calls_count",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Recent Calls Count",
                        "description": "Number of recent calls to display",
                        "min_value": 1,
                        "max_value": 10,
                        "default_value": 5,
                        "value_format": "{:.0f}"
                    }
                }
            ],
            "audio": [
                {
                    "key": "sound_effects_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Sound Effects",
                        "description": "Enable sound effects",
                        "default_value": True
                    }
                },
                {
                    "key": "sound_effects_volume",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Sound Effects Volume",
                        "description": "Volume level for sound effects",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "default_value": 0.7,
                        "value_format": "{:.0%}"
                    }
                },
                {
                    "key": "music_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Background Music",
                        "description": "Enable background music",
                        "default_value": True
                    }
                },
                {
                    "key": "music_volume",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Music Volume",
                        "description": "Volume level for background music",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "default_value": 0.5,
                        "value_format": "{:.0%}"
                    }
                },
                {
                    "key": "cartella_announcements_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Cartella Announcements",
                        "description": "Enable audio announcements when registering cartella numbers",
                        "default_value": True
                    }
                }
            ],
            "advertising": [
                {
                    "key": "enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Advertising",
                        "description": "Show advertising section",
                        "default_value": True
                    }
                },
                {
                    "key": "hidden",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Hide Advertising",
                        "description": "Temporarily hide the advertising section (can be toggled by clicking on it)",
                        "default_value": False
                    }
                },
                {
                    "key": "text",
                    "type": self.COMPONENT_TEXT_INPUT,
                    "params": {
                        "label": "Advertising Text",
                        "description": "Text to display in the advertising section",
                        "default_value": "PLACE THE ADVERT HERE!"
                    }
                },
                {
                    "key": "font",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Font",
                        "description": "Font for advertising text",
                        "options": ["Ebrima", "Arial", "Verdana", "Tahoma", "Times New Roman", "Courier New", "Georgia", "Impact"],
                        "default_value": "Ebrima"
                    }
                },
                {
                    "key": "font_size",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Font Size",
                        "description": "Size of the advertising text",
                        "min_value": 12,
                        "max_value": 48,
                        "default_value": 24,
                        "value_format": "{:.0f}px"
                    }
                },
                {
                    "key": "text_color",
                    "type": self.COMPONENT_COLOR_PICKER,
                    "params": {
                        "label": "Text Color",
                        "description": "Color of the advertising text",
                        "default_value": "#F0C020"
                    }
                },
                {
                    "key": "scroll_speed",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Scroll Speed",
                        "description": "Speed of the scrolling text",
                        "min_value": 0.5,
                        "max_value": 5.0,
                        "default_value": 2.0,
                        "value_format": "{:.1f}x"
                    }
                },
                {
                    "key": "bold",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Bold Text",
                        "description": "Make the advertising text bold",
                        "default_value": True
                    }
                },
                {
                    "key": "italic",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Italic Text",
                        "description": "Make the advertising text italic",
                        "default_value": False
                    }
                },
                {
                    "key": "led_style",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "LED Style Background",
                        "description": "Use LED-style background for the advertising section",
                        "default_value": True
                    }
                },
                {
                    "key": "led_pixel_size",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "LED Pixel Size",
                        "description": "Size of the LED pixels in the background",
                        "min_value": 2,
                        "max_value": 8,
                        "default_value": 4,
                        "value_format": "{:.0f}px"
                    }
                },
                {
                    "key": "rainbow_text",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Rainbow Text",
                        "description": "Use rainbow colors for the advertising text",
                        "default_value": True
                    }
                },
                {
                    "key": "text_glow",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Text Glow",
                        "description": "Add glow effect to the advertising text",
                        "default_value": True
                    }
                }
            ],
            "language": [
                {
                    "key": "import_language",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Language",
                        "description": "Import a custom language file from JSON",
                        "icon": "📥"
                    }
                },
                {
                    "key": "export_language",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Export Language",
                        "description": "Export current language settings to JSON",
                        "icon": "📤"
                    }
                }
            ],
            "boards": [
                {
                    "key": "import_preset",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Board File",
                        "description": "Import a board JSON file (automatically adds to presets and applies)",
                        "icon": "📥"
                    }
                }
            ],
            "import_export": [
                {
                    "key": "auto_backup",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Auto Backup",
                        "description": "Automatically backup game data",
                        "default_value": True
                    }
                },
                {
                    "key": "backup_interval",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Backup Interval",
                        "description": "Time between automatic backups in minutes",
                        "min_value": 5,
                        "max_value": 60,
                        "default_value": 30,
                        "value_format": "{:.0f} min"
                    }
                },
                {
                    "key": "import_boards",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Boards",
                        "description": "Import bingo boards from a file (adds to presets)",
                        "icon": "📥"
                    }
                },
                {
                    "key": "default_export_format",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Default Export Format",
                        "description": "Default format for exporting stats reports",
                        "options": ["pdf", "html"],
                        "default_value": "pdf"
                    }
                },
                {
                    "key": "include_game_history",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Include Game History",
                        "description": "Include game history table in exported reports",
                        "default_value": True
                    }
                },
                {
                    "key": "include_credit_history",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Include Credit History",
                        "description": "Include credit recharge history in exported reports",
                        "default_value": True
                    }
                },
                {
                    "key": "include_summary_data",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Include Summary Data",
                        "description": "Include summary cards data in exported reports",
                        "default_value": True
                    }
                },
                {
                    "key": "include_notifications",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Include Notifications",
                        "description": "Include current notification status in exported reports",
                        "default_value": True
                    }
                },
                {
                    "key": "auto_open_exported_file",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Auto-Open Exported Files",
                        "description": "Automatically open exported files after creation",
                        "default_value": True
                    }
                }
            ]
        }

    def load_sound_effects(self):
        """Load sound effects if enabled in settings"""
        self.button_click_sound = None

        # Check if sound effects are enabled in settings
        audio_settings = self.settings.get("audio", {})
        if isinstance(audio_settings, dict) and audio_settings.get("sound_effects_enabled", True):
            try:
                volume = audio_settings.get("sound_effects_volume", 0.7) if isinstance(audio_settings, dict) else 0.7
                self.button_click_sound = pygame.mixer.Sound("assets/sounds/button_click.wav")
                self.button_click_sound.set_volume(volume)
            except:
                print("Could not load sound effects")

    def run(self):
        """Main loop for the settings page"""
        clock = pygame.time.Clock()

        while self.running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    self.handle_mouse_click(event.pos, event.button)
                elif event.type == pygame.MOUSEBUTTONUP:
                    self.handle_mouse_button_up(event.pos, event.button)
                elif event.type == pygame.MOUSEMOTION:
                    self.handle_mouse_motion(event.pos)
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        if self.active_text_input:
                            # Cancel text input
                            self.active_text_input = None
                        else:
                            # Exit settings page
                            self.running = False
                            # Save settings when exiting
                            self.settings_manager.save_settings()
                    else:
                        # Handle text input
                        self.handle_key_press(event)

            # Update UI
            self.update()

            # Draw everything
            self.draw()

            # Update display
            pygame.display.flip()

            # Cap the frame rate
            clock.tick(60)

    # Simple clipboard functions using Windows clipboard
    def copy_to_clipboard(self, text):
        """Copy text to clipboard using Windows clipboard"""
        try:
            # Save text to a temporary file
            with open("temp_clipboard.txt", "w", encoding="utf-8") as f:
                f.write(text)

            # Use PowerShell to read the file and set clipboard
            cmd = ['powershell', '-command', f'Get-Content -Path "temp_clipboard.txt" -Encoding UTF8 | Set-Clipboard']
            subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Clean up
            os.remove("temp_clipboard.txt")

            print(f"Copied to clipboard: {text}")
            print(f"Hex representation: {text.encode('utf-8').hex()}")
            return True
        except Exception as e:
            print(f"Error copying to clipboard: {e}")
            return False

    def paste_from_clipboard(self):
        """Paste text from clipboard using Windows clipboard"""
        try:
            # Use PowerShell to get clipboard content and save to a file
            cmd = ['powershell', '-command', 'Get-Clipboard | Out-File -FilePath "temp_clipboard.txt" -Encoding utf8']
            subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Read the file
            if os.path.exists("temp_clipboard.txt"):
                with open("temp_clipboard.txt", "r", encoding="utf-8") as f:
                    text = f.read().strip()

                # Clean up
                os.remove("temp_clipboard.txt")

                # Remove BOM (Byte Order Mark) if present
                if text.startswith('\ufeff'):
                    text = text.replace('\ufeff', '')
                    print("Removed BOM character from clipboard text")

                print(f"Pasted from clipboard: {text}")
                print(f"Hex representation: {text.encode('utf-8').hex()}")
                return text
            return ""
        except Exception as e:
            print(f"Error pasting from clipboard: {e}")
            return ""

    def handle_key_press(self, event):
        """Handle keyboard input"""
        if not self.active_text_input:
            return

        # Check for keyboard shortcuts (Ctrl+C, Ctrl+V, Ctrl+X)
        ctrl_pressed = pygame.key.get_mods() & pygame.KMOD_CTRL

        if ctrl_pressed:
            if event.key == pygame.K_c:  # Ctrl+C (Copy)
                # Copy text to clipboard
                if self.copy_to_clipboard(self.text_input_value):
                    self.show_message("Text copied to clipboard", "success")
                else:
                    self.show_message("Failed to copy text", "error")
                return

            elif event.key == pygame.K_v:  # Ctrl+V (Paste)
                # Paste text from clipboard
                clipboard_text = self.paste_from_clipboard()
                if clipboard_text:
                    # Check if clipboard contains Amharic text
                    has_amharic = any(0x1200 <= ord(c) <= 0x137F or 0x1380 <= ord(c) <= 0x139F or 0x2D80 <= ord(c) <= 0x2DDF for c in clipboard_text)
                    if has_amharic and self.active_text_input == "text" and self.active_tab == "advertising":
                        # Set font to Ebrima for Amharic text
                        self.settings_manager.set_setting("advertising", "font", "Ebrima")
                        print(f"Setting font to Ebrima for Amharic text from clipboard")

                    # Filter out control characters
                    filtered_text = ''.join(c for c in clipboard_text if ord(c) >= 32 or ord(c) in (9, 10, 13))

                    # Add clipboard text to current text
                    self.text_input_value += filtered_text
                    self.show_message("Text pasted from clipboard", "success")
                else:
                    self.show_message("Clipboard is empty", "info")
                return

            elif event.key == pygame.K_x:  # Ctrl+X (Cut)
                # Copy text to clipboard and clear input
                if self.copy_to_clipboard(self.text_input_value):
                    self.text_input_value = ""
                    self.show_message("Text cut to clipboard", "success")
                else:
                    self.show_message("Failed to cut text", "error")
                return

        # Get the key pressed
        if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
            # Confirm text input
            if self.active_text_input:
                # Get the category for this setting
                category = self.get_category_for_setting(self.active_text_input)

                # For advertising text, ensure we're using Ebrima font for Amharic
                if self.active_text_input == "text" and self.active_tab == "advertising":
                    # Check if text contains Amharic characters
                    if any(0x1200 <= ord(c) <= 0x137F or 0x1380 <= ord(c) <= 0x139F or 0x2D80 <= ord(c) <= 0x2DDF for c in self.text_input_value):
                        # Set font to Ebrima for Amharic text
                        self.settings_manager.set_setting("advertising", "font", "Ebrima")
                        print(f"Setting font to Ebrima for Amharic text")

                # Print debug info
                print(f"Setting {self.active_text_input} to: {self.text_input_value}")
                print(f"Hex representation: {self.text_input_value.encode('utf-8').hex()}")

                # Update the setting
                self.settings_manager.set_setting(category, self.active_text_input, self.text_input_value)

                # Show message
                self.show_message(f"{self.active_text_input.replace('_', ' ').title()} set to {self.text_input_value}", "success")

                # Deactivate text input
                self.active_text_input = None
        elif event.key == pygame.K_BACKSPACE:
            # Remove last character
            self.text_input_value = self.text_input_value[:-1]
            # Reset cursor blink
            self.text_input_cursor_visible = True
            self.text_input_cursor_timer = 0
        elif event.unicode:
            # Filter out control characters (like 0x16)
            if ord(event.unicode) >= 32 or ord(event.unicode) in (9, 10, 13):  # Allow tab, newline, and carriage return
                # Add character to text input
                self.text_input_value += event.unicode
                # Reset cursor blink
                self.text_input_cursor_visible = True
                self.text_input_cursor_timer = 0

                # For advertising text, check if we're adding Amharic characters
                if self.active_text_input == "text" and self.active_tab == "advertising":
                    # Check if this is an Amharic character
                    if 0x1200 <= ord(event.unicode) <= 0x137F or 0x1380 <= ord(event.unicode) <= 0x139F or 0x2D80 <= ord(event.unicode) <= 0x2DDF:
                        # Set font to Ebrima for Amharic text
                        self.settings_manager.set_setting("advertising", "font", "Ebrima")
                        print(f"Amharic character detected: {event.unicode}, Unicode: U+{ord(event.unicode):04X}")
                        print(f"Setting font to Ebrima for Amharic text")

    def update(self):
        """Update UI elements and animations"""
        # Update message timer
        if self.message_timer > 0:
            self.message_timer -= 1

        # Update button animations
        self.update_button_animations()

        # Update text input cursor blink
        if self.active_text_input:
            self.text_input_cursor_timer += 1
            if self.text_input_cursor_timer > 30:  # Blink every half second (30 frames at 60 FPS)
                self.text_input_cursor_visible = not self.text_input_cursor_visible
                self.text_input_cursor_timer = 0

    def update_button_animations(self):
        """Update button hover and click animations"""
        current_time = pygame.time.get_ticks()
        mouse_pos = pygame.mouse.get_pos()

        for btn_id, btn_state in self.button_states.items():
            # Skip if button doesn't have a hit area
            if btn_id not in self.hit_areas:
                continue

            # Check if mouse is hovering over button
            hover = self.hit_areas[btn_id].collidepoint(mouse_pos)

            # Update click animation
            if btn_state["click"]:
                # If click animation has been playing for 200ms, end it
                if current_time - btn_state["click_time"] > 200:
                    btn_state["click"] = False

            # Animate hover alpha
            if hover and btn_state["hover_alpha"] < 255:
                # Fade in (faster)
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 25)
            elif not hover and btn_state["hover_alpha"] > 0:
                # Fade out (slower)
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 15)

    def draw(self):
        """Draw the settings page"""
        # Get current screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Draw background
        self.draw_background(screen_width, screen_height)

        # Draw navigation bar at the top
        self.draw_navigation_bar(screen_width, screen_height)

        # Draw WOW BINGO header and get the header height
        header_height = self.draw_wow_bingo_header()

        # Draw settings title
        title_font = pygame.font.SysFont("Arial", int(36 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("SETTINGS", True, WHITE)
        title_rect = title_text.get_rect(centerx=screen_width // 2, y=header_height)
        self.screen.blit(title_text, title_rect)

        # Draw settings tabs
        self.draw_settings_tabs(screen_width, screen_height, title_rect.bottom + int(20 * self.scale_y))

        # Draw toast message if active
        if self.message_timer > 0:
            self.draw_toast_message()

    def draw_background(self, screen_width, screen_height):
        """Draw the background with gradient"""
        # Create a surface for the background if not already created
        if not hasattr(self, '_bg_surface') or self._bg_surface.get_size() != (screen_width, screen_height):
            self._bg_surface = pygame.Surface((screen_width, screen_height))

            # Draw gradient background
            for y in range(screen_height):
                # Calculate gradient color (dark blue to slightly lighter blue)
                ratio = y / screen_height
                r = 20 * (1 - ratio) + 30 * ratio
                g = 30 * (1 - ratio) + 40 * ratio
                b = 45 * (1 - ratio) + 55 * ratio
                pygame.draw.line(self._bg_surface, (r, g, b), (0, y), (screen_width, y))

        self.screen.blit(self._bg_surface, (0, 0))

    def draw_navigation_bar(self, screen_width, _):
        # We don't use screen_height but keep the parameter for consistency with other methods
        """Draw a modern compact navigation menu at the top right of the screen"""
        # Configuration for the modern navbar
        nav_height = int(40 * self.scale_y)
        nav_width = int(screen_width * 0.4)  # 40% of screen width, positioned at right
        nav_x = screen_width - nav_width

        # Slightly transparent background for the nav bar
        nav_rect = pygame.Rect(nav_x, 0, nav_width, nav_height)
        nav_surface = pygame.Surface((nav_rect.width, nav_rect.height), pygame.SRCALPHA)
        nav_surface.fill((NAV_BAR_BG[0], NAV_BAR_BG[1], NAV_BAR_BG[2], 220))  # Semi-transparent
        self.screen.blit(nav_surface, nav_rect)

        # Add a subtle bottom border
        pygame.draw.line(self.screen, (60, 80, 100),
                        (nav_x, nav_height-1),
                        (screen_width, nav_height-1), 1)

        # Navigation items with modern icons
        nav_items = [
            {"id": "play", "text": "Game", "icon": "🎮"},
            {"id": "stats", "text": "Stats", "icon": "📊"},
            {"id": "settings", "text": "Settings", "icon": "⚙️"},
            {"id": "help", "text": "Help", "icon": "❓"}
        ]

        # Calculate positioning - more compact layout
        item_width = nav_width / len(nav_items)
        item_x_start = nav_x
        nav_font = pygame.font.SysFont("Arial", self.scaled_font_size(14), bold=True)

        # Draw each navigation item
        for i, item in enumerate(nav_items):
            # Calculate item position
            item_x = item_x_start + i * item_width
            item_rect = pygame.Rect(item_x, 0, item_width, nav_height)

            # Store hit area for this nav item
            self.hit_areas[f"nav_{item['id']}"] = item_rect

            # Check if this is the active nav item
            is_active = (item["id"] == self.active_nav)

            # Get button state for animations
            btn_state = self.button_states.get(f"nav_{item['id']}", {
                "hover": False,
                "click": False,
                "hover_alpha": 0
            })

            # Draw highlight for active or hovered item
            if is_active:
                # Active item gets a solid highlight
                highlight_color = (60, 130, 200, 200)  # Blue highlight
                pygame.draw.rect(self.screen, highlight_color, item_rect)
            elif btn_state["hover_alpha"] > 0:
                # Hovered item gets a semi-transparent highlight
                highlight_color = (60, 130, 200, btn_state["hover_alpha"])
                highlight_surface = pygame.Surface((item_rect.width, item_rect.height), pygame.SRCALPHA)
                highlight_surface.fill(highlight_color)
                self.screen.blit(highlight_surface, item_rect)

            # Draw text and icon
            text_color = WHITE if is_active else LIGHT_GRAY

            # Calculate icon and text positions
            icon_size = int(18 * min(self.scale_x, self.scale_y))
            icon_x = item_x + item_width / 2
            icon_y = nav_height / 2 - icon_size / 2

            # Draw icon based on item ID
            if item["id"] == "play":
                # Game controller icon
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/2, icon_y + icon_size/4,
                                          icon_size, icon_size/2))
                pygame.draw.circle(self.screen, text_color,
                                 (int(icon_x - icon_size/4), int(icon_y + icon_size/4)),
                                 int(icon_size/6))
                pygame.draw.circle(self.screen, text_color,
                                 (int(icon_x + icon_size/4), int(icon_y + icon_size/4)),
                                 int(icon_size/6))

            elif item["id"] == "stats":
                # Stats bar chart icon
                bar_width = icon_size/5
                # Draw three bars of increasing height
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/2, icon_y,
                                          bar_width, icon_size/2))
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/6, icon_y - icon_size/4,
                                          bar_width, icon_size*3/4))
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x + icon_size/6, icon_y - icon_size/2,
                                          bar_width, icon_size))

            elif item["id"] == "settings":
                # Settings gear icon
                center = (int(icon_x), int(icon_y + icon_size/2))
                outer_radius = int(icon_size/2)
                inner_radius = int(icon_size/4)
                teeth = 8

                for i in range(teeth):
                    angle = 2 * math.pi * i / teeth
                    next_angle = 2 * math.pi * (i + 0.5) / teeth

                    # Outer point
                    x1 = center[0] + outer_radius * math.cos(angle)
                    y1 = center[1] + outer_radius * math.sin(angle)

                    # Inner point
                    x2 = center[0] + inner_radius * math.cos(next_angle)
                    y2 = center[1] + inner_radius * math.sin(next_angle)

                    # Next outer point
                    x3 = center[0] + outer_radius * math.cos(next_angle + math.pi/teeth)
                    y3 = center[1] + outer_radius * math.sin(next_angle + math.pi/teeth)

                    # Draw triangle for gear tooth
                    pygame.draw.polygon(self.screen, text_color, [(x1, y1), (x2, y2), (x3, y3)])

                # Draw center circle
                pygame.draw.circle(self.screen, text_color, center, int(icon_size/6))

            elif item["id"] == "help":
                # Help question mark icon
                center = (int(icon_x), int(icon_y + icon_size/2))
                radius = int(icon_size/2)

                # Draw circle
                pygame.draw.circle(self.screen, text_color, center, radius, 2)

                # Draw question mark
                question_font = pygame.font.SysFont("Arial", int(icon_size * 0.8), bold=True)
                question_text = question_font.render("?", True, text_color)
                question_rect = question_text.get_rect(center=center)
                self.screen.blit(question_text, question_rect)

            # Draw text below icon
            text_surf = nav_font.render(item["text"], True, text_color)
            text_rect = text_surf.get_rect(centerx=item_x + item_width/2,
                                         centery=nav_height - int(10 * self.scale_y))
            self.screen.blit(text_surf, text_rect)

    def draw_wow_bingo_header(self):
        """Draw the WOW Games header consistent with the main window"""
        # Use the common header implementation
        return draw_wow_bingo_header(self.screen, self.scale_x, self.scale_y)

    def draw_settings_tabs(self, screen_width, screen_height, start_y):
        """Draw the settings tabs and content"""
        # Define tabs
        tabs = [
            {"id": "game", "text": "Game", "icon": "🎮"},
            {"id": "display", "text": "Display", "icon": "🖥️"},
            {"id": "audio", "text": "Audio", "icon": "🔊"},
            {"id": "notifications", "text": "Notifications", "icon": "🔔"},
            {"id": "advertising", "text": "Advertising", "icon": "📢"},
            {"id": "language", "text": "Language", "icon": "🌐"},
            {"id": "boards", "text": "Boards", "icon": "🎯"},
            {"id": "import_export", "text": "Import/Export", "icon": "📤"}
        ]

        # Calculate tab dimensions
        tab_height = int(50 * self.scale_y)
        tab_width = int(screen_width / len(tabs))
        tab_y = start_y

        # Draw tab background
        tab_bg_rect = pygame.Rect(0, tab_y, screen_width, tab_height)
        tab_bg_surface = pygame.Surface((tab_bg_rect.width, tab_bg_rect.height), pygame.SRCALPHA)
        tab_bg_surface.fill((30, 40, 60, 220))  # Semi-transparent dark blue
        self.screen.blit(tab_bg_surface, tab_bg_rect)

        # Draw tabs
        for i, tab in enumerate(tabs):
            tab_x = i * tab_width
            tab_rect = pygame.Rect(tab_x, tab_y, tab_width, tab_height)

            # Store hit area for this tab
            self.hit_areas[f"tab_{tab['id']}"] = tab_rect

            # Check if this is the active tab
            is_active = (tab["id"] == self.active_tab)

            # Draw tab background
            if is_active:
                # Active tab gets a brighter background
                pygame.draw.rect(self.screen, (50, 70, 100), tab_rect)
                # Draw bottom highlight line
                highlight_color = self.get_tab_color(tab["id"])
                pygame.draw.rect(self.screen, highlight_color,
                               pygame.Rect(tab_x, tab_y + tab_height - 3, tab_width, 3))
            elif tab_rect.collidepoint(pygame.mouse.get_pos()):
                # Hovered tab gets a slightly brighter background
                hover_surface = pygame.Surface((tab_rect.width, tab_rect.height), pygame.SRCALPHA)
                hover_surface.fill((50, 70, 100, 150))  # Semi-transparent highlight
                self.screen.blit(hover_surface, tab_rect)

            # Draw tab text
            tab_font = pygame.font.SysFont("Arial", self.scaled_font_size(16), bold=is_active)
            text_color = WHITE if is_active else LIGHT_GRAY
            tab_text = tab_font.render(tab["text"], True, text_color)
            text_rect = tab_text.get_rect(center=(tab_x + tab_width/2, tab_y + tab_height/2))
            self.screen.blit(tab_text, text_rect)

        # Draw content area
        content_y = tab_y + tab_height
        content_height = screen_height - content_y - int(40 * self.scale_y)  # Leave space at bottom
        content_rect = pygame.Rect(0, content_y, screen_width, content_height)

        # Draw content background
        pygame.draw.rect(self.screen, (20, 30, 50), content_rect)

        # Draw content based on active tab
        self.draw_tab_content(content_rect)

    def draw_tab_content(self, content_rect):
        """Draw the content for the active tab"""
        # Store the content rect for reference
        self.content_rect = content_rect

        # Clear previous hit areas for components (keep navigation and tabs)
        self.hit_areas = {k: v for k, v in self.hit_areas.items() if k.startswith("nav_") or k.startswith("tab_")}

        # Create a temporary surface for content with scrolling support
        content_surf = pygame.Surface((content_rect.width, 2000), pygame.SRCALPHA)
        content_surf.fill((20, 30, 50))

        # Calculate padding and spacing
        padding = int(20 * min(self.scale_x, self.scale_y))
        spacing = self.item_spacing

        # Draw section title
        title_x = padding
        title_y = padding
        title_text = f"{self.active_tab.replace('_', ' ').title()} Settings"
        title_font = pygame.font.SysFont("Arial", self.scaled_font_size(24), bold=True)
        title_surf = title_font.render(title_text, True, WHITE)
        content_surf.blit(title_surf, (title_x, title_y))

        # Draw settings items
        y_offset = title_y + title_surf.get_height() + spacing

        # Get settings for the active tab
        if self.active_tab in self.settings_definitions:
            settings = self.settings_definitions[self.active_tab]

            # Save current screen
            original_screen = self.screen
            self.screen = content_surf

            # Draw each setting component
            for setting in settings:
                item_rect = pygame.Rect(padding, y_offset, content_rect.width - padding * 2, self.item_height)
                self.draw_setting_component(item_rect, setting["type"], setting["key"], setting["params"])
                y_offset += self.item_height + spacing

            # Restore original screen
            self.screen = original_screen
        else:
            # No settings defined for this tab
            no_settings_text = "No settings available for this tab"
            no_settings_font = pygame.font.SysFont("Arial", self.scaled_font_size(18))
            no_settings_surf = no_settings_font.render(no_settings_text, True, LIGHT_GRAY)
            content_surf.blit(no_settings_surf, (title_x, y_offset))
            y_offset += no_settings_surf.get_height() + spacing

        # Calculate the actual content height
        self.content_height = y_offset + int(20 * self.scale_y)  # Add some padding at the bottom

        # Calculate maximum scroll value
        self.visible_content_height = content_rect.height
        self.max_scroll = max(0, self.content_height - self.visible_content_height)

        # Ensure scroll_y is within valid range
        self.scroll_y = min(self.max_scroll, self.scroll_y)

        # Draw the visible portion of content
        self.screen.blit(content_surf, content_rect.topleft,
                        (0, self.scroll_y, content_rect.width, content_rect.height))

        # Adjust all hit areas to account for content rect position and scrolling
        self.adjust_hit_areas(content_rect)

        # Draw scrollbar if needed
        if self.content_height > self.visible_content_height:
            self.draw_scrollbar(content_rect)

    def adjust_hit_areas(self, content_rect):
        """Adjust all hit areas to account for content rect position and scrolling"""
        # Create a copy of hit areas to iterate over while modifying the original
        hit_areas_copy = self.hit_areas.copy()

        for key, rect in hit_areas_copy.items():
            # Skip navigation and tab hit areas
            if key.startswith("nav_") or key.startswith("tab_"):
                continue

            # Adjust the rect to account for content rect position and scrolling
            adjusted_rect = rect.copy()
            adjusted_rect.x += content_rect.x
            adjusted_rect.y += content_rect.y - self.scroll_y

            # Update the hit area with the adjusted rect
            self.hit_areas[key] = adjusted_rect

    def draw_setting_component(self, rect, component_type, setting_key, params):
        """Draw a setting component based on its type"""
        if params is None:
            params = {}

        # Get default parameters
        label = params.get('label', setting_key.replace('_', ' ').title())
        description = params.get('description', None)
        default_value = params.get('default_value', None)

        # Draw the appropriate component based on type
        if component_type == self.COMPONENT_TOGGLE:
            self.draw_toggle_component(rect, setting_key, label, description, default_value)
        elif component_type == self.COMPONENT_SLIDER:
            min_value = params.get('min_value', 0)
            max_value = params.get('max_value', 1)
            value_format = params.get('value_format', '{:.1f}')
            self.draw_slider_component(rect, setting_key, label, description, min_value, max_value, default_value, value_format)
        elif component_type == self.COMPONENT_BUTTON:
            icon = params.get('icon', None)
            self.draw_button_component(rect, setting_key, label, description, icon)
        elif component_type == self.COMPONENT_TEXT_INPUT:
            self.draw_text_input_component(rect, setting_key, label, description, default_value)
        elif component_type == self.COMPONENT_DROPDOWN:
            options = params.get('options', [])
            self.draw_dropdown_component(rect, setting_key, label, description, options, default_value)
        elif component_type == self.COMPONENT_COLOR_PICKER:
            self.draw_color_picker_component(rect, setting_key, label, description, default_value)

    def draw_toggle_component(self, rect, setting_key, label, description, default_value):
        """Draw a toggle switch for boolean settings"""
        # Get category for this setting
        category = self.get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # We used to get mouse position for hover effects, but now we handle that in handle_mouse_motion

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate toggle dimensions
        toggle_width = int(60 * min(self.scale_x, self.scale_y))
        toggle_height = int(30 * min(self.scale_x, self.scale_y))
        toggle_x = rect.right - toggle_width - int(20 * self.scale_x)
        toggle_y = rect.y + (rect.height - toggle_height) // 2

        # Draw toggle background
        toggle_rect = pygame.Rect(toggle_x, toggle_y, toggle_width, toggle_height)
        toggle_color = (50, 150, 50) if current_value else (150, 50, 50)
        pygame.draw.rect(self.screen, toggle_color, toggle_rect, border_radius=int(toggle_height // 2))

        # Draw toggle handle
        handle_size = toggle_height - int(6 * min(self.scale_x, self.scale_y))
        handle_x = toggle_x + toggle_width - handle_size - int(3 * min(self.scale_x, self.scale_y)) if current_value else toggle_x + int(3 * min(self.scale_x, self.scale_y))
        handle_y = toggle_y + int(3 * min(self.scale_x, self.scale_y))
        handle_rect = pygame.Rect(handle_x, handle_y, handle_size, handle_size)
        pygame.draw.rect(self.screen, WHITE, handle_rect, border_radius=int(handle_size // 2))

        # Store hit area for this toggle
        self.hit_areas[f"toggle_{setting_key}"] = toggle_rect

    def draw_slider_component(self, rect, setting_key, label, description, min_value, max_value, default_value, value_format):
        """Draw a slider for numeric settings"""
        # Get category for this setting
        category = self.get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # We used to get mouse position for hover effects, but now we handle that in handle_mouse_motion

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate slider dimensions
        slider_width = int(200 * min(self.scale_x, self.scale_y))
        slider_height = int(6 * min(self.scale_x, self.scale_y))
        slider_x = rect.x + int(15 * self.scale_x)
        slider_y = rect.y + rect.height - slider_height - int(20 * self.scale_y)

        # Draw slider track
        slider_rect = pygame.Rect(slider_x, slider_y, slider_width, slider_height)
        pygame.draw.rect(self.screen, (50, 60, 80), slider_rect, border_radius=int(slider_height // 2))

        # Calculate handle position based on current value
        value_ratio = (current_value - min_value) / (max_value - min_value)
        handle_x = slider_x + int(value_ratio * slider_width)
        handle_size = int(20 * min(self.scale_x, self.scale_y))
        handle_y = slider_y + slider_height // 2 - handle_size // 2
        handle_rect = pygame.Rect(handle_x - handle_size // 2, handle_y, handle_size, handle_size)

        # Draw filled portion of slider
        filled_width = handle_x - slider_x
        filled_rect = pygame.Rect(slider_x, slider_y, filled_width, slider_height)
        pygame.draw.rect(self.screen, self.get_tab_color(self.active_tab), filled_rect, border_radius=int(slider_height // 2))

        # Draw handle
        pygame.draw.circle(self.screen, WHITE, (handle_x, slider_y + slider_height // 2), handle_size // 2)

        # Draw current value
        value_str = value_format.format(current_value)
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_text = value_font.render(value_str, True, WHITE)
        value_x = slider_x + slider_width + int(15 * self.scale_x)
        value_y = slider_y - value_text.get_height() // 2 + slider_height // 2
        self.screen.blit(value_text, (value_x, value_y))

        # Store hit areas for this slider
        self.hit_areas[f"slider_{setting_key}"] = slider_rect
        self.hit_areas[f"slider_handle_{setting_key}"] = handle_rect

    def draw_button_component(self, rect, setting_key, label, description, icon=None):
        """Draw a button for action settings"""
        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = rect.collidepoint(mouse_pos)

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate button dimensions
        button_width = int(150 * min(self.scale_x, self.scale_y))
        button_height = int(40 * min(self.scale_x, self.scale_y))
        button_x = rect.right - button_width - int(20 * self.scale_x)
        button_y = rect.y + (rect.height - button_height) // 2

        # Draw button
        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        button_color = self.get_tab_color(self.active_tab)
        if is_hovered:
            # Lighten color when hovered
            button_color = tuple(min(255, c + 30) for c in button_color)
        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw button text
        button_text = label
        button_font = pygame.font.SysFont("Arial", self.scaled_font_size(16), bold=True)
        text_surf = button_font.render(button_text, True, WHITE)
        text_rect = text_surf.get_rect(center=button_rect.center)

        # Adjust for icon if provided
        if icon:
            # Calculate icon and text positions
            icon_size = int(24 * min(self.scale_x, self.scale_y))
            icon_font = pygame.font.SysFont("Arial", icon_size)
            icon_surf = icon_font.render(icon, True, WHITE)
            icon_rect = icon_surf.get_rect(centery=button_rect.centery)

            # Position icon and text side by side
            total_width = icon_surf.get_width() + text_surf.get_width() + int(10 * min(self.scale_x, self.scale_y))
            icon_rect.x = button_rect.centerx - total_width // 2
            text_rect.x = icon_rect.right + int(10 * min(self.scale_x, self.scale_y))

            # Draw icon
            self.screen.blit(icon_surf, icon_rect)

        # Draw text
        self.screen.blit(text_surf, text_rect)

        # Store hit area for this button
        self.hit_areas[f"button_{setting_key}"] = button_rect

    def draw_text_input_component(self, rect, setting_key, label, description, default_value):
        """Draw a text input field for string settings with Amharic support"""
        # Get category for this setting
        category = self.get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Check if this input is active
        is_active = (self.active_text_input == setting_key)

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate input field dimensions
        input_width = int(300 * min(self.scale_x, self.scale_y))
        input_height = int(40 * min(self.scale_x, self.scale_y))
        input_x = rect.x + int(15 * self.scale_x)
        input_y = rect.y + rect.height - input_height - int(20 * self.scale_y)

        # Draw input field with different border color if active
        input_rect = pygame.Rect(input_x, input_y, input_width, input_height)
        pygame.draw.rect(self.screen, (20, 30, 50), input_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)))

        # Use accent color for active input field
        border_color = self.get_tab_color(self.active_tab) if is_active else (50, 60, 80)
        pygame.draw.rect(self.screen, border_color, input_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)), width=2 if is_active else 1)

        # Determine text to display
        display_text = self.text_input_value if is_active else str(current_value)

        # Special handling for Amharic text in advertising
        has_amharic = False
        if setting_key == "text" and self.active_tab == "advertising":
            # Check if text contains Amharic characters
            has_amharic = any(0x1200 <= ord(c) <= 0x137F or 0x1380 <= ord(c) <= 0x139F or 0x2D80 <= ord(c) <= 0x2DDF for c in display_text)

        # Choose appropriate font
        font_name = "Ebrima" if has_amharic else "Arial"
        value_font = pygame.font.SysFont(font_name, self.scaled_font_size(16))

        # Calculate text position
        value_x = input_x + int(10 * self.scale_x)
        value_y = input_y + (input_height - value_font.get_height()) // 2

        # Calculate maximum visible width
        max_visible_width = input_width - int(20 * self.scale_x)

        # Draw text with special handling for Amharic
        try:
            # Create a clipping rect
            clip_rect = pygame.Rect(input_x, input_y, max_visible_width, input_height)
            self.screen.set_clip(clip_rect)

            if has_amharic:
                # Use AmharicSupport for rendering
                AmharicSupport.draw_text(
                    self.screen,
                    display_text,
                    value_font,
                    WHITE,  # White text
                    value_x,
                    value_y
                )
            else:
                # Use standard rendering for non-Amharic text
                value_text = value_font.render(display_text, True, WHITE)
                if value_text.get_width() > max_visible_width:
                    self.screen.blit(value_text, (value_x, value_y))
                else:
                    self.screen.blit(value_text, (value_x, value_y))

            self.screen.set_clip(None)
        except Exception as e:
            print(f"Error rendering text: {e}")
            # Reset clip in case of error
            self.screen.set_clip(None)
            # Fallback to standard rendering
            try:
                value_text = value_font.render(display_text, True, WHITE)
                self.screen.blit(value_text, (value_x, value_y))
            except:
                pass

        # Draw cursor if active and cursor is visible
        if is_active and self.text_input_cursor_visible:
            try:
                # Calculate cursor position
                if has_amharic:
                    # For Amharic text, estimate cursor position based on character count
                    char_width = int(value_font.get_height() * 0.6)  # Estimate character width
                    cursor_x = value_x + min(len(display_text) * char_width, max_visible_width)
                else:
                    # For non-Amharic text, use standard font metrics
                    cursor_text = value_font.render(display_text, True, WHITE)
                    cursor_x = value_x + min(cursor_text.get_width(), max_visible_width)

                cursor_y = value_y
                cursor_height = value_font.get_height()

                # Draw cursor line
                pygame.draw.line(self.screen, WHITE,
                               (cursor_x, cursor_y),
                               (cursor_x, cursor_y + cursor_height),
                               2)
            except Exception as e:
                print(f"Error drawing cursor: {e}")

        # Add copy/paste buttons for advertising text
        if setting_key == "text" and self.active_tab == "advertising":
            # Add copy/paste buttons
            button_size = int(40 * min(self.scale_x, self.scale_y))
            button_y = input_y + (input_height - button_size) // 2

            # Copy button
            copy_button_x = input_x + input_width + int(10 * self.scale_x)
            copy_button_rect = pygame.Rect(copy_button_x, button_y, button_size, button_size)

            # Draw copy button
            pygame.draw.rect(
                self.screen,
                (60, 120, 200),  # Blue button
                copy_button_rect,
                border_radius=int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw copy icon (simplified document icon)
            icon_padding = int(10 * min(self.scale_x, self.scale_y))
            pygame.draw.rect(
                self.screen,
                WHITE,  # White icon
                pygame.Rect(
                    copy_button_x + icon_padding,
                    button_y + icon_padding,
                    button_size - (icon_padding * 2),
                    button_size - (icon_padding * 2)
                ),
                width=2,
                border_radius=int(2 * min(self.scale_x, self.scale_y))
            )

            # Paste button
            paste_button_x = copy_button_x + button_size + int(5 * self.scale_x)
            paste_button_rect = pygame.Rect(paste_button_x, button_y, button_size, button_size)

            # Draw paste button
            pygame.draw.rect(
                self.screen,
                (60, 120, 200),  # Blue button
                paste_button_rect,
                border_radius=int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw paste icon (clipboard icon)
            pygame.draw.rect(
                self.screen,
                WHITE,  # White icon
                pygame.Rect(
                    paste_button_x + icon_padding,
                    button_y + icon_padding,
                    button_size - (icon_padding * 2),
                    button_size - (icon_padding * 2)
                ),
                width=2,
                border_radius=int(2 * min(self.scale_x, self.scale_y))
            )

            # Store hit areas for copy/paste buttons
            self.hit_areas[f"copy_button_{setting_key}"] = copy_button_rect
            self.hit_areas[f"paste_button_{setting_key}"] = paste_button_rect

        # Store hit area for this input field
        self.hit_areas[f"text_input_{setting_key}"] = input_rect

        return input_rect

    def draw_dropdown_component(self, rect, setting_key, label, description, options, default_value):
        """Draw a dropdown menu for selection settings"""
        # This is a simplified version - in a real implementation, you would need to handle dropdown expansion
        # Get category for this setting
        category = self.get_category_for_setting(setting_key)

        # Get options for this setting if not provided or empty
        if not options:
            options = self.settings_manager.get_options_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate dropdown dimensions
        dropdown_width = int(200 * min(self.scale_x, self.scale_y))
        dropdown_height = int(40 * min(self.scale_x, self.scale_y))
        dropdown_x = rect.right - dropdown_width - int(20 * self.scale_x)
        dropdown_y = rect.y + (rect.height - dropdown_height) // 2

        # Draw dropdown
        dropdown_rect = pygame.Rect(dropdown_x, dropdown_y, dropdown_width, dropdown_height)
        pygame.draw.rect(self.screen, (20, 30, 50), dropdown_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)))
        pygame.draw.rect(self.screen, (50, 60, 80), dropdown_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)), width=1)

        # Draw current value
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_text = value_font.render(str(current_value), True, WHITE)
        value_x = dropdown_x + int(10 * self.scale_x)
        value_y = dropdown_y + (dropdown_height - value_text.get_height()) // 2
        self.screen.blit(value_text, (value_x, value_y))

        # Draw dropdown arrow
        arrow_size = int(10 * min(self.scale_x, self.scale_y))
        arrow_x = dropdown_rect.right - arrow_size - int(10 * self.scale_x)
        arrow_y = dropdown_rect.centery - arrow_size // 2
        pygame.draw.polygon(self.screen, WHITE, [
            (arrow_x, arrow_y),
            (arrow_x + arrow_size, arrow_y),
            (arrow_x + arrow_size // 2, arrow_y + arrow_size)
        ])

        # Store hit area for this dropdown
        self.hit_areas[f"dropdown_{setting_key}"] = dropdown_rect

    def draw_color_picker_component(self, rect, setting_key, label, description, default_value):
        """Draw a color picker for color settings"""
        # This is a simplified version - in a real implementation, you would need to handle color picker expansion
        # Get category for this setting
        category = self.get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Draw setting background
        pygame.draw.rect(self.screen, (30, 40, 60), rect, border_radius=int(10 * min(self.scale_x, self.scale_y)))

        # Draw label
        label_font = pygame.font.SysFont("Arial", self.scaled_font_size(18), bold=True)
        label_text = label_font.render(label, True, WHITE)
        self.screen.blit(label_text, (rect.x + int(15 * self.scale_x), rect.y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = pygame.font.SysFont("Arial", self.scaled_font_size(14))
            desc_text = desc_font.render(description, True, LIGHT_GRAY)
            desc_y = rect.y + label_text.get_height() + int(20 * self.scale_y)
            self.screen.blit(desc_text, (rect.x + int(15 * self.scale_x), desc_y))

        # Calculate color picker dimensions
        picker_size = int(40 * min(self.scale_x, self.scale_y))
        picker_x = rect.right - picker_size - int(20 * self.scale_x)
        picker_y = rect.y + (rect.height - picker_size) // 2

        # Parse color value (assuming it's in hex format like "#FF0000")
        try:
            if current_value.startswith('#'):
                color_value = current_value.lstrip('#')
                color = tuple(int(color_value[i:i+2], 16) for i in (0, 2, 4))
            else:
                color = (255, 255, 255)  # Default to white if invalid
        except:
            color = (255, 255, 255)  # Default to white if parsing fails

        # Draw color picker
        picker_rect = pygame.Rect(picker_x, picker_y, picker_size, picker_size)
        pygame.draw.rect(self.screen, color, picker_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)))
        pygame.draw.rect(self.screen, (50, 60, 80), picker_rect, border_radius=int(5 * min(self.scale_x, self.scale_y)), width=1)

        # Draw current value as hex
        value_font = pygame.font.SysFont("Arial", self.scaled_font_size(16))
        value_text = value_font.render(current_value if isinstance(current_value, str) else str(color), True, WHITE)
        value_x = picker_x - value_text.get_width() - int(10 * self.scale_x)
        value_y = picker_y + (picker_size - value_text.get_height()) // 2
        self.screen.blit(value_text, (value_x, value_y))

        # Store hit area for this color picker
        self.hit_areas[f"color_picker_{setting_key}"] = picker_rect

        # Add predefined color options
        predefined_colors = [
            "#FFFFFF",  # White
            "#F0C020",  # Gold
            "#FF0000",  # Red
            "#00FF00",  # Green
            "#0000FF",  # Blue
            "#FFFF00",  # Yellow
            "#FF00FF",  # Magenta
            "#00FFFF"   # Cyan
        ]

        # Draw predefined color swatches below the main picker
        swatch_size = int(20 * min(self.scale_x, self.scale_y))
        swatch_spacing = int(5 * min(self.scale_x, self.scale_y))
        swatch_y = picker_y + picker_size + swatch_spacing

        # Only show predefined colors if there's enough space
        if swatch_y + swatch_size < rect.bottom - int(10 * self.scale_y):
            for i, hex_color in enumerate(predefined_colors):
                swatch_x = picker_x - (len(predefined_colors) - 1 - i) * (swatch_size + swatch_spacing)
                swatch_rect = pygame.Rect(swatch_x, swatch_y, swatch_size, swatch_size)

                # Convert hex to RGB
                try:
                    color_value = hex_color.lstrip('#')
                    color = tuple(int(color_value[i:i+2], 16) for i in (0, 2, 4))
                except:
                    color = (255, 255, 255)  # Default to white if conversion fails

                # Draw color swatch
                pygame.draw.rect(self.screen, color, swatch_rect, border_radius=int(3 * min(self.scale_x, self.scale_y)))
                pygame.draw.rect(self.screen, (50, 60, 80), swatch_rect, border_radius=int(3 * min(self.scale_x, self.scale_y)), width=1)

                # Store hit area for this predefined color
                self.hit_areas[f"color_preset_{setting_key}_{i}"] = swatch_rect

    def get_category_for_setting(self, setting_key):
        """Get the category for a setting based on its key"""
        # Check each tab for the setting
        for tab, settings in self.settings_definitions.items():
            for setting in settings:
                if setting["key"] == setting_key:
                    return tab

        # Default to the active tab if not found
        return self.active_tab

    def draw_scrollbar(self, content_rect):
        """Draw a scrollbar for the content area"""
        # Calculate scrollbar dimensions
        scrollbar_width = int(8 * self.scale_x)
        scrollbar_height = int(self.visible_content_height * (self.visible_content_height / self.content_height))
        scrollbar_x = content_rect.right - scrollbar_width - int(5 * self.scale_x)
        scrollbar_y = content_rect.y + int(self.scroll_y * (self.visible_content_height / self.content_height))

        # Draw scrollbar background
        scrollbar_bg_rect = pygame.Rect(scrollbar_x, content_rect.y, scrollbar_width, content_rect.height)
        pygame.draw.rect(self.screen, (40, 50, 70, 100), scrollbar_bg_rect, border_radius=int(4 * self.scale_x))

        # Draw scrollbar handle
        scrollbar_rect = pygame.Rect(scrollbar_x, scrollbar_y, scrollbar_width, scrollbar_height)
        pygame.draw.rect(self.screen, (100, 120, 150), scrollbar_rect, border_radius=int(4 * self.scale_x))

    def get_tab_color(self, tab_id):
        """Get the color for a specific tab"""
        tab_colors = {
            "game": self.ACCENT_COLOR,
            "display": self.ACCENT_TEAL,
            "audio": self.ACCENT_PURPLE,
            "notifications": (255, 200, 50),  # Yellow/Gold
            "advertising": (255, 180, 50),  # Orange
            "language": self.ACCENT_ORANGE,
            "boards": (50, 180, 100),  # Green
            "import_export": (220, 70, 70)  # Red
        }
        return tab_colors.get(tab_id, self.ACCENT_COLOR)

    def draw_toast_message(self):
        """Draw a toast message at the bottom of the screen"""
        if not self.message:
            return

        screen_width, screen_height = self.screen.get_size()

        # Determine color based on message type
        if self.message_type == "error":
            color = (200, 50, 50, min(255, self.message_timer * 3))
        elif self.message_type == "success":
            color = (50, 200, 50, min(255, self.message_timer * 3))
        else:  # info
            color = (50, 120, 200, min(255, self.message_timer * 3))

        # Create message surface
        font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)))
        text = font.render(self.message, True, WHITE)

        # Calculate position and size
        padding = int(20 * min(self.scale_x, self.scale_y))
        margin = int(20 * min(self.scale_x, self.scale_y))
        toast_width = text.get_width() + padding * 2
        toast_height = text.get_height() + padding

        toast_x = (screen_width - toast_width) // 2
        toast_y = screen_height - toast_height - margin

        # Draw rounded rectangle background
        toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
        pygame.draw.rect(
            toast_surface,
            color,
            pygame.Rect(0, 0, toast_width, toast_height),
            border_radius=int(10 * min(self.scale_x, self.scale_y))
        )

        # Draw text
        text_x = (toast_width - text.get_width()) // 2
        text_y = (toast_height - text.get_height()) // 2
        toast_surface.blit(text, (text_x, text_y))

        # Draw to screen
        self.screen.blit(toast_surface, (toast_x, toast_y))

    def adjust_pos_for_scroll(self, pos):
        """Adjust mouse position for scrolling"""
        # For components in the scrollable content area, adjust y position
        # This is needed because hit areas are stored in screen coordinates after adjustment,
        # but we need to convert mouse events to content coordinates
        x, y = pos

        # If we don't have a content rect yet, return original position
        if not hasattr(self, 'content_rect'):
            return pos

        # Check if the mouse is in the content area
        if self.content_rect.collidepoint(pos):
            # Convert screen coordinates to content coordinates
            # Subtract content_rect.y to get relative position within content area
            # Add scroll_y to account for scrolled content
            content_x = x - self.content_rect.x
            content_y = y - self.content_rect.y + self.scroll_y
            return (content_x, content_y)

        # If not in content area, return original position
        return pos

    def handle_mouse_click(self, pos, button):
        """Handle mouse click events"""
        # Reset text input state if clicking elsewhere
        if self.active_text_input and button == 1:  # Left click
            text_input_rect = self.hit_areas.get(f"text_input_{self.active_text_input}")
            if text_input_rect and not text_input_rect.collidepoint(pos):
                self.active_text_input = None

        # Check for direct interactions with navigation menu
        for key in self.hit_areas:
            if key.startswith("nav_") and self.hit_areas[key].collidepoint(pos):
                nav_item = key.replace("nav_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Set button click animation
                if key in self.button_states:
                    self.button_states[key]["click"] = True
                    self.button_states[key]["click_time"] = pygame.time.get_ticks()

                # Handle navigation
                if nav_item == "play":
                    # Return to main game screen
                    self.running = False
                    # Save settings when exiting
                    self.settings_manager.save_settings()
                elif nav_item == "stats":
                    # Go to stats screen with screen mode consistency
                    self.running = False
                    # Save settings when exiting
                    self.settings_manager.save_settings()
                    # Import and show stats page with screen mode consistency
                    try:
                        from stats_page import show_stats_page
                        # Ensure screen mode consistency before navigation
                        current_screen = self.screen_mode_manager.ensure_consistent_mode(self.screen)
                        show_stats_page(current_screen, previous_page="settings")
                    except Exception as e:
                        print(f"Error showing stats page: {e}")
                elif nav_item == "settings":
                    # Already on settings screen
                    self.show_message("Already on Settings screen", "info")
                elif nav_item == "help":
                    self.show_message("Help feature coming soon", "info")

                return

        # Check for tab clicks
        for key in self.hit_areas:
            if key.startswith("tab_") and self.hit_areas[key].collidepoint(pos):
                tab_id = key.replace("tab_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Change active tab
                if tab_id != self.active_tab:
                    self.active_tab = tab_id
                    # Reset scroll position when changing tabs
                    self.scroll_y = 0
                    # Show message
                    self.show_message(f"Switched to {tab_id.replace('_', ' ').title()} settings", "info")

                return

        # Check for toggle clicks
        for key in self.hit_areas:
            if key.startswith("toggle_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("toggle_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Toggle the setting value
                category = self.get_category_for_setting(setting_key)
                current_value = self.settings_manager.get_setting(category, setting_key, False)
                new_value = not current_value

                # Update the setting
                self.settings_manager.set_setting(category, setting_key, new_value)

                # Handle special cases that require immediate action
                if category == "display" and setting_key == "fullscreen":
                    # Handle fullscreen toggle using screen mode manager
                    try:
                        self.screen = self.screen_mode_manager.apply_screen_mode(self.screen, force_mode=new_value)
                        # Update scaling factors after screen mode change
                        self.scale_x = self.screen.get_width() / 1024
                        self.scale_y = self.screen.get_height() / 768
                        print(f"Screen mode changed to {'fullscreen' if new_value else 'windowed'}")
                    except Exception as e:
                        print(f"Error changing screen mode: {e}")

                # Show message
                status = "enabled" if new_value else "disabled"
                self.show_message(f"{setting_key.replace('_', ' ').title()} {status}", "success")

                return

        # Check for slider clicks and handle dragging
        for key in self.hit_areas:
            if key.startswith("slider_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("slider_", "")

                # Start dragging this slider
                self.dragging_slider = setting_key

                # Get slider rect
                slider_rect = self.hit_areas[key]

                # Calculate new value based on click position
                category = self.get_category_for_setting(setting_key)

                # Find the setting definition to get min/max values
                setting_def = None
                for setting in self.settings_definitions.get(category, []):
                    if setting["key"] == setting_key:
                        setting_def = setting
                        break

                if setting_def and setting_def["type"] == self.COMPONENT_SLIDER:
                    params = setting_def["params"]
                    min_value = params.get("min_value", 0)
                    max_value = params.get("max_value", 1)

                    # Calculate value based on click position
                    slider_x = pos[0]
                    # Ensure slider_x is within the slider bounds
                    slider_x = max(slider_rect.left, min(slider_rect.right, slider_x))
                    # Calculate value ratio (0 to 1)
                    value_ratio = (slider_x - slider_rect.left) / slider_rect.width
                    value_ratio = max(0, min(1, value_ratio))  # Clamp between 0 and 1
                    new_value = min_value + value_ratio * (max_value - min_value)

                    # Update the setting
                    self.settings_manager.set_setting(category, setting_key, new_value)

                    # Show message
                    value_format = params.get("value_format", "{:.1f}")
                    formatted_value = value_format.format(new_value)
                    self.show_message(f"{setting_key.replace('_', ' ').title()} set to {formatted_value}", "success")

                return

            # Also check for slider handle clicks
            elif key.startswith("slider_handle_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("slider_handle_", "")

                # Start dragging this slider
                self.dragging_slider = setting_key

                return

        # Check for button clicks
        for key in self.hit_areas:
            if key.startswith("button_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("button_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle button action
                self.handle_button_action(setting_key)

                return

        # Check for text input clicks
        for key in self.hit_areas:
            if key.startswith("text_input_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("text_input_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Activate text input
                self.active_text_input = setting_key

                # Get current value
                category = self.get_category_for_setting(setting_key)
                current_value = self.settings_manager.get_setting(category, setting_key, "")
                self.text_input_value = str(current_value)

                # Reset cursor blink
                self.text_input_cursor_visible = True
                self.text_input_cursor_timer = 0

                return

            # Handle copy button clicks
            elif key.startswith("copy_button_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("copy_button_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle copy button click
                if setting_key == "text" and self.active_tab == "advertising":
                    # Make sure the text input is active
                    self.active_text_input = setting_key

                    # Copy text to clipboard
                    if self.copy_to_clipboard(self.text_input_value):
                        self.show_message("Text copied to clipboard", "success")
                    else:
                        self.show_message("Failed to copy text", "error")

                return

            # Handle paste button clicks
            elif key.startswith("paste_button_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("paste_button_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle paste button click
                if setting_key == "text" and self.active_tab == "advertising":
                    # Make sure the text input is active
                    self.active_text_input = setting_key

                    # Paste text from clipboard
                    clipboard_text = self.paste_from_clipboard()
                    if clipboard_text:
                        # Check if clipboard contains Amharic text
                        has_amharic = any(0x1200 <= ord(c) <= 0x137F or 0x1380 <= ord(c) <= 0x139F or 0x2D80 <= ord(c) <= 0x2DDF for c in clipboard_text)
                        if has_amharic:
                            # Set font to Ebrima for Amharic text
                            self.settings_manager.set_setting("advertising", "font", "Ebrima")
                            print(f"Setting font to Ebrima for Amharic text from clipboard")

                        # Filter out control characters
                        filtered_text = ''.join(c for c in clipboard_text if ord(c) >= 32 or ord(c) in (9, 10, 13))

                        # Add clipboard text to current text
                        self.text_input_value += filtered_text
                        self.show_message("Text pasted from clipboard", "success")
                    else:
                        self.show_message("Clipboard is empty", "info")

                return

        # Check for dropdown clicks
        for key in self.hit_areas:
            if key.startswith("dropdown_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("dropdown_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Show dropdown options
                category = self.get_category_for_setting(setting_key)
                options = self.settings_manager.get_options_for_setting(setting_key)

                if options:
                    # Get current value
                    current_value = self.settings_manager.get_setting(category, setting_key, options[0])

                    # Find the next option in the list
                    if current_value in options:
                        index = options.index(current_value)
                        next_index = (index + 1) % len(options)
                        new_value = options[next_index]
                    else:
                        new_value = options[0]

                    # Update the setting
                    self.settings_manager.set_setting(category, setting_key, new_value)

                    # Show message
                    self.show_message(f"{setting_key.replace('_', ' ').title()} set to {new_value}", "success")
                else:
                    self.show_message(f"No options available for {setting_key.replace('_', ' ').title()}", "error")

                return

        # Check for color picker clicks
        for key in self.hit_areas:
            if key.startswith("color_picker_") and self.hit_areas[key].collidepoint(pos):
                setting_key = key.replace("color_picker_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Get current color
                category = self.get_category_for_setting(setting_key)
                current_value = self.settings_manager.get_setting(category, setting_key, "#FFFFFF")

                # For now, just cycle through some predefined colors
                predefined_colors = [
                    "#FFFFFF",  # White
                    "#F0C020",  # Gold
                    "#FF0000",  # Red
                    "#00FF00",  # Green
                    "#0000FF",  # Blue
                    "#FFFF00",  # Yellow
                    "#FF00FF",  # Magenta
                    "#00FFFF"   # Cyan
                ]

                # Find the next color in the list
                if current_value in predefined_colors:
                    index = predefined_colors.index(current_value)
                    next_index = (index + 1) % len(predefined_colors)
                    new_value = predefined_colors[next_index]
                else:
                    new_value = predefined_colors[0]

                # Update the setting
                self.settings_manager.set_setting(category, setting_key, new_value)

                # Show message
                self.show_message(f"Color for {setting_key.replace('_', ' ').title()} changed", "success")

                return

        # Check for predefined color clicks
        for key in self.hit_areas:
            if key.startswith("color_preset_") and self.hit_areas[key].collidepoint(pos):
                # Extract setting key and color index
                parts = key.replace("color_preset_", "").split("_")
                if len(parts) >= 2:
                    setting_key = parts[0]
                    color_index = int(parts[1])

                    # Play button click sound if available
                    if self.button_click_sound:
                        self.button_click_sound.play()

                    # Get predefined colors
                    predefined_colors = [
                        "#FFFFFF",  # White
                        "#F0C020",  # Gold
                        "#FF0000",  # Red
                        "#00FF00",  # Green
                        "#0000FF",  # Blue
                        "#FFFF00",  # Yellow
                        "#FF00FF",  # Magenta
                        "#00FFFF"   # Cyan
                    ]

                    # Get the selected color
                    if 0 <= color_index < len(predefined_colors):
                        new_value = predefined_colors[color_index]

                        # Update the setting
                        category = self.get_category_for_setting(setting_key)
                        self.settings_manager.set_setting(category, setting_key, new_value)

                        # Show message
                        self.show_message(f"Color for {setting_key.replace('_', ' ').title()} changed", "success")

                return

        # Handle mouse wheel for scrolling
        if button == 4:  # Scroll up
            self.scroll_y = max(0, self.scroll_y - self.scroll_speed)
        elif button == 5:  # Scroll down
            self.scroll_y = min(self.max_scroll, self.scroll_y + self.scroll_speed)

    def handle_mouse_button_up(self, pos, button):
        """Handle mouse button up events"""
        # We don't need to use pos in this method, but we keep it for consistency
        # Stop dragging sliders
        if button == 1 and self.dragging_slider:  # Left button release
            # Show final value message
            setting_key = self.dragging_slider
            category = self.get_category_for_setting(setting_key)

            # Find the setting definition
            setting_def = None
            for setting in self.settings_definitions.get(category, []):
                if setting["key"] == setting_key:
                    setting_def = setting
                    break

            if setting_def and setting_def["type"] == self.COMPONENT_SLIDER:
                params = setting_def["params"]
                current_value = self.settings_manager.get_setting(category, setting_key, params.get("default_value", 0))
                value_format = params.get("value_format", "{:.1f}")
                formatted_value = value_format.format(current_value)
                self.show_message(f"{setting_key.replace('_', ' ').title()} set to {formatted_value}", "success")

            # Reset dragging state
            self.dragging_slider = None

    def handle_button_action(self, setting_key):
        """Handle button actions"""
        # Import/Export actions
        if setting_key == "import_preset" or setting_key == "import_boards":
            # Import board preset
            try:
                # Use tkinter to show a file dialog
                import tkinter as tk
                from tkinter import filedialog

                # Create a root window but hide it
                root = tk.Tk()
                root.withdraw()

                # Show the file dialog
                file_path = filedialog.askopenfilename(
                    title="Import Board Preset",
                    filetypes=[("JSON Files", "*.json")],
                    initialdir="data/boards"
                )

                # If a file was selected
                if file_path:
                    # Import the board preset
                    import json
                    import os

                    # Load the board preset (we don't need to use the data, just verify it's valid JSON)
                    with open(file_path, "r") as f:
                        json.load(f)  # Just validate the JSON

                    # Get the preset name from the filename
                    preset_name = os.path.splitext(os.path.basename(file_path))[0]

                    # Add the preset to the presets list
                    presets = self.settings_manager.get_setting("boards", "presets", [])
                    if not isinstance(presets, list):
                        presets = []

                    # Check if preset already exists
                    preset_exists = False
                    for preset in presets:
                        if preset.get("name") == preset_name:
                            preset_exists = True
                            break

                    # Add preset if it doesn't exist
                    if not preset_exists:
                        presets.append({
                            "name": preset_name,
                            "file": os.path.basename(file_path)
                        })

                        # Update the presets setting
                        self.settings_manager.set_setting("boards", "presets", presets)

                        # Show success message
                        self.show_message(f"Board preset '{preset_name}' imported successfully", "success")
                    else:
                        # Show message that preset already exists
                        self.show_message(f"Board preset '{preset_name}' already exists", "info")
                else:
                    # Show message that no file was selected
                    self.show_message("No file selected", "info")
            except Exception as e:
                # Show error message
                self.show_message(f"Error importing board preset: {str(e)}", "error")
        elif setting_key == "export_language":
            # Export language settings
            try:
                # Use tkinter to show a file dialog
                import tkinter as tk
                from tkinter import filedialog

                # Create a root window but hide it
                root = tk.Tk()
                root.withdraw()

                # Show the file dialog
                file_path = filedialog.asksaveasfilename(
                    title="Export Language Settings",
                    filetypes=[("JSON Files", "*.json")],
                    defaultextension=".json",
                    initialdir="data/language"
                )

                # If a file was selected
                if file_path:
                    # Export the language settings
                    import json

                    # Get the language settings
                    language_settings = self.settings_manager.get_setting("language", None, {})

                    # Save the language settings to the file
                    with open(file_path, "w") as f:
                        json.dump(language_settings, f, indent=4)

                    # Show success message
                    self.show_message(f"Language settings exported to {file_path}", "success")
                else:
                    # Show message that no file was selected
                    self.show_message("No file selected", "info")
            except Exception as e:
                # Show error message
                self.show_message(f"Error exporting language settings: {str(e)}", "error")
        elif setting_key == "import_language":
            # Import language settings
            try:
                # Use tkinter to show a file dialog
                import tkinter as tk
                from tkinter import filedialog

                # Create a root window but hide it
                root = tk.Tk()
                root.withdraw()

                # Show the file dialog
                file_path = filedialog.askopenfilename(
                    title="Import Language Settings",
                    filetypes=[("JSON Files", "*.json")],
                    initialdir="data/language"
                )

                # If a file was selected
                if file_path:
                    # Import the language settings
                    import json

                    # Load the language settings
                    with open(file_path, "r") as f:
                        language_settings = json.load(f)

                    # Update the language settings
                    self.settings_manager.set_setting("language", None, language_settings)

                    # Show success message
                    self.show_message(f"Language settings imported from {file_path}", "success")
                else:
                    # Show message that no file was selected
                    self.show_message("No file selected", "info")
            except Exception as e:
                # Show error message
                self.show_message(f"Error importing language settings: {str(e)}", "error")
        else:
            self.show_message(f"Button action for {setting_key.replace('_', ' ').title()} not implemented yet", "info")

    def handle_mouse_motion(self, pos):
        """Handle mouse motion events"""
        # Update button hover states for navigation and tabs (fixed elements)
        for btn_id, hit_area in self.hit_areas.items():
            if btn_id.startswith("nav_") or btn_id.startswith("tab_"):
                hover = hit_area.collidepoint(pos)
                if btn_id in self.button_states:
                    self.button_states[btn_id]["hover"] = hover

        # Check if mouse is in content area
        if hasattr(self, 'content_rect') and self.content_rect.collidepoint(pos):
            # Update hover states for content elements
            for btn_id, hit_area in self.hit_areas.items():
                if not (btn_id.startswith("nav_") or btn_id.startswith("tab_")):
                    hover = hit_area.collidepoint(pos)
                    if btn_id in self.button_states:
                        self.button_states[btn_id]["hover"] = hover

        # Handle slider dragging
        if self.dragging_slider:
            slider_key = self.dragging_slider
            slider_rect = self.hit_areas.get(f"slider_{slider_key}")

            if slider_rect:
                # Find the setting definition
                category = self.get_category_for_setting(slider_key)
                setting_def = None

                for setting in self.settings_definitions.get(category, []):
                    if setting["key"] == slider_key:
                        setting_def = setting
                        break

                if setting_def and setting_def["type"] == self.COMPONENT_SLIDER:
                    params = setting_def["params"]
                    min_value = params.get("min_value", 0)
                    max_value = params.get("max_value", 1)

                    # Calculate slider value based on mouse position
                    # For sliders, we need to calculate the relative position within the slider
                    slider_x = pos[0]

                    # Ensure slider_x is within the slider bounds
                    slider_x = max(slider_rect.left, min(slider_rect.right, slider_x))

                    # Calculate value ratio (0 to 1)
                    value_ratio = (slider_x - slider_rect.left) / slider_rect.width
                    value_ratio = max(0, min(1, value_ratio))  # Clamp between 0 and 1
                    new_value = min_value + value_ratio * (max_value - min_value)

                    # Update the setting
                    self.settings_manager.set_setting(category, slider_key, new_value)

    def show_message(self, message, message_type="info"):
        """Show a message with the specified type"""
        self.message = message
        self.message_type = message_type
        self.message_timer = 120  # Display for 3 seconds (60 frames per second)

    def scaled_font_size(self, base_size):
        """Calculate a font size based on screen scaling"""
        return int(base_size * min(self.scale_x, self.scale_y))