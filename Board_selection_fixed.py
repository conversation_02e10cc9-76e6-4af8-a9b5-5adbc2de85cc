import pygame
import math
import time
import sys
import os
import colorsys
import random
import json
from pygame import gfxdraw
from view_players import ViewPlayers, Player
from player_storage import save_players_to_json, load_players_from_json
import datetime
from game_state_handler import GameState
from game_ui_handler import GameUIHandler
from settings_window import SettingsWindow
from settings_manager import SettingsManager
from board_manager import BoardManager
from screen_mode_manager import get_screen_mode_manager

from view_players import Player

# Import cartella preview overlay
from cartella_preview_overlay import CartellaPreviewOverlay

# Global variable for storing window state when switching to stats page
previous_window_state = {
    'width': 1280,
    'height': 720,
    'is_fullscreen': False,
    'selected_cartella_numbers': []
}

# Animation class for transitions
class TransitionAnimation:
    """Handles smooth transitions and animations for the board selection page"""

    def __init__(self, screen, parent=None):
        self.screen = screen
        self.parent = parent  # Store reference to parent for accessing data
        self.active = False
        self.animation_type = None
        self.start_time = 0
        self.duration = 0
        self.fade_surface = None
        self.particles = []
        self.message = ""
        self.message_type = "info"
        self.audio_sound = None  # Store reference to audio sound

    def start_animation(self, animation_type, duration=1000, message="", message_type="info", play_audio=True):
        """Start a new animation"""
        try:
            self.active = True
            self.animation_type = animation_type
            self.start_time = pygame.time.get_ticks()
            self.duration = duration
            self.message = message
            self.message_type = message_type

            # Create a fade surface for transitions
            self.fade_surface = pygame.Surface(self.screen.get_size(), pygame.SRCALPHA)

            # Initialize particles for welcome back animation
            if animation_type == "welcome_back":
                try:
                    # Initialize particles array even if initialization fails
                    self.particles = []
                    self.initialize_particles()
                except Exception as e:
                    print(f"Error initializing particles: {e}")
                    # Create a minimal set of particles as fallback
                    screen_width, screen_height = self.screen.get_size()
                    for _ in range(10):
                        self.particles.append({
                            "x": screen_width // 2,
                            "y": screen_height // 2,
                            "size": 5,
                            "color": (255, 255, 255, 255),  # White
                            "speed": 1.0,
                            "angle": 0,
                            "spin": 0,
                            "fade_in": 0,
                        })

            # Handle game start animation with audio
            if animation_type == "game_start" and play_audio:
                self._play_game_start_audio()
        except Exception as e:
            print(f"Error starting animation: {e}")
            # Set minimal state to prevent errors
            self.active = False
            self.particles = []

    def initialize_particles(self):
        """Initialize particles for welcome back animation"""
        self.particles = []
        screen_width, screen_height = self.screen.get_size()

        # Define valid RGBA colors
        particle_colors = [
            (255, 220, 50, 255),  # Gold
            (50, 150, 255, 255),  # Blue
            (50, 200, 100, 255),  # Green
            (255, 100, 100, 255)  # Red
        ]

        # Create 100 particles with random properties
        for _ in range(100):
            try:
                # Choose a random color from the predefined list
                color = random.choice(particle_colors)

                # Ensure color is a valid RGBA tuple
                if len(color) != 4:
                    color = (255, 255, 255, 255)  # Default to white if invalid

                particle = {
                    "x": random.randint(0, screen_width),
                    "y": random.randint(0, screen_height),
                    "size": random.randint(2, 8),
                    "color": color,
                    "speed": random.uniform(0.5, 2.0),
                    "angle": random.uniform(0, 2 * math.pi),
                    "spin": random.uniform(-0.1, 0.1),
                    "fade_in": random.randint(0, 500),  # Random delay for fade-in
                }
                self.particles.append(particle)
            except Exception as e:
                print(f"Error creating particle: {e}")
                # Add a default particle if there's an error
                self.particles.append({
                    "x": screen_width // 2,
                    "y": screen_height // 2,
                    "size": 5,
                    "color": (255, 255, 255, 255),  # White
                    "speed": 1.0,
                    "angle": 0,
                    "spin": 0,
                    "fade_in": 0,
                })

    def update(self):
        """Update animation state"""
        if not self.active:
            return False

        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.start_time

        # Check if animation is complete
        if elapsed >= self.duration:
            self.active = False
            return False

        # Calculate progress (0.0 to 1.0)
        progress = elapsed / self.duration

        # Update particles
        if self.animation_type == "welcome_back":
            self.update_particles(progress, elapsed)

        return True

    def update_particles(self, progress, elapsed):
        """Update particle positions and properties"""
        screen_width, screen_height = self.screen.get_size()

        for particle in self.particles:
            # Only start moving after fade-in delay
            if elapsed > particle["fade_in"]:
                # Update position based on angle and speed
                particle["x"] += math.cos(particle["angle"]) * particle["speed"]
                particle["y"] += math.sin(particle["angle"]) * particle["speed"]

                # Update angle with spin
                particle["angle"] += particle["spin"]

                # Calculate alpha based on progress
                particle_progress = min(1.0, (elapsed - particle["fade_in"]) / 500)  # Fade in over 500ms
                fade_out_start = 0.7  # Start fading out at 70% of animation

                if progress < fade_out_start:
                    alpha = int(255 * particle_progress)
                else:
                    # Fade out during the last 30% of animation
                    fade_out_progress = (progress - fade_out_start) / (1.0 - fade_out_start)
                    alpha = int(255 * (1.0 - fade_out_progress))

                # Update color with new alpha
                # Ensure color is a valid RGBA tuple with 4 components
                color = particle["color"]
                if len(color) == 4:
                    r, g, b, _ = color
                    particle["color"] = (r, g, b, alpha)

                # Wrap around screen edges
                if particle["x"] < 0:
                    particle["x"] = screen_width
                elif particle["x"] > screen_width:
                    particle["x"] = 0

                if particle["y"] < 0:
                    particle["y"] = screen_height
                elif particle["y"] > screen_height:
                    particle["y"] = 0

    def draw(self):
        """Draw the current animation"""
        if not self.active:
            return

        try:
            current_time = pygame.time.get_ticks()
            elapsed = current_time - self.start_time
            progress = elapsed / self.duration

            if self.animation_type == "fade_in":
                self.draw_fade_in(progress)
            elif self.animation_type == "fade_out":
                self.draw_fade_out(progress)
            elif self.animation_type == "welcome_back":
                # Ensure particles are initialized
                if not hasattr(self, 'particles') or self.particles is None:
                    self.particles = []
                    # Create a minimal set of particles as fallback
                    screen_width, screen_height = self.screen.get_size()
                    for _ in range(10):
                        self.particles.append({
                            "x": screen_width // 2,
                            "y": screen_height // 2,
                            "size": 5,
                            "color": (255, 255, 255, 255),  # White
                            "speed": 1.0,
                            "angle": 0,
                            "spin": 0,
                            "fade_in": 0,
                        })
                self.draw_welcome_back(progress)
            elif self.animation_type == "game_start":
                self.draw_game_start(progress)
        except Exception as e:
            print(f"Error drawing animation: {e}")
            # Deactivate animation on error to prevent further issues
            self.active = False

    def draw_fade_in(self, progress):
        """Draw fade in animation"""
        alpha = int(255 * (1.0 - progress))
        self.fade_surface.fill((0, 0, 0, alpha))
        self.screen.blit(self.fade_surface, (0, 0))

    def draw_fade_out(self, progress):
        """Draw fade out animation"""
        alpha = int(255 * progress)
        self.fade_surface.fill((0, 0, 0, alpha))
        self.screen.blit(self.fade_surface, (0, 0))

    def draw_welcome_back(self, progress):
        """Draw welcome back animation with particles and message"""
        try:
            # Draw particles
            if hasattr(self, 'particles') and self.particles:
                for particle in self.particles:
                    try:
                        # Ensure particle has all required attributes
                        if not all(k in particle for k in ["x", "y", "size", "color"]):
                            continue

                        # Ensure color is a valid RGBA tuple with 4 components
                        color = particle["color"]
                        if len(color) == 4 and color[3] > 0:  # Only draw visible particles with valid color
                            pygame.draw.circle(
                                self.screen,
                                color,
                                (int(particle["x"]), int(particle["y"])),
                                particle["size"]
                            )
                    except Exception as e:
                        # Skip this particle if there's an error
                        print(f"Error drawing particle: {e}")
                        continue
        except Exception as e:
            print(f"Error in draw_welcome_back particles: {e}")

        # Draw welcome message with pulsing effect
        try:
            if self.message:
                screen_width, screen_height = self.screen.get_size()

                # Calculate pulse effect
                pulse = math.sin(progress * math.pi * 6) * 0.1 + 1.0  # Pulsing between 0.9 and 1.1

                # Determine message color based on type
                if self.message_type == "success":
                    color = (100, 255, 100)  # Green
                elif self.message_type == "error":
                    color = (255, 100, 100)  # Red
                else:  # info
                    color = (100, 200, 255)  # Blue

                # Calculate font size with pulse effect
                base_font_size = int(min(screen_width, screen_height) * 0.05)  # 5% of screen dimension
                font_size = max(12, int(base_font_size * pulse))  # Ensure minimum size

                try:
                    font = pygame.font.SysFont("Arial", font_size, bold=True)

                    # Render message with shadow for better visibility
                    shadow_surf = font.render(self.message, True, (0, 0, 0))
                    text_surf = font.render(self.message, True, color)

                    # Position in center of screen
                    shadow_rect = shadow_surf.get_rect(center=(screen_width // 2 + 3, screen_height // 2 + 3))
                    text_rect = text_surf.get_rect(center=(screen_width // 2, screen_height // 2))

                    # Draw shadow and text
                    self.screen.blit(shadow_surf, shadow_rect)
                    self.screen.blit(text_surf, text_rect)
                except Exception as e:
                    print(f"Error rendering welcome message: {e}")
        except Exception as e:
            print(f"Error in draw_welcome_back message: {e}")

    def draw_game_start(self, progress):
        """Draw game start animation with blinking cartella numbers and message"""
        if not hasattr(self, 'parent') or not self.parent:
            return

        screen_width, screen_height = self.screen.get_size()

        # Get selected cartella numbers from parent
        selected_numbers = self.parent.selected_cartella_numbers
        if not selected_numbers:
            return

        # Use cached overlay for better performance
        overlay_key = f"overlay_{screen_width}_{screen_height}"
        if overlay_key not in self.parent._overlay_cache:
            # Create semi-transparent overlay with gradient effect for more professional look
            overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)

            # Create a radial gradient for a more professional look
            # Optimize by using a lower resolution and scaling up
            scale_factor = 4  # Process only 1/4 of pixels in each dimension
            small_width = max(1, screen_width // scale_factor)
            small_height = max(1, screen_height // scale_factor)

            # Create a smaller surface for the gradient
            small_overlay = pygame.Surface((small_width, small_height), pygame.SRCALPHA)

            # Generate the gradient at lower resolution
            for y in range(small_height):
                for x in range(small_width):
                    # Calculate distance from center (normalized to 0-1)
                    dx = x / small_width - 0.5
                    dy = y / small_height - 0.5
                    distance = math.sqrt(dx*dx + dy*dy) * 2  # *2 to normalize to 0-1

                    # Clamp distance to 0-1
                    distance = max(0, min(1, distance))

                    # Calculate alpha based on distance (center is more transparent)
                    alpha = int(120 + 80 * distance)  # 120-200 range for alpha

                    # Set pixel color
                    small_overlay.set_at((x, y), (0, 0, 30, alpha))  # Dark blue tint instead of pure black

            # Scale up to full size
            overlay = pygame.transform.smoothscale(small_overlay, (screen_width, screen_height))

            self.parent._overlay_cache[overlay_key] = overlay

        # Draw the overlay
        self.screen.blit(self.parent._overlay_cache[overlay_key], (0, 0))

        # Calculate which numbers to highlight based on progress
        # We want to highlight multiple numbers simultaneously for a more impressive effect
        total_numbers = len(selected_numbers)

        # Enhanced animation pattern for 13 seconds:
        # 1. First 3 seconds: Highlight each number individually in sequence
        # 2. Next 4 seconds: Highlight numbers in pairs/groups with wave pattern
        # 3. Next 3 seconds: Create a "spotlight" effect that moves across all numbers
        # 4. Final 3 seconds: All numbers pulse together with increasing intensity

        # Determine animation phase (0-1 for each phase)
        phase1_duration = 0.23  # First 23% of animation (≈3 seconds of 13)
        phase2_duration = 0.31  # Next 31% of animation (≈4 seconds of 13)
        phase3_duration = 0.23  # Next 23% of animation (≈3 seconds of 13)
        phase4_duration = 0.23  # Final 23% of animation (≈3 seconds of 13)

        highlight_numbers = []

        if progress < phase1_duration:
            # Phase 1: Individual sequence highlighting - ensure each number gets highlighted
            phase1_progress = progress / phase1_duration

            # Calculate how many complete cycles we can do to ensure all numbers are shown
            cycles_needed = max(1, math.ceil(total_numbers / (phase1_duration * 13)))  # 13 seconds total
            current_cycle = phase1_progress * cycles_needed

            # Ensure we highlight each number at least once during this phase
            highlight_index = int(current_cycle * total_numbers) % total_numbers

            # Add a "trail" effect by also highlighting the previous number with lower intensity
            prev_index = (highlight_index - 1) % total_numbers if total_numbers > 1 else highlight_index

            # Primary highlight
            if highlight_index < total_numbers:
                highlight_numbers = [selected_numbers[highlight_index]]

            # Secondary highlight (previous number) with a tag to indicate it's secondary
            if prev_index != highlight_index and prev_index < total_numbers:
                # We'll use a tuple to indicate secondary highlight (number, is_primary)
                highlight_numbers = [(selected_numbers[highlight_index], True),
                                    (selected_numbers[prev_index], False)]

        elif progress < phase1_duration + phase2_duration:
            # Phase 2: Pairs/groups with wave pattern
            phase2_progress = (progress - phase1_duration) / phase2_duration

            # Create a more dynamic wave pattern
            wave_position = phase2_progress * 4  # Complete 4 waves during this phase

            # Group numbers into pairs or triplets based on total count
            group_size = 2 if total_numbers > 6 else 3

            # Create groups that shift over time to ensure all numbers get highlighted
            shift_amount = int(phase2_progress * total_numbers)

            for i in range(0, total_numbers, group_size):
                # Calculate which group is active based on the wave
                group_index = (i // group_size) % max(1, total_numbers // group_size)
                wave_offset = (group_index / max(1, total_numbers // group_size)) * 2 * math.pi
                wave_value = (math.sin(wave_position * 2 * math.pi + wave_offset) + 1) / 2

                # Highlight groups with high wave values
                if wave_value > 0.4:  # Lower threshold to include more numbers
                    # Add all numbers in this group with shifted indices
                    for j in range(group_size):
                        idx = (i + j + shift_amount) % total_numbers
                        if idx < total_numbers:
                            # Use intensity based on wave value
                            intensity = wave_value
                            highlight_numbers.append((selected_numbers[idx], intensity > 0.7))

        elif progress < phase1_duration + phase2_duration + phase3_duration:
            # Phase 3: Spotlight effect moving across all numbers
            phase3_progress = (progress - phase1_duration - phase2_duration) / phase3_duration

            # Create a spotlight that moves across all numbers
            spotlight_position = phase3_progress * total_numbers * 2  # Move across all numbers twice

            # Calculate spotlight center and radius
            spotlight_center = spotlight_position % total_numbers
            spotlight_radius = 1.5 + math.sin(phase3_progress * math.pi * 4) * 0.5  # Varying radius

            # Highlight numbers within the spotlight radius
            for i in range(total_numbers):
                # Calculate distance from spotlight center (circular array)
                distance = min(
                    abs(i - spotlight_center),
                    abs(i - (spotlight_center + total_numbers)),
                    abs(i - (spotlight_center - total_numbers))
                )

                # If within radius, highlight with intensity based on distance
                if distance < spotlight_radius:
                    intensity = 1.0 - (distance / spotlight_radius)
                    highlight_numbers.append((selected_numbers[i], intensity > 0.6))

        else:
            # Phase 4: All numbers pulse together with increasing intensity
            phase4_progress = (progress - phase1_duration - phase2_duration - phase3_duration) / phase4_duration

            # Calculate pulse frequency that increases over time
            pulse_frequency = 2 + phase4_progress * 8  # 2 to 10 Hz
            pulse_value = (math.sin(phase4_progress * pulse_frequency * math.pi * 2) + 1) / 2

            # Add all numbers with increasing intensity
            for i in range(total_numbers):
                # Calculate a unique phase offset for each number for more interesting effect
                phase_offset = (i / total_numbers) * math.pi
                individual_pulse = (math.sin(phase4_progress * pulse_frequency * math.pi * 2 + phase_offset) + 1) / 2

                # Combine global pulse with individual pulse
                combined_pulse = (pulse_value + individual_pulse) / 2

                # Add with intensity flag
                highlight_numbers.append((selected_numbers[i], combined_pulse > 0.5))

        # Draw message with enhanced pulsing effect
        if self.message:
            # Calculate pulse effect - more dynamic with time
            pulse_speed = 3 + progress * 5  # Accelerating pulse as animation progresses
            pulse = math.sin(progress * math.pi * pulse_speed) * 0.1 + 1.0

            # Calculate font size with pulse effect
            base_font_size = int(min(screen_width, screen_height) * 0.06)  # Slightly larger
            font_size = int(base_font_size * pulse)

            # Use a more professional font if available
            try:
                font = pygame.font.SysFont("Segoe UI", font_size, bold=True)
            except:
                font = pygame.font.SysFont("Arial", font_size, bold=True)

            # Dynamic color based on progress for more visual interest
            hue = (progress * 0.2) % 1.0  # Slowly changing hue
            r, g, b = colorsys.hsv_to_rgb(hue, 0.7, 1.0)
            text_color = (int(r * 255), int(g * 255), int(b * 255))

            # Render message with enhanced shadow for better visibility
            shadow_surf = font.render(self.message, True, (0, 0, 0))
            text_surf = font.render(self.message, True, text_color)

            # Position in center of screen
            shadow_rect = shadow_surf.get_rect(center=(screen_width // 2 + 3, screen_height // 2 - int(base_font_size * 0.5)))
            text_rect = text_surf.get_rect(center=(screen_width // 2, screen_height // 2 - int(base_font_size * 0.5)))

            # Draw shadow and text
            self.screen.blit(shadow_surf, shadow_rect)
            self.screen.blit(text_surf, text_rect)

            # Add subtle rays emanating from the text for a more professional effect
            # Use a cached ray surface for better performance
            ray_key = f"rays_{int(progress*100)}"

            if ray_key not in self.parent._glow_cache:
                # Create a smaller surface for rays to improve performance
                ray_scale = 0.5  # Half resolution
                small_width = int(screen_width * ray_scale)
                small_height = int(screen_height * ray_scale)

                ray_surface = pygame.Surface((small_width, small_height), pygame.SRCALPHA)
                ray_center = (small_width // 2, small_height // 2 - int(base_font_size * ray_scale * 0.5))

                # Draw rays
                num_rays = 8  # Reduced number of rays for better performance
                max_ray_length = min(small_width, small_height) * 0.4
                ray_width = int(10 * ray_scale)

                for i in range(num_rays):
                    angle = i * (2 * math.pi / num_rays) + progress * math.pi
                    ray_length = max_ray_length * (0.5 + 0.5 * math.sin(progress * 5))

                    end_x = ray_center[0] + math.cos(angle) * ray_length
                    end_y = ray_center[1] + math.sin(angle) * ray_length

                    # Draw ray with alpha gradient - fewer steps for better performance
                    for t in range(5):  # Reduced from 10 to 5 steps
                        alpha = int(100 * (1 - t/5))
                        t_factor = t / 5

                        x = int(ray_center[0] + t_factor * (end_x - ray_center[0]))
                        y = int(ray_center[1] + t_factor * (end_y - ray_center[1]))

                        # Draw a circle with decreasing size and alpha
                        circle_radius = int(ray_width * (1 - t_factor) * pulse)
                        if circle_radius > 0:
                            pygame.draw.circle(ray_surface, (*text_color, alpha), (x, y), circle_radius)

                # Scale up to full size
                full_ray_surface = pygame.transform.smoothscale(ray_surface, (screen_width, screen_height))

                # Cache the ray surface
                self.parent._glow_cache[ray_key] = full_ray_surface

            # Apply the ray effect with reduced opacity for subtlety
            self.screen.blit(self.parent._glow_cache[ray_key], (0, 0), special_flags=pygame.BLEND_ADD)

        # Draw countdown timer with enhanced visuals
        # Get the duration from the parent object
        if hasattr(self.parent, 'game_start_transition_duration'):
            total_seconds = self.parent.game_start_transition_duration / 1000  # Convert from ms to seconds
        else:
            total_seconds = 13  # Default to 13 seconds if not available
        remaining_seconds = int(total_seconds - progress * total_seconds)
        if remaining_seconds >= 0:
            # Cache the timer glow effects for better performance
            timer_key = f"timer_{remaining_seconds}"

            if timer_key not in self.parent._glow_cache:
                # Create a more professional looking timer with enhanced effects
                timer_size = int(min(screen_width, screen_height) * 0.15)  # Larger size

                try:
                    timer_font = pygame.font.SysFont("Impact", timer_size, bold=False)
                except:
                    timer_font = pygame.font.SysFont("Arial", timer_size, bold=True)

                timer_text = f"{remaining_seconds + 1}"  # +1 so it shows 13,12,11...3,2,1 instead of 12,11,10...2,1,0

                # Create base timer surface
                base_timer = timer_font.render(timer_text, True, (255, 255, 255))

                # Create glow effect surface (larger than the text)
                padding = timer_size // 2
                glow_surf = pygame.Surface((base_timer.get_width() + padding*2,
                                           base_timer.get_height() + padding*2), pygame.SRCALPHA)

                # Draw multiple layers of glow
                for offset in range(padding, 0, -2):
                    # Calculate color based on offset
                    glow_factor = offset / padding
                    glow_alpha = int(200 * (1 - glow_factor))

                    # Use a color that matches the countdown urgency
                    if remaining_seconds > 4:  # Early countdown - blue/green
                        glow_color = (100, 200, 255, glow_alpha)
                    elif remaining_seconds > 2:  # Middle countdown - yellow
                        glow_color = (255, 220, 100, glow_alpha)
                    else:  # Final countdown - red
                        glow_color = (255, 100, 100, glow_alpha)

                    # Render the text with this glow color
                    glow_text = timer_font.render(timer_text, True, glow_color)

                    # Position in the center of the glow surface with offset
                    glow_pos = ((glow_surf.get_width() - glow_text.get_width()) // 2,
                               (glow_surf.get_height() - glow_text.get_height()) // 2)

                    # Apply a blur effect by drawing slightly offset copies
                    blur_range = max(1, offset // 4)
                    for bx in range(-blur_range, blur_range+1):
                        for by in range(-blur_range, blur_range+1):
                            if bx*bx + by*by <= blur_range*blur_range:  # Circular blur
                                glow_surf.blit(glow_text, (glow_pos[0] + bx, glow_pos[1] + by))

                # Add the main text on top
                glow_surf.blit(base_timer, (padding, padding))

                # Store in cache
                self.parent._glow_cache[timer_key] = glow_surf

            # Get the cached glow surface
            timer_surf = self.parent._glow_cache[timer_key]

            # Apply pulsating effect to the timer
            pulse_factor = 1.0 + 0.2 * math.sin(progress * 15)  # Faster pulse for urgency
            scaled_width = int(timer_surf.get_width() * pulse_factor)
            scaled_height = int(timer_surf.get_height() * pulse_factor)

            # Only scale if significantly different to improve performance
            if abs(scaled_width - timer_surf.get_width()) > 5:
                scaled_timer = pygame.transform.smoothscale(timer_surf, (scaled_width, scaled_height))
            else:
                scaled_timer = timer_surf

            # Position the timer
            timer_rect = scaled_timer.get_rect(center=(screen_width // 2,
                                                     screen_height // 2 + int(min(screen_width, screen_height) * 0.1)))

            # Draw the timer
            self.screen.blit(scaled_timer, timer_rect)

        # Highlight the selected cartella numbers
        for highlight_item in highlight_numbers:
            # Handle both simple number format and tuple format with intensity
            if isinstance(highlight_item, tuple):
                highlight_number, is_primary = highlight_item
            else:
                highlight_number = highlight_item
                is_primary = True

            # Find the hit area for this number
            hit_area_key = f"lucky_number_{highlight_number}"
            if hit_area_key in self.parent.hit_areas:
                chip_rect = self.parent.hit_areas[hit_area_key]

                # Calculate a unique phase for each number to create varied animation
                number_phase = (highlight_number % 10) / 10

                # Calculate pulse effect for the highlight - more dynamic
                # Primary highlights pulse more dramatically than secondary ones
                base_pulse = 0.7 if is_primary else 0.4
                pulse_amplitude = 0.5 if is_primary else 0.3
                highlight_pulse = base_pulse + pulse_amplitude * math.sin((progress * 10 + number_phase) * math.pi * 2)

                # Create a unique color for each number that changes over time
                # Primary highlights get more vibrant colors
                if is_primary:
                    # Vibrant colors for primary highlights
                    hue = ((progress * 0.5) + (highlight_number / 100)) % 1.0
                    saturation = 0.9 + 0.1 * math.sin(progress * 5)
                    value = 1.0
                else:
                    # More subdued colors for secondary highlights
                    hue = ((progress * 0.3) + (highlight_number / 100)) % 1.0
                    saturation = 0.7 + 0.1 * math.sin(progress * 3)
                    value = 0.8 + 0.1 * math.sin(progress * 5 + number_phase)

                r, g, b = colorsys.hsv_to_rgb(hue, saturation, value)
                highlight_color = (int(r * 255), int(g * 255), int(b * 255))

                # Cache key for this highlight
                highlight_key = f"highlight_{highlight_number}_{int(highlight_pulse*100)}_{1 if is_primary else 0}"

                if highlight_key not in self.parent._number_highlight_cache:
                    # Create a surface for the highlight effect
                    highlight_size = int(chip_rect.width * (3.0 if is_primary else 2.2))
                    highlight_surf = pygame.Surface((highlight_size, highlight_size), pygame.SRCALPHA)

                    # Draw multiple concentric circles for a more professional glow effect
                    for i in range(10, 0, -1):
                        circle_radius = int(highlight_size/2 * (i/10) * highlight_pulse)
                        circle_alpha = int((200 if is_primary else 150) * (i/10))
                        circle_color = (*highlight_color, circle_alpha)

                        # Primary highlights get thicker rings
                        thickness = max(1, i//3) if is_primary else max(1, i//4)
                        pygame.draw.circle(highlight_surf, circle_color,
                                         (highlight_size//2, highlight_size//2),
                                         circle_radius, thickness)

                    # Add a bright inner circle - larger for primary highlights
                    inner_radius = int(highlight_size/4 * highlight_pulse * (1.2 if is_primary else 0.8))
                    inner_alpha = 180 if is_primary else 120
                    pygame.draw.circle(highlight_surf, (*highlight_color, inner_alpha),
                                     (highlight_size//2, highlight_size//2),
                                     inner_radius)

                    # For primary highlights, add an extra inner glow
                    if is_primary:
                        core_radius = int(inner_radius * 0.6)
                        pygame.draw.circle(highlight_surf, (255, 255, 255, 100),
                                         (highlight_size//2, highlight_size//2),
                                         core_radius)

                    # Store in cache
                    self.parent._number_highlight_cache[highlight_key] = highlight_surf

                # Draw the cached highlight
                highlight_surf = self.parent._number_highlight_cache[highlight_key]
                highlight_rect = highlight_surf.get_rect(center=(chip_rect.centerx, chip_rect.centery))
                self.screen.blit(highlight_surf, highlight_rect)

                # Draw number with enhanced visibility
                # Always draw for primary highlights, selectively for secondary
                if is_primary or progress >= phase1_duration + phase2_duration + phase3_duration * 0.5:
                    # Use a more professional font
                    try:
                        font_size = int(chip_rect.width * (0.75 if is_primary else 0.65))
                        number_font = pygame.font.SysFont("Impact", font_size, bold=False)
                    except:
                        number_font = pygame.font.SysFont("Arial", font_size, bold=True)

                    # Create a dynamic color based on the highlight color but with better contrast
                    text_color = (255, 255, 255)  # White for better visibility

                    # Render the number
                    number_text = number_font.render(str(highlight_number), True, text_color)
                    number_rect = number_text.get_rect(center=(chip_rect.centerx, chip_rect.centery))

                    # Draw with shadow for better visibility - thicker shadow for primary
                    shadow_offset = max(1, int(chip_rect.width * (0.06 if is_primary else 0.04)))
                    shadow_text = number_font.render(str(highlight_number), True, (0, 0, 0))
                    shadow_rect = shadow_text.get_rect(center=(chip_rect.centerx + shadow_offset,
                                                             chip_rect.centery + shadow_offset))

                    # Apply the shadow and text
                    self.screen.blit(shadow_text, shadow_rect)
                    self.screen.blit(number_text, number_rect)

                    # For primary highlights in later phases, add a pulsating ring around the number
                    if is_primary and progress >= phase1_duration + phase2_duration:
                        ring_pulse = 0.7 + 0.3 * math.sin(progress * 15)
                        ring_radius = int(chip_rect.width * 0.6 * ring_pulse)
                        ring_color = (255, 255, 255, int(100 * ring_pulse))

                        # Draw a thin ring that pulses
                        pygame.draw.circle(self.screen, ring_color,
                                         (chip_rect.centerx, chip_rect.centery),
                                         ring_radius, max(1, int(chip_rect.width * 0.03)))

        # Add a professional touch: particles floating upward
        # Use a cached particle surface for better performance
        particle_key = f"particles_{int(progress*20)}"

        if particle_key not in self.parent._glow_cache:
            # Create a particle surface
            particle_surface = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)

            # Initialize particles if needed
            if not hasattr(self, 'particles'):
                self.particles = []

                # Create initial particles - fewer for better performance
                for _ in range(30):  # Reduced from 50 to 30
                    self.particles.append({
                        'x': random.randint(0, screen_width),
                        'y': random.randint(0, screen_height),
                        'size': random.uniform(2, 6),
                        'speed': random.uniform(0.5, 2.0),
                        'color': random.choice([(255,220,100), (100,200,255), (255,100,100), (100,255,150)])
                    })

            # Update and draw particles
            for particle in self.particles:
                # Move particle upward
                particle['y'] -= particle['speed'] * 2

                # If particle goes off screen, reset at bottom
                if particle['y'] < 0:
                    particle['y'] = screen_height
                    particle['x'] = random.randint(0, screen_width)

                # Draw particle with fading effect
                size = particle['size'] * (0.5 + 0.5 * math.sin(progress * 5 + particle['x'] / 100))
                alpha = int(150 * size / particle['size'])
                color = (*particle['color'], alpha)

                pygame.draw.circle(particle_surface, color, (int(particle['x']), int(particle['y'])), int(size))

            # Cache the particle surface
            self.parent._glow_cache[particle_key] = particle_surface

        # Draw the cached particle surface
        self.screen.blit(self.parent._glow_cache[particle_key], (0, 0))

    def _play_game_start_audio(self):
        """Play the game start audio announcement"""
        if not self.parent:
            return

        # Check if audio announcements are enabled in settings
        announcements_enabled = self.parent.settings_manager.get_setting('audio', 'cartella_announcements_enabled', True)

        # Check if transition animation is enabled
        transition_enabled = self.parent.settings_manager.get_setting('animations', 'transition_animation_enabled', True)

        if not announcements_enabled or not transition_enabled:
            print("Audio announcements or transition animation are disabled in settings")
            return

        # Get the announcer language from settings
        announcer_language = self.parent.settings_manager.get_setting('audio', 'announcer_language', 'Default')

        # Use a cache for audio files to improve performance
        audio_cache_key = f"game_start_{announcer_language}"

        try:
            # Check if we already have this audio loaded
            if hasattr(self, 'audio_cache') and audio_cache_key in self.audio_cache:
                self.audio_sound = self.audio_cache[audio_cache_key]
            else:
                # Default path for Amharic
                audio_path = "assets/audio-effects/Game_about_to_start_Amharic.mp3"

                # If a different language is selected and the file exists, use that instead
                if announcer_language != 'Default' and announcer_language != 'Amharic':
                    alt_path = f"assets/audio-effects/Game_about_to_start_{announcer_language}.mp3"
                    if os.path.exists(alt_path):
                        audio_path = alt_path

                # Check if the file exists
                if not os.path.exists(audio_path):
                    print(f"Game start announcement audio file not found: {audio_path}")
                    return

                # Initialize audio cache if needed
                if not hasattr(self, 'audio_cache'):
                    self.audio_cache = {}

                # Load and cache the announcement
                self.audio_sound = pygame.mixer.Sound(audio_path)
                self.audio_cache[audio_cache_key] = self.audio_sound

            # Play the announcement
            self.audio_sound.play()

            # Adjust volume based on animation progress
            # This helps match the 7-second animation with potentially shorter audio
            pygame.mixer.Channel(0).set_volume(1.0)  # Ensure full volume at start

            return True
        except Exception as e:
            print(f"Error playing game start announcement: {e}")
            return False


# Constants for window dimensions and scaling
SCREEN_WIDTH, SCREEN_HEIGHT = 1024, 768
BASE_WIDTH, BASE_HEIGHT = 1024, 768

# Colors - synchronized with main.py for consistent UI
DARK_BLUE = (10, 30, 45)  # Background color
LIGHT_BLUE = (20, 50, 70)  # Slightly lighter background
YELLOW = (255, 200, 50)  # For 'O' in BINGO
RED = (255, 50, 50)  # For 'B' and 'G' in BINGO
GREEN = (50, 200, 100)  # For highlighted numbers
BLUE = (50, 100, 220)  # For 'N' in BINGO
ORANGE = (255, 120, 30)  # For the "Total Callout" text
WHITE = (255, 255, 255)  # For text
GRAY = (60, 60, 70)  # For non-highlighted number circles
BLACK = (20, 20, 25)  # For number row backgrounds
GOLD = (255, 215, 0)  # For special elements
DARK_GREEN = (0, 100, 50)  # Darker green for contrast
LIGHT_GREEN = (50, 200, 100)  # For highlighted numbers
PURPLE = (128, 0, 128)  # Unused but available
NAV_BAR_BG = (15, 35, 55)  # Dark blue for navigation bar
NAV_BAR_ACTIVE = (30, 100, 130)  # Teal highlight for active nav item

# Initialize scaling factors
scale_x = 1.0
scale_y = 1.0

# Fonts will be initialized in the main function


class BoardSelectionWindow:
    def __init__(self, screen, from_reset=False):
        self.screen = screen

        # Get screen dimensions
        screen_width, screen_height = screen.get_size()

        # Store base scaling factors
        self.base_scale_x = screen_width / BASE_WIDTH
        self.base_scale_y = screen_height / BASE_HEIGHT

        # Current scaling factors (will be updated dynamically during fullscreen toggle)
        self.scale_x = self.base_scale_x
        self.scale_y = self.base_scale_y

        # Store aspect ratio for responsive layout adjustments
        self.aspect_ratio = screen_width / screen_height

        # Initialize settings manager first
        self.settings_manager = SettingsManager()

        # Initialize screen mode manager
        self.screen_mode_manager = get_screen_mode_manager()
        self.screen_mode_manager.set_screen_reference(screen)

        # Initialize state variables
        self.selected_cartella_numbers = []
        self.cartella_number = 12  # Default
        self.bet_amount = 50  # Default
        self.prize_pool = 0
        self.prize_pool_manual_override = False  # Initialize to prevent AttributeError
        self.input_active = False
        self.input_text = str(self.cartella_number)
        self.input_cursor_visible = True
        self.input_cursor_timer = 0
        self.bet_input_active = False
        self.bet_input_text = str(self.bet_amount)
        self.bet_input_cursor_visible = True
        self.bet_input_cursor_timer = 0
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"
        self.called_numbers = []  # For lucky numbers display
        self.current_number = None
        self.bingo_board = []
        self.grid_page = 0  # Track which page of numbers we're viewing (0-5)

        # Remember cartella numbers checkbox state - initialize from settings
        self.remember_cartella_checkbox = self.settings_manager.get_setting('game', 'remember_cartella_numbers', False)

        # Load UI state if setting is enabled
        if self.remember_cartella_checkbox:
            try:
                from player_storage import save_ui_state, load_ui_state, save_remembered_cartellas
                remembered_data = load_ui_state()
                remembered_numbers = remembered_data.get('cartella_numbers', [])
                remembered_prize_pool = remembered_data.get('prize_pool', None)
                remembered_override = remembered_data.get('prize_pool_manual_override', False)
                remembered_bet_amount = remembered_data.get('bet_amount', None)
                if remembered_numbers:
                    self.selected_cartella_numbers = remembered_numbers
                    self.called_numbers = remembered_numbers.copy()
                    if remembered_bet_amount is not None:
                        self.bet_amount = remembered_bet_amount
                        self.bet_input_text = str(self.bet_amount)
                    self.prize_pool_manual_override = remembered_override
                    if self.prize_pool_manual_override and remembered_prize_pool is not None:
                        self.prize_pool = remembered_prize_pool
                    else:
                        self.calculate_prize_pool()
                else:
                    # Save empty list to clear UI state
                    save_ui_state([], None, False, None)
                    print("Cleared UI state")
            except Exception as e:
                print(f"Error clearing or restoring UI state: {e}")

        # Game start transition state
        self.game_start_transition_active = False
        self.game_start_transition_start_time = 0

        # Get transition duration from settings (default 13 seconds)
        self.game_start_transition_duration = int(self.settings_manager.get_setting(
            'animations', 'transition_animation_duration', 13
        ) * 1000)  # Convert to milliseconds

        # Settings for transition animation
        self.transition_animation_enabled = self.settings_manager.get_setting(
            'animations', 'transition_animation_enabled', True
        )

        # Pre-render surfaces for better performance
        self._overlay_cache = {}
        self._glow_cache = {}
        self._number_highlight_cache = {}

        # Initialize transition animation with reference to self (disabled for stability)
        # self.transition = TransitionAnimation(screen, self)

        # Show a simple message if coming from reset
        if from_reset:
            # Show a simple message instead of animation to avoid potential errors
            self.message = "Game Reset Successfully!"
            self.message_type = "success"
            self.message_timer = 180  # Show for 3 seconds (60 frames per second)

        # Load bingo boards from JSON file
        self.bingo_boards = self.load_bingo_boards()

        # Create a bingo board based on default cartella number
        self.create_board_from_number(self.cartella_number)

        # Button states
        self.button_states = {
            "add_player": {"hover": False, "click": False, "click_time": 0, "hover_alpha": 0},
            "continue": {"hover": False, "click": False, "click_time": 0, "hover_alpha": 0},
        }

        # Popup state variables
        self.showing_reset_confirmation = False  # Reset confirmation has higher priority
        self.showing_board_preview = False
        self.preview_cartella_number = None
        self.preview_board = None

        # Popup opacity settings
        self.reset_popup_opacity = 220  # More opaque for reset confirmation
        self.preview_popup_opacity = 180  # Slightly transparent for board preview

        # Preset management
        self.current_preset_name = "Default"  # Default preset name

        # Store hit areas for buttons
        self.hit_areas = {}

        # Load players
        self.players = load_players_from_json()
        if self.players is None:
            self.players = []

        # Ensure players list matches the remembered cartella numbers
        if self.remember_cartella_checkbox and hasattr(self, "selected_cartella_numbers") and self.selected_cartella_numbers:
            # Create a set of existing cartella numbers
            existing_cartellas = {player.cartela_no for player in self.players}

            # Add players for any remembered cartella numbers not already in the players list
            for cartella_number in self.selected_cartella_numbers:
                if cartella_number not in existing_cartellas:
                    # Create a new player with the remembered bet amount
                    player = Player(cartela_no=cartella_number, bet_amount=self.bet_amount)
                    self.players.append(player)
                    print(f"Created player for remembered cartella {cartella_number}")

            # Save updated players list
            save_players_to_json(self.players)

        # Only calculate prize pool if not restoring from override
        if not self.prize_pool_manual_override:
            self.calculate_prize_pool()

        # Sound effects
        try:
            self.button_click_sound = pygame.mixer.Sound("assets/audio-effects/button-click.mp3")
        except Exception as e:
            print(f"Error loading sound effects: {e}")
            self.button_click_sound = None

        # Settings window - pass the settings manager
        self.settings_window = SettingsWindow(screen, self)

        # Initialize board manager
        self.board_manager = BoardManager(self.settings_manager)

        # Initialize cartella preview overlay
        self.cartella_preview = CartellaPreviewOverlay(screen, self)

        # Visibility state
        self.visible = True

    def show(self):
        """Show the board selection window"""
        print("BoardSelectionWindow: show() called")
        self.visible = True

    def hide(self):
        """Hide the board selection window"""
        print("BoardSelectionWindow: hide() called")
        self.visible = False

    def update_scaling(self):
        """Update scaling factors based on current screen size"""
        screen_width, screen_height = self.screen.get_size()
        self.scale_x = screen_width / BASE_WIDTH
        self.scale_y = screen_height / BASE_HEIGHT
        self.aspect_ratio = screen_width / screen_height

    def draw_gradient_rect(self, rect, start_color, end_color, *args, **kwargs):
        """Draw a rectangle with a gradient from start_color to end_color

        Args:
            rect: The pygame.Rect to draw
            start_color: The starting color (RGB tuple)
            end_color: The ending color (RGB tuple)
            vertical (bool, optional): If True, gradient is vertical, otherwise horizontal. Default is True.
            border_radius (int, optional): Border radius for rounded corners. Default is 0.

        Note: This method can be called in two ways:
            1. With positional arguments: draw_gradient_rect(rect, start_color, end_color, border_radius)
            2. With named arguments: draw_gradient_rect(rect, start_color, end_color, vertical=True, border_radius=5)
        """
        # Handle both positional and named arguments
        vertical = True  # Default value
        border_radius = 0  # Default value

        # Check if vertical is passed as a named argument
        if 'vertical' in kwargs:
            vertical = kwargs['vertical']

        # Check if border_radius is passed as a named argument
        if 'border_radius' in kwargs:
            border_radius = kwargs['border_radius']
        # Check if border_radius is passed as a positional argument (3rd argument)
        elif len(args) >= 1:
            border_radius = args[0]

        # Create a surface with per-pixel alpha
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        if vertical:
            for y in range(rect.height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / rect.height
                # Linear interpolation of colors
                r = int(start_color[0] * (1 - t) + end_color[0] * t)
                g = int(start_color[1] * (1 - t) + end_color[1] * t)
                b = int(start_color[2] * (1 - t) + end_color[2] * t)
                pygame.draw.line(surf, (r, g, b), (0, y), (rect.width, y))
        else:
            for x in range(rect.width):
                # Calculate blend factor (0.0 to 1.0)
                t = x / rect.width
                # Linear interpolation of colors
                r = int(start_color[0] * (1 - t) + end_color[0] * t)
                g = int(start_color[1] * (1 - t) + end_color[1] * t)
                b = int(start_color[2] * (1 - t) + end_color[2] * t)
                pygame.draw.line(surf, (r, g, b), (x, 0), (x, rect.height))

        # Create mask for rounded corners if needed
        if border_radius > 0:
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)
            surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

        # Draw the gradient surface
        self.screen.blit(surf, rect)

    def load_bingo_boards(self):
        """Load bingo boards from JSON file using the board manager"""
        try:
            # Check if both settings_manager and board_manager exist
            if hasattr(self, 'settings_manager') and hasattr(self, 'board_manager'):
                # Get the current preset from settings
                current_preset = self.settings_manager.get_setting('boards', 'current_preset', "None")

                # If a preset is selected and it's not "None", use it
                if current_preset and current_preset != "None":
                    # Get the preset data
                    preset_data = self.board_manager.get_preset(current_preset)
                    if preset_data:
                        print(f"Using board preset: {current_preset}")
                        return preset_data

                # Otherwise, load the default boards
                return self.board_manager.boards
            else:
                # If no settings_manager or board_manager, just use an empty dict
                # This will cause the system to generate random boards
                if not hasattr(self, 'settings_manager'):
                    print("No settings manager available")
                if not hasattr(self, 'board_manager'):
                    print("No board manager available")
                return {}
        except Exception as e:
            print(f"Error loading bingo boards: {e}")
            return {}

    def import_boards_from_json(self):
        """Import bingo boards from a JSON file (automatically adds as preset and applies)"""
        try:
            # Import boards from file (board manager now handles adding preset, setting as current, and applying)
            success, _, preset_name = self.board_manager.import_boards_from_file()

            if success:
                # Reload the boards
                self.bingo_boards = self.load_bingo_boards()

                # Show success message
                self.message = f"Imported and applied board file: {preset_name}"
                self.message_type = "success"
                self.message_timer = 180

                return True
            else:
                # Show error message
                self.message = "Failed to import board file"
                self.message_type = "error"
                self.message_timer = 180

                return False
        except Exception as e:
            print(f"Error importing boards: {e}")

            # Show error message
            self.message = f"Error importing boards: {e}"
            self.message_type = "error"
            self.message_timer = 180

            return False

    def start_game_transition(self):
        """Start the transition animation before going to the main page"""
        if len(self.selected_cartella_numbers) == 0:
            # Don't start transition if no cartella numbers are selected
            self.show_message("Please select at least one cartella number", "warning")
            return False

        # Check if transition animation is enabled in settings
        self.transition_animation_enabled = self.settings_manager.get_setting(
            'animations', 'transition_animation_enabled', True
        )

        if not self.transition_animation_enabled:
            # If animation is disabled, just set the flag to exit immediately
            self.hit_areas["continue_clicked"] = True
            return True

        # Check if transition animation is available
        if not hasattr(self, 'transition') or not self.transition:
            # If transition animation is not available, just set the flag to exit immediately
            self.hit_areas["continue_clicked"] = True
            return True

        # Set transition state
        self.game_start_transition_active = True
        self.game_start_transition_start_time = pygame.time.get_ticks()

        # Start the transition animation
        try:
            self.transition.start_animation(
                "game_start",
                duration=self.game_start_transition_duration,
                message="Game Starting...",
                message_type="success"
            )
        except Exception as e:
            print(f"Error starting game transition animation: {e}")
            # If there's an error, just set the flag to exit immediately
            self.hit_areas["continue_clicked"] = True
            return True

        return True

    def run(self):
        """Run the board selection window loop"""
        running = True
        clock = pygame.time.Clock()
        # Store initial window size for returning from fullscreen
        self.current_w, self.current_h = self.screen.get_size()

        # Performance optimization variables
        need_redraw = True
        last_cursor_time = pygame.time.get_ticks()
        cursor_update_interval = 500  # Update cursor blink less frequently
        idle_counter = 0
        max_idle_frames = 3  # After this many frames with no events, reduce redraw frequency

        while running:
            # Start with assumption that we don't need to redraw this frame
            current_frame_needs_redraw = False

            # Process events
            events = pygame.event.get()
            had_events = len(events) > 0

            # If we had events, we need to redraw
            if had_events:
                current_frame_needs_redraw = True
                idle_counter = 0
            else:
                idle_counter += 1

            for event in events:
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()

                elif event.type == pygame.MOUSEMOTION:
                    # Update button hover states
                    self.update_button_hover_states(event.pos)

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        # First check if cartella preview overlay handled the event
                        if hasattr(self, 'cartella_preview') and self.cartella_preview.active:
                            if self.cartella_preview.handle_event(event):
                                continue

                        # Then check if settings window handled the event
                        if hasattr(self, 'settings_window') and self.settings_window.visible:
                            print(f"Board selection: Passing mouse event to settings window at {event.pos}")
                            if self.settings_window.handle_event(event):
                                print("Settings window handled the event")
                                continue

                        # Check if Ctrl key is pressed for board preview
                        keys = pygame.key.get_pressed()
                        ctrl_pressed = keys[pygame.K_LCTRL] or keys[pygame.K_RCTRL]

                        # Direct handling of card selection interactions
                        self.handle_direct_interaction(event.pos, ctrl_pressed)

                elif event.type == pygame.KEYUP:
                    # Check if Ctrl key was released
                    if event.key == pygame.K_LCTRL or event.key == pygame.K_RCTRL:
                        # Hide board preview when Ctrl is released
                        if self.showing_board_preview:
                            self.showing_board_preview = False
                            self.preview_cartella_number = None
                            self.preview_board = None
                            print("Board preview closed due to Ctrl key release")

                elif event.type == pygame.KEYDOWN:
                    # First check if cartella preview overlay handled the event
                    if hasattr(self, 'cartella_preview') and self.cartella_preview.active:
                        if self.cartella_preview.handle_event(event):
                            continue

                    # Get the current state of modifier keys
                    keys = pygame.key.get_pressed()
                    ctrl_pressed = keys[pygame.K_LCTRL] or keys[pygame.K_RCTRL]

                    # Check for Ctrl+P shortcut for cartella preview
                    if event.key == pygame.K_p and ctrl_pressed:
                        # Show cartella preview overlay
                        if hasattr(self, 'cartella_preview'):
                            self.cartella_preview.show()
                            print("Cartella preview activated via Ctrl+P hotkey on board selection page")

                        # Play sound if available
                        if self.button_click_sound:
                            self.button_click_sound.play()

                    # Check for Ctrl+Enter shortcut for the "Next" button
                    elif (event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER) and ctrl_pressed:
                        # Only proceed if there are selected cartella numbers
                        if len(self.selected_cartella_numbers) > 0:
                            # Start the game transition animation
                            if not self.game_start_transition_active:
                                self.start_game_transition()

                            # Play sound if available
                            if self.button_click_sound:
                                self.button_click_sound.play()

                            # Show a message to indicate the shortcut was used
                            self.show_message("Ctrl+Enter shortcut: Navigating to game...", "success")
                        else:
                            # Show warning if no cartella numbers are selected
                            self.show_message("Please select at least one cartella number", "warning")

                            # Play sound if available
                            if self.button_click_sound:
                                self.button_click_sound.play()
                    # Always check for fullscreen toggle
                    elif event.key == pygame.K_f:
                        # Toggle fullscreen using screen mode manager for consistency
                        self.screen = self.screen_mode_manager.handle_f_key_toggle(self.screen)

                        # Update scaling after changing display mode
                        self.update_scaling()

                        # Play sound if available
                        if self.button_click_sound:
                            self.button_click_sound.play()

                    # Check for Escape key to exit fullscreen
                    elif event.key == pygame.K_ESCAPE:
                        # First check if we have any popups to close
                        if hasattr(self, 'cartella_preview') and self.cartella_preview.active:
                            self.cartella_preview.hide()
                        elif self.showing_reset_confirmation:
                            self.showing_reset_confirmation = False
                        elif self.showing_board_preview:
                            self.showing_board_preview = False
                            self.preview_cartella_number = None
                            self.preview_board = None
                        # Then check for input field cancellation
                        elif self.input_active or self.bet_input_active:
                            # Let the input handler deal with this
                            self.handle_input(event)
                        # Otherwise try to exit fullscreen using screen mode manager
                        else:
                            self.screen, mode_changed = self.screen_mode_manager.handle_escape_key(self.screen)
                            if mode_changed:
                                # Update scaling after changing display mode
                                self.update_scaling()

                        # Play sound if available
                        if self.button_click_sound:
                            self.button_click_sound.play()

                    else:
                        # Handle other keyboard input
                        if self.button_click_sound:
                            self.button_click_sound.play()
                        self.handle_input(event)

            # Update cursor blink state for input fields - less frequently for better performance
            current_time = pygame.time.get_ticks()
            if current_time - last_cursor_time > cursor_update_interval:
                last_cursor_time = current_time
                cursor_changed = False

                if self.input_active:
                    self.input_cursor_visible = not self.input_cursor_visible
                    cursor_changed = True

                if self.bet_input_active:
                    self.bet_input_cursor_visible = not self.bet_input_cursor_visible
                    cursor_changed = True

                if cursor_changed:
                    current_frame_needs_redraw = True

            # Update message timer
            if self.message_timer > 0:
                self.message_timer -= 1
                current_frame_needs_redraw = True

            # Update cartella preview overlay
            if hasattr(self, 'cartella_preview'):
                self.cartella_preview.update()

            # Check if game start transition is active
            if hasattr(self, 'game_start_transition_active') and self.game_start_transition_active:
                current_time = pygame.time.get_ticks()
                elapsed = current_time - self.game_start_transition_start_time

                # Always redraw during transition
                current_frame_needs_redraw = True

                # Check if transition is complete
                if elapsed >= self.game_start_transition_duration:
                    # Transition complete, exit the selection screen
                    self.game_start_transition_active = False
                    self.hit_areas["continue_clicked"] = True

            # Determine if we need to redraw the screen
            need_redraw = current_frame_needs_redraw or \
                         self.showing_board_preview or \
                         self.showing_reset_confirmation or \
                         (hasattr(self, 'game_start_transition_active') and self.game_start_transition_active) or \
                         (hasattr(self, 'settings_window') and self.settings_window.visible) or \
                         (hasattr(self, 'cartella_preview') and self.cartella_preview.active) or \
                         (idle_counter < max_idle_frames)  # Always redraw for a few frames after activity

            # Draw the screen only when needed
            if need_redraw:
                self.draw()
                pygame.display.flip()
            else:
                # When not redrawing, use a very short sleep to reduce CPU usage
                pygame.time.wait(5)

            # Check for "Continue" button - exits the selection screen
            if "continue_clicked" in self.hit_areas:
                del self.hit_areas["continue_clicked"]

                # Save game settings before exiting
                try:
                    from player_storage import save_game_settings, save_remembered_cartellas, load_game_settings

                    # Load existing settings first to preserve other values like number_call_delay
                    existing_settings = load_game_settings()

                    # Create settings dictionary with updated values
                    settings = existing_settings.copy()

                    # Update only the values that are set in the board selection
                    settings.update({
                        'commission_percentage': getattr(self, 'commission_percentage', 20.0),
                        'bet_amount': self.bet_amount,
                        'prize_pool': self.prize_pool,
                        'prize_pool_manual_override': self.prize_pool_manual_override
                    })

                    # Save settings to JSON
                    save_game_settings(settings)
                    print(f"Board selection: Saved game settings before exiting: {settings}")

                    # Save selected cartella numbers if remember checkbox is checked
                    if self.remember_cartella_checkbox and self.selected_cartella_numbers:
                        from player_storage import save_ui_state
                        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
                        print(f"Board selection: Saved {len(self.selected_cartella_numbers)} UI state (override: {self.prize_pool_manual_override})")
                except Exception as e:
                    print(f"Error saving game settings or remembered cartellas: {e}")

                running = False

            # No longer needed - stats navigation removed from board selection

            # Limit frame rate for better performance
            clock.tick(25)  # Reduced frame rate

        # Return the list of selected cartella numbers
        return self.selected_cartella_numbers

    def draw(self):
        """Draw the board selection screen"""
        # No need for complex popup checks with our simplified system

        # Clear the screen with dark background color
        self.screen.fill(DARK_BLUE)

        screen_width, screen_height = self.screen.get_size()

        # Calculate more responsive scaling based on screen dimensions
        # This ensures better adaptation to non-standard aspect ratios
        aspect_ratio = screen_width / screen_height

        # Use balanced scaling to maintain proportions
        balanced_scale = min(self.scale_x, self.scale_y)

        # Draw navigation bar at the top
        nav_height = int(40 * self.scale_y)
        self.draw_navigation_bar(screen_width, screen_height)

        adaptive_scale_x = self.scale_x
        adaptive_scale_y = self.scale_y

        # Adjust scaling for extreme aspect ratios
        if aspect_ratio > 2.0:  # Very wide screen
            adaptive_scale_x = adaptive_scale_x * 0.9
        elif aspect_ratio < 1.0:  # Tall/narrow screen
            adaptive_scale_y = adaptive_scale_y * 0.9

        # Calculate responsive section heights and spacing with the adaptive scaling
        header_height = int(min(110 * adaptive_scale_y, screen_height * 0.15))

        # Calculate available space after header
        available_height = screen_height - header_height

        # Allocate space proportionally to ensure elements fit
        # For very tall screens, reduce the grid proportion to prevent sections from being too large
        grid_proportion = 0.65 if aspect_ratio >= 1.0 else 0.55
        bottom_proportion = 0.30 if aspect_ratio >= 1.0 else 0.40

        grid_height = int(available_height * grid_proportion)
        bottom_section_height = int(available_height * bottom_proportion)

        # Dynamic padding based on screen size - ensure it's proportional to the screen
        padding = int(min(15 * balanced_scale, screen_height * 0.02, screen_width * 0.02))

        # Draw the WOW BINGO header with responsive sizing
        self.draw_wow_bingo_header()

        # Draw subtle separator line between header and content
        line_y = header_height
        pygame.draw.line(
            self.screen, (40, 60, 80), (padding, line_y), (screen_width - padding, line_y), 2
        )

        # Draw enhanced CARTELLA SELECTION PAGE title with gradient background
        # Create a stylish background for the title
        # Increase font size for better visibility
        title_font_size = int(
            min(32 * balanced_scale, screen_width * 0.035, screen_height * 0.045)
        )
        title_font_size = max(title_font_size, 22)  # Increased minimum size for better visibility
        title_font = pygame.font.SysFont("Arial", title_font_size, bold=True)
        title_text = title_font.render("CARTELLA SELECTION PAGE", True, WHITE)

        # Calculate title dimensions and position
        title_width = title_text.get_width() + int(40 * balanced_scale)  # Add padding
        title_height = title_text.get_height() + int(16 * balanced_scale)  # Add padding
        title_x = (screen_width - title_width) // 2
        # Move the title significantly higher in the layout
        title_y = int(header_height * 0.75)  # Positioned at 75% of the header height instead of below it

        # Create a stylish background with gradient
        title_rect = pygame.Rect(title_x, title_y, title_width, title_height)
        # Use a gradient that matches the app's color scheme (dark blue to slightly lighter blue)
        self.draw_gradient_rect(title_rect, (20, 60, 100), (40, 100, 160), 10)

        # Add a subtle border glow
        for i in range(1, 3):
            glow_rect = pygame.Rect(
                title_rect.x - i,
                title_rect.y - i,
                title_rect.width + i * 2,
                title_rect.height + i * 2
            )
            glow_color = (60, 140, 200, 70 - i * 20)
            pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=10 + i)

        # Add highlight to top of title bar for 3D effect
        highlight_rect = pygame.Rect(title_x + 2, title_y + 2, title_width - 4, int(title_height * 0.3))
        highlight_color = (100, 180, 255, 60)  # Semi-transparent light blue
        pygame.draw.rect(self.screen, highlight_color, highlight_rect, border_radius=8)

        # Draw text with shadow for depth
        shadow_offset = int(2 * balanced_scale)
        shadow_text = title_font.render("CARTELLA SELECTION PAGE", True, (10, 20, 30))
        shadow_x = title_x + (title_width - shadow_text.get_width()) // 2
        shadow_y = title_y + (title_height - shadow_text.get_height()) // 2
        self.screen.blit(shadow_text, (shadow_x + shadow_offset, shadow_y + shadow_offset))

        # Draw main text
        text_x = title_x + (title_width - title_text.get_width()) // 2
        text_y = title_y + (title_height - title_text.get_height()) // 2
        self.screen.blit(title_text, (text_x, text_y))

        # Check if we should show the total selected count
        # Get the setting from settings manager
        show_total_selected = True  # Default to True if setting not found
        if hasattr(self, 'settings_window') and hasattr(self.settings_window, 'settings_manager'):
            settings_manager = self.settings_window.settings_manager
            show_total_selected = settings_manager.get_setting('game', 'show_total_selected', True)

        # Draw total selected count on the right side with matching style if enabled
        if show_total_selected:
            count_font = pygame.font.SysFont("Arial", int(title_font_size * 0.85), bold=True)
            count_text = count_font.render(
                f"#Total selected: {len(self.selected_cartella_numbers)}", True, (255, 180, 50)
            )

            # Create a background for the count with matching style
            count_width = count_text.get_width() + int(30 * balanced_scale)  # Add padding
            count_height = count_text.get_height() + int(12 * balanced_scale)  # Add padding
            count_x = screen_width - count_width - padding
            count_y = title_y + (title_height - count_height) // 2  # Center vertically with title

            # Create a stylish background with gradient
            count_rect = pygame.Rect(count_x, count_y, count_width, count_height)
            # Use a complementary gradient (orange/gold tones)
            self.draw_gradient_rect(count_rect, (80, 40, 10), (120, 60, 20), 8)

            # Add a subtle border glow
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    count_rect.x - i,
                    count_rect.y - i,
                    count_rect.width + i * 2,
                    count_rect.height + i * 2
                )
                glow_color = (200, 150, 50, 70 - i * 20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8 + i)

            # Add highlight to top of count bar for 3D effect
            highlight_rect = pygame.Rect(count_x + 2, count_y + 2, count_width - 4, int(count_height * 0.3))
            highlight_color = (255, 200, 100, 60)  # Semi-transparent light orange
            pygame.draw.rect(self.screen, highlight_color, highlight_rect, border_radius=6)

            # Draw text with shadow for depth
            shadow_offset = int(1.5 * balanced_scale)
            shadow_text = count_font.render(f"#Total selected: {len(self.selected_cartella_numbers)}", True, (30, 20, 10))
            shadow_x = count_x + (count_width - shadow_text.get_width()) // 2
            shadow_y = count_y + (count_height - shadow_text.get_height()) // 2
            self.screen.blit(shadow_text, (shadow_x + shadow_offset, shadow_y + shadow_offset))

            # Draw main text
            text_x = count_x + (count_width - count_text.get_width()) // 2
            text_y = count_y + (count_height - count_text.get_height()) // 2
            self.screen.blit(count_text, (text_x, text_y))

        # Draw selected cartella numbers right under the title if any exist
        # Adjust the position to maintain proper spacing with the moved-up title
        selected_y = title_y + title_height + int(10 * balanced_scale)  # Adjusted spacing for the new title position
        if self.selected_cartella_numbers:
            self.draw_selected_cartellas(padding, selected_y, screen_width - padding * 2)
            selected_height = int(min(40 * balanced_scale, available_height * 0.05))
        else:
            selected_height = 0

        # Calculate grid starting position
        grid_y = selected_y + selected_height + padding

        # Ensure grid fits within available space with proper margins
        max_grid_height = screen_height - grid_y - bottom_section_height - padding * 2
        grid_height = min(grid_height, max_grid_height)

        # Draw number grid (BINGO board) in a responsive area
        self.draw_bingo_grid()

        # Recalculate bottom section position to ensure it's visible
        # Ensure minimal spacing between sections while preventing overflow
        min_bottom_margin = int(padding * 1.5)
        bottom_y = min(
            grid_y + grid_height + padding,
            screen_height - bottom_section_height - min_bottom_margin,
        )

        # Dynamic column layout based on aspect ratio
        if aspect_ratio > 1.8:  # Very wide screen
            # Use wider columns with more spacing
            column_spacing = int(padding * 1.5)
            col_width = int((screen_width - (column_spacing * 4)) / 3)
        elif aspect_ratio < 1.2:  # Narrow/tall screen
            # Stack columns vertically for very narrow screens
            column_spacing = int(padding * 0.8)
            col_width = screen_width - (column_spacing * 2)

            # Adjust bottom section height to accommodate stacked layout
            bottom_section_height = int(bottom_section_height / 3)
        else:
            # Normal layout
            column_spacing = padding
            col_width = int((screen_width - (column_spacing * 4)) / 3)

        # For vertical layout on narrow screens
        if aspect_ratio < 1.2:
            # Draw sections stacked vertically
            # Draw PRIZE POOL section (top)
            prize_pool_x = column_spacing
            prize_pool_width = col_width
            self.draw_prize_pool_section(
                prize_pool_x, bottom_y, prize_pool_width, bottom_section_height
            )

            # Draw Add Players section (middle)
            add_players_x = prize_pool_x
            add_players_width = col_width
            add_players_y = bottom_y + bottom_section_height + column_spacing
            self.draw_add_players_section(
                add_players_x, add_players_y, add_players_width, bottom_section_height
            )

            # Draw Next button (bottom)
            next_btn_width = int(min(col_width * 0.6, col_width - 2 * column_spacing))
            next_btn_height = int(min(60 * balanced_scale, bottom_section_height * 0.7))

            # Center the button
            next_btn_x = add_players_x + (col_width - next_btn_width) // 2
            next_btn_y = add_players_y + bottom_section_height + column_spacing + (bottom_section_height - next_btn_height) // 3

            self.draw_next_button(next_btn_x, next_btn_y, next_btn_width, next_btn_height)

            # Draw remember checkbox below the Next button
            checkbox_height = int(min(30 * balanced_scale, bottom_section_height * 0.25))
            checkbox_y = next_btn_y + next_btn_height + int(10 * balanced_scale)
            self.draw_remember_checkbox(next_btn_x, checkbox_y, next_btn_width, checkbox_height)
        else:
            # Standard horizontal layout for normal/wide screens
            # Draw PRIZE POOL section (left column)
            prize_pool_x = column_spacing
            prize_pool_width = col_width
            self.draw_prize_pool_section(
                prize_pool_x, bottom_y, prize_pool_width, bottom_section_height
            )

            # Draw Add Players section (middle column)
            add_players_x = prize_pool_x + prize_pool_width + column_spacing
            add_players_width = col_width
            self.draw_add_players_section(
                add_players_x, bottom_y, add_players_width, bottom_section_height
            )

            # Draw Next button (right column, centered vertically)
            # Scale button size based on available space
            next_btn_width = int(min(col_width * 0.8, col_width - 2 * column_spacing))
            next_btn_height = int(min(70 * balanced_scale, bottom_section_height * 0.5))

            # Center the button in the right column
            next_btn_x = (
                add_players_x + add_players_width + column_spacing + (col_width - next_btn_width) // 2
            )
            next_btn_y = bottom_y + int((bottom_section_height - next_btn_height) / 3)  # Position higher to make room for checkbox

            self.draw_next_button(next_btn_x, next_btn_y, next_btn_width, next_btn_height)

            # Draw remember checkbox below the Next button
            checkbox_height = int(min(30 * balanced_scale, bottom_section_height * 0.25))
            checkbox_y = next_btn_y + next_btn_height + int(10 * balanced_scale)
            self.draw_remember_checkbox(next_btn_x, checkbox_y, next_btn_width, checkbox_height)

        # Draw toast message if active
        if self.message_timer > 0:
            self.draw_toast_message()

        # Draw popups - reset confirmation takes priority over board preview
        if self.showing_reset_confirmation:
            self.draw_reset_confirmation()
        elif self.showing_board_preview:
            self.draw_board_preview()

        # Draw settings window if visible
        if hasattr(self, 'settings_window'):
            self.settings_window.draw()

        # Draw cartella preview overlay if active
        if hasattr(self, 'cartella_preview') and self.cartella_preview.active:
            self.cartella_preview.draw()

        # Draw transition animation if active and available - always on top of everything else
        if hasattr(self, 'transition') and self.transition:
            if self.transition.active or getattr(self, 'game_start_transition_active', False):
                try:
                    self.transition.update()
                    self.transition.draw()
                except Exception as e:
                    print(f"Error in transition animation: {e}")
                    # Deactivate animation on error
                    self.transition.active = False
                    if hasattr(self, 'game_start_transition_active'):
                        self.game_start_transition_active = False

    def draw_continue_button(self, x, y, width, height):
        """Draw the continue/next button with hover effect"""
        # Get current mouse position for hover check
        mouse_pos = pygame.mouse.get_pos()

        # Create a rect for the button
        button_rect = pygame.Rect(x, y, width, height)

        # Get button animation state
        btn_state = self.button_states["continue"]

        # Check for hover animation
        hover_alpha = btn_state["hover_alpha"]

        # Base color and hover color
        base_color = (0, 120, 180)
        hover_color = (0, 150, 220)

        # Calculate effective color based on hover state
        if hover_alpha > 0:
            # Blend colors based on hover alpha
            effective_color = (
                int(base_color[0] + (hover_color[0] - base_color[0]) * hover_alpha / 255),
                int(base_color[1] + (hover_color[1] - base_color[1]) * hover_alpha / 255),
                int(base_color[2] + (hover_color[2] - base_color[2]) * hover_alpha / 255),
            )
        else:
            effective_color = base_color

        # Draw button
        self.draw_gradient_rect(
            button_rect,
            effective_color,
            (effective_color[0] // 2, effective_color[1] // 2, effective_color[2] // 2),
            10,
        )

        # Draw text
        btn_font = pygame.font.SysFont(
            "Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True
        )
        btn_text = btn_font.render("Continue to Game", True, WHITE)
        text_x = button_rect.centerx - btn_text.get_width() // 2
        text_y = button_rect.centery - btn_text.get_height() // 2 - int(5 * min(self.scale_x, self.scale_y))  # Move up slightly to make room for shortcut text
        self.screen.blit(btn_text, (text_x, text_y))

        # Draw shortcut hint below the main text
        shortcut_font = pygame.font.SysFont(
            "Arial", int(14 * min(self.scale_x, self.scale_y)), bold=False
        )
        shortcut_text = shortcut_font.render("(Ctrl+Enter)", True, (200, 200, 200))  # Light gray color
        shortcut_x = button_rect.centerx - shortcut_text.get_width() // 2
        shortcut_y = text_y + btn_text.get_height() + int(2 * min(self.scale_x, self.scale_y))
        self.screen.blit(shortcut_text, (shortcut_x, shortcut_y))

        # Store hit area
        self.hit_areas["continue"] = button_rect

    def draw_remember_checkbox(self, x, y, width, height):
        """Draw a checkbox for remembering cartella numbers"""
        # Get current mouse position for hover check
        mouse_pos = pygame.mouse.get_pos()

        # Calculate checkbox dimensions
        checkbox_size = int(min(24 * self.scale_y, height * 0.6))

        # Create a rect for the checkbox
        checkbox_rect = pygame.Rect(x, y + (height - checkbox_size) // 2, checkbox_size, checkbox_size)

        # Check if mouse is hovering over the checkbox
        is_hovering = checkbox_rect.collidepoint(mouse_pos)

        # Draw checkbox background
        if is_hovering:
            # Lighter background when hovering
            pygame.draw.rect(self.screen, (60, 80, 120), checkbox_rect, border_radius=4)
        else:
            # Normal background
            pygame.draw.rect(self.screen, (40, 60, 100), checkbox_rect, border_radius=4)

        # Draw checkbox border
        pygame.draw.rect(self.screen, (100, 140, 200), checkbox_rect, width=2, border_radius=4)

        # Draw checkmark if checked
        if self.remember_cartella_checkbox:
            # Calculate checkmark points
            margin = int(checkbox_size * 0.2)
            # Draw a stylized checkmark
            pygame.draw.line(
                self.screen,
                (100, 255, 100),  # Bright green
                (checkbox_rect.left + margin, checkbox_rect.centery),
                (checkbox_rect.centerx - margin//2, checkbox_rect.bottom - margin),
                width=3
            )
            pygame.draw.line(
                self.screen,
                (100, 255, 100),  # Bright green
                (checkbox_rect.centerx - margin//2, checkbox_rect.bottom - margin),
                (checkbox_rect.right - margin, checkbox_rect.top + margin),
                width=3
            )

        # Draw label text
        font_size = int(min(20 * self.scale_y, height * 0.7))
        font = pygame.font.SysFont("Arial", font_size)
        label = font.render("Remember for next game", True, (220, 220, 220))

        # Position label to the right of the checkbox
        label_x = checkbox_rect.right + int(10 * self.scale_x)
        label_y = y + (height - label.get_height()) // 2

        # Draw label
        self.screen.blit(label, (label_x, label_y))

        # Store hit area for the checkbox
        self.hit_areas["remember_checkbox"] = checkbox_rect

        # Return the total width of the checkbox + label for layout calculations
        return checkbox_rect.width + int(10 * self.scale_x) + label.get_width()

    def draw_next_button(self, x, y, width, height):
        """Draw the modern 'NEXT' button with arrow and hover effects"""
        # Get current mouse position for hover check
        mouse_pos = pygame.mouse.get_pos()

        # Create a rect for the button
        button_rect = pygame.Rect(x, y, width, height)

        # Check if mouse is hovering over the button
        is_hovering = button_rect.collidepoint(mouse_pos)

        # Draw button with modern gradient and glow effect
        if is_hovering:
            # Brighter colors for hover state
            self.draw_gradient_rect(button_rect, (0, 180, 220), (0, 140, 200), 12)

            # Add enhanced glow effect when hovering
            for i in range(1, 5):
                glow_rect = pygame.Rect(
                    button_rect.x - i,
                    button_rect.y - i,
                    button_rect.width + i * 2,
                    button_rect.height + i * 2,
                )
                glow_color = (0, 220, 255, 70 - i * 15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=12 + i)

            # Add inner highlight
            inner_highlight = pygame.Rect(
                button_rect.x + 2,
                button_rect.y + 2,
                button_rect.width - 4,
                int(button_rect.height * 0.3),
            )
            pygame.draw.rect(self.screen, (100, 220, 255, 40), inner_highlight, border_radius=10)
        else:
            # Normal state colors
            self.draw_gradient_rect(button_rect, (0, 140, 180), (0, 100, 140), 12)

        # Draw elegant "NEXT" text
        font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)
        text = font.render("NEXT", True, (255, 255, 255))

        # Calculate position for text and icon
        content_width = text.get_width() + int(30 * self.scale_x)  # Text + space + arrow
        text_x = x + int((width - content_width) / 2)
        text_y = y + int((height - text.get_height()) / 2)

        # Draw text with slight shadow for depth
        shadow_offset = int(1 * min(self.scale_x, self.scale_y))
        text_shadow = font.render("NEXT", True, (0, 0, 0, 150))
        self.screen.blit(text_shadow, (text_x + shadow_offset, text_y + shadow_offset))
        self.screen.blit(text, (text_x, text_y))

        # Draw modern arrow icon instead of simple triangle
        arrow_x = text_x + text.get_width() + int(10 * self.scale_x)
        arrow_y = int(y + height / 2)
        arrow_size = int(16 * min(self.scale_x, self.scale_y))

        # Draw circular background for arrow
        circle_radius = int(arrow_size * 0.8)
        pygame.draw.circle(
            self.screen,
            (255, 255, 255, 90),
            (int(arrow_x + arrow_size / 2), arrow_y),
            circle_radius,
        )

        # Draw arrow shape
        arrow_points = [
            (arrow_x, int(arrow_y - arrow_size / 2)),  # Top point
            (arrow_x + arrow_size, arrow_y),  # Right point
            (arrow_x, int(arrow_y + arrow_size / 2)),  # Bottom point
            (arrow_x + int(arrow_size / 4), arrow_y),  # Inner point
        ]

        # Draw shadow for arrow
        shadow_points = [(p[0] + shadow_offset, p[1] + shadow_offset) for p in arrow_points]
        pygame.draw.polygon(self.screen, (0, 0, 0, 100), shadow_points)

        # Draw arrow
        pygame.draw.polygon(self.screen, (255, 255, 255), arrow_points)

        # Add subtle pulsating animation effect when hovering
        if is_hovering:
            # Calculate pulse based on time
            pulse = (math.sin(time.time() * 4) + 1) / 8  # Subtle pulse value

            # Draw pulsating border
            pulse_rect = pygame.Rect(
                button_rect.x - int(2 * pulse * self.scale_x),
                button_rect.y - int(2 * pulse * self.scale_y),
                button_rect.width + int(4 * pulse * self.scale_x),
                button_rect.height + int(4 * pulse * self.scale_y),
            )
            pygame.draw.rect(
                self.screen, (100, 220, 255, int(100 * pulse)), pulse_rect, 2, border_radius=14
            )

        # Store hit area for next button
        self.hit_areas["next_button"] = button_rect

    def draw_selected_cartellas(self, x, y, width):
        """Draw a visually appealing list of selected cartella numbers"""
        if not self.selected_cartella_numbers:
            return

        # Use balanced scaling for consistency
        balanced_scale = min(self.scale_x, self.scale_y)

        # Calculate appropriate padding and sizes
        padding = int(10 * balanced_scale)
        chip_size = int(28 * balanced_scale)
        chip_spacing = int(6 * balanced_scale)

        # Draw header with small indicator icon
        header_font_size = int(16 * balanced_scale)
        header_font_size = max(header_font_size, 12)  # Minimum readable size
        header_font = pygame.font.SysFont("Arial", header_font_size, bold=True)
        header_text = header_font.render("Selected Cartella Numbers:", True, (200, 200, 220))

        # Create a small tag icon before the header
        icon_size = int(header_text.get_height() * 0.9)
        icon_x = x
        icon_y = y + (header_text.get_height() - icon_size) // 2

        # Draw tag icon
        tag_points = [
            (icon_x, icon_y),
            (icon_x + int(icon_size * 0.7), icon_y),
            (icon_x + icon_size, icon_y + int(icon_size * 0.5)),
            (icon_x + int(icon_size * 0.7), icon_y + icon_size),
            (icon_x, icon_y + icon_size),
        ]
        pygame.draw.polygon(self.screen, (0, 160, 220), tag_points)
        pygame.draw.polygon(self.screen, (0, 120, 180), tag_points, 1)

        # Draw small circle in the tag
        pygame.draw.circle(
            self.screen,
            (255, 255, 255, 180),
            (icon_x + int(icon_size * 0.3), icon_y + int(icon_size * 0.5)),
            int(icon_size * 0.2),
        )

        # Position header text after the icon
        header_x = icon_x + icon_size + padding // 2
        self.screen.blit(header_text, (header_x, y))

        # Calculate available width for chips
        available_width = width - padding * 2

        # Determine number of chips per row based on available space
        max_chips_per_row = max(1, int(available_width / (chip_size + chip_spacing)))

        # Organize numbers into rows
        rows = []
        current_row = []

        for num in sorted(self.selected_cartella_numbers):
            if len(current_row) >= max_chips_per_row:
                rows.append(current_row)
                current_row = []
            current_row.append(num)

        if current_row:
            rows.append(current_row)

        # Start position for drawing chips
        chips_y = y + header_text.get_height() + padding

        # Draw each row of chips
        for row_idx, row in enumerate(rows):
            row_y = chips_y + row_idx * (chip_size + chip_spacing // 2)

            # Center the row of chips
            row_width = len(row) * chip_size + (len(row) - 1) * chip_spacing
            row_x = x + (available_width - row_width) // 2

            # Draw each chip in the row
            for i, num in enumerate(row):
                chip_x = row_x + i * (chip_size + chip_spacing)

                # Determine color based on the row index (alternate colors)
                colors = [(60, 140, 230), (230, 100, 100), (100, 200, 100), (220, 180, 50)]
                color_idx = row_idx % len(colors)
                chip_color = colors[color_idx]

                # Draw chip with shadow for 3D effect
                shadow_offset = max(1, int(2 * balanced_scale))
                pygame.draw.circle(
                    self.screen,
                    (30, 30, 40),
                    (chip_x + chip_size // 2 + shadow_offset, row_y + chip_size // 2 + shadow_offset),
                    chip_size // 2,
                )
                pygame.draw.circle(
                    self.screen, chip_color, (chip_x + chip_size // 2, row_y + chip_size // 2), chip_size // 2
                )

                # Add highlight to give 3D effect
                highlight_radius = (chip_size // 2) - 2
                highlight_rect = pygame.Rect(
                    chip_x + chip_size // 2 - highlight_radius,
                    row_y + chip_size // 2 - highlight_radius,
                    highlight_radius * 2,
                    highlight_radius * 2,
                )
                pygame.draw.ellipse(
                    self.screen,
                    (min(255, chip_color[0] + 40), min(255, chip_color[1] + 40), min(255, chip_color[2] + 40)),
                    highlight_rect.inflate(-highlight_radius, -highlight_radius * 1.2),
                )

                # Draw number text inside chip
                num_font_size = int(15 * balanced_scale)
                num_font_size = max(num_font_size, 12)  # Minimum readable size
                num_font = pygame.font.SysFont("Arial", num_font_size, bold=True)
                num_text = num_font.render(str(num), True, (255, 255, 255))
                num_x = chip_x + (chip_size - num_text.get_width()) // 2
                num_y = row_y + (chip_size - num_text.get_height()) // 2
                self.screen.blit(num_text, (num_x, num_y))

                # Create a hit area for the entire chip
                chip_rect = pygame.Rect(
                    chip_x,
                    row_y,
                    chip_size,
                    chip_size
                )
                # Store hit area for the chip to handle clicks on the number
                self.hit_areas[f"lucky_number_{num}"] = chip_rect

                # Add remove button (small X) at the top right of each chip
                remove_size = max(8, int(10 * balanced_scale))
                remove_x = chip_x + chip_size - remove_size - 2
                remove_y = row_y + 2
                remove_rect = pygame.Rect(remove_x, remove_y, remove_size, remove_size)

                # Draw remove button
                pygame.draw.rect(self.screen, (200, 50, 50), remove_rect, border_radius=3)

                # Draw X
                line_thickness = max(1, int(1 * balanced_scale))
                pygame.draw.line(
                    self.screen,
                    (255, 255, 255),
                    (remove_x + 2, remove_y + 2),
                    (remove_x + remove_size - 2, remove_y + remove_size - 2),
                    line_thickness,
                )
                pygame.draw.line(
                    self.screen,
                    (255, 255, 255),
                    (remove_x + remove_size - 2, remove_y + 2),
                    (remove_x + 2, remove_y + remove_size - 2),
                    line_thickness,
                )

                # Store hit area for this remove button
                self.hit_areas[f"remove_cartella_{num}"] = remove_rect

    def update_button_hover_states(self, mouse_pos):
        """Update hover states for all buttons based on mouse position"""
        for key, area in self.hit_areas.items():
            # Skip if this button doesn't have animation state
            if key not in self.button_states:
                continue

            # Check if mouse is over this button
            hover = area.collidepoint(mouse_pos)
            btn_state = self.button_states[key]

            # Update hover state
            if hover != btn_state["hover"]:
                btn_state["hover"] = hover
                if hover:
                    # Mouse entered - start hover-in animation
                    btn_state["hover_alpha"] = 1
                else:
                    # Mouse left - start hover-out animation
                    btn_state["hover_alpha"] = 255

            # Animate hover alpha
            if hover and btn_state["hover_alpha"] < 255:
                # Fade in (faster)
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 25)
            elif not hover and btn_state["hover_alpha"] > 0:
                # Fade out (slower)
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 15)

    def draw_wow_bingo_header(self):
        """Draw the WOW Games header consistent with the main window"""
        screen_width, screen_height = self.screen.get_size()

        # Calculate adaptive values for responsive layout
        balanced_scale = min(self.scale_x, self.scale_y)

        # Scale positions based on screen size - left aligned
        title_x = int(110 * self.scale_x)
        title_y = int(50 * self.scale_y)  # Position at top of screen
        circle_radius = int(90 * balanced_scale)

        # Draw the yellow circle background with enhanced glow effect
        yellow_color = (255, 215, 0)  # Vibrant gold yellow to match main.py
        for i in range(5):
            alpha = 100-i*20
            if alpha < 0:
                alpha = 0
            glow_surface = pygame.Surface((circle_radius*2+i*6*balanced_scale, circle_radius*2+i*6*balanced_scale), pygame.SRCALPHA)
            pygame.draw.circle(glow_surface, (*yellow_color, alpha),
                              (glow_surface.get_width()//2, glow_surface.get_height()//2),
                              circle_radius+i*3*balanced_scale)
            self.screen.blit(glow_surface, (title_x-glow_surface.get_width()//2, title_y-glow_surface.get_height()//2))

        # Draw main yellow circle
        pygame.draw.circle(self.screen, yellow_color, (title_x, title_y), circle_radius)

        # Add some subtle shading for 3D effect
        for i in range(1, 20, 2):
            shade_radius = circle_radius - i
            if shade_radius > 0:
                shade_color = (
                    max(0, min(255, int(yellow_color[0] - i*2))),
                    max(0, min(255, int(yellow_color[1] - i*3))),
                    max(0, min(255, int(yellow_color[2] - i*1)))
                )
                pygame.draw.circle(self.screen, shade_color, (title_x, title_y), shade_radius, 1)

        # Draw "WOW Games" text with enhanced 3D effect
        # Match colors exactly with main.py
        colors = [
            (255, 50, 50),    # Bright red for W
            (255, 90, 50),    # Reddish orange for O
            (255, 130, 50),   # Orange for W
            (200, 200, 200),  # Silver for space
            (50, 220, 100),   # Bright green for G
            (50, 180, 160),   # Teal for a
            (50, 140, 200),   # Sky blue for m
            (50, 100, 220),   # Blue for e
            (100, 80, 220)    # Purplish blue for s
        ]
        letters = "WOW Games"

        # Calculate font size to match proportions in main.py
        title_font_size = int(54 * balanced_scale)
        title_font_size = max(title_font_size, 32)  # Ensure minimum readable size
        title_font = pygame.font.SysFont("Arial", title_font_size, bold=True)

        x_pos = int(10 * self.scale_x)  # Start position
        letter_spacing = int(28 * self.scale_x)  # Spacing between letters
        y_position = int(title_y - circle_radius * 0.5)  # Position text relative to circle

        # Process each letter
        for i, letter in enumerate(letters):
            # Skip spaces but account for them in positioning
            if letter == " ":
                x_pos += letter_spacing // 2  # Half space for the space character
                continue

            # Get the color index safely
            color_index = min(i, len(colors) - 1)
            current_color = colors[color_index]

            # Multiple shadow layers for stronger 3D effect
            for offset in range(1, 4):
                shadow_color = (max(0, current_color[0] - 150),
                                max(0, current_color[1] - 150),
                                max(0, current_color[2] - 150))
                shadow_surf = title_font.render(letter, True, shadow_color)
                self.screen.blit(shadow_surf,
                               (x_pos + offset*self.scale_x,
                                y_position + int(3*self.scale_y) + offset*self.scale_y))

            # Main letter - using exact colors from main.py
            letter_surf = title_font.render(letter, True, current_color)
            self.screen.blit(letter_surf, (x_pos, y_position))

            # Highlight/reflection on top of letter for glossy effect
            highlight_color = (min(255, current_color[0] + 50),
                              min(255, current_color[1] + 50),
                              min(255, current_color[2] + 50))
            highlight_surf = title_font.render(letter, True, highlight_color)

            # Only show top portion of highlight using a mask
            highlight_height = int(letter_surf.get_height() * 0.3)
            highlight_mask = pygame.Surface((letter_surf.get_width(), highlight_height), pygame.SRCALPHA)
            highlight_mask.blit(highlight_surf, (0, 0))
            self.screen.blit(highlight_mask, (x_pos, y_position))

            x_pos += letter_spacing

    def draw_bingo_grid(self):
        """Draw a more compact and visually appealing BINGO grid with numbers 1-75"""
        # Get current mouse position for all hover checks
        mouse_pos = pygame.mouse.get_pos()

        # Clear all number-related hit areas before redrawing the grid
        # This ensures that only the currently visible numbers have active hit areas
        number_keys_to_remove = [key for key in list(self.hit_areas.keys()) if key.startswith("lucky_number_")]
        for key in number_keys_to_remove:
            del self.hit_areas[key]

        screen_width = self.screen.get_width()
        padding = int(15 * min(self.scale_x, self.scale_y))

        # Calculate grid position dynamically based on layout
        grid_start_x = padding
        grid_start_y = int(150 * self.scale_y)

        # Completely redesigned colors with a modern, flat design approach
        row_colors = {
            "B": (235, 69, 95),   # Modern pink-red for B
            "O": (59, 201, 119),  # Fresh mint green for O
            "A": (67, 126, 237),  # Medium blue for A
            "R": (235, 69, 95),   # Same pink-red for R (matching B)
            "D": (248, 190, 42),  # Warm amber for D
        }
        row_letters = "BOARD"

        # Increased row dimensions for better spacing
        row_height = int(65 * self.scale_y)
        row_spacing = int(15 * self.scale_y)

        # Modern font sizes for better visibility and appeal
        letter_font_size = int(50 * min(self.scale_x, self.scale_y))  # Slightly smaller for better fit in background
        letter_font_size = max(letter_font_size, 36)  # Ensure minimum readable size

        # Try to use a clean, modern font for better visual appeal
        try:
            letter_font = pygame.font.SysFont("Arial Black", letter_font_size, bold=True)  # Bold Arial Black for a cleaner look
        except:
            # Fall back to regular Arial if Arial Black is not available
            letter_font = pygame.font.SysFont("Arial", letter_font_size, bold=True)
        number_font = pygame.font.SysFont(
            "Arial", int(24 * min(self.scale_x, self.scale_y)), bold=False
        )
        small_number_font = pygame.font.SysFont(
            "Arial", int(22 * min(self.scale_x, self.scale_y)), bold=False
        )

        # Larger font sizes for selected numbers (2x size)
        large_number_font = pygame.font.SysFont(
            "Arial", int(48 * min(self.scale_x, self.scale_y)), bold=True
        )
        large_small_number_font = pygame.font.SysFont(
            "Arial", int(44 * min(self.scale_x, self.scale_y)), bold=True
        )

        # Calculate max pages and max number
        max_num = 1200  # Maximum number to display (increased from 400 to 1200)
        nums_per_page = 75  # Numbers per page
        max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)

        # Position for the first row
        current_y = grid_start_y

        # Calculate animation pulse effect
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1

        # Draw page indicator above grid
        page_font = pygame.font.SysFont(
            "Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True
        )
        page_text = page_font.render(
            f"PAGE {self.grid_page + 1}/{max_pages}", True, (180, 180, 180)
        )
        page_x = grid_start_x + int(200 * self.scale_x)  # Centered position
        page_y = grid_start_y - int(20 * self.scale_y)  # Position above grid
        self.screen.blit(page_text, (page_x, page_y))

        # Calculate the total height of the bingo grid
        total_grid_height = (row_height + row_spacing) * 5 - row_spacing

        # Draw vertical navigation buttons on the right side - more compact design
        nav_btn_width = int(15 * self.scale_x)  # Reduced width
        nav_btn_height = total_grid_height

        # Position next button at the right edge
        nav_btn_x = screen_width - padding - nav_btn_width
        nav_btn_y = grid_start_y
        nav_btn_rect = pygame.Rect(nav_btn_x, nav_btn_y, nav_btn_width, nav_btn_height)

        # Position previous button immediately to the left of the next button with minimal gap
        prev_btn_x = nav_btn_x - nav_btn_width - int(2 * self.scale_x)
        prev_btn_y = grid_start_y
        prev_btn_rect = pygame.Rect(prev_btn_x, prev_btn_y, nav_btn_width, nav_btn_height)

        # Check mouse hover for prev button
        prev_btn_hover = prev_btn_rect.collidepoint(mouse_pos)

        # Draw the previous button with gradient effect (matching the next button style)
        # Use more vibrant colors for an amazing look
        prev_start_color = (60, 140, 255) if prev_btn_hover else (40, 100, 220)  # Brighter blue
        prev_end_color = (20, 80, 200) if prev_btn_hover else (10, 60, 180)  # Deeper blue

        # Create gradient surface for previous button
        prev_btn_surf = pygame.Surface((nav_btn_width, nav_btn_height), pygame.SRCALPHA)
        for y in range(nav_btn_height):
            # Calculate blend factor
            t = y / nav_btn_height
            # Linear interpolation of colors
            r = int(prev_start_color[0] * (1 - t) + prev_end_color[0] * t)
            g = int(prev_start_color[1] * (1 - t) + prev_end_color[1] * t)
            b = int(prev_start_color[2] * (1 - t) + prev_end_color[2] * t)
            pygame.draw.line(prev_btn_surf, (r, g, b), (0, y), (nav_btn_width, y))

        # Draw the previous button with rounded corners
        pygame.draw.rect(
            prev_btn_surf, (0, 0, 0, 0), (0, 0, nav_btn_width, nav_btn_height), border_radius=5
        )
        self.screen.blit(prev_btn_surf, prev_btn_rect)

        # Draw previous button border with glowing effect
        border_color = (100, 180, 255) if prev_btn_hover else (70, 130, 220)
        pygame.draw.rect(self.screen, border_color, prev_btn_rect, width=1, border_radius=5)

        # Add subtle glow effect for previous button when hovered
        if prev_btn_hover:
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    prev_btn_rect.x - i,
                    prev_btn_rect.y - i,
                    prev_btn_rect.width + i * 2,
                    prev_btn_rect.height + i * 2,
                )
                glow_color = (100, 180, 255, 70 - i * 20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=5 + i)

        # Draw "<" arrow symbol in the center of previous button with improved visibility
        prev_arrow_font = pygame.font.SysFont(
            "Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True
        )
        prev_arrow_text = prev_arrow_font.render("<", True, (255, 255, 255))  # Brighter white
        prev_arrow_x = prev_btn_x + (nav_btn_width - prev_arrow_text.get_width()) // 2
        prev_arrow_y = prev_btn_y + (nav_btn_height - prev_arrow_text.get_height()) // 2
        self.screen.blit(prev_arrow_text, (prev_arrow_x, prev_arrow_y))

        # Store prev button hit area
        self.hit_areas["prev_grid_page"] = prev_btn_rect

        # Check mouse hover for nav button
        nav_btn_hover = nav_btn_rect.collidepoint(mouse_pos)

        # Draw the navigation button with gradient effect - matching style with previous button
        start_color = (60, 140, 255) if nav_btn_hover else (40, 100, 220)  # Brighter blue
        end_color = (20, 80, 200) if nav_btn_hover else (10, 60, 180)  # Deeper blue

        # Create gradient surface
        nav_btn_surf = pygame.Surface((nav_btn_width, nav_btn_height), pygame.SRCALPHA)
        for y in range(nav_btn_height):
            # Calculate blend factor
            t = y / nav_btn_height
            # Linear interpolation of colors
            r = int(start_color[0] * (1 - t) + end_color[0] * t)
            g = int(start_color[1] * (1 - t) + end_color[1] * t)
            b = int(start_color[2] * (1 - t) + end_color[2] * t)
            pygame.draw.line(nav_btn_surf, (r, g, b), (0, y), (nav_btn_width, y))

        # Draw the button with rounded corners
        pygame.draw.rect(
            nav_btn_surf, (0, 0, 0, 0), (0, 0, nav_btn_width, nav_btn_height), border_radius=5
        )
        self.screen.blit(nav_btn_surf, nav_btn_rect)

        # Draw button border with glowing effect - matching previous button
        border_color = (100, 180, 255) if nav_btn_hover else (70, 130, 220)
        pygame.draw.rect(self.screen, border_color, nav_btn_rect, width=1, border_radius=5)

        # Add subtle glow effect for next button when hovered
        if nav_btn_hover:
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    nav_btn_rect.x - i,
                    nav_btn_rect.y - i,
                    nav_btn_rect.width + i * 2,
                    nav_btn_rect.height + i * 2,
                )
                glow_color = (100, 180, 255, 70 - i * 20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=5 + i)

        # Draw ">" arrow symbol in the center of button with improved visibility
        arrow_font = pygame.font.SysFont(
            "Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True
        )
        arrow_text = arrow_font.render(">", True, (255, 255, 255))  # Brighter white
        arrow_x = nav_btn_x + (nav_btn_width - arrow_text.get_width()) // 2
        arrow_y = nav_btn_y + (nav_btn_height - arrow_text.get_height()) // 2
        self.screen.blit(arrow_text, (arrow_x, arrow_y))

        # Store nav button hit area
        self.hit_areas["next_grid_page"] = nav_btn_rect

        # Draw each row (B, I, N, G, O)
        for row_idx, letter in enumerate(row_letters):
            row_color = row_colors[letter]

            # COMPLETELY NEW APPROACH: Modern flat design with no shadows

            # Calculate letter position
            letter_x = grid_start_x

            # Create a background rectangle for the letter
            letter_bg_width = int(50 * min(self.scale_x, self.scale_y))
            letter_bg_height = int(row_height * 0.9)
            letter_bg_x = letter_x - int(10 * min(self.scale_x, self.scale_y))
            letter_bg_y = current_y + (row_height - letter_bg_height) // 2

            # Create a rect for the letter background
            letter_bg_rect = pygame.Rect(letter_bg_x, letter_bg_y, letter_bg_width, letter_bg_height)

            # Draw a flat background with rounded corners
            bg_radius = int(8 * min(self.scale_x, self.scale_y))
            pygame.draw.rect(self.screen, row_color, letter_bg_rect, border_radius=bg_radius)

            # Draw a subtle border for depth instead of shadow
            border_color = (int(row_color[0] * 0.8), int(row_color[1] * 0.8), int(row_color[2] * 0.8))
            pygame.draw.rect(self.screen, border_color, letter_bg_rect, width=1, border_radius=bg_radius)

            # Render the letter in white for better contrast against the colored background
            letter_surf = letter_font.render(letter, True, (255, 255, 255))

            # Center the letter in the background rectangle
            letter_y = letter_bg_y + (letter_bg_height - letter_surf.get_height()) // 2
            letter_x = letter_bg_x + (letter_bg_width - letter_surf.get_width()) // 2

            # Draw the letter
            self.screen.blit(letter_surf, (letter_x, letter_y))

            # Draw modern background for numbers with adjusted height to prevent overlap
            # Add extra buffer for the navigation buttons to prevent overlap
            nav_buttons_width = nav_btn_width * 2 + int(
                5 * self.scale_x
            )  # Width of both nav buttons + gap
            row_bg_width = (
                nav_btn_x - letter_x - letter_surf.get_width() - padding - nav_buttons_width
            )
            row_bg_x = letter_x + letter_surf.get_width() + int(10 * self.scale_x)

            # Adjust row background height to be slightly smaller than row_height to prevent overlap
            row_bg_height = int(row_height * 0.9)  # 90% of row height
            row_bg_y = current_y + (row_height - row_bg_height) // 2  # Center vertically
            row_bg_rect = pygame.Rect(row_bg_x, row_bg_y, row_bg_width, row_bg_height)

            # Draw rounded rectangle with subtle gradient
            bg_color_top = (25, 30, 40)
            bg_color_bottom = (20, 25, 30)
            radius = int(8 * self.scale_y)

            # Create gradient background
            bg_surf = pygame.Surface((int(row_bg_width), row_height), pygame.SRCALPHA)
            for y in range(row_height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / row_height
                # Linear interpolation of colors
                r = int(bg_color_top[0] * (1 - t) + bg_color_bottom[0] * t)
                g = int(bg_color_top[1] * (1 - t) + bg_color_bottom[1] * t)
                b = int(bg_color_top[2] * (1 - t) + bg_color_bottom[2] * t)
                pygame.draw.line(bg_surf, (r, g, b), (0, y), (int(row_bg_width), y))

            # Create mask for rounded corners
            mask = pygame.Surface((int(row_bg_width), row_height), pygame.SRCALPHA)
            pygame.draw.rect(
                mask, (255, 255, 255), (0, 0, int(row_bg_width), row_height), border_radius=radius
            )

            # Apply mask
            bg_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            self.screen.blit(bg_surf, row_bg_rect)

            # Draw subtle border
            pygame.draw.rect(self.screen, (40, 50, 60), row_bg_rect, width=1, border_radius=radius)

            # Calculate number of numbers in this row and spacing
            nums_per_row = 15
            num_spacing = row_bg_width / nums_per_row
            num_radius = min(int(22 * self.scale_x), int(num_spacing * 0.4))

            # Draw numbers for this row
            for col in range(nums_per_row):
                # Calculate actual number based on grid page
                base_num = row_idx * nums_per_row + col + 1
                num = base_num + (self.grid_page * nums_per_page)

                # Skip numbers beyond max_num
                if num > max_num:
                    continue

                # Calculate number position - centered within the row background
                num_x = row_bg_x + int(num_spacing * (col + 0.5))
                num_y = row_bg_y + row_bg_height // 2

                # Check if number is called/selected or current
                is_selected = num in self.called_numbers
                is_current = num == self.cartella_number

                # Mouse hover check - constrain hit area height to prevent overlap with adjacent rows
                hit_area_width = (num_radius + int(2 * self.scale_x)) * 2
                hit_area_height = min(
                    (num_radius + int(2 * self.scale_y)) * 2,
                    row_bg_height - 4  # Ensure hit area stays within row background with small margin
                )
                number_rect = pygame.Rect(
                    num_x - hit_area_width // 2,
                    num_y - hit_area_height // 2,
                    hit_area_width,
                    hit_area_height
                )
                is_hover = number_rect.collidepoint(mouse_pos)

                # Determine circle color with animation for current number
                if is_current:
                    # Use a different color scheme for current focused (not selected) cartella numbers
                    # Using a cyan/teal color palette that's distinct from the row colors

                    # Base color for current focused number - cyan/teal
                    focus_color = (20, 180, 200)  # Bright teal/cyan base

                    # Pulsating highlight for current number
                    highlight_intensity = int(150 + 105 * pulse)
                    highlight_color = (
                        min(255, focus_color[0] + highlight_intensity//3),
                        min(255, focus_color[1] + highlight_intensity//3),
                        min(255, focus_color[2] + highlight_intensity//3),
                    )

                    # Draw outer glow with the new color
                    for i in range(1, 4):
                        glow_radius = num_radius + int(i * 2 * self.scale_x)
                        glow_alpha = 150 - i * 40
                        glow_color = (*focus_color, glow_alpha)
                        glow_surf = pygame.Surface(
                            (glow_radius * 2, glow_radius * 2), pygame.SRCALPHA
                        )
                        pygame.draw.circle(
                            glow_surf, glow_color, (glow_radius, glow_radius), glow_radius
                        )
                        self.screen.blit(
                            glow_surf,
                            (num_x - glow_radius, num_y - glow_radius),
                            special_flags=pygame.BLEND_RGBA_ADD,
                        )

                    # Draw the main circle
                    pygame.draw.circle(
                        self.screen,
                        highlight_color,
                        (num_x, num_y),
                        num_radius + int(2 * pulse * self.scale_x),
                    )

                    # Draw inner circle for better contrast
                    pygame.draw.circle(
                        self.screen, focus_color, (num_x, num_y), int(num_radius * 0.8)
                    )
                elif is_selected:
                    # Enhanced highlight for selected numbers

                    # Draw outer glow
                    glow_color = (*row_color, 100)
                    glow_radius = num_radius + int(2 * self.scale_x)
                    glow_surf = pygame.Surface((glow_radius * 2, glow_radius * 2), pygame.SRCALPHA)
                    pygame.draw.circle(
                        glow_surf, glow_color, (glow_radius, glow_radius), glow_radius
                    )
                    self.screen.blit(
                        glow_surf,
                        (num_x - glow_radius, num_y - glow_radius),
                        special_flags=pygame.BLEND_RGBA_ADD,
                    )

                    # Brighter color for selected numbers with a subtle pulse
                    bright_factor = 0.7 + 0.3 * pulse
                    bright_color = (
                        min(255, int(row_color[0] * bright_factor)),
                        min(255, int(row_color[1] * bright_factor)),
                        min(255, int(row_color[2] * bright_factor)),
                    )
                    pygame.draw.circle(self.screen, bright_color, (num_x, num_y), num_radius)

                    # Draw a subtle ring around selected numbers
                    ring_color = (255, 255, 255, 100)
                    pygame.draw.circle(self.screen, ring_color, (num_x, num_y), num_radius, width=2)
                elif is_hover:
                    # Hover effect for non-selected numbers
                    hover_color = (100, 110, 130)
                    pygame.draw.circle(self.screen, hover_color, (num_x, num_y), num_radius)
                else:
                    # Improved gray color for non-selected numbers
                    pygame.draw.circle(self.screen, (70, 75, 85), (num_x, num_y), num_radius)

                # Choose font based on selection state and number size
                if is_selected or is_current:
                    # Use larger, bold font for selected/current numbers
                    current_font = large_number_font if num < 10 else large_small_number_font
                else:
                    # Use regular font for unselected numbers
                    current_font = number_font if num < 10 else small_number_font

                # Draw number text with improved contrast
                num_text = str(num)

                # Text color depends on state
                if is_selected:
                    text_color = (255, 255, 255)  # White for selected
                elif is_current:
                    text_color = (255, 255, 255)  # White for current
                elif is_hover:
                    text_color = (255, 255, 255)  # White for hover
                else:
                    text_color = (220, 220, 220)  # Light gray for normal

                num_surf = current_font.render(num_text, True, text_color)
                num_rect = num_surf.get_rect(center=(num_x, num_y))

                # Add subtle shadow for better readability
                shadow_surf = current_font.render(num_text, True, (20, 20, 20))
                shadow_rect = shadow_surf.get_rect(
                    center=(num_rect.centerx + 1, num_rect.centery + 1)
                )
                self.screen.blit(shadow_surf, shadow_rect)
                self.screen.blit(num_surf, num_rect)

                # Store hit area for each number with slightly larger clickable area
                self.hit_areas[f"lucky_number_{num}"] = number_rect

            # Draw a subtle separator line between rows
            if row_idx < len(row_letters) - 1:  # Don't draw after the last row
                separator_y = current_y + row_height + row_spacing // 2
                separator_color = (40, 50, 60, 120)  # Semi-transparent dark color
                pygame.draw.line(
                    self.screen,
                    separator_color,
                    (grid_start_x, separator_y),
                    (nav_btn_x - padding, separator_y),
                    1
                )

            # Move to next row with increased spacing
            current_y += row_height + row_spacing

    def handle_direct_interaction(self, pos, ctrl_pressed=False):
        """Direct handling of card selection interactions"""
        # Check for reset confirmation dialog buttons FIRST if the dialog is showing
        # This ensures these buttons have the highest priority
        if self.showing_reset_confirmation:
            if "reset_confirm" in self.hit_areas and self.hit_areas["reset_confirm"].collidepoint(pos):
                # Confirm reset - execute the reset_all_cartellas method
                self.reset_all_cartellas()
                # Close the confirmation dialog
                self.showing_reset_confirmation = False
                # Play sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
                print("Reset confirmed - dialog closed")
                return True

            if "reset_cancel" in self.hit_areas and self.hit_areas["reset_cancel"].collidepoint(pos):
                # Cancel reset - just close the confirmation dialog
                self.showing_reset_confirmation = False
                # Play sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
                print("Reset canceled - dialog closed")
                return True

            # If the dialog is showing but the click wasn't on a button,
            # don't process any other interactions (prevent clicks through the dialog)
            return True

        # Check for navigation bar interactions
        for key in self.hit_areas:
            if key.startswith("nav_") and self.hit_areas[key].collidepoint(pos):
                nav_item = key.replace("nav_", "")

                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Handle navigation
                if nav_item == "stats":
                    # Navigate to stats page - use the same pattern as settings page
                    try:
                        # Check if we have access to the main game instance for integrated view
                        if hasattr(self, 'game_instance') and self.game_instance and hasattr(self.game_instance, 'stats_view'):
                            # Set the active navigation to stats
                            self.game_instance.active_nav = "stats"
                            # Show the stats view
                            self.game_instance.stats_view.show()
                            print("Showing integrated stats view")
                            return True
                        else:
                            # Use standalone stats page (same as settings page)
                            from stats_page import show_stats_page

                            # Save current state before navigating away
                            try:
                                from player_storage import save_game_settings, save_ui_state

                                # Save game settings
                                settings = {
                                    'commission_percentage': getattr(self, 'commission_percentage', 20.0),
                                    'bet_amount': self.bet_amount,
                                    'prize_pool': self.prize_pool,
                                    'prize_pool_manual_override': self.prize_pool_manual_override
                                }
                                save_game_settings(settings)

                                # Save UI state if remember checkbox is checked
                                if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
                                    save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)

                            except Exception as save_error:
                                print(f"Warning: Could not save state before navigation: {save_error}")

                            # Show the stats page with previous page information
                            show_stats_page(self.screen, previous_page="board_selection")

                            # After stats page closes, we need to refresh the board selection display
                            pygame.display.update()
                            pygame.display.flip()
                            return True

                    except Exception as e:
                        # Show error message if stats page fails to load
                        self.message = f"Error loading stats page: {str(e)}"
                        self.message_type = "error"
                        self.message_timer = 180
                        print(f"Error showing stats page: {e}")
                        import traceback
                        traceback.print_exc()
                        return True
                elif nav_item == "preview":
                    # Show cartella preview overlay
                    if hasattr(self, 'cartella_preview'):
                        self.cartella_preview.show()
                    else:
                        self.show_message("Preview feature not available", "error")
                    return True
                elif nav_item == "settings":
                    # Show settings page
                    try:
                        from settings_page import show_settings_page
                        # Save settings before showing settings page
                        if hasattr(self, 'settings_window') and hasattr(self.settings_window, 'settings_manager'):
                            self.settings_window.settings_manager.save_settings()
                        show_settings_page(self.screen)
                        return True
                    except Exception as e:
                        # Show error message if settings page fails to load
                        self.message = f"Error loading settings page: {str(e)}"
                        self.message_type = "error"
                        self.message_timer = 180
                        return True
                elif nav_item == "help":
                    self.show_message("Help feature coming soon", "info")
                    return True

        # Check for direct interactions with card numbers
        for num in range(1, 1201):  # All possible card numbers (increased from 401 to 1201)
            key = f"lucky_number_{num}"
            if key in self.hit_areas and self.hit_areas[key].collidepoint(pos):
                # Handle card number selection directly
                if ctrl_pressed and not self.showing_reset_confirmation:
                    # Show board preview
                    self.showing_board_preview = True
                    self.preview_cartella_number = num
                    self.preview_board = self.get_board_for_number(num)
                    return
                else:
                    # Toggle card selection
                    if num in self.called_numbers:
                        # Remove the number
                        self.called_numbers.remove(num)

                        # Remove from selected cartella numbers if it exists
                        if num in self.selected_cartella_numbers:
                            self.selected_cartella_numbers.remove(num)

                            # Remove from players list
                            for i, player in enumerate(self.players):
                                if player.cartela_no == num:
                                    self.players.pop(i)
                                    break

                            # Save updated players to JSON
                            save_players_to_json(self.players)

                            # Update prize pool
                            self.calculate_prize_pool()

                            # Play cartella cancellation announcement
                            self.play_cartella_cancellation(num)

                            # Show success message
                            self.show_message(f"Removed player with cartella #{num}", "success")
                    else:
                        # Select the number
                        self.cartella_number = num
                        self.input_text = str(num)
                        self.create_board_from_number(num)

                        # Add this number to called_numbers for highlighting
                        if num not in self.called_numbers:
                            self.called_numbers.append(num)

                        # Add player with this cartella number
                        self.add_player()

                    # Don't play button click sound here as cartella announcement will be played
                    return

        # Check for direct interactions with navigation buttons
        if "prev_grid_page" in self.hit_areas and self.hit_areas["prev_grid_page"].collidepoint(pos):
            # Move to the previous page of numbers
            max_num = 1200  # Increased from 400 to 1200
            nums_per_page = 75
            max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)

            # Decrement page and wrap around if needed
            self.grid_page = (self.grid_page - 1) % max_pages

            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        if "next_grid_page" in self.hit_areas and self.hit_areas["next_grid_page"].collidepoint(pos):
            # Move to the next page of numbers
            max_num = 1200  # Increased from 400 to 1200
            nums_per_page = 75
            max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)

            # Increment page and wrap around if needed
            self.grid_page = (self.grid_page + 1) % max_pages

            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with input fields
        if "cartella_input" in self.hit_areas and self.hit_areas["cartella_input"].collidepoint(pos):
            self.input_active = True
            self.bet_input_active = False
            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        if "bet_input" in self.hit_areas and self.hit_areas["bet_input"].collidepoint(pos):
            self.bet_input_active = True
            self.input_active = False
            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with add/remove player button
        if ("add_player" in self.hit_areas and self.hit_areas["add_player"].collidepoint(pos)) or \
           ("add_button" in self.hit_areas and self.hit_areas["add_button"].collidepoint(pos)):
            # If input is active, apply the input value first
            if self.input_active:
                is_valid, error_msg = self.validate_cartella_number(self.input_text)
                if is_valid:
                    self.cartella_number = int(self.input_text)
                    self.create_board_from_number(self.cartella_number)
                    self.input_active = False
                else:
                    self.show_message(error_msg, "error")
                    # Play sound if available
                    if self.button_click_sound:
                        self.button_click_sound.play()
                    return

            # Check if cartella is already selected
            if self.cartella_number in self.selected_cartella_numbers:
                # Remove the cartella
                self.selected_cartella_numbers.remove(self.cartella_number)

                # Remove from called_numbers for highlighting
                if self.cartella_number in self.called_numbers:
                    self.called_numbers.remove(self.cartella_number)

                # Remove from players list
                for i, player in enumerate(self.players):
                    if player.cartela_no == self.cartella_number:
                        self.players.pop(i)
                        break

                # Save UI state if option is enabled
                if self.remember_cartella_checkbox:
                    try:
                        from player_storage import save_ui_state, load_ui_state
                        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
                        print(f"Updated UI state after removal: {len(self.selected_cartella_numbers)} numbers")
                    except Exception as e:
                        print(f"Error saving UI state after removal: {e}")

                # Save updated players to JSON
                save_players_to_json(self.players)

                # Update prize pool
                self.calculate_prize_pool()

                # Play cartella cancellation announcement
                self.play_cartella_cancellation(self.cartella_number)

                # Show success message
                self.show_message(
                    f"Removed player with cartella #{self.cartella_number}", "success"
                )
            else:
                # Add current cartella to the selected list
                self.add_player()

            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with set button
        if "set_button" in self.hit_areas and self.hit_areas["set_button"].collidepoint(pos):
            # Apply the bet input value - this will update all players and recalculate the prize pool
            self._apply_bet_input()
            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with remember checkbox
        if "remember_checkbox" in self.hit_areas and self.hit_areas["remember_checkbox"].collidepoint(pos):
            # Toggle checkbox state
            self.remember_cartella_checkbox = not self.remember_cartella_checkbox

            # Update setting in settings manager
            self.settings_manager.set_setting('game', 'remember_cartella_numbers', self.remember_cartella_checkbox)
            self.settings_manager.save_settings()

            # Show message
            if self.remember_cartella_checkbox:
                self.show_message("Selected numbers will be remembered for next game", "success")
            else:
                self.show_message("Selected numbers will not be remembered", "info")

            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with next/continue button
        if ("next_button" in self.hit_areas and self.hit_areas["next_button"].collidepoint(pos)) or \
           ("continue" in self.hit_areas and self.hit_areas["continue"].collidepoint(pos)):
            if len(self.selected_cartella_numbers) > 0:
                # Start the game transition animation instead of immediately exiting
                if not self.game_start_transition_active:
                    self.start_game_transition()

                # Play sound if available (button click sound will be played in addition to the announcement)
                if self.button_click_sound:
                    self.button_click_sound.play()
            else:
                self.show_message("Please select at least one cartella number", "warning")
                # Play sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
            return

        # Check for direct interactions with reset button
        if "reset_all" in self.hit_areas and self.hit_areas["reset_all"].collidepoint(pos):
            # Show reset confirmation dialog
            # Close any board preview first
            self.showing_board_preview = False
            self.preview_cartella_number = None
            self.preview_board = None

            # Show reset confirmation dialog
            self.showing_reset_confirmation = True
            # Play sound if available
            if self.button_click_sound:
                self.button_click_sound.play()
            return

        # Check for direct interactions with cartella up/down buttons
        if "cartella_up" in self.hit_areas and self.hit_areas["cartella_up"].collidepoint(pos):
            # Increase cartella number by 1
            new_number = self.cartella_number + 1
            if self.validate_cartella_number(new_number)[0]:
                self.cartella_number = new_number
                self.input_text = str(new_number)
                self.create_board_from_number(new_number)
                # Play sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
            return

        if "cartella_down" in self.hit_areas and self.hit_areas["cartella_down"].collidepoint(pos):
            # Decrease cartella number by 1
            new_number = self.cartella_number - 1
            if self.validate_cartella_number(new_number)[0]:
                self.cartella_number = new_number
                self.input_text = str(new_number)
                self.create_board_from_number(new_number)
                # Play sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
            return

        # Reset confirmation dialog buttons are now handled at the beginning of this method

        # Navigation menu is handled earlier in this method - this section is redundant
        # Removed duplicate navigation handling to prevent conflicts

        # If no direct interaction was handled, fall back to the existing button click handler
        self.check_button_click(pos, ctrl_pressed)

    def check_button_click(self, pos, ctrl_pressed=False):
        """Handle button click events based on cursor position"""
        # Make a copy of the hit areas dictionary to avoid modification during iteration
        hit_areas_copy = dict(self.hit_areas.items())

        # Check if we clicked on any of our hit areas
        for key, area in hit_areas_copy.items():
            if area.collidepoint(pos):
                # Play button click sound if available, but not for cartella numbers
                # since they now have their own announcement sound
                if self.button_click_sound and not key.startswith("lucky_number_"):
                    self.button_click_sound.play()

                # Navigation menu buttons are now handled in handle_direct_interaction method
                # This prevents duplicate handling and ensures proper navigation functionality
                if key.startswith("nav_"):
                    # Skip navigation handling here as it's already handled in handle_direct_interaction
                    return

                # Handle specific button actions
                if key == "next_button":
                    if len(self.selected_cartella_numbers) > 0:
                        self.hit_areas["continue_clicked"] = True
                    else:
                        self.show_message("Please select at least one cartella number", "warning")

                elif key == "continue":
                    if len(self.selected_cartella_numbers) > 0:
                        self.hit_areas["continue_clicked"] = True
                    else:
                        self.show_message("Please select at least one cartella number", "warning")

                elif key == "cartella_input":
                    self.input_active = True
                    self.bet_input_active = False

                elif key == "bet_input":
                    self.bet_input_active = True
                    self.input_active = False

                elif key == "set_button":
                    # Apply the bet input value - this will update all players and recalculate the prize pool
                    self._apply_bet_input()

                elif key == "reset_all":
                    # Show reset confirmation dialog
                    # Close any board preview first
                    self.showing_board_preview = False
                    self.preview_cartella_number = None
                    self.preview_board = None

                    # Show reset confirmation dialog
                    self.showing_reset_confirmation = True
                    print("Showing reset confirmation dialog")

                elif key == "next_grid_page":
                    # Move to the next page of numbers
                    max_num = 1200  # Increased from 400 to 1200
                    nums_per_page = 75
                    max_pages = (max_num // nums_per_page) + (
                        1 if max_num % nums_per_page > 0 else 0
                    )

                    # Increment page and wrap around if needed
                    self.grid_page = (self.grid_page + 1) % max_pages

                    # Show message about the current page
                    page_msg = f"Showing numbers {self.grid_page * nums_per_page + 1}-"
                    if self.grid_page == max_pages - 1:  # Last page
                        page_msg += str(max_num)
                    else:
                        page_msg += str((self.grid_page + 1) * nums_per_page)

                    self.show_message(page_msg, "info")

                elif key == "prev_grid_page":
                    # Move to the previous page of numbers
                    max_num = 1200  # Increased from 400 to 1200
                    nums_per_page = 75
                    max_pages = (max_num // nums_per_page) + (
                        1 if max_num % nums_per_page > 0 else 0
                    )

                    # Decrement page and wrap around if needed
                    self.grid_page = (self.grid_page - 1) % max_pages

                    # Show message about the current page
                    page_msg = f"Showing numbers {self.grid_page * nums_per_page + 1}-"
                    if self.grid_page == max_pages - 1:  # Last page
                        page_msg += str(max_num)
                    else:
                        page_msg += str((self.grid_page + 1) * nums_per_page)

                    self.show_message(page_msg, "info")

                # Settings button
                elif key == "settings_button":
                    # Show a simple message that settings will be available after game setup
                    self.show_message("Settings will be available after completing game setup", "info")

                # Reset confirmation dialog buttons are now handled in handle_direct_interaction

                elif key == "cartella_up":
                    # Increase cartella number by 1
                    new_number = self.cartella_number + 1
                    if self.validate_cartella_number(new_number):
                        self.cartella_number = new_number
                        self.input_text = str(new_number)
                        self.create_board_from_number(new_number)

                elif key == "cartella_down":
                    # Decrease cartella number by 1
                    new_number = self.cartella_number - 1
                    if self.validate_cartella_number(new_number):
                        self.cartella_number = new_number
                        self.input_text = str(new_number)
                        self.create_board_from_number(new_number)

                elif key == "add_player" or key == "add_button":
                    # If input is active, apply the input value first
                    if self.input_active:
                        is_valid, error_msg = self.validate_cartella_number(self.input_text)
                        if is_valid:
                            self.cartella_number = int(self.input_text)
                            self.create_board_from_number(self.cartella_number)
                            self.input_active = False
                        else:
                            self.show_message(error_msg, "error")
                            return

                    # Check if cartella is already selected
                    if self.cartella_number in self.selected_cartella_numbers:
                        # Remove the cartella
                        self.selected_cartella_numbers.remove(self.cartella_number)

                        # Remove from called_numbers for highlighting
                        if self.cartella_number in self.called_numbers:
                            self.called_numbers.remove(self.cartella_number)

                        # Remove from players list
                        for i, player in enumerate(self.players):
                            if player.cartela_no == self.cartella_number:
                                self.players.pop(i)
                                break

                        # Save updated players to JSON
                        save_players_to_json(self.players)

                        # Update prize pool
                        self.calculate_prize_pool()

                        # Show success message
                        self.show_message(
                            f"Removed player with cartella #{self.cartella_number}", "success"
                        )
                    else:
                        # Add current cartella to the selected list
                        self.add_player()

                elif key.startswith("lucky_number_"):
                    # Extract number from key (format: "lucky_number_XX")
                    try:
                        number = int(key.split("_")[-1])
                        if number >= 1 and number <= 1200:  # Ensure it's a valid grid number (increased from 400 to 1200)
                            if ctrl_pressed and not self.showing_reset_confirmation:
                                # Show board preview for this cartella number only if reset confirmation is not active
                                self.showing_board_preview = True
                                self.preview_cartella_number = number
                                self.preview_board = self.get_board_for_number(number)
                                print(f"Showing board preview for cartella {number}")
                            else:
                                # Check if number is already selected
                                if number in self.called_numbers:
                                    # Remove the number from highlighting and selection
                                    self.called_numbers.remove(number)

                                    # Remove from selected cartella numbers if it exists
                                    if number in self.selected_cartella_numbers:
                                        self.selected_cartella_numbers.remove(number)

                                    # Remove from players list
                                    for i, player in enumerate(self.players):
                                        if player.cartela_no == number:
                                            self.players.pop(i)
                                            break

                                    # Save updated players to JSON
                                    save_players_to_json(self.players)

                                    # Save UI state if option is enabled
                                    if self.remember_cartella_checkbox:
                                        try:
                                            from player_storage import save_ui_state, load_ui_state
                                            save_ui_state(self.selected_cartella_numbers, self.prize_pool)
                                            print(f"Updated UI state after removal: {len(self.selected_cartella_numbers)} numbers")
                                        except Exception as e:
                                            print(f"Error saving UI state after removal: {e}")

                                    # Update prize pool
                                    self.calculate_prize_pool()

                                    # Play cartella cancellation announcement
                                    self.play_cartella_cancellation(number)

                                    # Show success message
                                    self.show_message(
                                        f"Removed player with cartella #{number}", "success"
                                    )
                                else:
                                    # Update current cartella number to clicked number
                                    self.cartella_number = number
                                    self.input_text = str(number)
                                    self.create_board_from_number(number)

                                    # Add this number to called_numbers for highlighting
                                    if number not in self.called_numbers:
                                        self.called_numbers.append(number)

                                    # Add player with this cartella number
                                    # Note: add_player will play the cartella announcement
                                    self.add_player()
                    except:
                        pass
    def play_cartella_announcement(self, cartella_number):
        """Play audio announcement for cartella number registration"""
        # Check if cartella announcements are enabled in settings
        # The settings_manager should already be initialized in __init__
        announcements_enabled = self.settings_manager.get_setting('audio', 'cartella_announcements_enabled', True)
        if not announcements_enabled:
            print("Cartella announcements are disabled in settings")
            return False

        try:
            # Construct the path to the audio file
            audio_path = f"assets/cartella-announcer/{cartella_number} Registration Amharic.mp3"

            # Check if the file exists
            if not os.path.exists(audio_path):
                print(f"Cartella announcement audio file not found: {audio_path}")
                return False

            # Load and play the announcement
            announcement = pygame.mixer.Sound(audio_path)
            announcement.play()
            return True
        except Exception as e:
            print(f"Error playing cartella announcement: {e}")
            return False

    def play_cartella_cancellation(self, cartella_number):
        """Play audio announcement for cartella number cancellation"""
        # Check if cartella announcements are enabled in settings
        # The settings_manager should already be initialized in __init__
        announcements_enabled = self.settings_manager.get_setting('audio', 'cartella_announcements_enabled', True)
        if not announcements_enabled:
            print("Cartella announcements are disabled in settings")
            return False

        try:
            # Construct the path to the audio file
            audio_path = f"assets/cartella-cancellation-announcements/{cartella_number} Cancellation Amharic.mp3"

            # Check if the file exists
            if not os.path.exists(audio_path):
                print(f"Cartella cancellation audio file not found: {audio_path}")
                return False

            # Load and play the announcement
            announcement = pygame.mixer.Sound(audio_path)
            announcement.play()
            return True
        except Exception as e:
            print(f"Error playing cartella cancellation announcement: {e}")
            return False

    def add_player(self):
        """Add player with current cartella number"""
        # Validate cartella number
        is_valid, error_msg = self.validate_cartella_number(str(self.cartella_number))
        if not is_valid:
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180
            return

        # Add cartella number to selected list if not already there
        if self.cartella_number not in self.selected_cartella_numbers:
            self.selected_cartella_numbers.append(self.cartella_number)

            # Add the number to called_numbers list for highlighting in the grid
            if self.cartella_number not in self.called_numbers:
                self.called_numbers.append(self.cartella_number)

            # Create a new player
            player = Player(cartela_no=self.cartella_number, bet_amount=self.bet_amount)

            # Add to players list
            self.players.append(player)

            # Save players to JSON
            save_players_to_json(self.players)

            # Save UI state if option is enabled
            if self.remember_cartella_checkbox:
                try:
                    from player_storage import save_ui_state, load_ui_state
                    save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, self.prize_pool_manual_override)
                    print(f"Updated UI state after addition: {len(self.selected_cartella_numbers)} numbers")
                except Exception as e:
                    print(f"Error saving UI state after addition: {e}")

            # Update prize pool
            self.calculate_prize_pool()

            # Play cartella announcement
            self.play_cartella_announcement(self.cartella_number)

            # Show success message
            self.message = f"Added player with cartella #{self.cartella_number}"
            self.message_type = "success"
            self.message_timer = 180
        else:
            # Show error message
            self.message = f"Cartella #{self.cartella_number} already selected"
            self.message_type = "error"
            self.message_timer = 180

    def calculate_prize_pool(self):
        """Calculate prize pool based on players and bet amount"""
        if getattr(self, 'prize_pool_manual_override', False):
            # If manual override is set, do not recalculate
            print(f"Prize pool manual override active: {self.prize_pool}")
            return
        if not self.players:
            self.prize_pool = 0
            return
        total_bets = self.bet_amount * len(self.players)
        if hasattr(self, 'settings_window') and hasattr(self.settings_window, 'settings_manager'):
            settings_manager = self.settings_window.settings_manager
        else:
            from settings_manager import SettingsManager
            settings_manager = SettingsManager()
        commission_percentage = settings_manager.get_setting('game', 'commission_percentage', 20.0)
        self.commission_percentage = commission_percentage
        print(f"Board selection: Using commission percentage: {commission_percentage}%")
        print(f"Board selection: Total bets: {total_bets} ETB")
        commission_factor = (100.0 - commission_percentage) / 100.0
        self.prize_pool = round(total_bets * commission_factor)
        print(f"Board selection: Calculated prize pool: {self.prize_pool} ETB")

    def validate_cartella_number(self, number):
        """Validate cartella number input"""
        try:
            num = int(number)
            if num < 1 or num > 1200:  # Increased upper limit from 100 to 1200
                return False, "Cartella number must be between 1 and 1200"
            return True, ""
        except ValueError:
            return False, "Cartella number must be a valid integer"

    def navigate_to_number_page(self, number):
        """Navigate to the page containing the specified number"""
        # Constants for pagination
        max_num = 1200
        nums_per_page = 75
        max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)

        # Calculate which page the number should be on
        target_page = (number - 1) // nums_per_page

        # Only change page if needed
        if target_page != self.grid_page:
            # Set the grid page to show the number
            self.grid_page = target_page

            # Show message about the current page
            page_start = (self.grid_page * nums_per_page) + 1
            page_end = min((self.grid_page + 1) * nums_per_page, max_num)
            self.show_message(f"Showing numbers {page_start}-{page_end}", "info")

            return True
        return False

    def handle_input(self, event):
        """Handle keyboard input events"""
        if not self.input_active and not self.bet_input_active:
            return False

        if event.type != pygame.KEYDOWN:
            return False

        # Flag to track if we should play a sound
        play_sound = False

        if event.key == pygame.K_ESCAPE:
            # Cancel input
            if self.input_active:
                self.input_active = False
                self.input_text = str(self.cartella_number)
                play_sound = True
            elif self.bet_input_active:
                self.bet_input_active = False
                self.bet_input_text = str(self.bet_amount)
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
            # Confirm input
            if self.input_active:
                self.input_active = False
                is_valid, error_msg = self.validate_cartella_number(self.input_text)
                if is_valid:
                    self.cartella_number = int(self.input_text)

                    # Navigate to the correct page to show the entered number
                    self.navigate_to_number_page(self.cartella_number)

                    # Update bingo board based on new cartella number
                    self.create_board_from_number(self.cartella_number)

                    # Enhanced functionality: Register the player directly when Enter is pressed
                    self.add_player()

                    # Clear the input field to prepare for the next number
                    self.input_text = ""

                    # Keep the input field active for the next entry
                    self.input_active = True

                    play_sound = True
                else:
                    # Show error message
                    self.message = error_msg
                    self.message_type = "error"
                    self.message_timer = 180
                    play_sound = True
            elif self.bet_input_active:
                # Apply the bet input value - this will update all players and recalculate the prize pool
                self._apply_bet_input()
                self.bet_input_active = False
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        if event.key == pygame.K_BACKSPACE:
            # Remove last character
            if self.input_active and self.input_text:
                self.input_text = self.input_text[:-1]
                play_sound = True
            elif self.bet_input_active and self.bet_input_text:
                self.bet_input_text = self.bet_input_text[:-1]
                play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        # Only allow numeric input
        if event.unicode.isdigit():
            if self.input_active:
                # Limit input length
                if len(self.input_text) < 4:  # Increased to 4 digits for cartella (1-1200)
                    self.input_text += event.unicode
                    play_sound = True

                    # Try to navigate to the correct page as the user types
                    # Only do this if we have a valid number
                    try:
                        number = int(self.input_text)
                        if self.validate_cartella_number(str(number))[0]:
                            # Update the cartella number
                            self.cartella_number = number
                            # Navigate to the correct page
                            self.navigate_to_number_page(number)
                            # Update the board preview
                            self.create_board_from_number(number)
                    except ValueError:
                        # Not a valid number yet, just continue
                        pass
            elif self.bet_input_active:
                # Limit input length
                if len(self.bet_input_text) < 4:  # Max 4 digits for bet (up to 9999)
                    self.bet_input_text += event.unicode
                    play_sound = True

            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True

        return False

    def _apply_bet_input(self):
        """Apply the bet input value and update all existing players"""
        try:
            value = int(self.bet_input_text)
            if value < 10:
                value = 10  # Minimum bet amount
            elif value > 1000:
                value = 1000  # Maximum bet amount
            # Only proceed if the value has actually changed
            if value != self.bet_amount:
                old_bet_amount = self.bet_amount
                self.bet_amount = value
                self.bet_input_text = str(value)
                # Update bet amount for all existing players
                for player in self.players:
                    player.bet_amount = value
                # Save updated players to JSON
                save_players_to_json(self.players)
                # Recalculate prize pool with new bet amounts
                # If manual override is active, clear it (user is changing bet, not prize pool directly)
                self.prize_pool_manual_override = False
                self.calculate_prize_pool()

                # Save UI state if option is enabled
                if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
                    try:
                        from player_storage import save_ui_state
                        save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, False)
                        print(f"Updated UI state after changing bet: {self.bet_amount}")
                    except Exception as e:
                        print(f"Error saving UI state after changing bet: {e}")
                self.show_message(f"Bet amount updated from {old_bet_amount} to {value} ETB", "success")
            else:
                self.bet_input_text = str(value)
        except ValueError:
            self.bet_input_text = str(self.bet_amount)

    def show_message(self, message, message_type):
        """Show a toast message of the specified type (success, warning, error)"""
        self.message = message
        self.message_type = message_type
        self.message_timer = 180  # Show for about 6 seconds at 30 FPS

    def import_boards_from_json(self):
        """Import bingo boards from a JSON file selected by the user"""
        try:
            import json
            import tkinter as tk
            from tkinter import filedialog
            import os

            # Create a root window but hide it
            root = tk.Tk()
            root.withdraw()

            # Show file dialog to select a JSON file
            file_path = filedialog.askopenfilename(
                title="Select Bingo Boards JSON File",
                filetypes=[("JSON files", "*.json")],
                initialdir=os.path.join(os.getcwd(), "data")
            )

            # If user cancels, return without doing anything
            if not file_path:
                return

            # Load the JSON file
            with open(file_path, "r") as file:
                new_boards = json.load(file)

            # Validate the JSON structure
            if not isinstance(new_boards, dict):
                self.show_message("Invalid JSON format: must be a dictionary", "error")
                return

            # Check if there are any boards in the file
            if len(new_boards) == 0:
                self.show_message("No boards found in the JSON file", "error")
                return

            # Update the bingo boards
            self.bingo_boards = new_boards

            # Extract the preset name from the file name (without extension)
            preset_name = os.path.splitext(os.path.basename(file_path))[0]
            self.current_preset_name = preset_name

            # Show success message
            self.show_message(f"Imported {len(new_boards)} boards from '{preset_name}'", "success")

            # If the current cartella number exists in the new boards, update the board
            current_number_key = str(self.cartella_number)
            if current_number_key in self.bingo_boards:
                self.create_board_from_number(self.cartella_number)

        except Exception as e:
            self.show_message(f"Error importing boards: {str(e)}", "error")
            print(f"Error importing boards: {str(e)}")

    def draw_toast_message(self):
        """Draw a toast message in the right bottom (footer) area"""
        screen_width, screen_height = self.screen.get_size()

        # Use balanced scaling for proportional sizing
        balanced_scale = min(self.scale_x, self.scale_y)

        # Responsive font sizing based on screen dimensions
        font_size = int(min(22 * balanced_scale, screen_width * 0.022, screen_height * 0.033))
        font_size = max(font_size, 16)  # Ensure minimum size for readability

        # Check if this is a cartella removal message
        is_removal_message = "Removed player with cartella" in self.message

        # Calculate message box dimensions
        padding = int(min(18 * balanced_scale, screen_width * 0.012, screen_height * 0.018))

        if is_removal_message:
            # Extract cartella number for special styling
            try:
                cartella_number = self.message.split("#")[1]
            except:
                cartella_number = "?"

            # Create two-part message with enhanced styling
            removed_text = "Removed player with cartella "
            number_text = f"#{cartella_number}"

            # Render the first part of the message
            message_font = pygame.font.SysFont("Arial", font_size, bold=True)
            removed_surf = message_font.render(removed_text, True, WHITE)

            # Render the cartella number with larger font and special color
            number_font = pygame.font.SysFont("Arial", int(font_size * 1.2), bold=True)
            number_surf = number_font.render(number_text, True, (255, 220, 100))  # Gold color for number

            # Calculate total width
            total_width = removed_surf.get_width() + number_surf.get_width()
            box_width = total_width + padding * 2
            box_height = max(removed_surf.get_height(), number_surf.get_height()) + padding * 1.2
        else:
            # Standard message rendering
            message_font = pygame.font.SysFont("Arial", font_size, bold=True)
            message_surf = message_font.render(self.message, True, WHITE)
            box_width = message_surf.get_width() + padding * 2
            box_height = message_surf.get_height() + padding * 2

        # Position the message box in the right bottom corner with proper margins
        right_margin = int(min(20 * balanced_scale, screen_width * 0.02))
        bottom_margin = int(min(20 * balanced_scale, screen_height * 0.02))
        box_x = screen_width - box_width - right_margin
        box_y = screen_height - box_height - bottom_margin

        # Draw different backgrounds based on message type
        box_rect = pygame.Rect(box_x, box_y, box_width, box_height)

        if is_removal_message:
            # Special gradient for removal messages - vibrant red to deep crimson
            self.draw_gradient_rect(box_rect, (220, 50, 50), (150, 20, 20), 12)

            # Add a decorative border with animation effect
            border_pulse = (math.sin(pygame.time.get_ticks() * 0.005) + 1) / 2  # Value between 0 and 1
            border_color = (255, 100 + int(155 * border_pulse), 50, 200)  # Pulsing orange-red

            # Draw animated border
            pygame.draw.rect(self.screen, border_color, box_rect,
                           width=max(2, int(3 * balanced_scale)),
                           border_radius=12)
        elif self.message_type == "error":
            self.draw_gradient_rect(box_rect, (180, 50, 50), (120, 30, 30), 8)
        elif self.message_type == "success":
            self.draw_gradient_rect(box_rect, (50, 180, 50), (30, 120, 30), 8)
        else:  # info
            self.draw_gradient_rect(box_rect, (50, 100, 180), (30, 70, 120), 8)

        # Add subtle glow based on message type
        if is_removal_message:
            # Enhanced glow for removal messages
            glow_color = (255, 100, 50, 60)  # Brighter, more visible glow
        else:
            glow_color = {
                "error": (255, 100, 100, 40),
                "success": (100, 255, 100, 40),
                "info": (100, 180, 255, 40)
            }.get(self.message_type, (150, 150, 150, 40))

        # Draw glow effect
        glow_rect = pygame.Rect(
            box_x - padding//2,
            box_y - padding//2,
            box_width + padding,
            box_height + padding
        )
        pygame.draw.rect(self.screen, glow_color, glow_rect, border_radius=12)

        if is_removal_message:
            # Position the two-part message
            removed_x = box_x + padding
            number_x = removed_x + removed_surf.get_width()
            text_y = box_y + (box_height - removed_surf.get_height()) // 2

            # Draw text with enhanced shadow for better readability
            shadow_offset = max(1, int(2 * balanced_scale))

            # Draw shadows
            removed_shadow = message_font.render(removed_text, True, (20, 0, 0))
            number_shadow = number_font.render(number_text, True, (20, 0, 0))

            self.screen.blit(removed_shadow, (removed_x + shadow_offset, text_y + shadow_offset))
            self.screen.blit(number_shadow, (number_x + shadow_offset, text_y + shadow_offset - 2))  # Slight offset for number

            # Draw main text
            self.screen.blit(removed_surf, (removed_x, text_y))
            self.screen.blit(number_surf, (number_x, text_y - 2))  # Slight offset for number

            # Add a small icon or indicator
            icon_size = int(24 * balanced_scale)
            icon_x = box_x + box_width - icon_size - int(padding * 0.5)
            icon_y = box_y + (box_height - icon_size) // 2

            # Draw a small X icon
            line_width = max(2, int(3 * balanced_scale))
            pygame.draw.line(self.screen, (255, 200, 100),
                           (icon_x, icon_y),
                           (icon_x + icon_size, icon_y + icon_size),
                           line_width)
            pygame.draw.line(self.screen, (255, 200, 100),
                           (icon_x, icon_y + icon_size),
                           (icon_x + icon_size, icon_y),
                           line_width)
        else:
            # Standard message positioning
            text_x = box_x + (box_width - message_surf.get_width()) // 2
            text_y = box_y + (box_height - message_surf.get_height()) // 2

            # Draw text with slight shadow for better readability
            shadow_offset = max(1, int(1.5 * balanced_scale))  # Reduced shadow for cleaner look
            shadow_surf = message_font.render(self.message, True, (0, 0, 0))
            self.screen.blit(shadow_surf, (text_x + shadow_offset, text_y + shadow_offset))
            self.screen.blit(message_surf, (text_x, text_y))

    def draw_reset_confirmation(self):
        """Draw the reset confirmation dialog"""
        if not self.showing_reset_confirmation:
            return

        screen_width, screen_height = self.screen.get_size()

        # Use balanced scaling for proportional sizing
        balanced_scale = min(self.scale_x, self.scale_y)

        # Draw semi-transparent overlay with enhanced opacity
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, self.reset_popup_opacity))  # Black with custom opacity
        self.screen.blit(overlay, (0, 0))

        # Create confirmation box
        box_width = int(screen_width * 0.4)
        box_height = int(screen_height * 0.35)  # Increase height for more text
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # Draw box with gradient
        box_rect = pygame.Rect(box_x, box_y, box_width, box_height)
        self.draw_gradient_rect(box_rect, (150, 50, 50), (170, 70, 70), 15)

        # Title text
        title_font_size = int(30 * balanced_scale)
        title_font_size = max(title_font_size, 20)  # Ensure minimum size for readability
        title_font = pygame.font.SysFont("Arial", title_font_size, bold=True)
        title_text = title_font.render("Reset Selected Cartellas?", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=box_x + box_width // 2, y=box_y + 20)
        self.screen.blit(title_text, title_rect)

        # Warning icon (exclamation mark)
        icon_size = int(40 * balanced_scale)
        icon_x = box_x + (box_width // 2) - (icon_size // 2)
        icon_y = box_y + 60

        # Draw warning triangle
        warning_color = (255, 220, 50)  # Yellow
        pygame.draw.polygon(self.screen, warning_color, [
            (icon_x + icon_size//2, icon_y),
            (icon_x, icon_y + icon_size),
            (icon_x + icon_size, icon_y + icon_size)
        ])

        # Draw exclamation mark
        pygame.draw.rect(self.screen, (0, 0, 0),
                       (icon_x + icon_size//2 - 3, icon_y + 10,
                        6, icon_size//2), 0)
        pygame.draw.rect(self.screen, (0, 0, 0),
                       (icon_x + icon_size//2 - 3, icon_y + icon_size//2 + 15,
                        6, 6), 0)

        # Confirmation text - multiple lines with warning about data
        confirm_font_size = int(18 * balanced_scale)
        confirm_font_size = max(confirm_font_size, 14)  # Ensure minimum size for readability
        confirm_font = pygame.font.SysFont("Arial", confirm_font_size)
        warning_text = [
            "Are you sure you want to reset all selected cartella numbers?",
            "This will remove all players and reset the prize pool.",
            "This action cannot be undone!"
        ]

        # Draw each line of warning text
        text_y = icon_y + icon_size + 20
        for line in warning_text:
            text_surf = confirm_font.render(line, True, (255, 255, 255))
            text_rect = text_surf.get_rect(centerx=box_x + box_width // 2, y=text_y)
            self.screen.blit(text_surf, text_rect)
            text_y += text_surf.get_height() + 5

        # Calculate button dimensions and positions
        button_width = int(box_width * 0.35)
        button_height = int(40 * balanced_scale)
        button_spacing = int(20 * balanced_scale)
        buttons_y = box_y + box_height - button_height - 20

        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Confirm button
        confirm_button_rect = pygame.Rect(
            box_x + (box_width // 2) - button_width - (button_spacing // 2),
            buttons_y,
            button_width,
            button_height
        )

        # Check if mouse is hovering over confirm button
        confirm_hover = confirm_button_rect.collidepoint(mouse_pos)

        # Draw confirm button with enhanced interactive effects
        if confirm_hover:
            # Brighter colors for hover state
            self.draw_gradient_rect(
                confirm_button_rect,
                (200, 60, 60),  # Brighter red
                (220, 80, 80),  # Even brighter red
                8  # Border radius
            )

            # Add glow effect when hovering
            for i in range(1, 4):
                glow_rect = pygame.Rect(
                    confirm_button_rect.x - i,
                    confirm_button_rect.y - i,
                    confirm_button_rect.width + i * 2,
                    confirm_button_rect.height + i * 2
                )
                glow_color = (255, 100, 100, 70 - i * 15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8 + i)

            # Add subtle pulsating animation effect when hovering
            pulse = (math.sin(time.time() * 4) + 1) / 8  # Subtle pulse value

            # Draw pulsating border
            pulse_rect = pygame.Rect(
                confirm_button_rect.x - int(2 * pulse * self.scale_x),
                confirm_button_rect.y - int(2 * pulse * self.scale_y),
                confirm_button_rect.width + int(4 * pulse * self.scale_x),
                confirm_button_rect.height + int(4 * pulse * self.scale_y)
            )
            pygame.draw.rect(
                self.screen, (255, 150, 150, int(100 * pulse)), pulse_rect, 2, border_radius=10
            )

            # Change cursor to hand when hovering
            pygame.mouse.set_cursor(pygame.SYSTEM_CURSOR_HAND)
        else:
            # Normal state colors
            self.draw_gradient_rect(
                confirm_button_rect,
                (150, 50, 50),  # Red
                (170, 70, 70),  # Lighter red
                8  # Border radius
            )

        # Draw confirm button text with shadow for depth
        button_font = pygame.font.SysFont("Arial", int(22 * balanced_scale), bold=True)
        confirm_button_text = button_font.render("Confirm", True, (255, 255, 255))

        # Add text shadow
        shadow_offset = int(1.5 * balanced_scale)
        shadow_text = button_font.render("Confirm", True, (50, 0, 0))
        shadow_rect = shadow_text.get_rect(center=(
            confirm_button_rect.centerx + shadow_offset,
            confirm_button_rect.centery + shadow_offset
        ))
        self.screen.blit(shadow_text, shadow_rect)

        # Draw main text
        confirm_button_text_rect = confirm_button_text.get_rect(center=confirm_button_rect.center)
        self.screen.blit(confirm_button_text, confirm_button_text_rect)
        self.hit_areas["reset_confirm"] = confirm_button_rect

        # Cancel button
        cancel_button_rect = pygame.Rect(
            box_x + (box_width // 2) + (button_spacing // 2),
            buttons_y,
            button_width,
            button_height
        )

        # Check if mouse is hovering over cancel button
        cancel_hover = cancel_button_rect.collidepoint(mouse_pos)

        # Reset cursor if not hovering over either button
        if not confirm_hover and not cancel_hover:
            pygame.mouse.set_cursor(pygame.SYSTEM_CURSOR_ARROW)

        # Draw cancel button with enhanced interactive effects
        if cancel_hover:
            # Brighter colors for hover state
            self.draw_gradient_rect(
                cancel_button_rect,
                (100, 100, 120),  # Brighter gray
                (130, 130, 150),  # Even brighter gray
                8  # Border radius
            )

            # Add glow effect when hovering
            for i in range(1, 4):
                glow_rect = pygame.Rect(
                    cancel_button_rect.x - i,
                    cancel_button_rect.y - i,
                    cancel_button_rect.width + i * 2,
                    cancel_button_rect.height + i * 2
                )
                glow_color = (150, 150, 200, 70 - i * 15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8 + i)

            # Add subtle pulsating animation effect when hovering
            pulse = (math.sin(time.time() * 4) + 1) / 8  # Subtle pulse value

            # Draw pulsating border
            pulse_rect = pygame.Rect(
                cancel_button_rect.x - int(2 * pulse * self.scale_x),
                cancel_button_rect.y - int(2 * pulse * self.scale_y),
                cancel_button_rect.width + int(4 * pulse * self.scale_x),
                cancel_button_rect.height + int(4 * pulse * self.scale_y)
            )
            pygame.draw.rect(
                self.screen, (150, 150, 200, int(100 * pulse)), pulse_rect, 2, border_radius=10
            )

            # Change cursor to hand when hovering
            pygame.mouse.set_cursor(pygame.SYSTEM_CURSOR_HAND)
        else:
            # Normal state colors
            self.draw_gradient_rect(
                cancel_button_rect,
                (70, 70, 80),  # Dark gray
                (100, 100, 110),  # Lighter gray
                8  # Border radius
            )

        # Draw cancel button text with shadow for depth
        cancel_button_text = button_font.render("Cancel", True, (255, 255, 255))

        # Add text shadow
        shadow_text = button_font.render("Cancel", True, (30, 30, 40))
        shadow_rect = shadow_text.get_rect(center=(
            cancel_button_rect.centerx + shadow_offset,
            cancel_button_rect.centery + shadow_offset
        ))
        self.screen.blit(shadow_text, shadow_rect)

        # Draw main text
        cancel_button_text_rect = cancel_button_text.get_rect(center=cancel_button_rect.center)
        self.screen.blit(cancel_button_text, cancel_button_text_rect)
        self.hit_areas["reset_cancel"] = cancel_button_rect

    def draw_board_preview(self):
        """Draw the board preview popup"""
        if not self.showing_board_preview:
            return

        # Get board data
        cartella_number = self.preview_cartella_number
        board = self.preview_board

        if not cartella_number or not board:
            return

        screen_width, screen_height = self.screen.get_size()

        # Use balanced scaling for proportional sizing
        balanced_scale = min(self.scale_x, self.scale_y)

        # Cache key for this preview
        cache_key = f"preview_{cartella_number}_{screen_width}_{screen_height}"

        # Initialize caches if they don't exist
        if not hasattr(self, '_overlay_cache'):
            self._overlay_cache = {}
        if not hasattr(self, '_preview_cache'):
            self._preview_cache = {}

        # Create or use cached overlay
        overlay_key = f"overlay_{screen_width}_{screen_height}"
        if overlay_key not in self._overlay_cache:
            # Create a semi-transparent overlay with enhanced opacity
            overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, self.preview_popup_opacity))  # Black with custom opacity
            self._overlay_cache[overlay_key] = overlay

        # Blit the overlay
        self.screen.blit(self._overlay_cache[overlay_key], (0, 0))

        # Create preview box dimensions
        box_width = int(screen_width * 0.5)  # Increased from 0.3 to 0.5
        box_height = int(screen_height * 0.6)  # Increased from 0.4 to 0.6
        box_x = (screen_width - box_width) // 2
        box_y = (screen_height - box_height) // 2

        # Check if we need to create a new preview surface
        if cache_key not in self._preview_cache:
            # Create a new surface for the preview
            preview_surf = pygame.Surface((box_width, box_height), pygame.SRCALPHA)

            # Draw box with gradient
            box_rect = pygame.Rect(0, 0, box_width, box_height)

            # Create a gradient background directly on the preview surface
            for y in range(box_height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / box_height

                # Linear interpolation of colors
                r = int((20) * (1 - t) + (30) * t)
                g = int((40) * (1 - t) + (60) * t)
                b = int((80) * (1 - t) + (100) * t)

                # Draw a line of the gradient
                pygame.draw.line(preview_surf, (r, g, b), (0, y), (box_width, y))

            # Add rounded corners
            mask = pygame.Surface((box_width, box_height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, box_width, box_height), border_radius=15)

            # Apply the mask
            temp_surf = preview_surf.copy()
            preview_surf.fill((0, 0, 0, 0))
            preview_surf.blit(temp_surf, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            preview_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

            # Title text - enlarged for better visibility
            title_font_size = int(36 * balanced_scale)  # Increased from 24 to 36
            title_font_size = max(title_font_size, 28)  # Increased minimum size from 18 to 28
            title_font = pygame.font.SysFont("Arial", title_font_size, bold=True)
            title_text = title_font.render(f"Playing Board #{cartella_number}", True, (255, 255, 255))
            title_rect = title_text.get_rect(centerx=box_width // 2, y=25)  # Adjusted y position
            preview_surf.blit(title_text, title_rect)

            # Draw BINGO header - enlarged for better visibility
            bingo_letters = ["B", "I", "N", "G", "O"]
            bingo_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 165, 0), (255, 255, 0)]
            letter_font_size = int(32 * balanced_scale)  # Increased from 22 to 32
            letter_font_size = max(letter_font_size, 24)  # Increased minimum size from 16 to 24
            letter_font = pygame.font.SysFont("Arial", letter_font_size, bold=True)

            # Calculate board dimensions - adjusted for larger popup
            board_margin = 30  # Increased from 20 to 30
            board_width = box_width - (board_margin * 2)
            board_height = box_height - title_rect.height - board_margin - 50  # Adjusted spacing
            board_x = board_margin
            board_y = title_rect.bottom + 25  # Increased spacing from 15 to 25

            # Calculate cell dimensions
            cell_width = board_width / 5
            cell_height = board_height / 6  # 5 rows + 1 for BINGO header

            # Draw BINGO header
            for i, (letter, color) in enumerate(zip(bingo_letters, bingo_colors)):
                letter_x = board_x + (i * cell_width) + (cell_width / 2)
                letter_y = board_y + (cell_height / 2)
                letter_surf = letter_font.render(letter, True, color)
                letter_rect = letter_surf.get_rect(center=(letter_x, letter_y))
                preview_surf.blit(letter_surf, letter_rect)

            # Draw board grid - enlarged for better visibility
            number_font_size = int(30 * balanced_scale)  # Increased from 20 to 30
            number_font_size = max(number_font_size, 22)  # Increased minimum size from 14 to 22
            number_font = pygame.font.SysFont("Arial", number_font_size, bold=True)

            # Draw cells and numbers
            for row in range(5):
                for col in range(5):
                    # Calculate cell position
                    cell_x = board_x + (col * cell_width)
                    cell_y = board_y + cell_height + (row * cell_height)

                    # Draw cell background
                    cell_rect = pygame.Rect(cell_x, cell_y, cell_width, cell_height)
                    pygame.draw.rect(preview_surf, (30, 60, 90), cell_rect)
                    pygame.draw.rect(preview_surf, (100, 150, 200), cell_rect, 1)

                    # Get number from board data (transposed from column-major to row-major)
                    number = board[col][row]

                    # Draw number or star for free space
                    if number == 0:  # Free space (center)
                        # Draw star
                        star_color = (255, 255, 0)  # Yellow
                        star_size = min(cell_width, cell_height) * 0.4
                        center_x = cell_x + (cell_width / 2)
                        center_y = cell_y + (cell_height / 2)

                        # Draw a simple star
                        points = 5
                        outer_radius = star_size
                        inner_radius = star_size * 0.4
                        star_points = []

                        for i in range(points * 2):
                            angle = math.pi * i / points
                            radius = outer_radius if i % 2 == 0 else inner_radius
                            x = center_x + radius * math.sin(angle)
                            y = center_y + radius * math.cos(angle)
                            star_points.append((x, y))

                        pygame.draw.polygon(preview_surf, star_color, star_points)
                    else:
                        # Draw number
                        num_surf = number_font.render(str(number), True, (255, 255, 255))
                        num_rect = num_surf.get_rect(center=(cell_x + cell_width/2, cell_y + cell_height/2))
                        preview_surf.blit(num_surf, num_rect)

            # Draw instruction text - enlarged for better visibility
            instruction_font = pygame.font.SysFont("Arial", int(20 * balanced_scale))  # Increased from 14 to 20
            instruction_text = instruction_font.render("Release Ctrl key to close", True, (220, 220, 220))  # Brighter color
            instruction_rect = instruction_text.get_rect(centerx=box_width // 2, bottom=box_height - 15)
            preview_surf.blit(instruction_text, instruction_rect)

            # Cache the preview surface
            self._preview_cache[cache_key] = preview_surf

            # Limit cache size to prevent memory issues
            if len(self._preview_cache) > 20:  # Keep only 20 most recent previews
                # Remove oldest cache entry
                oldest_key = next(iter(self._preview_cache))
                del self._preview_cache[oldest_key]

        # Blit the cached preview surface
        self.screen.blit(self._preview_cache[cache_key], (box_x, box_y))

    def _wrap_text(self, text, font, max_width):
        """Helper method to wrap text to fit within a given width"""
        words = text.split(" ")
        lines = []
        current_line = []

        for word in words:
            # Try adding this word to the current line
            test_line = " ".join(current_line + [word])
            test_width = font.size(test_line)[0]

            if test_width <= max_width:
                # Word fits, add it to the current line
                current_line.append(word)
            else:
                # Word doesn't fit, start a new line
                if current_line:  # Only append if there are words in the line
                    lines.append(" ".join(current_line))
                current_line = [word]

        # Add the last line
        if current_line:
            lines.append(" ".join(current_line))

        return lines

    # Methods to be implemented from main.py (copied/modified)
    def draw_gradient_rect(self, rect, color1, color2, border_radius=0, shadow=True):
        """Draw a rectangle with a vertical gradient"""
        # Ensure color1 and color2 are valid RGB or RGBA tuples
        if not isinstance(color1, tuple) or len(color1) < 3:
            color1 = (0, 0, 0)  # Default to black if invalid
        if not isinstance(color2, tuple) or len(color2) < 3:
            color2 = (0, 0, 0)  # Default to black if invalid

        # Create a surface with alpha channel
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Draw a filled rounded rect on the surface
        if border_radius > 0:
            # Draw gradient by blending two colors over height
            for y in range(rect.height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / rect.height

                # Linear interpolation of colors
                r = int(color1[0] * (1 - t) + color2[0] * t)
                g = int(color1[1] * (1 - t) + color2[1] * t)
                b = int(color1[2] * (1 - t) + color2[2] * t)

                # Handle alpha if present
                a = 255
                if len(color1) > 3 and len(color2) > 3:
                    a = int(color1[3] * (1 - t) + color2[3] * t)
                elif len(color1) > 3:
                    a = int(color1[3] * (1 - t) + 255 * t)
                elif len(color2) > 3:
                    a = int(255 * (1 - t) + color2[3] * t)

                # Draw a line of the gradient
                pygame.draw.line(surf, (r, g, b, a), (0, y), (rect.width, y))

            # Apply rounded corners by drawing a rounded rect "mask"
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(
                mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius
            )

            # Combine surfaces
            final_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            final_surf.blit(surf, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

            # Apply mask
            final_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)

            # Apply a shadow effect for depth if requested
            if shadow:
                shadow_offset = int(3 * self.scale_x)  # Increased shadow offset
                shadow_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
                pygame.draw.rect(
                    shadow_surf,
                    (0, 0, 0, 100),
                    pygame.Rect(
                        shadow_offset,
                        shadow_offset,
                        rect.width - shadow_offset * 2,
                        rect.height - shadow_offset * 2,
                    ),
                    border_radius=border_radius,
                )
                self.screen.blit(shadow_surf, (rect.x, rect.y))

            self.screen.blit(final_surf, rect)
        else:
            # Without rounded corners, draw gradient directly
            for y in range(rect.height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / max(1, rect.height - 1)

                # Linear interpolation of colors
                r = int(color1[0] * (1 - t) + color2[0] * t)
                g = int(color1[1] * (1 - t) + color2[1] * t)
                b = int(color1[2] * (1 - t) + color2[2] * t)

                # Handle alpha if present
                a = 255
                if len(color1) > 3 and len(color2) > 3:
                    a = int(color1[3] * (1 - t) + color2[3] * t)
                elif len(color1) > 3:
                    a = int(color1[3] * (1 - t) + 255 * t)
                elif len(color2) > 3:
                    a = int(255 * (1 - t) + color2[3] * t)

                # Draw a line of the gradient
                pygame.draw.line(surf, (r, g, b, a), (0, y), (rect.width, y))

            # Apply a shadow effect for depth if requested
            if shadow:
                shadow_offset = int(3 * self.scale_x)  # Increased shadow offset
                shadow_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
                pygame.draw.rect(
                    shadow_surf, (0, 0, 0, 100), pygame.Rect(0, 0, rect.width, rect.height)
                )
                self.screen.blit(shadow_surf, (rect.x + shadow_offset, rect.y + shadow_offset))

            self.screen.blit(surf, rect)

    def draw_lucky_numbers(self, x_position, section_width, section_height):
        """Draw the Lucky Numbers section at the specified position with the given dimensions"""
        # Position header correctly using passed coordinates and height
        header_rect = pygame.Rect(x_position, int(40 * self.scale_y), section_width, section_height)
        self.draw_gradient_rect(header_rect, DARK_BLUE, LIGHT_BLUE, 10)

        # Calculate responsive font sizes based on available space
        header_size = max(int(28 * min(self.scale_x, self.scale_y)), int(section_width * 0.03))
        header_size = min(header_size, int(section_height * 0.06))  # Prevent too large fonts

        # Draw "LUCKY NUMBERS" text
        header_font = pygame.font.SysFont("Arial", header_size, bold=True)
        lucky_surf = header_font.render("CARTELLA NUMBERS", True, WHITE)
        lucky_x = header_rect.x + int(20 * self.scale_x)
        lucky_y = int(44 * self.scale_y)  # Moved up slightly
        self.screen.blit(lucky_surf, (lucky_x, lucky_y))

        # Draw total callout with orange color
        total_surf = header_font.render(f"#Total players:{len(self.called_numbers)}", True, ORANGE)
        total_x = header_rect.right - total_surf.get_width() - int(20 * self.scale_x)
        total_y = int(44 * self.scale_y)  # Moved up slightly
        self.screen.blit(total_surf, (total_x, total_y))

        # Enhanced BINGO letter colors with more vibrant and distinct hues
        bingo_letters = "BINGO"
        row_colors = {
            "B": (255, 50, 50),  # Vibrant red for B
            "I": (40, 230, 120),  # Bright emerald green for I
            "N": (50, 130, 255),  # Bright royal blue for N
            "G": (255, 50, 50),  # Same red for G (matching B)
            "O": (255, 200, 40),  # Rich gold/yellow for O
        }

        # Calculate responsive row spacing based on available height - make more compact
        content_start_y = (
            lucky_y + lucky_surf.get_height() + int(6 * self.scale_y)
        )  # Reduced padding
        available_height = (
            header_rect.bottom - content_start_y - int(8 * self.scale_y)
        )  # Reduced bottom padding
        row_height = available_height / 5  # 5 rows for B, I, N, G, O

        # Create responsive font sizes for letters and numbers with enhanced visibility
        letter_size = max(
            int(60 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.045)
        )
        letter_size = min(letter_size, int(row_height * 0.75))  # Allow slightly larger fonts for better visibility
        # Use a more visually appealing font for BINGO letters
        try:
            # Try to use a more stylized font if available
            bingo_letter_font = pygame.font.SysFont("Impact", letter_size)
        except:
            # Fall back to Arial Black if Impact is not available
            bingo_letter_font = pygame.font.SysFont("Arial Black", letter_size, bold=True)

        # DOUBLED base number size (from 28 to 56)
        number_size = max(
            int(56 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.04)
        )
        number_size = min(
            number_size, int(row_height * 0.8)
        )  # Increased max size to prevent capping
        number_font = pygame.font.SysFont("Arial", number_size, bold=True)

        # Calculate pulsating effect for animations
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1
        fast_pulse = (math.sin(time.time() * 6) + 1) / 2  # Faster pulsing for current number

        # Draw each row of the bingo board
        for row_idx, letter in enumerate(bingo_letters):
            row_color = row_colors[letter]

            # Use more compact layout with letters on left and numbers spread across width
            letter_x = header_rect.x + int(20 * self.scale_x)  # Moved closer to left edge
            letter_y = content_start_y + row_idx * row_height

            # Draw cleaner horizontal lines (reduced count for cleaner look)
            line_count = 2  # Reduced line count
            line_length = int(10 * self.scale_x)  # Shorter lines
            line_thickness = max(1, int(2 * min(self.scale_x, self.scale_y)))
            line_spacing = int(5 * self.scale_y)  # Reduced spacing
            line_start_y = letter_y + int(28 * self.scale_y)

            for i in range(line_count):
                line_y_pos = line_start_y + i * line_spacing
                pygame.draw.line(
                    self.screen,
                    row_color,
                               (letter_x - line_length - int(4 * self.scale_x), line_y_pos),
                               (letter_x - int(4 * self.scale_x), line_y_pos),
                    line_thickness,
                )

            # Draw the BINGO letter with enhanced 3D effect for better visibility
            letter_surf = bingo_letter_font.render(letter, True, row_color)

            # Create a subtle glow effect behind the letter
            glow_size = int(4 * min(self.scale_x, self.scale_y))
            glow_surf = pygame.Surface((letter_surf.get_width() + glow_size*2, letter_surf.get_height() + glow_size*2), pygame.SRCALPHA)

            # Draw the glow with the letter color but semi-transparent
            glow_color = (row_color[0], row_color[1], row_color[2], 100)
            pygame.draw.ellipse(glow_surf, glow_color,
                              (0, 0, letter_surf.get_width() + glow_size*2, letter_surf.get_height() + glow_size*2))

            # Position and draw the glow
            glow_pos = (letter_x - glow_size, letter_y - glow_size)
            self.screen.blit(glow_surf, glow_pos)

            # Add multiple shadow layers for enhanced 3D effect
            for offset in range(1, 4):
                # Create darker shadow for deeper layers
                shadow_color = (max(0, row_color[0] // 3), max(0, row_color[1] // 3), max(0, row_color[2] // 3))
                shadow_surf = bingo_letter_font.render(letter, True, shadow_color)
                shadow_pos = (letter_x + offset * self.scale_x, letter_y + offset * self.scale_y)
                self.screen.blit(shadow_surf, shadow_pos)

            # Add a subtle white highlight for more depth
            highlight_offset = int(1 * self.scale_x)
            highlight_surf = bingo_letter_font.render(letter, True, (255, 255, 255, 150))
            highlight_pos = (letter_x - highlight_offset, letter_y - highlight_offset)
            self.screen.blit(highlight_surf, highlight_pos)

            # Draw main letter on top
            self.screen.blit(letter_surf, (letter_x, letter_y))

            # Calculate responsive row background width - use more of the available width
            letter_width = letter_surf.get_width()
            number_area_width = (
                section_width - letter_width - int(50 * self.scale_x)
            )  # More width for numbers
            row_bg_width = min(
                number_area_width, header_rect.right - letter_x - int(40 * self.scale_x)
            )

            # Make row background taller to fit larger numbers with enhanced visual appeal
            row_bg_rect = pygame.Rect(
                letter_x + int(45 * self.scale_x),  # Reduced gap between letter and numbers
                letter_y + int(6 * self.scale_y),  # Moved closer to letter
                row_bg_width,
                min(
                    int(54 * self.scale_y), int(row_height * 0.85)
                ),  # Taller row background for larger numbers
            )

            # Create a gradient background for better visual appeal
            # Use a slightly darker shade of the row color for the gradient
            dark_color = (max(10, row_color[0]//4), max(10, row_color[1]//4), max(10, row_color[2]//4))

            # Draw a subtle gradient background
            self.draw_gradient_rect(
                row_bg_rect,
                (15, 15, 20),  # Slightly lighter than pure black for better contrast
                dark_color,     # Subtle hint of the row color
                border_radius=int(20 * self.scale_y)
            )

            # Add a subtle border with the row color for better definition
            pygame.draw.rect(
                self.screen,
                (row_color[0]//2, row_color[1]//2, row_color[2]//2),
                row_bg_rect,
                width=max(1, int(1.5 * min(self.scale_x, self.scale_y))),
                border_radius=int(20 * self.scale_y)
            )

            # Calculate number sizes and spacing based on available width
            available_width = row_bg_width
            # Increased circle size to accommodate larger numbers
            circle_radius = min(
                int(22 * min(self.scale_x, self.scale_y)), int(available_width / 42)
            )

            # Each row will display two sets of numbers for a total of 20 numbers per row
            # For example: B row will have numbers 1-20, I row will have 21-40, etc.
            numbers_per_row = 20
            # Adjusted spacing to account for larger circles
            num_spacing = available_width / (
                numbers_per_row + 0.2
            )  # Reduced margin to fit larger circles

            # Draw the numbers in a single row - with animation for called numbers
            for col in range(numbers_per_row):
                num = row_idx * numbers_per_row + col + 1  # Numbers 1-100

                # Skip numbers beyond 100
                if num > 100:
                    continue

                # Calculate number position - ensure it's within the row background
                num_x = row_bg_rect.x + int(num_spacing / 2) + col * num_spacing
                num_x = min(
                    num_x, row_bg_rect.right - circle_radius
                )  # Ensure it doesn't exceed right boundary
                num_y = row_bg_rect.centery

                # Check if number has been called (for Board Selection, we want all numbers with hover effect)
                is_called = num in self.called_numbers
                is_current = num == self.current_number
                is_recently_called = is_current

                if is_recently_called:
                    # Recently called number - more dramatic pulsating glow effect
                    # Create pulsating glow effect with multiple layers
                    glow_radius = circle_radius * (1.0 + 0.5 * fast_pulse)

                    # Draw outer glow circles with decreasing opacity
                    for i in range(2):  # Reduced glow layers
                        alpha = int(230 - i * 70)
                        glow_color = (*row_color, alpha)
                        glow_size = int(glow_radius) + i * 2
                        pygame.draw.circle(self.screen, glow_color, (num_x, num_y), glow_size, 2)

                    # Strong highlight fill with pulsating brightness
                    brightness = int(220 + 35 * fast_pulse)
                    highlight_color = (
                        min(255, row_color[0] + brightness // 3),
                        min(255, row_color[1] + brightness // 3),
                        min(255, row_color[2] + brightness // 3),
                    )
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)

                    # Add a white center for extra pop
                    pygame.draw.circle(self.screen, WHITE, (num_x, num_y), int(circle_radius * 0.7))

                elif is_called:
                    # Called but not recent - highlight with row color
                    # Subtle pulsating effect for all called numbers
                    highlight_intensity = int(170 + 40 * pulse)
                    highlight_color = (
                        min(255, row_color[0] * highlight_intensity // 255),
                                      min(255, row_color[1] * highlight_intensity // 255),
                        min(255, row_color[2] * highlight_intensity // 255),
                    )

                    # Draw background circle with row color
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)

                    # Add inner circle for better contrast
                    inner_color = (
                        min(255, highlight_color[0] + 30),
                                  min(255, highlight_color[1] + 30),
                        min(255, highlight_color[2] + 30),
                    )
                    pygame.draw.circle(
                        self.screen, inner_color, (num_x, num_y), int(circle_radius * 0.75)
                    )
                else:
                    # Not called - dark background
                    pygame.draw.circle(self.screen, (60, 60, 70), (num_x, num_y), circle_radius)

                # Calculate responsive font size for numbers based on circle size - maximized
                # Increased font-to-circle ratio significantly for 2x larger numbers
                num_font_size = min(number_size, int(circle_radius * 1.8))
                if num_font_size < 10:  # Min font size for readability (increased)
                    continue  # Skip rendering numbers if they'd be too small

                current_font = pygame.font.SysFont("Arial", num_font_size, bold=True)

                # Two-digit numbers need special handling
                text_to_render = str(num)
                # Adjusted scaling for multi-digit numbers to better fit larger text
                if num >= 10 and num < 100:
                    current_font = pygame.font.SysFont(
                        "Arial", int(num_font_size * 0.95), bold=True
                    )
                elif num >= 100:
                    current_font = pygame.font.SysFont(
                        "Arial", int(num_font_size * 0.85), bold=True
                    )

                # Draw the number text
                if is_recently_called:
                    # Text color for recently called number - always black for contrast against white center
                    text_color = (0, 0, 0)
                    # Use larger, bolder font for the recently called number
                    num_surf = current_font.render(text_to_render, True, text_color)
                elif is_called:
                    # Text color for called numbers - white with high contrast
                    text_color = WHITE
                    num_surf = current_font.render(text_to_render, True, text_color)
                else:
                    # Text color for uncalled numbers - slightly dimmer
                    text_color = (200, 200, 200)
                    num_surf = current_font.render(text_to_render, True, text_color)

                num_rect = num_surf.get_rect(center=(num_x, num_y))
                self.screen.blit(num_surf, num_rect)

                # Store hit area for each number - each number circle is clickable
                lucky_number_rect = pygame.Rect(
                    num_x - circle_radius,
                    num_y - circle_radius,
                    circle_radius * 2,
                    circle_radius * 2,
                )
                self.hit_areas[f"lucky_number_{num}"] = lucky_number_rect

    def draw_add_players_section(self, x, y, width, height):
        """Draw a more compact and visually appealing Add Players section"""
        # Get current mouse position for all hover checks
        mouse_pos = pygame.mouse.get_pos()

        # Draw section background with a smoother gradient
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (15, 25, 45), (25, 35, 65), 12)

        # Calculate padding based on scale
        padding = int(10 * min(self.scale_x, self.scale_y))

        # Draw title "ADD PLAYERS" with a modern look
        title_font = pygame.font.SysFont(
            "Arial", int(22 * min(self.scale_x, self.scale_y)), bold=True
        )
        title_text = title_font.render("ADD PLAYERS", True, (230, 230, 230))

        # Add a colored indicator bar before the title
        indicator_width = int(4 * min(self.scale_x, self.scale_y))
        indicator_height = title_text.get_height()
        indicator_x = x + padding
        indicator_y = y + padding
        indicator_rect = pygame.Rect(indicator_x, indicator_y, indicator_width, indicator_height)
        pygame.draw.rect(self.screen, (0, 160, 220), indicator_rect, border_radius=2)

        # Position title text after the indicator
        title_x = indicator_x + indicator_width + padding
        title_y = y + padding
        self.screen.blit(title_text, (title_x, title_y))

        # Draw separator line with gradient effect
        line_y = title_y + title_text.get_height() + int(padding / 2)
        line_width = width - padding * 2
        line_height = int(2 * min(self.scale_x, self.scale_y))
        line_rect = pygame.Rect(x + padding, line_y, line_width, line_height)
        line_gradient = pygame.Surface((line_width, line_height), pygame.SRCALPHA)

        # Create gradient for line
        for i in range(line_width):
            alpha = (
                150
                if i < line_width / 2
                else 150 - int(150 * (i - line_width / 2) / (line_width / 2))
            )
            line_color = (70, 130, 180, alpha)
            pygame.draw.line(line_gradient, line_color, (i, 0), (i, line_height))

        self.screen.blit(line_gradient, line_rect)

        # Position for input components
        components_y = line_y + padding * 2

        # Calculate available space for components
        available_height = height - (components_y - y) - padding
        component_height = int(available_height * 0.4)

        # Draw cartella number label with a cleaner font
        label_font = pygame.font.SysFont(
            "Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True
        )
        cartella_label = label_font.render("CARTELLA NUMBER", True, (180, 180, 180))
        cartella_label_x = x + padding
        cartella_label_y = components_y
        self.screen.blit(cartella_label, (cartella_label_x, cartella_label_y))

        # Draw compact row with input box, arrows and add button
        input_y = cartella_label_y + cartella_label.get_height() + int(padding / 2)
        row_height = component_height

        # Calculate widths for a more balanced layout
        input_width = int(width * 0.3)
        arrow_size = int(row_height * 0.6)
        arrow_gap = int(padding / 2)
        add_btn_width = int(width * 0.3)

        # Draw cartella number input box with a modern design
        input_x = x + padding
        input_rect = pygame.Rect(input_x, input_y, input_width, row_height)

        # Check mouse hover for input
        input_hover = input_rect.collidepoint(mouse_pos)

        # Draw stylish input box with colored border
        if self.input_active or input_hover:
            # Active style with colored border
            border_color = (0, 160, 220)
            pygame.draw.rect(self.screen, (20, 30, 50), input_rect, border_radius=6)
            pygame.draw.rect(self.screen, border_color, input_rect, width=2, border_radius=6)

            # Draw subtle glow effect when active
        if self.input_active:
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    input_rect.x - i,
                    input_rect.y - i,
                    input_rect.width + i * 2,
                    input_rect.height + i * 2,
                )
                glow_color = (*border_color, 60 - i * 20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=6 + i)
        else:
            # Inactive style
            pygame.draw.rect(self.screen, (20, 30, 50), input_rect, border_radius=6)
            pygame.draw.rect(self.screen, (40, 50, 70), input_rect, width=1, border_radius=6)

        # Draw cartella number value
        number_font = pygame.font.SysFont(
            "Arial", int(22 * min(self.scale_x, self.scale_y)), bold=True
        )

        # Show input text with cursor if active
        if self.input_active:
            display_text = self.input_text
            if self.input_cursor_visible:
                display_text += "|"
            number_text = number_font.render(display_text, True, (255, 255, 255))
        else:
            number_text = number_font.render(str(self.cartella_number), True, (255, 255, 255))

        number_x = input_x + (input_width - number_text.get_width()) // 2
        number_y = input_y + (row_height - number_text.get_height()) // 2
        self.screen.blit(number_text, (number_x, number_y))

        # Position arrows and add button in the same row
        controls_x = input_x + input_width + arrow_gap

        # Draw up/down arrows grouped in a single control
        arrows_width = arrow_size * 2
        arrows_height = row_height
        arrows_rect = pygame.Rect(controls_x, input_y, arrows_width, arrows_height)

        # Draw up arrow
        up_arrow_rect = pygame.Rect(controls_x, input_y, arrow_size, arrow_size)
        up_hover = up_arrow_rect.collidepoint(mouse_pos)

        # Draw down arrow
        down_arrow_rect = pygame.Rect(controls_x + arrow_size, input_y, arrow_size, arrow_size)
        down_hover = down_arrow_rect.collidepoint(mouse_pos)

        # Draw arrows container
        pygame.draw.rect(self.screen, (25, 35, 55), arrows_rect, border_radius=6)

        # Draw divider between arrows
        pygame.draw.line(
            self.screen,
            (50, 60, 80),
            (controls_x + arrow_size, input_y + int(arrows_height * 0.15)),
            (controls_x + arrow_size, input_y + int(arrows_height * 0.85)),
            1,
        )

        # Draw up arrow with hover effect
        if up_hover:
            up_color = (0, 160, 220)
            # Highlight background
            pygame.draw.rect(self.screen, (30, 45, 65), up_arrow_rect, border_radius=6)
        else:
            up_color = (150, 160, 180)

        # Draw up triangle with improved design
        triangle_size = int(arrow_size * 0.4)
        pygame.draw.polygon(
            self.screen,
            up_color,
            [
                (controls_x + arrow_size // 2, input_y + arrows_height // 4),
                (
                    controls_x + arrow_size // 2 - triangle_size // 2,
                    input_y + arrows_height // 4 + triangle_size,
                ),
                (
                    controls_x + arrow_size // 2 + triangle_size // 2,
                    input_y + arrows_height // 4 + triangle_size,
                ),
            ],
        )

        # Draw down arrow with hover effect
        if down_hover:
            down_color = (0, 160, 220)
            # Highlight background
            pygame.draw.rect(self.screen, (30, 45, 65), down_arrow_rect, border_radius=6)
        else:
            down_color = (150, 160, 180)

        # Draw down triangle with improved design
        pygame.draw.polygon(
            self.screen,
            down_color,
            [
                (
                    controls_x + arrow_size + arrow_size // 2,
                    input_y + arrows_height // 4 + triangle_size,
                ),
                (
                    controls_x + arrow_size + arrow_size // 2 - triangle_size // 2,
                    input_y + arrows_height // 4,
                ),
                (
                    controls_x + arrow_size + arrow_size // 2 + triangle_size // 2,
                    input_y + arrows_height // 4,
                ),
            ],
        )

        # Add button with modern design
        add_btn_x = controls_x + arrows_width + arrow_gap
        add_btn_rect = pygame.Rect(add_btn_x, input_y, add_btn_width, row_height)

        # Check hover for ADD button
        add_btn_hover = add_btn_rect.collidepoint(mouse_pos)

        # Draw ADD button with modern gradient and subtle animation
        if add_btn_hover:
            self.draw_gradient_rect(add_btn_rect, (0, 140, 220), (0, 100, 180), 8)

            # Add glow effect on hover
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    add_btn_rect.x - i,
                    add_btn_rect.y - i,
                    add_btn_rect.width + i * 2,
                    add_btn_rect.height + i * 2,
                )
                glow_color = (0, 150, 255, 50 - i * 15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8 + i)
        else:
            self.draw_gradient_rect(add_btn_rect, (0, 100, 180), (0, 80, 140), 8)

        # Draw ADD text with a modern small icon
        add_text = label_font.render("ADD", True, (255, 255, 255))
        add_x = add_btn_x + (add_btn_width - add_text.get_width()) // 2
        add_y = input_y + (row_height - add_text.get_height()) // 2
        self.screen.blit(add_text, (add_x, add_y))

        # Add the plus icon next to text
        plus_size = int(min(12 * min(self.scale_x, self.scale_y), add_text.get_height() * 0.8))
        plus_x = add_x + add_text.get_width() + int(5 * min(self.scale_x, self.scale_y))
        plus_y = add_y + (add_text.get_height() - plus_size) // 2

        # Draw plus circle
        pygame.draw.circle(self.screen, (255, 255, 255), (plus_x + plus_size // 2, plus_y + plus_size // 2), plus_size // 2)

        # Horizontal and vertical lines to create + symbol
        line_thickness = max(1, int(plus_size * 0.1))
        pygame.draw.line(
            self.screen,
            (0, 0, 0),
            (int(plus_x + plus_size / 4), int(plus_y + plus_size / 2)),
            (int(plus_x + plus_size * 3 / 4), int(plus_y + plus_size / 2)),
            line_thickness,
        )
        pygame.draw.line(
            self.screen,
            (0, 0, 0),
            (int(plus_x + plus_size / 2), int(plus_y + plus_size / 4)),
            (int(plus_x + plus_size / 2), int(plus_y + plus_size * 3 / 4)),
            line_thickness,
        )

        # Draw Reset All button below the input row
        reset_btn_height = row_height
        reset_btn_y = input_y + row_height + padding
        reset_btn_width = width - padding * 2
        reset_btn_x = x + padding
        reset_btn_rect = pygame.Rect(reset_btn_x, reset_btn_y, reset_btn_width, reset_btn_height)

        # Check hover for Reset All button
        reset_btn_hover = reset_btn_rect.collidepoint(mouse_pos)

        # Draw Reset All button with appropriate styling
        if reset_btn_hover:
            self.draw_gradient_rect(reset_btn_rect, (220, 60, 60), (180, 40, 40), 8)

            # Add glow effect on hover
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    reset_btn_rect.x - i,
                    reset_btn_rect.y - i,
                    reset_btn_rect.width + i * 2,
                    reset_btn_rect.height + i * 2,
                )
                glow_color = (255, 100, 100, 50 - i * 15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8 + i)
        else:
            self.draw_gradient_rect(reset_btn_rect, (180, 50, 50), (150, 40, 40), 8)

        # Draw Reset All text
        reset_text = label_font.render("RESET ALL", True, (255, 255, 255))
        reset_x = reset_btn_x + (reset_btn_width - reset_text.get_width()) // 2
        reset_y = reset_btn_y + (reset_btn_height - reset_text.get_height()) // 2
        self.screen.blit(reset_text, (reset_x, reset_y))

        # Store hit areas
        self.hit_areas["cartella_input"] = input_rect
        self.hit_areas["cartella_up"] = up_arrow_rect
        self.hit_areas["cartella_down"] = down_arrow_rect
        self.hit_areas["add_player"] = add_btn_rect
        self.hit_areas["reset_all"] = reset_btn_rect

    def reset_all_cartellas(self):
        """Reset all selected cartella numbers"""
        # Clear selected cartella numbers
        self.selected_cartella_numbers = []

        # Clear called numbers that correspond to cartella numbers
        self.called_numbers = []

        # Clear players list
        self.players = []

        # Save empty players list to JSON
        save_players_to_json(self.players)

        # Reset prize pool
        self.calculate_prize_pool()

        # Always clear the UI state in the JSON file when reset all is clicked
        try:
            from player_storage import save_ui_state
            # Save empty list to clear UI state
            save_ui_state([], self.bet_amount, None, False)
            print("Cleared UI state from JSON storage")
        except Exception as e:
            print(f"Error clearing UI state: {e}")

    def draw_prize_pool_section(self, x, y, width, height):
        """Draw the Prize Pool section with responsive scaling for fullscreen"""
        # Adaptive scaling based on section dimensions for better proportions
        relative_scale = min(width / 300, height / 200)
        # Use balanced scaling to maintain proportions
        balanced_scale = min(self.scale_x, self.scale_y)

        # Create a background with subtle gradient and rounded corners
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (20, 40, 60), (30, 50, 70), 10)

        # "Prize Pool" title text with gold gradient
        header_height = int(height * 0.15)  # 15% for header

        # Draw gold-gradient title bar on top
        title_bar_rect = pygame.Rect(x, y, width, header_height)
        pygame.draw.rect(self.screen, GOLD, title_bar_rect, border_radius=10)
        pygame.draw.rect(
            self.screen,
            GOLD,
            (x, y + int(header_height / 2), width, header_height / 2),
            border_radius=0,
        )

        # Add highlight to top of title bar for 3D effect
        highlight_rect = pygame.Rect(x + 2, y + 2, width - 4, int(header_height * 0.3))
        highlight_color = (255, 240, 180, 90)  # Semi-transparent light gold
        pygame.draw.rect(self.screen, highlight_color, highlight_rect, border_radius=8)

        # Draw title text - size scales with the section and screen proportions
        title_font_size = int(24 * balanced_scale * relative_scale)
        title_font_size = max(title_font_size, 16)  # Ensure minimum readable size
        title_font = pygame.font.SysFont("Arial", title_font_size, bold=True)

        title_text = title_font.render("PRIZE POOL", True, (20, 20, 20))
        title_x = x + (width - title_text.get_width()) // 2
        title_y = y + (header_height - title_text.get_height()) // 2

        # Add text shadow for better visibility
        shadow_text = title_font.render("PRIZE POOL", True, (10, 10, 10))
        self.screen.blit(shadow_text, (title_x + 2, title_y + 2))
        self.screen.blit(title_text, (title_x, title_y))

        # Calculate remaining usable area with proper spacing
        # Adapt padding based on section size and screen proportions
        content_padding = max(5, int(10 * balanced_scale * relative_scale))
        content_y = y + header_height + content_padding
        content_height = height - header_height - content_padding * 2

        # BET AMOUNT section - responsive text sizing
        label_font_size = int(16 * balanced_scale * relative_scale)
        label_font_size = max(label_font_size, 13)  # Ensure minimum readable size
        label_font = pygame.font.SysFont("Arial", label_font_size, bold=True)

        bet_label = label_font.render("BET AMOUNT:", True, (200, 200, 200))
        bet_label_y = content_y + content_padding
        self.screen.blit(bet_label, (x + content_padding, bet_label_y))

        # Calculate dimensions that adapt to the section size
        input_height = max(24, int(36 * balanced_scale * relative_scale))
        input_width = int(width * 0.45)
        input_x = x + content_padding
        input_y = bet_label_y + bet_label.get_height() + content_padding

        # Draw bet input rectangle with dark background
        input_rect = pygame.Rect(input_x, input_y, input_width, input_height)
        pygame.draw.rect(self.screen, (10, 20, 30), input_rect, border_radius=5)
        pygame.draw.rect(self.screen, (60, 100, 150), input_rect, 1, border_radius=5)

        # Draw inset shadow at top of input
        pygame.draw.line(
            self.screen,
            (0, 0, 0, 100),
            (input_x + 2, input_y + 2),
            (input_x + input_width - 2, input_y + 2),
        )

        # Add highlight to bottom of input
        pygame.draw.line(
            self.screen,
            (100, 140, 180),
            (input_x + 2, input_y + input_height - 2),
            (input_x + input_width - 2, input_y + input_height - 2),
        )

        # Draw bet amount text with responsive sizing
        input_font_size = int(22 * balanced_scale * relative_scale)
        input_font_size = max(input_font_size, 16)  # Ensure minimum readable size
        input_font = pygame.font.SysFont("Arial", input_font_size, bold=True)

        # If input is active, use input_text, otherwise use stored value
        if self.bet_input_active:
            display_text = self.bet_input_text
            # Add cursor if visible
            if self.bet_input_cursor_visible:
                display_text += "|"
            text_color = (255, 255, 255)  # Brighter white when active
        else:
            display_text = str(self.bet_amount)
            text_color = (200, 200, 200)  # Slightly dimmer when inactive

        # Calculate position to right-align text with padding
        bet_text = input_font.render(display_text, True, text_color)
        text_x = input_x + input_width - bet_text.get_width() - content_padding
        text_y = input_y + (input_height - bet_text.get_height()) // 2
        self.screen.blit(bet_text, (text_x, text_y))

        # Draw "ETB" label with responsive positioning and size
        etb_font = pygame.font.SysFont("Arial", label_font_size, bold=True)
        etb_label = etb_font.render("ETB", True, (255, 160, 0))
        etb_x = input_x + input_width + content_padding
        etb_y = input_y + (input_height - etb_label.get_height()) // 2
        self.screen.blit(etb_label, (etb_x, etb_y))

        # Draw "SET" button - calculate responsive size
        set_btn_width = max(50, int(width * 0.25))
        set_btn_height = input_height

        # Calculate button position with adaptive spacing
        set_btn_x = (
            input_x + input_width + content_padding + etb_label.get_width() + content_padding
        )

        # Make sure the button fits within the section width
        if set_btn_x + set_btn_width > x + width - content_padding:
            set_btn_width = (x + width) - set_btn_x - content_padding

        set_btn_y = input_y
        set_btn_rect = pygame.Rect(set_btn_x, set_btn_y, set_btn_width, set_btn_height)

        # Check if mouse is over set button
        mouse_pos = pygame.mouse.get_pos()
        set_btn_hover = set_btn_rect.collidepoint(mouse_pos)

        # Draw button with hover effect
        if set_btn_hover:
            self.draw_gradient_rect(set_btn_rect, (60, 150, 255), (40, 100, 200), 5)
        else:
            self.draw_gradient_rect(set_btn_rect, (50, 100, 200), (30, 80, 150), 5)

        # Draw set button text with responsive sizing
        set_btn_font_size = int(18 * min(self.scale_x, self.scale_y) * relative_scale)
        set_btn_font_size = max(set_btn_font_size, 14)  # Ensure minimum readable size
        set_btn_font = pygame.font.SysFont("Arial", set_btn_font_size, bold=True)

        set_btn_text = set_btn_font.render("SET", True, (255, 255, 255))
        set_text_x = set_btn_x + (set_btn_width - set_btn_text.get_width()) // 2
        set_text_y = set_btn_y + (set_btn_height - set_btn_text.get_height()) // 2
        self.screen.blit(set_btn_text, (set_text_x, set_text_y))

        # TOTAL POT section
        total_label = label_font.render("TOTAL POT:", True, (200, 200, 200))
        total_label_y = input_y + input_height + content_padding * 2
        self.screen.blit(total_label, (x + content_padding, total_label_y))

        # Draw prize display with glowing gold background - adapt to available height
        # Increase the prize section ratio to accommodate larger text
        prize_section_ratio = 0.4  # Increased from 0.3 to 0.4 (40% of total height)
        prize_display_height = min(
            int(70 * self.scale_y), max(60, int(height * prize_section_ratio))
        )  # Increased height for larger text

        prize_display_width = width - content_padding * 2
        prize_display_x = x + content_padding
        prize_display_y = total_label_y + total_label.get_height() + content_padding

        # Make sure the prize display fits within the available height
        max_prize_y = y + height - prize_display_height - content_padding
        if prize_display_y > max_prize_y:
            prize_display_y = max_prize_y

        # Create a rectangle for the prize display with rounded corners and gradient
        prize_display_rect = pygame.Rect(
            prize_display_x, prize_display_y, prize_display_width, prize_display_height
        )

        # Draw a more stylized background for the prize amount with enhanced gradient
        # Rich golden gradient background for better visual appeal
        self.draw_gradient_rect(prize_display_rect, (40, 35, 10), (60, 45, 15), 12)

        # Add an enhanced inner border glow
        inner_border_color = (220, 180, 40, 120)  # Brighter semi-transparent gold

        # Draw multiple layers of inner glow for a more dramatic effect
        for i in range(1, 4):
            smaller_rect = pygame.Rect(
                prize_display_rect.x + i,
                prize_display_rect.y + i,
                prize_display_rect.width - (i * 2),
                prize_display_rect.height - (i * 2),
            )
            glow_alpha = 150 - (i * 30)  # Fade out as we go inward
            glow_color = (220, 180, 40, glow_alpha)
            pygame.draw.rect(self.screen, glow_color, smaller_rect, 1, border_radius=10)

        # Prize amount text with 3x larger font and responsive sizing
        # Increase font size by 3x as requested
        prize_font_size = int(90 * min(self.scale_x, self.scale_y) * relative_scale)  # 3x larger (from 30 to 90)
        prize_font_size = max(prize_font_size, 60)  # Ensure minimum readable size (3x from 20 to 60)
        prize_font = pygame.font.SysFont("Arial", prize_font_size, bold=True)

        # Format prize amount with commas for thousands
        formatted_prize = f"{self.prize_pool:,}"
        prize_text = prize_font.render(formatted_prize, True, (255, 220, 50))  # Gold text

        # Make sure text fits within the display area
        # If text is too wide, scale it down while maintaining 3x larger than original
        if prize_text.get_width() > prize_display_width - 20:  # Leave 10px padding on each side
            scale_factor = (prize_display_width - 20) / prize_text.get_width()
            new_size = int(prize_font_size * scale_factor)
            # Ensure it's still significantly larger than original
            new_size = max(new_size, int(60 * min(self.scale_x, self.scale_y) * relative_scale))
            prize_font = pygame.font.SysFont("Arial", new_size, bold=True)
            prize_text = prize_font.render(str(self.prize_pool), True, (255, 220, 50))

        # Center prize text in display area
        prize_x = prize_display_x + (prize_display_width - prize_text.get_width()) // 2
        prize_y = prize_display_y + (prize_display_height - prize_text.get_height()) // 2

        # Enhanced glowing effect for prize text
        glow_size = min(20, int(20 * self.scale_x * relative_scale))  # Increased glow size

        # Create multiple layers of glow for a more dramatic effect
        for i in range(3):
            layer_size = glow_size * (3 - i) // 3  # Larger outer layers, smaller inner layers
            glow_surf = pygame.Surface(
                (prize_text.get_width() + layer_size * 2, prize_text.get_height() + layer_size * 2),
                pygame.SRCALPHA,
            )

            # Vary the alpha for each layer
            alpha = 40 - (i * 10)
            pygame.draw.ellipse(
                glow_surf,
                (255, 220, 50, alpha),  # Gold with varying transparency
                (
                    0,
                    0,
                    prize_text.get_width() + layer_size * 2,
                    prize_text.get_height() + layer_size * 2,
                ),
            )

            # Position each glow layer
            offset = layer_size
            self.screen.blit(
                glow_surf, (prize_x - offset, prize_y - offset)
            )

        # Draw the prize amount
        self.screen.blit(prize_text, (prize_x, prize_y))

        # Draw ETB label with modern styling and responsive size
        etb_font_size = int(24 * min(self.scale_x, self.scale_y) * relative_scale)  # Slightly larger
        etb_font_size = max(etb_font_size, 18)  # Ensure minimum readable size
        etb_font = pygame.font.SysFont("Arial", etb_font_size, bold=True)

        etb_text = etb_font.render("ETB", True, (255, 150, 50))

        # Reposition ETB to avoid overlapping with the larger pot amount
        # Place it at the right side of the prize display, vertically centered
        etb_padding = max(10, int(15 * self.scale_x * relative_scale))

        # Check if there's enough space to the right of the prize text
        if prize_x + prize_text.get_width() + etb_padding + etb_text.get_width() <= prize_display_rect.right - etb_padding:
            # Place ETB to the right of the prize amount
            etb_x = prize_x + prize_text.get_width() + etb_padding
            etb_y = prize_y + (prize_text.get_height() - etb_text.get_height()) // 2  # Align vertically with prize text
        else:
            # If not enough space, place ETB below the prize amount
            etb_x = prize_display_rect.centerx - etb_text.get_width() // 2  # Center horizontally
            etb_y = prize_y + prize_text.get_height() + etb_padding  # Below prize text
        self.screen.blit(etb_text, (etb_x, etb_y))

        # Store hit areas
        self.hit_areas["bet_input"] = input_rect
        self.hit_areas["set_button"] = set_btn_rect

    def draw_playing_board(self, y_position, section_height, section_width):
        """Draw the Playing Board section on the left side of the screen"""
        # Scale dimensions based on section height
        title_height = int(section_height * 0.1)
        board_height = int(section_height * 0.85)

        # Use the left side position
        x_position = int(20 * self.scale_x)

        # "Playing Board" title text with glow
        header_font = pygame.font.SysFont(
            "Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True
        )
        playing_board_text = header_font.render("Playing Board", True, WHITE)
        play_x = x_position + int(10 * self.scale_x)
        play_y = y_position + int(title_height * 0.2)  # Slight offset from top

        # Add glow effect for title
        glow_surf = pygame.Surface(
            (
                playing_board_text.get_width() + int(8 * self.scale_x),
                playing_board_text.get_height() + int(8 * self.scale_y),
            ),
            pygame.SRCALPHA,
        )
        pygame.draw.ellipse(
            glow_surf,
            (WHITE[0], WHITE[1], WHITE[2], 80),
            (
                0,
                0,
                playing_board_text.get_width() + int(8 * self.scale_x),
                playing_board_text.get_height() + int(8 * self.scale_y),
            ),
        )
        self.screen.blit(
            glow_surf, (play_x - int(4 * self.scale_x), play_y - int(4 * self.scale_y))
        )
        self.screen.blit(playing_board_text, (play_x, play_y))

        # Draw board background - ensure proper width
        board_rect = pygame.Rect(
            x_position,
            play_y + playing_board_text.get_height() + int(5 * self.scale_y),
            section_width,  # Use passed width
            board_height,
        )
        self.draw_gradient_rect(board_rect, (40, 70, 80), (50, 80, 90), 10)

        # Define BINGO colors
        b_color = (255, 30, 30)  # Bright red
        i_color = (30, 255, 80)  # Bright green
        n_color = (80, 80, 220)  # Blue/Purple
        g_color = (255, 30, 30)  # Bright red (matching B)
        o_color = (255, 220, 0)  # Gold yellow

        # Calculate grid dimensions based on available space
        grid_height = board_rect.height * 0.85
        grid_start_x = board_rect.x + int(board_rect.width * 0.05)  # 5% from left edge
        grid_start_y = board_rect.y + int(board_rect.height * 0.12)  # 12% from top
        grid_width = board_rect.width * 0.9

        # Calculate cell size based on available space
        cell_size = min(grid_width / 5, grid_height / 5)

        # Draw BINGO letters - positioned to align with columns
        colors = [b_color, i_color, n_color, g_color, o_color]
        letters = "BINGO"

        # Use a bolder font for BINGO letters
        bingo_header_font = pygame.font.SysFont(
            "Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True
        )

        # Draw each letter centered above its column
        for i, letter in enumerate(letters):
            # Calculate the center position of each column
            letter_x = grid_start_x + (i * cell_size) + (cell_size / 2)
            letter_y = board_rect.y + int(board_rect.height * 0.05)  # 5% from top of board

            # Reduced shadow effect for better visibility while maintaining design
            shadow_surf = bingo_header_font.render(letter, True, (20, 20, 20))
            shadow_rect = shadow_surf.get_rect(
                center=(letter_x + int(1.5 * self.scale_x), letter_y + int(1.5 * self.scale_y))
            )
            self.screen.blit(shadow_surf, shadow_rect)

            # Main letter with slightly increased font size for better visibility
            bingo_visible_font = pygame.font.SysFont(
                "Arial", int(30 * min(self.scale_x, self.scale_y)), bold=True
            )

            # Draw subtle outline for better visibility
            for offset_x, offset_y in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                outline_pos = (letter_x + offset_x, letter_y + offset_y)
                outline_surf = bingo_visible_font.render(letter, True, (20, 20, 30))
                outline_rect = outline_surf.get_rect(center=outline_pos)
                self.screen.blit(outline_surf, outline_rect)

            letter_surf = bingo_visible_font.render(letter, True, colors[i])
            letter_rect = letter_surf.get_rect(center=(letter_x, letter_y))
            self.screen.blit(letter_surf, letter_rect)

        grid_line_thickness = max(1, int(1 * min(self.scale_x, self.scale_y)))
        grid_line_color = WHITE

        # Draw grid lines over the background
        # Horizontal lines
        for row in range(6):
            y = grid_start_y + row * cell_size
            pygame.draw.line(
                self.screen,
                grid_line_color,
                            (grid_start_x, y),
                            (grid_start_x + 5 * cell_size, y),
                grid_line_thickness,
            )

        # Vertical lines
        for col in range(6):
            x = grid_start_x + col * cell_size
            pygame.draw.line(
                self.screen,
                grid_line_color,
                            (x, grid_start_y),
                            (x, grid_start_y + 5 * cell_size),
                grid_line_thickness,
            )

        # Draw star in center (free space)
        center_col, center_row = 2, 2
        star_cell_rect = pygame.Rect(
            grid_start_x + center_col * cell_size + grid_line_thickness,
            grid_start_y + center_row * cell_size + grid_line_thickness,
            cell_size - grid_line_thickness * 2,
            cell_size - grid_line_thickness * 2,
        )

        # Draw dark green square background for the star
        green_bg_color = (0, 100, 50)  # Dark green
        pygame.draw.rect(self.screen, green_bg_color, star_cell_rect)

        # Draw the gold star
        star_size = int(cell_size * 0.6)
        star_x = star_cell_rect.centerx - star_size // 2
        star_y = star_cell_rect.centery - star_size // 2

        star_img = pygame.Surface((star_size, star_size), pygame.SRCALPHA)
        star_center_x = star_size // 2
        star_center_y = star_size // 2
        star_radius = star_size // 2 * 0.9

        # Draw star points (gold)
        star_points = [
            (star_center_x, star_center_y - star_radius),  # Top point
            (
                star_center_x + star_radius * 0.3,
                star_center_y - star_radius * 0.3,
            ),  # Top right point
            (star_center_x + star_radius, star_center_y),  # Right point
            (
                star_center_x + star_radius * 0.4,
                star_center_y + star_radius * 0.3,
            ),  # Bottom right point
            (star_center_x + star_radius * 0.6, star_center_y + star_radius),  # Bottom point
            (star_center_x, star_center_y + star_radius * 0.5),  # Bottom middle point
            (star_center_x - star_radius * 0.6, star_center_y + star_radius),  # Bottom left point
            (
                star_center_x - star_radius * 0.4,
                star_center_y + star_radius * 0.3,
            ),  # Top left point
            (star_center_x - star_radius, star_center_y),  # Left point
            (
                star_center_x - star_radius * 0.3,
                star_center_y - star_radius * 0.3,
            ),  # Top left point
        ]
        pygame.draw.polygon(star_img, GOLD, star_points)

        # Add subtle highlight to star
        highlight_color = (255, 255, 180)  # Lighter gold
        highlight_points = [
            (star_center_x, star_center_y - star_radius),  # Top
             (star_center_x + star_radius * 0.1, star_center_y - star_radius * 0.3),
            (star_center_x - star_radius * 0.1, star_center_y - star_radius * 0.3),
        ]
        pygame.draw.polygon(star_img, highlight_color, highlight_points)

        self.screen.blit(star_img, (star_x, star_y))

        # Draw board numbers (excluding the center free space)
        number_font_size = min(int(20 * min(self.scale_x, self.scale_y)), int(cell_size * 0.4))
        number_font = pygame.font.SysFont("Arial", number_font_size)
        for col in range(5):
            for row in range(5):
                # Skip the free space drawing logic here
                if col == center_col and row == center_row:
                    continue

                number = self.bingo_board[col][row]
                if number > 0:
                    # Calculate cell center position
                    cell_center_x = grid_start_x + cell_size // 2 + col * cell_size
                    cell_center_y = grid_start_y + cell_size // 2 + row * cell_size

                    # Check if number is marked/called
                    if number in self.called_numbers:
                        # Highlight called numbers with color matching the column header
                        pygame.draw.circle(
                            self.screen,
                            colors[col],
                                          (cell_center_x, cell_center_y),
                            int(cell_size * 0.3),
                        )

                    # Render number with increased shadow offset
                    shadow_surf = number_font.render(str(number), True, (0, 0, 0))
                    shadow_rect = shadow_surf.get_rect(
                        center=(
                            cell_center_x + int(3 * self.scale_x),
                            cell_center_y + int(3 * self.scale_y),
                        )
                    )
                    self.screen.blit(shadow_surf, shadow_rect)

                    # Number
                    num_surf = number_font.render(str(number), True, WHITE)
                    num_rect = num_surf.get_rect(center=(cell_center_x, cell_center_y))
                    self.screen.blit(num_surf, num_rect)

    def get_board_for_number(self, number):
        """Get a bingo board for a specific cartella number"""
        # Convert number to string for JSON dictionary key
        number_key = str(number)

        # Use a cache to avoid regenerating boards
        if hasattr(self, '_board_cache') and number_key in self._board_cache:
            return self._board_cache[number_key]

        # Initialize cache if it doesn't exist
        if not hasattr(self, '_board_cache'):
            self._board_cache = {}

        # First check if the board exists in the already loaded JSON data
        if number_key in self.bingo_boards:
            # Cache the board for future use
            self._board_cache[number_key] = self.bingo_boards[number_key]
            return self.bingo_boards[number_key]

        # If not found in the loaded data, try to load directly from the JSON file
        # This ensures we always get the latest data, especially for board previews
        try:
            import json
            import os

            # Use the same file path as used in other parts of the code
            boards_path = os.path.join('data', 'bingo_boards.json')

            if os.path.exists(boards_path):
                try:
                    with open(boards_path, 'r') as file:
                        boards_data = json.load(file)

                        # Check if the board exists in the file
                        if number_key in boards_data:
                            # Update our cached boards data with this board
                            self.bingo_boards[number_key] = boards_data[number_key]
                            self._board_cache[number_key] = boards_data[number_key]
                            return boards_data[number_key]

                        # If the board doesn't exist in the file but is in the extended range (101-1200),
                        # check if we need to generate extended boards
                        if 1 <= number <= 1200 and number > 100:
                            print(f"Board for cartella {number} not found in JSON file but is in extended range (101-1200)")
                            # We'll continue to the deterministic generation below
                except Exception as e:
                    print(f"Error reading JSON file: {e}")
        except Exception as e:
            print(f"Error loading board from JSON file: {e}")


        # If still not found, generate a deterministic board
        print(f"Board for cartella {number} not found in loaded data, generating deterministically...")
        import random

        # Use the cartella number as seed for consistent results
        random.seed(number)

        board = []

        # For each column (B, I, N, G, O)
        for col in range(5):
            column_values = []
            # Range for this column: col*15+1 to col*15+15
            min_val = col * 15 + 1
            max_val = min_val + 14

            # Generate 5 unique random numbers for this column
            values = list(range(min_val, max_val + 1))
            random.shuffle(values)
            column_values = values[:5]

            # Set the center square (N column, 3rd row) to 0 (free space)
            if col == 2:
                column_values[2] = 0

            board.append(column_values)

        # Reset the random seed to avoid affecting other random operations
        random.seed()

        # Cache the generated board
        self._board_cache[number_key] = board

        return board

    def create_board_from_number(self, number):
        """Get a bingo board for a specific cartella number from the loaded JSON file"""
        # Set flag to indicate board has changed (used for animations)
        self.board_just_changed = True
        self.board_change_time = pygame.time.get_ticks()

        # Get the board using the helper method
        self.bingo_board = self.get_board_for_number(number)

        return self.bingo_board

    def draw_navigation_bar(self, screen_width, screen_height):
        """Draw a modern compact navigation menu at the top right of the screen"""
        # Configuration for the modern navbar
        nav_height = int(40 * self.scale_y)

        # Draw settings button in top left corner
        settings_size = int(40 * min(self.scale_x, self.scale_y))
        settings_margin = int(10 * min(self.scale_x, self.scale_y))
        settings_rect = pygame.Rect(settings_margin, settings_margin, settings_size, settings_size)

        # Draw settings button with gradient
        self.draw_gradient_rect(settings_rect, (40, 60, 80), (60, 80, 100), 8)

        # Draw gear icon
        gear_color = (220, 220, 220)
        center_x = settings_rect.centerx
        center_y = settings_rect.centery
        outer_radius = int(settings_size * 0.35)
        inner_radius = int(settings_size * 0.2)
        teeth = 8

        # Draw gear teeth
        for i in range(teeth * 2):
            angle = math.pi * i / teeth
            radius = outer_radius if i % 2 == 0 else inner_radius
            x = center_x + int(radius * math.cos(angle))
            y = center_y + int(radius * math.sin(angle))
            if i == 0:
                points = [(x, y)]
            else:
                points.append((x, y))

        pygame.draw.polygon(self.screen, gear_color, points)

        # Draw inner circle
        pygame.draw.circle(self.screen, gear_color, (center_x, center_y), int(inner_radius * 0.8))
        pygame.draw.circle(self.screen, (40, 60, 80), (center_x, center_y), int(inner_radius * 0.5))

        # Store hit area for settings button
        self.hit_areas["settings_button"] = settings_rect

        # Display current preset name if available
        if hasattr(self, 'current_preset_name') and self.current_preset_name:
            preset_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)))
            preset_text = preset_font.render(f"Preset: {self.current_preset_name}", True, (200, 200, 200))
            preset_rect = preset_text.get_rect(left=settings_rect.right + settings_margin, centery=settings_rect.centery)
            self.screen.blit(preset_text, preset_rect)
        nav_width = int(screen_width * 0.4)  # 40% of screen width, positioned at right
        nav_x = screen_width - nav_width

        # Slightly transparent background for the nav bar
        nav_rect = pygame.Rect(nav_x, 0, nav_width, nav_height)
        nav_surface = pygame.Surface((nav_rect.width, nav_rect.height), pygame.SRCALPHA)
        nav_surface.fill((NAV_BAR_BG[0], NAV_BAR_BG[1], NAV_BAR_BG[2], 220))  # Semi-transparent
        self.screen.blit(nav_surface, nav_rect)

        # Add a subtle bottom border
        pygame.draw.line(self.screen, (60, 80, 100),
                        (nav_x, nav_height-1),
                        (screen_width, nav_height-1), 1)

        # Navigation items with modern icons
        nav_items = [
            {"id": "play", "text": "Game", "icon": "🎮"},
            {"id": "preview", "text": "Preview", "icon": "👁️"},
            {"id": "stats", "text": "Stats", "icon": "📊"},
            {"id": "settings", "text": "Settings", "icon": "⚙️"},
            {"id": "help", "text": "Help", "icon": "❓"}
        ]

        # Set active navigation item
        active_nav = "play"  # Default to "Game" tab for board selection

        # Calculate positioning - more compact layout
        item_width = nav_width / len(nav_items)
        item_x_start = nav_x
        nav_font = pygame.font.SysFont("Arial", self.scaled_font_size(14), bold=True)

        # Draw each navigation item
        for i, item in enumerate(nav_items):
            # Calculate item position
            item_x = item_x_start + i * item_width
            item_rect = pygame.Rect(item_x, 0, item_width, nav_height)

            # Highlight active item with a more subtle effect
            if item["id"] == active_nav:
                # Modern highlight with rounded bottom corners
                highlight_rect = pygame.Rect(item_x + item_width * 0.15, 0,
                                          item_width * 0.7, nav_height)

                # Draw a rounded rectangle for active item with semi-transparency
                highlight_surface = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
                pygame.draw.rect(highlight_surface, (*NAV_BAR_ACTIVE, 180),
                               (0, 0, highlight_rect.width, highlight_rect.height),
                               border_radius=int(5 * min(self.scale_x, self.scale_y)))
                self.screen.blit(highlight_surface, highlight_rect)

                # Add subtle bottom indicator
                indicator_width = item_width * 0.4
                indicator_x = item_x + (item_width - indicator_width) / 2
                indicator_height = int(3 * self.scale_y)
                pygame.draw.rect(self.screen, GOLD,
                               pygame.Rect(indicator_x, nav_height-indicator_height,
                                          indicator_width, indicator_height),
                               border_radius=int(2 * min(self.scale_x, self.scale_y)))

                text_color = WHITE
            else:
                # Add hover detection for navigation items
                mouse_pos = pygame.mouse.get_pos()
                if item_rect.collidepoint(mouse_pos):
                    # Hover state - subtle highlight
                    hover_surface = pygame.Surface((item_rect.width * 0.7, item_rect.height), pygame.SRCALPHA)
                    pygame.draw.rect(hover_surface, (255, 255, 255, 15),
                                   (0, 0, hover_surface.get_width(), hover_surface.get_height()),
                                   border_radius=int(5 * min(self.scale_x, self.scale_y)))
                    self.screen.blit(hover_surface, (item_x + item_rect.width * 0.15, 0))

                text_color = (180, 180, 180)  # Slightly dimmed text for inactive items

            # Draw icons directly without relying on emoji rendering
            icon_size = int(16 * min(self.scale_x, self.scale_y))
            icon_x = item_x + item_width/2
            icon_y = nav_height/2 - int(7 * self.scale_y)

            # Draw custom icons based on navigation item
            if item["id"] == "play":
                # Game controller icon
                controller_rect = pygame.Rect(
                    icon_x - icon_size/2,
                    icon_y - icon_size/2,
                    icon_size, icon_size
                )
                # Main controller body
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/3, icon_y - icon_size/4,
                                          icon_size/1.5, icon_size/2),
                               border_radius=int(2 * min(self.scale_x, self.scale_y)))
                # Controller buttons
                pygame.draw.circle(self.screen, text_color,
                                 (icon_x - icon_size/4, icon_y + icon_size/6), icon_size/8)
                pygame.draw.circle(self.screen, text_color,
                                 (icon_x + icon_size/4, icon_y + icon_size/6), icon_size/8)

            elif item["id"] == "stats":
                # Stats bar chart icon
                bar_width = icon_size/5
                # Draw three bars of increasing height
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/2, icon_y,
                                          bar_width, icon_size/2))
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x - icon_size/6, icon_y - icon_size/4,
                                          bar_width, icon_size*3/4))
                pygame.draw.rect(self.screen, text_color,
                               pygame.Rect(icon_x + icon_size/6, icon_y - icon_size/2,
                                          bar_width, icon_size))

            elif item["id"] == "preview":
                # Preview (eye) icon
                eye_radius = icon_size/4
                # Draw outer eye shape (ellipse)
                pygame.draw.ellipse(self.screen, text_color,
                                  pygame.Rect(icon_x - icon_size/3, icon_y - eye_radius,
                                             icon_size*2/3, eye_radius*2), width=int(1.5 * min(self.scale_x, self.scale_y)))
                # Draw inner circle (pupil)
                pygame.draw.circle(self.screen, text_color, (icon_x, icon_y), eye_radius/2)
                # Draw highlight dot
                pygame.draw.circle(self.screen, (255, 255, 255),
                                 (icon_x - eye_radius/4, icon_y - eye_radius/4), eye_radius/4)

            elif item["id"] == "settings":
                # Settings gear icon
                gear_radius = icon_size/3
                # Draw gear circle
                pygame.draw.circle(self.screen, text_color, (icon_x, icon_y), gear_radius, width=int(1.5 * min(self.scale_x, self.scale_y)))
                # Draw gear teeth
                for angle in range(0, 360, 45):
                    rad = math.radians(angle)
                    pygame.draw.line(self.screen, text_color,
                                   (icon_x + gear_radius * math.cos(rad), icon_y + gear_radius * math.sin(rad)),
                                   (icon_x + (gear_radius+icon_size/4) * math.cos(rad), icon_y + (gear_radius+icon_size/4) * math.sin(rad)),
                                   width=int(2 * min(self.scale_x, self.scale_y)))

            elif item["id"] == "help":
                # Help (?) icon
                # Draw circle
                pygame.draw.circle(self.screen, text_color, (icon_x, icon_y), icon_size/3, width=int(1.5 * min(self.scale_x, self.scale_y)))
                # Draw question mark
                question_font = pygame.font.SysFont("Arial", int(icon_size/1.5), bold=True)
                question_text = question_font.render("?", True, text_color)
                question_rect = question_text.get_rect(center=(icon_x, icon_y))
                self.screen.blit(question_text, question_rect)

            # Text below icon
            text_surf = nav_font.render(item["text"], True, text_color)
            text_rect = text_surf.get_rect(center=(item_x + item_width/2, nav_height/2 + int(7 * self.scale_y)))
            self.screen.blit(text_surf, text_rect)

            # Store hit area for navigation item
            self.hit_areas[f"nav_{item['id']}"] = item_rect

    def scaled_font_size(self, base_size):
        """Scale font size based on screen dimensions"""
        return int(base_size * min(self.scale_x, self.scale_y))

    def set_prize_pool_manually(self, value):
        """Set the prize pool manually and activate override mode"""
        try:
            value = int(value)
            if value < 0:
                value = 0
            self.prize_pool = value
            self.prize_pool_manual_override = True

            # Save UI state if option is enabled
            if hasattr(self, 'remember_cartella_checkbox') and self.remember_cartella_checkbox:
                try:
                    from player_storage import save_ui_state
                    save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, True)
                    print(f"Updated UI state after setting prize pool manually: {self.prize_pool}")
                except Exception as e:
                    print(f"Error saving UI state after setting prize pool manually: {e}")

            self.show_message(f"Prize pool set manually to {value} ETB", "success")
        except ValueError:
            self.show_message("Invalid prize pool value", "error")

    def load_bingo_boards(self):
        """Load bingo boards from the JSON file"""
        try:
            # Path to the bingo boards file
            boards_file = os.path.join('data', 'bingo_boards.json')

            # Ensure the data directory exists
            os.makedirs('data', exist_ok=True)

            # Check if the file exists
            if not os.path.exists(boards_file):
                print(f"Bingo boards file not found at {boards_file}, creating new file")
                # Create an empty JSON file
                with open(boards_file, 'w') as file:
                    json.dump({}, file)
                return {}

            # Load the boards from the file
            try:
                with open(boards_file, 'r') as file:
                    file_content = file.read().strip()
                    if not file_content:  # File is empty
                        print(f"Bingo boards file is empty, initializing with empty dictionary")
                        with open(boards_file, 'w') as write_file:
                            json.dump({}, write_file)
                        return {}

                    boards = json.loads(file_content)
                    if not isinstance(boards, dict):
                        print(f"Invalid bingo boards format, expected dictionary but got {type(boards)}")
                        # Reset to empty dictionary
                        with open(boards_file, 'w') as write_file:
                            json.dump({}, write_file)
                        return {}

                    print(f"Loaded {len(boards)} bingo boards from {boards_file}")
                    return boards
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON in bingo boards file: {e}")
                # Reset to empty dictionary
                with open(boards_file, 'w') as write_file:
                    json.dump({}, write_file)
                return {}
        except Exception as e:
            print(f"Error loading bingo boards: {e}")
            return {}

    def create_board_from_number(self, cartella_number):
        """Create a bingo board based on the cartella number"""
        try:
            # Convert to string for dictionary lookup
            cartella_key = str(cartella_number)

            # Check if the board exists in the loaded boards
            if cartella_key in self.bingo_boards:
                print(f"Using existing board for cartella {cartella_number}")
                self.preview_board = self.bingo_boards[cartella_key]
            else:
                # If not found, create a deterministic board
                print(f"Creating deterministic board for cartella {cartella_number}")
                self.preview_board = self.create_deterministic_board(cartella_number)

                # Save the board for future use
                self.bingo_boards[cartella_key] = self.preview_board

                # Save the updated boards to the file, but first load any existing boards
                boards_file = os.path.join('data', 'bingo_boards.json')
                try:
                    # First load existing boards from file to avoid overwriting them
                    existing_boards = {}
                    if os.path.exists(boards_file):
                        with open(boards_file, 'r') as file:
                            try:
                                existing_boards = json.load(file)
                                print(f"Loaded {len(existing_boards)} existing boards from {boards_file}")
                            except json.JSONDecodeError:
                                print(f"Error parsing JSON in {boards_file}, creating new file")
                                existing_boards = {}

                    # Merge existing boards with the new board
                    existing_boards[cartella_key] = self.preview_board

                    # Save all boards back to the file
                    with open(boards_file, 'w') as file:
                        json.dump(existing_boards, file, indent=2)
                    print(f"Saved board for cartella {cartella_number} to {boards_file} (total boards: {len(existing_boards)})")

                    # Update our in-memory boards with all boards from file
                    self.bingo_boards = existing_boards
                except Exception as e:
                    print(f"Error saving board to file: {e}")
        except Exception as e:
            print(f"Error creating board from number: {e}")
            # Create a fallback board
            self.preview_board = self.create_fallback_board()

    def create_deterministic_board(self, cartella_number):
        """Create a deterministic board based on cartella number"""
        # Use the cartella number as seed for consistent results
        random.seed(cartella_number)

        board = []

        # For each column (B, I, N, G, O)
        for col in range(5):
            column_values = []
            # Range for this column: col*15+1 to col*15+15
            min_val = col * 15 + 1
            max_val = min_val + 14

            # Generate 5 unique random numbers for this column
            values = list(range(min_val, max_val + 1))
            random.shuffle(values)
            column_values = values[:5]

            # Set the center square (N column, 3rd row) to 0 (free space)
            if col == 2:
                column_values[2] = 0

            board.append(column_values)

        # Reset the random seed to avoid affecting other random operations
        random.seed()

        return board

    def create_fallback_board(self):
        """Create a fallback board if loading fails"""
        board = []

        # Create a simple board with sequential numbers
        for col in range(5):
            column = []
            for row in range(5):
                # Calculate the number based on column and row
                if col == 2 and row == 2:
                    # Free space in the center
                    number = 0
                else:
                    # Sequential numbers for each column
                    number = col * 15 + row + 1
                column.append(number)
            board.append(column)

        return board

def show_board_selection(screen, from_reset=False, maintain_fullscreen=True, game_instance=None):
    """
    Show the board selection window

    Args:
        screen: The pygame screen surface
        from_reset: Whether the board selection is being shown after a game reset
        maintain_fullscreen: If provided, ensures the screen stays in the specified mode (True=fullscreen, False=windowed)
                            Default is now True to start in maximized window mode
        game_instance: Optional reference to the main game instance for integration with other components

    Returns:
        List of selected cartella numbers
    """
    # Use the global screen mode manager to ensure consistent screen mode
    try:
        screen_mode_manager = get_screen_mode_manager()

        # If coming from reset, inherit the current screen mode from settings
        if from_reset:
            print("Board selection called from reset - ensuring screen mode consistency")
            screen = screen_mode_manager.ensure_consistent_mode(screen)
        else:
            # For non-reset scenarios, apply the maintain_fullscreen parameter if different from current setting
            current_setting = screen_mode_manager.get_current_screen_mode()
            if current_setting != maintain_fullscreen:
                # Update the setting and apply the requested mode
                screen_mode_manager.settings_manager.set_screen_mode(maintain_fullscreen)
                screen = screen_mode_manager.apply_screen_mode(screen, force_mode=maintain_fullscreen)
            else:
                # Ensure consistency with current setting
                screen = screen_mode_manager.ensure_consistent_mode(screen)

        print(f"Board selection screen mode: {'fullscreen' if screen_mode_manager.is_fullscreen() else 'windowed'}")

    except Exception as e:
        print(f"Error applying screen mode in board selection: {e}")
        # Fallback to original logic if screen mode manager fails
        current_is_fullscreen = bool(screen.get_flags() & pygame.FULLSCREEN)
        if current_is_fullscreen != maintain_fullscreen:
            if maintain_fullscreen:
                screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
            else:
                screen_info = pygame.display.Info()
                window_width = int(screen_info.current_w * 0.85)
                window_height = int(screen_info.current_h * 0.85)
                screen = pygame.display.set_mode((window_width, window_height), pygame.RESIZABLE)

    # Create and run the board selection window with animation if coming from reset
    board_selection = BoardSelectionWindow(screen, from_reset=from_reset)

    # Pass the game_instance to the board selection window if provided
    if game_instance:
        board_selection.game_instance = game_instance
        # Store a reference to the board selection in the game instance
        game_instance.board_selection = board_selection

    # Explicitly set transition to None to avoid errors when coming from reset
    if from_reset:
        # Disable transition animation to avoid errors
        board_selection.transition = None
        print("Disabled transition animation for reset scenario")

    selected_cartellas = board_selection.run()

    return selected_cartellas

# Add this section to allow running this file directly
if __name__ == "__main__":
    # Initialize pygame
    pygame.init()

    # Get the current screen info for maximized window
    screen_info = pygame.display.Info()

    # Set up the display in fullscreen mode
    flags = pygame.FULLSCREEN | pygame.DOUBLEBUF
    screen = pygame.display.set_mode((0, 0), flags)
    pygame.display.set_caption("WOW BINGO - Board Selection")

    # Show an information message about the fullscreen toggle
    info_font = pygame.font.SysFont("Arial", 24)
    info_text = info_font.render("Press F to toggle fullscreen mode", True, WHITE)
    screen.fill(DARK_BLUE)
    screen.blit(info_text, (screen.get_width()//2 - info_text.get_width()//2, screen.get_height()//2))
    pygame.display.flip()
    pygame.time.delay(1500)  # Show for 1.5 seconds

    # Run the board selection screen (maintain_fullscreen=True is the default now)
    selected_numbers = show_board_selection(screen)

    # Display selected numbers or exit message
    if selected_numbers:
        print(f"Selected cartella numbers: {selected_numbers}")
    else:
        print("No cartella numbers were selected")

    # Clean up
    pygame.quit()
    sys.exit()

