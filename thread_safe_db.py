"""
Thread-Safe Database Access Module

This module provides thread-safe database access functions for the WOW Games application.
It ensures that SQLite connections are properly managed across threads.
"""

import os
import sqlite3
import threading
import logging
import json
from datetime import datetime, timedelta
import time

# Debug flag - set to True to enable detailed debugging
DEBUG_MODE = True

def debug_log(message):
    """Log debug messages with a timestamp."""
    if DEBUG_MODE:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print(f"[DB DEBUG {timestamp}] {message}")

        # Also write to a log file
        try:
            os.makedirs('data', exist_ok=True)
            with open(os.path.join('data', 'thread_safe_db.log'), 'a', encoding='utf-8') as f:
                f.write(f"{timestamp} - {message}\n")
        except Exception as e:
            print(f"Error writing to log file: {e}")

# Constants
DATA_DIR = os.path.join('data')
STATS_DB_PATH = os.path.join(DATA_DIR, 'stats.db')
STATS_JSON_PATH = os.path.join(DATA_DIR, 'stats.json')

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'thread_safe_db.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Thread-local storage for database connections
_thread_local = threading.local()

# Lock for thread safety
_db_lock = threading.RLock()

# Connection timeout
_connection_timeout = 300  # 5 minutes before automatic connection close

def get_connection():
    """
    Get a connection to the database.

    Returns:
        sqlite3.Connection: Connection to the database
    """
    # Check if this thread already has a connection
    if not hasattr(_thread_local, 'connection') or _thread_local.connection is None:
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(STATS_DB_PATH), exist_ok=True)

        # Create new connection for this thread
        debug_log(f"Creating new thread-specific database connection to {STATS_DB_PATH} for thread {threading.current_thread().ident}")
        _thread_local.connection = sqlite3.connect(STATS_DB_PATH)
        _thread_local.last_connection_time = time.time()
        debug_log(f"New database connection created successfully for thread {threading.current_thread().ident}")
    else:
        # Check if connection is too old
        current_time = time.time()
        if current_time - _thread_local.last_connection_time > _connection_timeout:
            # Close and recreate connection
            try:
                _thread_local.connection.close()
            except Exception as e:
                debug_log(f"Error closing old connection: {e}")

            debug_log(f"Recreating expired connection for thread {threading.current_thread().ident}")
            _thread_local.connection = sqlite3.connect(STATS_DB_PATH)
            _thread_local.last_connection_time = current_time
        else:
            # Update last connection time
            _thread_local.last_connection_time = current_time
            debug_log(f"Using existing connection for thread {threading.current_thread().ident}")

    return _thread_local.connection

def close_connection():
    """Close the database connection for the current thread."""
    if hasattr(_thread_local, 'connection') and _thread_local.connection is not None:
        try:
            _thread_local.connection.close()
            _thread_local.connection = None
            debug_log(f"Database connection closed for thread {threading.current_thread().ident}")
        except Exception as e:
            debug_log(f"Error closing database connection: {e}")

def ensure_database_exists():
    """
    Ensure the stats database exists and has the required tables.
    This should be called at game startup.
    """
    debug_log(f"ensure_database_exists called from thread {threading.current_thread().ident}")

    # Ensure data directory exists
    os.makedirs(DATA_DIR, exist_ok=True)

    # Get a thread-specific connection
    conn = get_connection()
    cursor = conn.cursor()

    # Create daily_stats table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_stats (
        date TEXT PRIMARY KEY,
        games_played INTEGER DEFAULT 0,
        earnings REAL DEFAULT 0,
        winners INTEGER DEFAULT 0,
        total_players INTEGER DEFAULT 0
    )
    ''')

    # Create game_history table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS game_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date_time TEXT,
        username TEXT,
        house TEXT,
        stake REAL,
        players INTEGER,
        total_calls INTEGER,
        commission_percent REAL,
        fee REAL,
        total_prize REAL,
        details TEXT,
        status TEXT
    )
    ''')

    # Create wallet_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS wallet_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date_time TEXT,
        amount REAL,
        transaction_type TEXT,
        description TEXT,
        balance_after REAL
    )
    ''')

    # Create indices for better performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_game_history_date_time ON game_history(date_time)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date_time ON wallet_transactions(date_time)')

    # Commit changes
    conn.commit()

    logging.info("Database schema initialized successfully")
    debug_log("Stats database initialized successfully")
    print("Stats database initialized successfully")

def record_game_started(player_count, bet_amount=50, is_demo_mode=False):
    """
    Record a game start event in the stats database.

    Args:
        player_count: Number of players in the game
        bet_amount: Bet amount per player
        is_demo_mode: Boolean indicating if the game is in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if is_demo_mode:
        print("Game started in demo mode - statistics not updated")
        return False

    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        today = datetime.now().strftime('%Y-%m-%d')

        # Update daily stats
        cursor.execute('''
        INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
        VALUES (?, 0, 0, 0, 0)
        ''', (today,))

        # Update player count if needed
        cursor.execute('''
        UPDATE daily_stats SET total_players = total_players + ? WHERE date = ?
        ''', (player_count, today))

        # Commit changes
        conn.commit()

        logging.info(f"Game start event recorded in database (players: {player_count})")
        print("Game start event recorded in database")
        return True
    except Exception as e:
        logging.error(f"Error recording game start event in database: {e}")
        print(f"Error recording game start event in database: {e}")

        # Fallback to JSON storage
        try:
            # Load current statistics
            stats_data = _load_json_stats()

            # Format current time
            current_time = datetime.now().strftime("%H:%M")

            # Create activity entry
            activity = {
                "time": current_time,
                "event": f"Game started with {player_count} players"
            }

            # Add to recent activity (keep most recent 10)
            recent_activity = stats_data.get("recent_activity", [])
            recent_activity.insert(0, activity)  # Add at beginning
            stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

            # Update player count if needed
            stats_data["player_count"] = max(stats_data["player_count"], player_count)

            # Save updated statistics
            _save_json_stats(stats_data)

            logging.info("Game start event added to JSON stats (fallback)")
            print("Game start event added to JSON stats")
            return True
        except Exception as json_e:
            logging.error(f"Error saving to JSON stats: {json_e}")
            print(f"Error saving to JSON stats: {json_e}")
            return False

def record_game_completed(game_data):
    """
    Record a completed game in the stats database.

    Args:
        game_data: Dictionary containing game data
            - winner_name: Name of the winner
            - winner_cartella: Cartella number of the winner
            - claim_type: Type of claim (e.g., 'Full House', 'First Line')
            - game_duration: Duration of the game in seconds
            - player_count: Number of players in the game
            - prize_amount: Prize amount for this game
            - commission_percentage: Commission percentage for this game
            - called_numbers: List of numbers called during the game
            - is_demo_mode: Boolean indicating if the game was in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Debug print to help diagnose issues
    print("=" * 50)
    print("RECORDING GAME COMPLETED IN THREAD_SAFE_DB")
    print(f"Game data: {game_data}")
    print("=" * 50)

    # Log to file for better tracking
    logging.info("RECORDING GAME COMPLETED IN THREAD_SAFE_DB")
    logging.info(f"Game data: {game_data}")

    # Skip if in demo mode - with extra logging
    is_demo_mode = game_data.get("is_demo_mode", False)
    if is_demo_mode:
        print("Game was in demo mode - statistics not updated")
        print(f"Full game data: {game_data}")
        return False

    print("IMPORTANT: Recording real game data to database (not in demo mode)")

    try:
        # Get connection for this thread
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        today = datetime.now().strftime('%Y-%m-%d')

        # Update daily stats
        cursor.execute('''
        INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
        VALUES (?, 0, 0, 0, 0)
        ''', (today,))

        # Increment games played
        cursor.execute('''
        UPDATE daily_stats SET games_played = games_played + 1 WHERE date = ?
        ''', (today,))

        # Calculate fee based on prize amount or default values
        fee = 0

        # Get prize amount, defaulting to a calculated value if not available
        prize_amount = game_data.get('prize_amount', 0)
        if prize_amount == 0:
            # Calculate a default prize amount based on stake and player count
            stake = game_data.get('stake', 50)  # Default to 50 if not specified
            player_count = game_data.get('player_count', 4)  # Default to 4 if not specified
            prize_amount = stake * player_count * 0.8  # 80% of total stakes
            print(f"CRITICAL: Calculated default prize amount of {prize_amount} for fee calculation")

        # Calculate fee based on prize amount and commission percentage
        commission_percentage = game_data.get('commission_percentage', 20)
        fee = prize_amount * (commission_percentage / 100)
        print(f"CRITICAL: Calculated fee of {fee} based on prize amount {prize_amount} and commission {commission_percentage}%")

        # Update daily stats with the fee - with retry logic
        try:
            cursor.execute('''
            UPDATE daily_stats SET earnings = earnings + ? WHERE date = ?
            ''', (fee, today))
            print(f"CRITICAL: Updated daily stats with earnings += {fee}")
        except Exception as update_e:
            print(f"Error updating daily stats earnings: {update_e}")
            # Try again with a new connection
            print("Retrying with a fresh connection...")
            conn.close()
            conn = sqlite3.connect(STATS_DB_PATH)
            cursor = conn.cursor()
            cursor.execute('''
            UPDATE daily_stats SET earnings = earnings + ? WHERE date = ?
            ''', (fee, today))
            print(f"CRITICAL: Updated daily stats with earnings += {fee} (retry successful)")

        # Add winner if available
        if 'winner_name' in game_data and game_data['winner_name']:
            cursor.execute('''
            UPDATE daily_stats SET winners = winners + 1 WHERE date = ?
            ''', (today,))

        # Ensure we have non-zero values for key fields - FIXED to use correct default
        stake = game_data.get('stake', 0)
        if stake == 0:
            # Try to get from bet_amount first, then use updated default
            stake = game_data.get('bet_amount', 25)  # Updated default to match current settings
            print(f"CRITICAL: Using stake value of {stake} (from bet_amount or default)")

        player_count = game_data.get('player_count', 0)
        if player_count == 0:
            # Default to 4 if not specified
            player_count = 4
            print(f"CRITICAL: Using default player count of {player_count}")

        prize_amount = game_data.get('prize_amount', 0)
        if prize_amount == 0:
            # Calculate a default prize amount based on stake and player count
            prize_amount = stake * player_count * 0.8  # 80% of total stakes
            print(f"CRITICAL: Calculated default prize amount of {prize_amount}")

        # Get current date and time
        event_datetime_str = game_data.get('date_time')
        if event_datetime_str:
            # Attempt to parse it, assuming a common format like YYYY-MM-DD HH:MM:SS
            try:
                # Ensure it's a string before parsing
                if isinstance(event_datetime_str, datetime):
                    event_datetime = event_datetime_str.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    # Try parsing if it's a string
                    datetime.strptime(str(event_datetime_str), '%Y-%m-%d %H:%M:%S') # Validate format
                    event_datetime = str(event_datetime_str)
                print(f"Using provided event date_time: {event_datetime}")
            except (ValueError, TypeError):
                event_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"CRITICAL: Invalid or missing event date_time, using current date and time for game record: {event_datetime}")
        else:
            event_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"CRITICAL: Missing event date_time, using current date and time for game record: {event_datetime}")

        # Insert into game history with guaranteed non-zero values and event date
        cursor.execute('''
        INSERT INTO game_history (
            date_time, username, house, stake, players, total_calls,
            commission_percent, fee, total_prize, details, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            event_datetime,
            game_data.get('winner_name', 'Unknown'),
            game_data.get('house', 'Main House'),
            stake,
            player_count,
            len(game_data.get('called_numbers', [])),
            game_data.get('commission_percentage', 20),
            fee,
            prize_amount,
            json.dumps(game_data),
            'Won'
        ))

        print(f"CRITICAL: Inserted game history record with stake={stake}, players={player_count}, prize={prize_amount}, fee={fee}")

        # Get the ID of the new record
        game_id = cursor.lastrowid

        # Commit changes
        conn.commit()

        # Verify the data was actually saved
        try:
            cursor.execute('SELECT COUNT(*) FROM game_history')
            count = cursor.fetchone()[0]
            print(f"Verified game_history now has {count} records")

            # Get the most recent game record
            cursor.execute('SELECT id, date_time, username FROM game_history ORDER BY id DESC LIMIT 1')
            latest = cursor.fetchone()
            if latest:
                print(f"Latest game record: ID={latest[0]}, Date={latest[1]}, Winner={latest[2]}")

            # Verify daily stats was updated
            cursor.execute('SELECT games_played, earnings FROM daily_stats WHERE date = ?', (today,))
            daily = cursor.fetchone()
            if daily:
                print(f"Daily stats for {today}: games_played={daily[0]}, earnings={daily[1]}")
        except Exception as verify_e:
            print(f"Error verifying game record: {verify_e}")

        # Force another commit to be absolutely sure
        try:
            conn.commit()
            print("Forced second commit to ensure data is saved")

            # Verify data after commit
            cursor.execute('SELECT COUNT(*) FROM game_history')
            count = cursor.fetchone()[0]
            print(f"VERIFICATION: Database has {count} game history records after second commit")

            # Get the most recent record ID
            cursor.execute('SELECT MAX(id) FROM game_history')
            max_id = cursor.fetchone()[0]
            print(f"VERIFICATION: Latest game record ID: {max_id}")
        except Exception as commit_e:
            print(f"Error in second commit: {commit_e}")

        logging.info(f"Game statistics recorded in database (ID: {game_id})")
        print(f"Game statistics recorded in database (ID: {game_id})")

        # Add enhanced logging for game winner recording
        print(f"Game winner recorded via thread_safe_db.py - Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
        try:
            logging.info(f"Game winner recorded via thread_safe_db.py - Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
        except ImportError:
            pass

        # Close and reopen the connection to ensure data is saved
        try:
            # Force another commit before closing
            conn.commit()
            print("Forced final commit before closing connection")

            # Close connection
            close_connection()
            print("Closed connection to ensure data is saved")

            # Verify the data was saved by opening a new connection and checking
            verify_conn = sqlite3.connect(STATS_DB_PATH)
            verify_cursor = verify_conn.cursor()
            verify_cursor.execute('SELECT COUNT(*) FROM game_history')
            count = verify_cursor.fetchone()[0]
            print(f"VERIFICATION: Database has {count} game history records after closing/reopening")
            verify_conn.close()
        except Exception as close_e:
            print(f"Error during connection close/verification: {close_e}")

        # Record to log file that this game was successfully recorded
        try:
            with open(os.path.join('data', 'successful_game_records.log'), 'a') as log_file:
                log_file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Recorded game: Winner={game_data.get('winner_name', 'Unknown')}, Players={game_data.get('player_count', 0)}\n")
        except Exception as log_e:
            print(f"Error writing to success log: {log_e}")

        return True
    except Exception as e:
        logging.error(f"Error recording game statistics in database: {e}")
        print(f"Error recording game statistics in database: {e}")

        # Fallback to JSON storage
        try:
            # Load current statistics
            stats_data = _load_json_stats()

            # Update statistics with new game data
            stats_data["games_played"] += 1
            stats_data["total_winners"] += 1

            # Update prize pool
            if "prize_amount" in game_data and game_data["prize_amount"] > 0:
                stats_data["total_prize_pool"] += game_data["prize_amount"]

            # Update player count
            if "player_count" in game_data and game_data["player_count"] > 0:
                stats_data["player_count"] = max(stats_data["player_count"], game_data["player_count"])

            # Update average game duration
            if "game_duration" in game_data and game_data["game_duration"] > 0:
                current_avg = stats_data["average_game_duration"]
                games_played = stats_data["games_played"]

                if games_played > 1:
                    # Calculate new average
                    total_duration = current_avg * (games_played - 1)
                    total_duration += game_data["game_duration"]
                    stats_data["average_game_duration"] = total_duration / games_played
                else:
                    # First game, just use its duration
                    stats_data["average_game_duration"] = game_data["game_duration"]

            # Update number frequencies
            if "called_numbers" in game_data and game_data["called_numbers"]:
                number_freq = stats_data.get("number_frequencies", {})
                for num in game_data["called_numbers"]:
                    if str(num) in number_freq:
                        number_freq[str(num)] += 1
                    else:
                        number_freq[str(num)] = 1
                stats_data["number_frequencies"] = number_freq

            # Add to recent activity
            if "winner_name" in game_data and "claim_type" in game_data:
                # Format current time
                current_time = datetime.now().strftime("%H:%M")

                # Create activity entry
                activity = {
                    "time": current_time,
                    "event": f"Player '{game_data['winner_name']}' won with {game_data['claim_type']}"
                }

                # Add to recent activity (keep most recent 10)
                recent_activity = stats_data.get("recent_activity", [])
                recent_activity.insert(0, activity)  # Add at beginning
                stats_data["recent_activity"] = recent_activity[:10]  # Keep only most recent 10

            # Save updated statistics
            _save_json_stats(stats_data)

            logging.info("Game statistics updated in JSON stats (fallback)")
            print("Game statistics updated in JSON stats")
            return True
        except Exception as json_e:
            logging.error(f"Error saving to JSON stats: {json_e}")
            print(f"Error saving to JSON stats: {json_e}")
            return False

def get_game_history(page=0, page_size=10):
    """
    Get game history from the database.

    Args:
        page: Page number (0-based)
        page_size: Number of records per page

    Returns:
        tuple: (list of game history records, total pages)
    """
    try:
        debug_log(f"get_game_history called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # CRITICAL FIX: Check if we have any data with future dates (2025+)
        cursor.execute("SELECT COUNT(*) FROM game_history WHERE date_time >= '2025-01-01'")
        future_count = cursor.fetchone()[0]

        if future_count > 0:
            debug_log(f"CRITICAL: Found {future_count} records with future dates in game_history - fixing them")

            # Get all future date_times
            cursor.execute("SELECT id, date_time FROM game_history WHERE date_time >= '2025-01-01'")
            future_date_times = [(row[0], row[1]) for row in cursor.fetchall()]

            # Update each future date_time to a current date_time
            for i, (id, old_date_time) in enumerate(future_date_times):
                # Calculate new date_time (now - i hours)
                new_date_time = (datetime.now() - timedelta(hours=i)).strftime('%Y-%m-%d %H:%M:%S')

                # Update the date_time
                cursor.execute('UPDATE game_history SET date_time = ? WHERE id = ?', (new_date_time, id))
                debug_log(f"CRITICAL: Updated date_time in game_history: {old_date_time} -> {new_date_time}")

            # Commit changes
            conn.commit()
            debug_log("CRITICAL: Updated game_history date_times to current dates")

        # Calculate offset
        offset = page * page_size

        # Get total count for pagination
        cursor.execute('SELECT COUNT(*) FROM game_history')
        result = cursor.fetchone()
        total_games = result[0] if result else 0
        total_pages = max(1, (total_games + page_size - 1) // page_size)
        debug_log(f"Total games: {total_games}, total pages: {total_pages}")

        # Get game history for this page
        cursor.execute('''
        SELECT id, date_time, username, house, stake, players, total_calls,
               commission_percent, fee, total_prize, details, status
        FROM game_history
        ORDER BY date_time DESC
        LIMIT ? OFFSET ?
        ''', (page_size, offset))

        results = cursor.fetchall()
        debug_log(f"Query returned {len(results)} rows")

        # Convert to list of dictionaries
        history = []
        for row in results:
            # SQLite doesn't return dictionaries by default, so we need to handle both tuple and dict formats
            if isinstance(row, dict):
                # If row is already a dictionary, use it directly
                game_record = {
                    'id': row['id'],
                    'date_time': row['date_time'],
                    'username': row['username'],
                    'house': row['house'],
                    'stake': row['stake'],
                    'players': row['players'],
                    'total_calls': row['total_calls'],
                    'commission_percent': row['commission_percent'],
                    'fee': row['fee'],
                    'total_prize': row['total_prize'],
                    'details': row['details'],
                    'status': row['status']
                }
            else:
                # If row is a tuple, map it based on the column order in the SELECT statement
                game_record = {
                    'id': row[0],
                    'date_time': row[1],
                    'username': row[2],
                    'house': row[3],
                    'stake': row[4],
                    'players': row[5],
                    'total_calls': row[6],
                    'commission_percent': row[7],
                    'fee': row[8],
                    'total_prize': row[9],
                    'details': row[10],
                    'status': row[11]
                }

            # Log for debugging
            debug_log(f"Game record: ID={game_record['id']}, Date={game_record['date_time']}, Winner={game_record['username']}, Prize={game_record['total_prize']}")

            history.append(game_record)

        logging.info(f"Got game history page {page}: {len(history)} records")
        debug_log(f"Returning {len(history)} game history records")

        if len(history) > 0:
            debug_log(f"First record: {history[0]}")

        return history, total_pages
    except Exception as e:
        logging.error(f"Error getting game history: {e}")
        debug_log(f"Error getting game history: {e}")
        import traceback
        traceback.print_exc()
        return [], 1

def get_weekly_stats():
    """
    Get weekly statistics from the database.

    Returns:
        list: List of daily statistics for the week
    """
    try:
        debug_log(f"get_weekly_stats called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get current date
        end_date = datetime.now()

        # Calculate start date (6 days before end date to get 7 days total)
        start_date = end_date - timedelta(days=6)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        debug_log(f"Getting weekly stats from {start_date_str} to {end_date_str}")

        # Make sure the daily_stats table exists
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            date TEXT PRIMARY KEY,
            games_played INTEGER DEFAULT 0,
            earnings REAL DEFAULT 0,
            winners INTEGER DEFAULT 0,
            total_players INTEGER DEFAULT 0
        )
        ''')

        # Generate the full week with default values
        weekly_stats = []
        current_date = start_date

        # Insert default records for the week if they don't exist
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            cursor.execute('''
            INSERT OR IGNORE INTO daily_stats (date, games_played, earnings, winners, total_players)
            VALUES (?, 0, 0, 0, 0)
            ''', (date_str,))
            current_date += timedelta(days=1)

        # Commit the changes
        conn.commit()

        # Get weekly stats
        cursor.execute('''
        SELECT date, games_played, earnings, winners, total_players
        FROM daily_stats
        WHERE date BETWEEN ? AND ?
        ORDER BY date
        ''', (start_date_str, end_date_str))

        results = cursor.fetchall()
        debug_log(f"Query returned {len(results)} rows")

        # Convert to dictionary format
        stats_by_date = {}
        for row in results:
            if isinstance(row, dict):
                date = row['date']
                games_played = row['games_played'] or 0
                earnings = row['earnings'] or 0
                winners = row['winners'] or 0
                total_players = row['total_players'] or 0
            else:
                date = row[0]
                games_played = row[1] or 0
                earnings = row[2] or 0
                winners = row[3] or 0
                total_players = row[4] or 0
            stats_by_date[date] = {
                'date': date,
                'games_played': games_played,
                'earnings': earnings,
                'winners': winners,
                'total_players': total_players
            }
            debug_log(f"Row from database: {date} - {games_played} games, {earnings} earnings")

        # Generate the full week with values from database or defaults
        weekly_stats = []
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            if date_str in stats_by_date:
                weekly_stats.append(stats_by_date[date_str])
            else:
                weekly_stats.append({
                    'date': date_str,
                    'games_played': 0,
                    'earnings': 0,
                    'winners': 0,
                    'total_players': 0
                })
            current_date += timedelta(days=1)

        logging.info(f"Got weekly stats: {len(weekly_stats)} days")
        debug_log(f"Final weekly stats: {weekly_stats}")
        return weekly_stats
    except Exception as e:
        logging.error(f"Error getting weekly stats: {e}")
        debug_log(f"Error getting weekly stats: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_total_earnings():
    """
    Get the total earnings from the database.

    Returns:
        float: Total earnings
    """
    try:
        debug_log(f"get_total_earnings called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        total_earnings = result[0] if result and result[0] is not None else 0
        debug_log(f"Total earnings from database: {total_earnings}")

        return total_earnings
    except Exception as e:
        logging.error(f"Error getting total earnings: {e}")
        debug_log(f"Error getting total earnings: {e}")
        import traceback
        traceback.print_exc()
        return 0

def get_daily_earnings():
    """
    Get the daily earnings from the database for today.

    Returns:
        float: Daily earnings
    """
    try:
        debug_log(f"get_daily_earnings called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get today's date
        today = datetime.now().strftime('%Y-%m-%d')

        # Get daily earnings
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_earnings = result[0] if result and result[0] is not None else 0
        debug_log(f"Daily earnings from database: {daily_earnings}")

        return daily_earnings
    except Exception as e:
        logging.error(f"Error getting daily earnings: {e}")
        debug_log(f"Error getting daily earnings: {e}")
        import traceback
        traceback.print_exc()
        return 0

def get_daily_games_played():
    """
    Get the number of games played today.

    Returns:
        int: Number of games played today
    """
    try:
        debug_log(f"get_daily_games_played called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get today's date
        today = datetime.now().strftime('%Y-%m-%d')

        # Get daily games played
        cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_games = result[0] if result and result[0] is not None else 0
        debug_log(f"Daily games from database: {daily_games}")

        return daily_games
    except Exception as e:
        logging.error(f"Error getting daily games: {e}")
        debug_log(f"Error getting daily games: {e}")
        import traceback
        traceback.print_exc()
        return 0

def get_wallet_balance():
    """
    Get the current wallet balance.

    Returns:
        float: Current wallet balance
    """
    try:
        debug_log(f"get_wallet_balance called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get wallet balance
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0
        debug_log(f"Wallet balance from database: {wallet_balance}")

        return wallet_balance
    except Exception as e:
        logging.error(f"Error getting wallet balance: {e}")
        debug_log(f"Error getting wallet balance: {e}")
        import traceback
        traceback.print_exc()
        return 0

def get_summary_stats():
    """
    Get summary statistics from the database.

    Returns:
        dict: Dictionary of summary statistics
    """
    try:
        debug_log(f"get_summary_stats called from thread {threading.current_thread().ident}")

        # Get thread-specific connection
        conn = get_connection()
        cursor = conn.cursor()

        # Get total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        total_earnings = result[0] if result and result[0] is not None else 0
        debug_log(f"Total earnings from database: {total_earnings}")

        # Get today's date
        today = datetime.now().strftime('%Y-%m-%d')

        # Get daily earnings
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_earnings = result[0] if result and result[0] is not None else 0
        debug_log(f"Daily earnings from database: {daily_earnings}")

        # Get daily games played
        cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (today,))
        result = cursor.fetchone()
        daily_games = result[0] if result and result[0] is not None else 0
        debug_log(f"Daily games from database: {daily_games}")

        # Get wallet balance
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0
        debug_log(f"Wallet balance from database: {wallet_balance}")

        # Get total games played (for verification)
        cursor.execute('SELECT SUM(games_played) FROM daily_stats')
        result = cursor.fetchone()
        total_games = result[0] if result and result[0] is not None else 0
        debug_log(f"Total games played from database: {total_games}")

        # Get total winners (for verification)
        cursor.execute('SELECT SUM(winners) FROM daily_stats')
        result = cursor.fetchone()
        total_winners = result[0] if result and result[0] is not None else 0
        debug_log(f"Total winners from database: {total_winners}")

        summary = {
            'total_earnings': total_earnings,
            'daily_earnings': daily_earnings,
            'daily_games': daily_games,
            'wallet_balance': wallet_balance
        }

        logging.info("Got summary stats")
        debug_log(f"Returning summary stats: {summary}")
        return summary
    except Exception as e:
        logging.error(f"Error getting summary stats: {e}")
        debug_log(f"Error getting summary stats: {e}")
        import traceback
        traceback.print_exc()
        return {
            'total_earnings': 0,
            'daily_earnings': 0,
            'daily_games': 0,
            'wallet_balance': 0
        }

def _load_json_stats():
    """
    Load statistics from JSON file.

    Returns:
        dict: Statistics data
    """
    stats_data = {
        "games_played": 0,
        "total_winners": 0,
        "total_prize_pool": 0,
        "player_count": 0,
        "average_game_duration": 0,
        "top_players": [],
        "number_frequencies": {},
        "session_start_time": datetime.now().timestamp(),
        "recent_activity": []
    }

    # Load existing stats if available
    if os.path.exists(STATS_JSON_PATH):
        try:
            with open(STATS_JSON_PATH, 'r') as f:
                loaded_stats = json.load(f)
                # Update stats_data with loaded values
                for key, value in loaded_stats.items():
                    if key in stats_data:
                        stats_data[key] = value
        except Exception as e:
            logging.error(f"Error loading statistics from JSON: {e}")

    return stats_data

def _save_json_stats(stats_data):
    """
    Save statistics to JSON file.

    Args:
        stats_data: Dictionary containing statistics data
    """
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(STATS_JSON_PATH), exist_ok=True)

    # Save the stats data to file
    with open(STATS_JSON_PATH, 'w') as f:
        json.dump(stats_data, f, indent=4)

    logging.info(f"Statistics saved to {STATS_JSON_PATH}")

# Initialize database when module is imported
ensure_database_exists()
