import json
import os
import shutil
from view_players import Player
import time  # Add time module import at the top

# Default game settings
DEFAULT_GAME_SETTINGS = {
    'commission_percentage': 20.0,
    'bet_amount': 50,
    'prize_pool': 0,
    'prize_pool_manual_override': False,
    'number_call_delay': 3.0  # Add number_call_delay to persist game caller speed
}

# Filename for remembered cartella numbers
REMEMBERED_CARTELLAS_FILE = 'remembered_cartellas.json'

# Filename for UI state
UI_STATE_FILE = 'ui_state.json'

def save_players_to_json(players, filename='players.json'):
    """
    Save players list to a JSON file with backup

    Args:
        players: List of Player objects
        filename: Path to the JSON file

    Returns:
        bool: True if save was successful, False otherwise
    """
    try:
        # Create players directory if it doesn't exist
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)

        # Create backup of existing file if it exists
        if os.path.exists(filepath):
            backup_path = filepath + '.bak'
            shutil.copy2(filepath, backup_path)

        # Convert players to dictionary format
        players_data = [player.to_dict() for player in players]

        # Save to JSON file with pretty formatting
        with open(filepath, 'w') as f:
            json.dump(players_data, f, indent=4)

        return True
    except Exception as e:
        print(f"Error saving players: {str(e)}")
        return False

def load_players_from_json(filename='players.json'):
    """
    Load players from a JSON file

    Args:
        filename: Path to the JSON file

    Returns:
        list: List of Player objects, or empty list if file not found or invalid
    """
    try:
        filepath = os.path.join('data', filename)

        # If file doesn't exist, return empty list
        if not os.path.exists(filepath):
            return []

        # Try to load from main file
        try:
            with open(filepath, 'r') as f:
                players_data = json.load(f)

            # Validate and convert data to Player objects
            players = []
            for player_data in players_data:
                # Basic validation
                required_fields = ['cartela_no', 'bet_amount', 'deposited']
                if all(field in player_data for field in required_fields):
                    players.append(Player.from_dict(player_data))

            return players

        except json.JSONDecodeError:
            # If main file is corrupt, try backup
            backup_path = filepath + '.bak'
            if os.path.exists(backup_path):
                print(f"Main file corrupt, trying backup: {backup_path}")
                with open(backup_path, 'r') as f:
                    players_data = json.load(f)

                # Convert data to Player objects
                players = []
                for player_data in players_data:
                    players.append(Player.from_dict(player_data))

                return players
            else:
                print("No valid backup found")
                return []

    except Exception as e:
        print(f"Error loading players: {str(e)}")
        return []

def save_game_settings(settings, filename='game_settings.json'):
    """
    Save game settings to a JSON file with backup

    Args:
        settings: Dictionary of game settings
        filename: Path to the JSON file

    Returns:
        bool: True if save was successful, False otherwise
    """
    try:
        # Create data directory if it doesn't exist
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)

        # Create backup of existing file if it exists
        if os.path.exists(filepath):
            backup_path = filepath + '.bak'
            shutil.copy2(filepath, backup_path)

            # Try to load existing settings to preserve number_call_delay
            try:
                with open(filepath, 'r') as f:
                    existing_settings = json.load(f)

                # If number_call_delay exists in existing settings but not in new settings,
                # copy it over to preserve it
                if 'number_call_delay' in existing_settings and 'number_call_delay' not in settings:
                    settings['number_call_delay'] = existing_settings['number_call_delay']
                    print(f"Preserved number_call_delay={existing_settings['number_call_delay']} from existing settings")
            except Exception as e:
                print(f"Error loading existing settings: {e}")
                # If we can't load existing settings, ensure number_call_delay has a default value
                if 'number_call_delay' not in settings:
                    settings['number_call_delay'] = DEFAULT_GAME_SETTINGS['number_call_delay']
                    print(f"Using default number_call_delay={settings['number_call_delay']}")
        else:
            # If file doesn't exist, ensure number_call_delay has a default value
            if 'number_call_delay' not in settings:
                settings['number_call_delay'] = DEFAULT_GAME_SETTINGS['number_call_delay']
                print(f"Using default number_call_delay={settings['number_call_delay']}")

        # Save to JSON file with pretty formatting
        with open(filepath, 'w') as f:
            json.dump(settings, f, indent=4)

        print(f"Game settings saved successfully: {settings}")
        return True
    except Exception as e:
        print(f"Error saving game settings: {str(e)}")
        return False

def load_game_settings(filename='game_settings.json'):
    """
    Load game settings from a JSON file

    Args:
        filename: Path to the JSON file

    Returns:
        dict: Dictionary of game settings, or default settings if file not found or invalid
    """
    try:
        filepath = os.path.join('data', filename)

        # If file doesn't exist, return default settings
        if not os.path.exists(filepath):
            print(f"Game settings file not found, using defaults: {DEFAULT_GAME_SETTINGS}")
            return DEFAULT_GAME_SETTINGS.copy()

        # Try to load from main file
        try:
            with open(filepath, 'r') as f:
                settings = json.load(f)

            # Ensure all required settings are present
            for key, default_value in DEFAULT_GAME_SETTINGS.items():
                if key not in settings:
                    settings[key] = default_value

            print(f"Game settings loaded successfully: {settings}")
            return settings

        except json.JSONDecodeError:
            # If main file is corrupt, try backup
            backup_path = filepath + '.bak'
            if os.path.exists(backup_path):
                print(f"Main settings file corrupt, trying backup: {backup_path}")
                with open(backup_path, 'r') as f:
                    settings = json.load(f)

                # Ensure all required settings are present
                for key, default_value in DEFAULT_GAME_SETTINGS.items():
                    if key not in settings:
                        settings[key] = default_value

                return settings
            else:
                print("No valid backup found, using default settings")
                return DEFAULT_GAME_SETTINGS.copy()

    except Exception as e:
        print(f"Error loading game settings: {str(e)}")
        return DEFAULT_GAME_SETTINGS.copy()

def save_ui_state(cartella_numbers, bet_amount, prize_pool=None, prize_pool_manual_override=False):
    """
    Save complete UI state in a single function

    Args:
        cartella_numbers: List of cartella numbers to remember
        bet_amount: The bet amount to remember
        prize_pool: The prize pool value to remember (optional)
        prize_pool_manual_override: Whether the prize pool is in manual override mode

    Returns:
        bool: True if save was successful, False otherwise
    """
    try:
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', UI_STATE_FILE)
        if os.path.exists(filepath):
            backup_path = filepath + '.bak'
            shutil.copy2(filepath, backup_path)

        state = {
            'cartella_numbers': cartella_numbers,
            'bet_amount': bet_amount,
            'prize_pool': prize_pool,
            'prize_pool_manual_override': prize_pool_manual_override,
            'timestamp': time.time()  # Add timestamp for debugging
        }

        with open(filepath, 'w') as f:
            json.dump(state, f, indent=4)
        print(f"UI state saved: {len(cartella_numbers)} cartellas, bet={bet_amount}, prize={prize_pool}, override={prize_pool_manual_override}")
        return True
    except Exception as e:
        print(f"Error saving UI state: {str(e)}")
        return False

def load_ui_state():
    """
    Load complete UI state in a single function

    Returns:
        dict: Complete UI state or default state if file not found
    """
    default_state = {
        'cartella_numbers': [],
        'bet_amount': 50,  # Default bet amount
        'prize_pool': None,
        'prize_pool_manual_override': False
    }

    try:
        filepath = os.path.join('data', UI_STATE_FILE)
        if not os.path.exists(filepath):
            return default_state

        try:
            with open(filepath, 'r') as f:
                state = json.load(f)
            print(f"UI state loaded: {len(state.get('cartella_numbers', []))} cartellas, bet={state.get('bet_amount')}")
            return state
        except json.JSONDecodeError:
            # Try backup if main file is corrupt
            backup_path = filepath + '.bak'
            if os.path.exists(backup_path):
                with open(backup_path, 'r') as f:
                    state = json.load(f)
                return state
            else:
                return default_state
    except Exception as e:
        print(f"Error loading UI state: {str(e)}")
        return default_state

# For backward compatibility
def save_remembered_cartellas(cartella_numbers, prize_pool=None, prize_pool_manual_override=False, bet_amount=None):
    """Legacy function for backward compatibility"""
    return save_ui_state(cartella_numbers, bet_amount or 50, prize_pool, prize_pool_manual_override)

def load_remembered_cartellas():
    """Legacy function for backward compatibility"""
    state = load_ui_state()
    return {
        'cartella_numbers': state['cartella_numbers'],
        'prize_pool': state['prize_pool'],
        'prize_pool_manual_override': state['prize_pool_manual_override'],
        'bet_amount': state['bet_amount']
    }
