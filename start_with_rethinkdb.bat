@echo off
echo Starting WOW Games with RethinkDB integration...

REM Check if RethinkDB server is running
python setup_rethinkdb.py --check

REM Start RethinkDB server if it's not running
IF %ERRORLEVEL% NEQ 0 (
    echo RethinkDB server is not running, attempting to start...
    python setup_rethinkdb.py --start
    timeout /t 5
)

REM Check RethinkDB status
python rethink_status.py --status

REM Start the game
python main.py
