@echo off
:: ================================================================
:: WOW Bingo Game - Easy Build Script for Windows
:: ================================================================
:: This batch file provides an easy way to build the WOW Bingo Game
:: executable that can run on any Windows PC without Python.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "MAGENTA=%ESC%[35m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Easy Build System%RESET%
echo %CYAN%================================================================%RESET%
echo.
echo %BLUE%This script will build a standalone executable that can run%RESET%
echo %BLUE%on any Windows PC without requiring Python installation.%RESET%
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found in PATH!%RESET%
    echo %YELLOW%Please install Python 3.7+ and add it to your PATH.%RESET%
    echo %YELLOW%Download from: https://www.python.org/downloads/%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Python found%RESET%
python --version

:: Check if build script exists
if not exist "build_executable.py" (
    echo %RED%Error: build_executable.py not found!%RESET%
    echo %YELLOW%Please ensure you're running this from the game directory.%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Build script found%RESET%

:: Display menu
:menu
echo.
echo %BLUE%Please select a build option:%RESET%
echo.
echo %GREEN%1.%RESET% Quick Build (Recommended for first-time users)
echo %GREEN%2.%RESET% Install Dependencies + Build
echo %GREEN%3.%RESET% Clean Build (Remove previous builds first)
echo %GREEN%4.%RESET% Optimized Build (Slower but smaller executable)
echo %GREEN%5.%RESET% Test Build (Build + Test the executable)
echo %GREEN%6.%RESET% Advanced Options
echo %GREEN%7.%RESET% Exit
echo.
set /p "choice=Enter your choice (1-7): "

:: Process user choice
if "%choice%"=="1" goto quick_build
if "%choice%"=="2" goto install_and_build
if "%choice%"=="3" goto clean_build
if "%choice%"=="4" goto optimized_build
if "%choice%"=="5" goto test_build
if "%choice%"=="6" goto advanced_menu
if "%choice%"=="7" goto exit
echo %RED%Invalid choice. Please try again.%RESET%
echo.
goto menu

:quick_build
echo %CYAN%Starting quick build...%RESET%
python build_executable.py
goto build_complete

:install_and_build
echo %CYAN%Installing dependencies and building...%RESET%
python build_executable.py --install-deps
goto build_complete

:clean_build
echo %CYAN%Starting clean build...%RESET%
python build_executable.py --clean
goto build_complete

:optimized_build
echo %CYAN%Starting optimized build (this will take longer)...%RESET%
python build_executable.py --optimize
goto build_complete

:test_build
echo %CYAN%Starting build with testing...%RESET%
python build_executable.py --test --verbose
goto build_complete

:advanced_menu
echo.
echo %BLUE%Advanced Build Options:%RESET%
echo.
echo %GREEN%1.%RESET% Build with Nuitka (Alternative compiler)
echo %GREEN%2.%RESET% Clean + Optimized Build
echo %GREEN%3.%RESET% Verbose Build (Show detailed output)
echo %GREEN%4.%RESET% Install Dependencies Only
echo %GREEN%5.%RESET% Back to Main Menu
echo.
set /p "adv_choice=Enter your choice (1-5): "

if "%adv_choice%"=="1" goto nuitka_build
if "%adv_choice%"=="2" goto clean_optimized_build
if "%adv_choice%"=="3" goto verbose_build
if "%adv_choice%"=="4" goto install_only
if "%adv_choice%"=="5" goto menu
echo %RED%Invalid choice. Please try again.%RESET%
goto advanced_menu

:nuitka_build
echo %CYAN%Building with Nuitka compiler...%RESET%
echo %YELLOW%Note: Nuitka may take longer but can produce smaller executables.%RESET%
python build_executable.py --tool nuitka --verbose
goto build_complete

:clean_optimized_build
echo %CYAN%Starting clean optimized build...%RESET%
python build_executable.py --clean --optimize --verbose
goto build_complete

:verbose_build
echo %CYAN%Starting verbose build...%RESET%
python build_executable.py --verbose
goto build_complete

:install_only
echo %CYAN%Installing build dependencies...%RESET%
python build_executable.py --install-deps
echo.
echo %GREEN%Dependencies installed successfully!%RESET%
echo %BLUE%You can now run any build option.%RESET%
pause
goto menu

:build_complete
echo.
if %errorlevel% equ 0 (
    echo %GREEN%================================================================%RESET%
    echo %GREEN%    BUILD COMPLETED SUCCESSFULLY!%RESET%
    echo %GREEN%================================================================%RESET%
    echo.
    echo %CYAN%Your executable has been created in the 'dist' directory.%RESET%
    echo %CYAN%You can now copy this executable to any Windows PC and run it.%RESET%
    echo.
    echo %BLUE%Files created:%RESET%
    if exist "dist\WOW_Bingo_Game.exe" (
        echo %GREEN%✓ dist\WOW_Bingo_Game.exe%RESET%
        for %%F in ("dist\WOW_Bingo_Game.exe") do echo %BLUE%  Size: %%~zF bytes%RESET%
    )
    if exist "dist\build_report.json" (
        echo %GREEN%✓ dist\build_report.json (Build details)%RESET%
    )
    echo.
    echo %YELLOW%Next steps:%RESET%
    echo %BLUE%1. Test the executable by running: dist\WOW_Bingo_Game.exe%RESET%
    echo %BLUE%2. Copy the executable to any Windows PC%RESET%
    echo %BLUE%3. The executable includes all required files and assets%RESET%
    echo.
) else (
    echo %RED%================================================================%RESET%
    echo %RED%    BUILD FAILED!%RESET%
    echo %RED%================================================================%RESET%
    echo.
    echo %YELLOW%Common solutions:%RESET%
    echo %BLUE%1. Try installing dependencies first (option 2)%RESET%
    echo %BLUE%2. Check that all required files are present%RESET%
    echo %BLUE%3. Run with verbose output for more details%RESET%
    echo %BLUE%4. Check the error messages above%RESET%
    echo.
)

echo %BLUE%Press any key to return to menu or Ctrl+C to exit...%RESET%
pause >nul
goto menu

:exit
echo.
echo %CYAN%Thank you for using the WOW Bingo Game build system!%RESET%
echo %BLUE%For support, check the README.md file or project documentation.%RESET%
pause
exit /b 0
