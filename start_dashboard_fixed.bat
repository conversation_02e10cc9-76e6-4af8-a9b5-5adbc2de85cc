@echo off
echo ========================================================
echo     WOW Games RethinkDB Dashboard
echo ========================================================
echo.

echo Step 1: Installing required packages...
python -m pip install flask rethinkdb==2.4.9
if %ERRORLEVEL% NEQ 0 (
    echo Error installing packages. Please try again.
    pause
    exit /b 1
)
echo Packages installed successfully.
echo.

echo Step 2: Creating a namespace fix file...
echo import rethinkdb > rethinkdb_fix.py
echo r = rethinkdb >> rethinkdb_fix.py
echo print("RethinkDB namespace fix applied") >> rethinkdb_fix.py
python rethinkdb_fix.py
echo RethinkDB namespace fixed.
echo.

echo Step 3: Starting the fixed dashboard...
echo.
echo ========================================================
echo     Dashboard will be available at:
echo     http://localhost:5000
echo.
echo     You can access it from other devices using:
echo     http://YOUR_IP_ADDRESS:5000
echo.
echo     Press Ctrl+C to stop the dashboard
echo ========================================================
echo.

python rethink_dashboard_fixed.py