"""
Fix dates in the stats database.

This script updates all dates in the stats database to use current dates instead of future dates.
"""

import os
import sqlite3
from datetime import datetime, timedelta

# Constants
STATS_DB_PATH = os.path.join('data', 'stats.db')

def fix_dates():
    """Fix dates in the stats database."""
    print("Fixing dates in the stats database...")
    
    # Connect to the database
    conn = sqlite3.connect(STATS_DB_PATH)
    cursor = conn.cursor()
    
    # Get current date
    today = datetime.now().strftime('%Y-%m-%d')
    
    # Update daily_stats table
    try:
        # First, get all dates from daily_stats
        cursor.execute('SELECT date FROM daily_stats')
        dates = [row[0] for row in cursor.fetchall()]
        
        print(f"Found {len(dates)} dates in daily_stats: {dates}")
        
        # Update each date to a current date
        for i, old_date in enumerate(dates):
            # Calculate new date (today - i days)
            new_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            
            # Update the date
            cursor.execute('UPDATE daily_stats SET date = ? WHERE date = ?', (new_date, old_date))
            print(f"Updated date in daily_stats: {old_date} -> {new_date}")
        
        # Commit changes
        conn.commit()
        print("Updated daily_stats dates")
    except Exception as e:
        print(f"Error updating daily_stats dates: {e}")
    
    # Update game_history table
    try:
        # First, get all date_time values from game_history
        cursor.execute('SELECT id, date_time FROM game_history')
        date_times = [(row[0], row[1]) for row in cursor.fetchall()]
        
        print(f"Found {len(date_times)} date_times in game_history")
        
        # Update each date_time to a current date_time
        for i, (id, old_date_time) in enumerate(date_times):
            # Calculate new date_time (now - i hours)
            new_date_time = (datetime.now() - timedelta(hours=i)).strftime('%Y-%m-%d %H:%M:%S')
            
            # Update the date_time
            cursor.execute('UPDATE game_history SET date_time = ? WHERE id = ?', (new_date_time, id))
            print(f"Updated date_time in game_history: {old_date_time} -> {new_date_time}")
        
        # Commit changes
        conn.commit()
        print("Updated game_history date_times")
    except Exception as e:
        print(f"Error updating game_history date_times: {e}")
    
    # Close the connection
    conn.close()
    print("Finished fixing dates in the stats database")

if __name__ == "__main__":
    fix_dates()