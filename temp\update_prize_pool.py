import os
import re
import datetime
import shutil

def create_backup(filename):
    """Create a backup of the original file with timestamp"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"{filename}.{timestamp}.bak"
    shutil.copy2(filename, backup_filename)
    print(f"Created backup: {backup_filename}")
    return backup_filename

def update_prize_pool_method(file_path):
    """Update the set_prize_pool_manually method to include UI state saving"""
    
    # Create a backup before making changes
    backup_file = create_backup(file_path)
    
    # Read the content of the file
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Define the pattern to match the set_prize_pool_manually method
    pattern = r'(def set_prize_pool_manually\(self, value\):.*?""".*?"""\s*try:.*?value = int\(value\).*?if value < 0:.*?value = 0.*?self\.prize_pool = value.*?self\.prize_pool_manual_override = True)(\s*self\.show_message.*?"success"\).*?except ValueError:.*?self\.show_message.*?"error"\))'
    
    # Define the replacement with UI state saving
    replacement = r'\1\n        \n        # Save UI state if option is enabled\n        if hasattr(self, \'remember_cartella_checkbox\') and self.remember_cartella_checkbox:\n            try:\n                from player_storage import save_ui_state\n                save_ui_state(self.selected_cartella_numbers, self.bet_amount, self.prize_pool, True)\n                print(f"Updated UI state after setting prize pool manually: {self.prize_pool}")\n            except Exception as e:\n                print(f"Error saving UI state after setting prize pool manually: {e}")\n                \2'
    
    # Replace the method in the content using regex with re.DOTALL flag to match across lines
    updated_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # Check if any changes were made
    if updated_content == content:
        print("No changes made. Method pattern not found or already updated.")
        return False
    
    # Write the updated content back to the file
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print(f"Updated set_prize_pool_manually method in {file_path}")
    return True

if __name__ == "__main__":
    target_file = "Board_selection_fixed.py"
    
    if not os.path.exists(target_file):
        print(f"Error: File {target_file} not found.")
        exit(1)
    
    success = update_prize_pool_method(target_file)
    
    if success:
        print("Update completed successfully!")
    else:
        print("Update did not complete. No changes were made.") 