"""
Global Screen Mode Manager for WOW Games

This module provides centralized screen mode management across all pages
to ensure consistent fullscreen/windowed behavior throughout the application.
"""

import pygame
from settings_manager import SettingsManager


class ScreenModeManager:
    """
    Centralized manager for screen mode consistency across all pages
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        """Singleton pattern to ensure only one instance exists"""
        if cls._instance is None:
            cls._instance = super(ScreenModeManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the screen mode manager"""
        if not self._initialized:
            self.settings_manager = SettingsManager()
            self._current_screen = None
            self._windowed_size = (1280, 720)  # Default windowed size
            self._last_windowed_size = None
            self._screen_mode_callbacks = []  # Callbacks to notify when screen mode changes
            ScreenModeManager._initialized = True

    def register_screen_mode_callback(self, callback):
        """
        Register a callback to be called when screen mode changes

        Args:
            callback: Function to call when screen mode changes
        """
        if callback not in self._screen_mode_callbacks:
            self._screen_mode_callbacks.append(callback)

    def unregister_screen_mode_callback(self, callback):
        """
        Unregister a screen mode callback

        Args:
            callback: Function to remove from callbacks
        """
        if callback in self._screen_mode_callbacks:
            self._screen_mode_callbacks.remove(callback)

    def _notify_screen_mode_change(self, is_fullscreen):
        """
        Notify all registered callbacks about screen mode change

        Args:
            is_fullscreen (bool): New screen mode
        """
        for callback in self._screen_mode_callbacks:
            try:
                callback(is_fullscreen)
            except Exception as e:
                print(f"Error in screen mode callback: {e}")

    def get_current_screen_mode(self):
        """
        Get the current screen mode from settings

        Returns:
            bool: True if fullscreen, False if windowed
        """
        return self.settings_manager.get_current_screen_mode()

    def is_fullscreen(self):
        """
        Check if currently in fullscreen mode

        Returns:
            bool: True if fullscreen, False if windowed
        """
        if self._current_screen:
            return bool(self._current_screen.get_flags() & pygame.FULLSCREEN)
        return self.get_current_screen_mode()

    def set_screen_reference(self, screen):
        """
        Set the current screen reference for mode detection

        Args:
            screen: Pygame screen surface
        """
        self._current_screen = screen

        # Store windowed size if not in fullscreen
        if not self.is_fullscreen():
            self._windowed_size = screen.get_size()

    def apply_screen_mode(self, screen, force_mode=None):
        """
        Apply the current screen mode setting to the screen with external display support

        Args:
            screen: Pygame screen surface
            force_mode (bool, optional): Force specific mode (True=fullscreen, False=windowed)

        Returns:
            pygame.Surface: Updated screen surface
        """
        target_mode = force_mode if force_mode is not None else self.get_current_screen_mode()
        current_is_fullscreen = bool(screen.get_flags() & pygame.FULLSCREEN)

        # Only change mode if needed
        if current_is_fullscreen != target_mode:
            try:
                # Import external display manager for enhanced mode switching
                from external_display_manager import get_external_display_manager
                display_manager = get_external_display_manager()

                if target_mode:
                    # Switch to fullscreen with external display support
                    print("Switching to fullscreen mode with external display support")
                    # Store current windowed size before going fullscreen
                    if not current_is_fullscreen:
                        self._last_windowed_size = screen.get_size()

                    # Use external display manager for better compatibility
                    screen, _, _ = display_manager.create_compatible_display(fullscreen=True)
                else:
                    # Switch to windowed mode with external display support
                    print("Switching to windowed mode with external display support")
                    # Use last windowed size or get recommended size
                    if self._last_windowed_size:
                        windowed_width, windowed_height = self._last_windowed_size
                    else:
                        windowed_width, windowed_height = display_manager.get_recommended_resolution()

                    # Create windowed display with external display support
                    screen, _, _ = display_manager.create_compatible_display(
                        preferred_width=windowed_width,
                        preferred_height=windowed_height,
                        fullscreen=False
                    )

            except Exception as e:
                print(f"External display manager failed, using fallback: {e}")
                # Fallback to original logic
                if target_mode:
                    # Store current windowed size before going fullscreen
                    if not current_is_fullscreen:
                        self._last_windowed_size = screen.get_size()
                    screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                else:
                    # Use last windowed size or default
                    windowed_size = self._last_windowed_size or self._windowed_size
                    screen = pygame.display.set_mode(windowed_size, pygame.RESIZABLE)

            # Update screen reference
            self.set_screen_reference(screen)

            # Notify callbacks about the change
            self._notify_screen_mode_change(target_mode)

        return screen

    def toggle_screen_mode(self, screen):
        """
        Toggle between fullscreen and windowed mode

        Args:
            screen: Pygame screen surface

        Returns:
            tuple: (updated_screen, new_mode)
        """
        current_mode = self.is_fullscreen()
        new_mode = not current_mode

        # Update settings
        self.settings_manager.set_screen_mode(new_mode)

        # Apply the new mode
        updated_screen = self.apply_screen_mode(screen, force_mode=new_mode)

        print(f"Screen mode toggled to: {'fullscreen' if new_mode else 'windowed'}")
        return updated_screen, new_mode

    def ensure_consistent_mode(self, screen):
        """
        Ensure the screen is in the correct mode based on settings

        Args:
            screen: Pygame screen surface

        Returns:
            pygame.Surface: Updated screen surface
        """
        return self.apply_screen_mode(screen)

    def get_windowed_size(self):
        """
        Get the preferred windowed size

        Returns:
            tuple: (width, height) for windowed mode
        """
        return self._windowed_size

    def set_windowed_size(self, size):
        """
        Set the preferred windowed size

        Args:
            size (tuple): (width, height) for windowed mode
        """
        self._windowed_size = size
        print(f"Windowed size set to: {size}")

    def sync_with_settings(self):
        """
        Synchronize screen mode with current settings
        """
        if self._current_screen:
            self._current_screen = self.apply_screen_mode(self._current_screen)

    def handle_f_key_toggle(self, screen):
        """
        Handle F key press for screen mode toggle

        Args:
            screen: Pygame screen surface

        Returns:
            pygame.Surface: Updated screen surface
        """
        updated_screen, new_mode = self.toggle_screen_mode(screen)
        return updated_screen

    def handle_escape_key(self, screen):
        """
        Handle Escape key press (exit fullscreen if in fullscreen)

        Args:
            screen: Pygame screen surface

        Returns:
            tuple: (updated_screen, mode_changed)
        """
        if self.is_fullscreen():
            # Exit fullscreen mode
            self.settings_manager.set_screen_mode(False)
            updated_screen = self.apply_screen_mode(screen, force_mode=False)
            return updated_screen, True
        return screen, False


# Global instance
_screen_mode_manager = None

def get_screen_mode_manager():
    """
    Get the global screen mode manager instance

    Returns:
        ScreenModeManager: Global screen mode manager
    """
    global _screen_mode_manager
    if _screen_mode_manager is None:
        _screen_mode_manager = ScreenModeManager()
    return _screen_mode_manager
