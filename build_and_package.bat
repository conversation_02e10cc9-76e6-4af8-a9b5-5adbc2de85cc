@echo off
:: ================================================================
:: WOW Bingo Game - Complete Build and Package Script
:: ================================================================
:: This script handles the complete workflow from source code to
:: distributable installer that can run on any Windows PC.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Colors for output
for /f "delims=" %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "CYAN=%ESC%[36m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "MAGENTA=%ESC%[35m"
set "RESET=%ESC%[0m"

echo %CYAN%================================================================%RESET%
echo %CYAN%    WOW Bingo Game - Complete Build and Package System%RESET%
echo %CYAN%================================================================%RESET%
echo.
echo %BLUE%This script will:%RESET%
echo %BLUE%1. Install all required dependencies%RESET%
echo %BLUE%2. Build a standalone executable%RESET%
echo %BLUE%3. Create a professional Windows installer%RESET%
echo %BLUE%4. Package everything for distribution%RESET%
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Error: Python not found in PATH!%RESET%
    echo %YELLOW%Please install Python 3.7+ from https://www.python.org/downloads/%RESET%
    echo %YELLOW%Make sure to check "Add Python to PATH" during installation.%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Python found%RESET%
python --version

:: Check required files
if not exist "main.py" (
    echo %RED%Error: main.py not found!%RESET%
    echo %YELLOW%Please run this script from the WOW Bingo Game directory.%RESET%
    pause
    exit /b 1
)

if not exist "build_executable.py" (
    echo %RED%Error: build_executable.py not found!%RESET%
    echo %YELLOW%Please ensure all build scripts are present.%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Required files found%RESET%

:: Display workflow menu
:menu
echo.
echo %BLUE%Select build workflow:%RESET%
echo.
echo %GREEN%1.%RESET% Complete Workflow (Recommended)
echo %GREEN%   %RESET% - Install dependencies
echo %GREEN%   %RESET% - Build executable
echo %GREEN%   %RESET% - Create installer
echo %GREEN%   %RESET% - Package for distribution
echo.
echo %GREEN%2.%RESET% Quick Build Only
echo %GREEN%   %RESET% - Build executable only
echo %GREEN%   %RESET% - Skip installer creation
echo.
echo %GREEN%3.%RESET% Dependencies Only
echo %GREEN%   %RESET% - Install/update all dependencies
echo %GREEN%   %RESET% - Skip building
echo.
echo %GREEN%4.%RESET% Installer Only
echo %GREEN%   %RESET% - Create installer from existing executable
echo %GREEN%   %RESET% - Skip building
echo.
echo %GREEN%5.%RESET% Clean Everything
echo %GREEN%   %RESET% - Remove all build artifacts
echo %GREEN%   %RESET% - Start fresh
echo.
echo %GREEN%6.%RESET% Exit
echo.
set /p "choice=Enter your choice (1-6): "

:: Process user choice
if "%choice%"=="1" goto complete_workflow
if "%choice%"=="2" goto quick_build
if "%choice%"=="3" goto dependencies_only
if "%choice%"=="4" goto installer_only
if "%choice%"=="5" goto clean_everything
if "%choice%"=="6" goto exit
echo %RED%Invalid choice. Please try again.%RESET%
echo.
goto menu

:complete_workflow
echo %CYAN%Starting complete workflow...%RESET%
echo.

echo %BLUE%Step 1/4: Installing dependencies...%RESET%
python build_executable.py --install-deps
if %errorlevel% neq 0 (
    echo %RED%Failed to install dependencies!%RESET%
    goto error_exit
)
echo %GREEN%✓ Dependencies installed successfully%RESET%

echo.
echo %BLUE%Step 2/4: Building executable...%RESET%
python build_executable.py --clean --verbose
if %errorlevel% neq 0 (
    echo %RED%Failed to build executable!%RESET%
    goto error_exit
)
echo %GREEN%✓ Executable built successfully%RESET%

echo.
echo %BLUE%Step 3/4: Creating installer...%RESET%
python create_installer.py --type inno
if %errorlevel% neq 0 (
    echo %YELLOW%Warning: Installer creation failed or skipped%RESET%
    echo %YELLOW%You can still distribute the executable directly%RESET%
)

echo.
echo %BLUE%Step 4/4: Creating portable package...%RESET%
python -c "
import zipfile
import shutil
from pathlib import Path

dist_dir = Path('dist')
if (dist_dir / 'WOW_Bingo_Game.exe').exists():
    with zipfile.ZipFile(dist_dir / 'WOW_Bingo_Game_Portable.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.write(dist_dir / 'WOW_Bingo_Game.exe', 'WOW_Bingo_Game.exe')
        zipf.writestr('README.txt', '''WOW Bingo Game - Portable Version

To run the game:
1. Extract this ZIP file
2. Double-click WOW_Bingo_Game.exe
3. Enjoy!

No installation required.
''')
    print('Portable package created: dist/WOW_Bingo_Game_Portable.zip')
else:
    print('Executable not found, skipping portable package')
"

goto workflow_complete

:quick_build
echo %CYAN%Starting quick build...%RESET%
python build_executable.py --verbose
if %errorlevel% neq 0 (
    echo %RED%Build failed!%RESET%
    goto error_exit
)
goto build_complete

:dependencies_only
echo %CYAN%Installing dependencies...%RESET%
python build_executable.py --install-deps
if %errorlevel% neq 0 (
    echo %RED%Failed to install dependencies!%RESET%
    goto error_exit
)
echo %GREEN%Dependencies installed successfully!%RESET%
pause
goto menu

:installer_only
echo %CYAN%Creating installer from existing executable...%RESET%
if not exist "dist\WOW_Bingo_Game.exe" (
    echo %RED%Error: Executable not found in dist directory!%RESET%
    echo %YELLOW%Please build the executable first.%RESET%
    pause
    goto menu
)
python create_installer.py --type inno
goto installer_complete

:clean_everything
echo %CYAN%Cleaning all build artifacts...%RESET%
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "installer" rmdir /s /q "installer"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo %GREEN%All build artifacts cleaned!%RESET%
pause
goto menu

:workflow_complete
echo.
echo %GREEN%================================================================%RESET%
echo %GREEN%    COMPLETE WORKFLOW FINISHED SUCCESSFULLY!%RESET%
echo %GREEN%================================================================%RESET%
echo.
echo %CYAN%Distribution files created:%RESET%
if exist "dist\WOW_Bingo_Game.exe" (
    echo %GREEN%✓ Standalone Executable:%RESET% dist\WOW_Bingo_Game.exe
    for %%F in ("dist\WOW_Bingo_Game.exe") do echo %BLUE%  Size: %%~zF bytes%RESET%
)
if exist "dist\WOW_Bingo_Game_Portable.zip" (
    echo %GREEN%✓ Portable Package:%RESET% dist\WOW_Bingo_Game_Portable.zip
)
if exist "installer\output\*.exe" (
    echo %GREEN%✓ Professional Installer:%RESET% installer\output\
)
echo.
echo %YELLOW%What you can distribute:%RESET%
echo %BLUE%• Single executable (dist\WOW_Bingo_Game.exe)%RESET%
echo %BLUE%• Portable ZIP package (dist\WOW_Bingo_Game_Portable.zip)%RESET%
echo %BLUE%• Professional installer (installer\output\*.exe)%RESET%
echo.
echo %CYAN%All files can run on any Windows PC without Python!%RESET%
goto success_exit

:build_complete
echo.
echo %GREEN%================================================================%RESET%
echo %GREEN%    BUILD COMPLETED SUCCESSFULLY!%RESET%
echo %GREEN%================================================================%RESET%
echo.
if exist "dist\WOW_Bingo_Game.exe" (
    echo %GREEN%✓ Executable created:%RESET% dist\WOW_Bingo_Game.exe
    for %%F in ("dist\WOW_Bingo_Game.exe") do echo %BLUE%  Size: %%~zF bytes%RESET%
    echo.
    echo %CYAN%Your executable can run on any Windows PC!%RESET%
)
goto success_exit

:installer_complete
echo.
echo %GREEN%================================================================%RESET%
echo %GREEN%    INSTALLER CREATED SUCCESSFULLY!%RESET%
echo %GREEN%================================================================%RESET%
echo.
if exist "installer\output\*.exe" (
    echo %GREEN%✓ Professional installer created in:%RESET% installer\output\
    echo %CYAN%The installer can be distributed to end users.%RESET%
)
goto success_exit

:error_exit
echo.
echo %RED%================================================================%RESET%
echo %RED%    BUILD PROCESS FAILED!%RESET%
echo %RED%================================================================%RESET%
echo.
echo %YELLOW%Troubleshooting steps:%RESET%
echo %BLUE%1. Check error messages above%RESET%
echo %BLUE%2. Try installing dependencies first (option 3)%RESET%
echo %BLUE%3. Ensure Python 3.7+ is installed%RESET%
echo %BLUE%4. Check that all source files are present%RESET%
echo %BLUE%5. Try cleaning everything first (option 5)%RESET%
echo.
pause
goto menu

:success_exit
echo.
echo %BLUE%Press any key to return to menu or Ctrl+C to exit...%RESET%
pause >nul
goto menu

:exit
echo.
echo %CYAN%Thank you for using the WOW Bingo Game build system!%RESET%
echo %BLUE%For support and documentation, check BUILD_README.md%RESET%
pause
exit /b 0
