"""
Cryptographic utilities for the payment system.

This module provides cryptographic functions for voucher generation and verification.
It implements Ed25519 signatures for secure voucher validation.
"""

import os
import struct
import time
import hashlib
import base64
import platform
import uuid
import wmi
import winreg

# Try to import nacl, but provide fallback if not available
try:
    import nacl.signing
    import nacl.encoding
    NACL_AVAILABLE = True
except ImportError:
    NACL_AVAILABLE = False
    print("Warning: PyNaCl not installed. Using fallback cryptography.")

# <PERSON><PERSON>ford's Base32 alphabet (no I/O confusion)
CROCKFORD_ALPHABET = "0123456789ABCDEFGHJKMNPQRSTVWXYZ"

class CryptoUtils:
    """Cryptographic utilities for the payment system."""

    @staticmethod
    def get_machine_uuid():
        """
        Get the machine's UUID from BIOS.

        Returns:
            str: UUID string or None if not available
        """
        try:
            c = wmi.WMI()
            for system in c.Win32_ComputerSystemProduct():
                if system.UUID:
                    return system.UUID.upper()

            # Fallback to Windows registry
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography") as key:
                    machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                    return machine_guid.upper()
            except:
                pass

            # Last resort: use MAC address + volume serial
            return CryptoUtils.get_fallback_machine_id()
        except Exception as e:
            print(f"Error getting machine UUID: {e}")
            return CryptoUtils.get_fallback_machine_id()

    @staticmethod
    def get_fallback_machine_id():
        """
        Get a fallback machine ID using MAC address and volume serial.

        Returns:
            str: A hash-based machine ID
        """
        components = []

        # Get MAC address
        try:
            mac = uuid.getnode()
            mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                              for elements in range(0, 8*6, 8)][::-1])
            components.append(mac_str)
        except:
            components.append("UNKNOWN_MAC")

        # Get volume serial for C: drive
        try:
            import ctypes
            volume_serial = ctypes.c_ulong()
            ctypes.windll.kernel32.GetVolumeInformationW(
                ctypes.c_wchar_p("C:\\"),
                None, 0,
                ctypes.byref(volume_serial),
                None, None, None, 0
            )
            components.append(str(volume_serial.value))
        except:
            components.append("UNKNOWN_VOLUME")

        # Get computer name
        try:
            components.append(platform.node())
        except:
            components.append("UNKNOWN_NAME")

        # Create a hash of all components
        machine_id = hashlib.sha256('|'.join(components).encode()).hexdigest()
        return machine_id.upper()

    @staticmethod
    def to_base32(data):
        """
        Convert binary data to Crockford's Base32.

        Args:
            data: Binary data to encode

        Returns:
            str: Base32 encoded string
        """
        if NACL_AVAILABLE:
            # Use nacl's Base32 encoder if available
            return nacl.encoding.Base32Encoder.encode(data).decode('ascii').rstrip('=')

        # Fallback implementation of Crockford's Base32
        result = ""
        bits = 0
        value = 0

        for byte in data:
            value = (value << 8) | byte
            bits += 8

            while bits >= 5:
                bits -= 5
                index = (value >> bits) & 0x1F
                result += CROCKFORD_ALPHABET[index]

        if bits > 0:
            index = (value << (5 - bits)) & 0x1F
            result += CROCKFORD_ALPHABET[index]

        return result

    @staticmethod
    def from_base32(text):
        """
        Convert Crockford's Base32 to binary data.

        Args:
            text: Base32 encoded string

        Returns:
            bytes: Decoded binary data
        """
        if NACL_AVAILABLE:
            # Use nacl's Base32 decoder if available
            # Add padding if needed
            padded = text + '=' * ((8 - len(text) % 8) % 8)
            return nacl.encoding.Base32Encoder.decode(padded)

        # Fallback implementation
        value = 0
        bits = 0
        result = bytearray()

        for char in text.upper():
            if char not in CROCKFORD_ALPHABET:
                continue

            index = CROCKFORD_ALPHABET.index(char)
            value = (value << 5) | index
            bits += 5

            if bits >= 8:
                bits -= 8
                result.append((value >> bits) & 0xFF)

        return bytes(result)

    @staticmethod
    def format_voucher(voucher_str):
        """
        Format a voucher string with dashes for readability.

        Args:
            voucher_str: Raw voucher string

        Returns:
            str: Formatted voucher string with dashes
        """
        # Group into chunks of 5 characters
        chunks = [voucher_str[i:i+5] for i in range(0, len(voucher_str), 5)]
        return '-'.join(chunks)

    @staticmethod
    def unformat_voucher(formatted_voucher):
        """
        Remove formatting from a voucher string.

        Args:
            formatted_voucher: Voucher string with formatting

        Returns:
            str: Raw voucher string without dashes
        """
        return formatted_voucher.replace('-', '').upper()

    @staticmethod
    def hash_voucher(voucher_code):
        """
        Create a hash of a voucher code for storage.

        Args:
            voucher_code: Voucher code to hash

        Returns:
            str: Hashed voucher code
        """
        # Create a SHA-256 hash of the voucher code
        return hashlib.sha256(voucher_code.encode()).hexdigest()

    @staticmethod
    def validate_voucher(voucher_code, machine_uuid):
        """
        Validate a voucher code.

        Args:
            voucher_code: Voucher code to validate
            machine_uuid: Machine UUID to validate against

        Returns:
            dict: Validation result with keys:
                - valid: True if valid, False otherwise
                - amount: Credit amount (if valid)
                - share: Commission share percentage (if valid)
                - expiry: Expiry timestamp (if valid)
        """
        try:
            # Clean up the voucher code
            voucher_code = voucher_code.strip().upper().replace('-', '')

            # Check character set
            for char in voucher_code:
                if char not in CROCKFORD_ALPHABET:
                    return {"valid": False}

            # Check if this is a compact voucher (15 characters or less)
            if len(voucher_code) <= 15:
                # Try to validate as a compact voucher
                try:
                    # Import the compact voucher generator
                    from compact_voucher_generator import CompactVoucherGenerator
                    generator = CompactVoucherGenerator()
                    result = generator.validate_voucher(voucher_code)

                    if result["valid"]:
                        return {
                            "valid": True,
                            "amount": result["amount"],
                            "share": result["share"],
                            "expiry": result["expiry"]
                        }
                    else:
                        # If compact validation fails, continue with legacy validation
                        pass
                except Exception as e:
                    print(f"Compact voucher validation failed: {e}")
                    # Continue with legacy validation

            # Legacy validation for longer vouchers

            # Check minimum length for legacy vouchers
            if len(voucher_code) < 20:
                return {"valid": False}

            # Extract the checksum (last character)
            checksum_char = voucher_code[-1]
            voucher_body = voucher_code[:-1]

            # Calculate the checksum
            checksum_value = 0
            for i, char in enumerate(voucher_body):
                checksum_value += (i + 1) * CROCKFORD_ALPHABET.index(char)
            expected_checksum = CROCKFORD_ALPHABET[checksum_value % len(CROCKFORD_ALPHABET)]

            # Validate checksum
            if checksum_char != expected_checksum:
                return {"valid": False}

            # Try to decode the voucher body
            try:
                # If PyNaCl is available, try to decode and validate the cryptographic signature
                if NACL_AVAILABLE:
                    # Add padding if needed for Base32 decoding
                    padded = voucher_body + '=' * ((8 - len(voucher_body) % 8) % 8)

                    # Decode the voucher
                    voucher_bin = nacl.encoding.Base32Encoder.decode(padded)

                    # Extract components from the binary data
                    # Format: [UUID(16)] + [Amount(2)] + [Share(1)] + [Expiry(4)] + [Nonce(4)] + [Signature(64)]
                    if len(voucher_bin) < 91:  # Minimum length for a valid voucher
                        # Fall back to the simple validation if binary data is too short
                        raise ValueError("Voucher binary data too short")

                    # Extract the components
                    uuid_bytes = voucher_bin[:16]
                    amount = struct.unpack('>H', voucher_bin[16:18])[0]
                    share = voucher_bin[18]
                    expiry = struct.unpack('>I', voucher_bin[19:23])[0]

                    # Check if this is a universal voucher (all zeros UUID)
                    is_universal = all(b == 0 for b in uuid_bytes)

                    # If not universal, validate against the machine UUID
                    if not is_universal and machine_uuid:
                        # Convert machine UUID to bytes for comparison
                        try:
                            machine_uuid_bytes = bytes.fromhex(machine_uuid.replace('-', ''))[:16]
                            # Pad if needed
                            if len(machine_uuid_bytes) < 16:
                                machine_uuid_bytes = machine_uuid_bytes + bytes([0] * (16 - len(machine_uuid_bytes)))

                            # Compare UUIDs
                            if uuid_bytes != machine_uuid_bytes:
                                # UUID mismatch - voucher is for a different machine
                                return {"valid": False}
                        except:
                            # If we can't parse the machine UUID, accept the voucher anyway
                            pass

                    # Check expiry
                    # An expiry of 0 means the voucher never expires
                    if expiry != 0 and expiry < int(time.time()):
                        # Voucher has expired
                        return {"valid": False, "message": "Voucher has expired"}

                    # Voucher is valid
                    return {
                        "valid": True,
                        "amount": amount,
                        "share": share,
                        "expiry": expiry
                    }

            except Exception as e:
                # If cryptographic validation fails, fall back to the simple validation
                print(f"Cryptographic validation failed: {e}")
                # Continue with fallback validation

            # Fallback validation for compatibility with older vouchers
            # Extract a pseudo-random amount based on the voucher code
            voucher_hash = CryptoUtils.hash_voucher(voucher_code)
            amount_seed = int(voucher_hash[:8], 16)

            # Generate a deterministic amount between 10 and 1000
            amount = (amount_seed % 991) + 10

            # Generate a deterministic share percentage between 70 and 90
            share = (amount_seed % 21) + 70

            # Generate an expiry date 30 days from now
            expiry = int(time.time()) + (30 * 24 * 60 * 60)

            return {
                "valid": True,
                "amount": amount,
                "share": share,
                "expiry": expiry
            }

        except Exception as e:
            print(f"Error validating voucher: {e}")
            return {"valid": False}
